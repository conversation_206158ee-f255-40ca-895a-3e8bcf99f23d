﻿@model LoginFormModel

@{
    ViewData["Title"] = "Влизане";
}

<h1 class="text-center">@ViewData["Title"]</h1>
<div class="row text-center">
    <div class="col-md-4 offset-md-4">
        <section>
            <form id="account" method="post">
                <h3 class="text-center ">Използвай имейл за да влезеш.</h3>
                <hr />
                <div asp-validation-summary="ModelOnly" class="text-danger"></div>
                <div class="form-floating">
                    <input asp-for="Email" class="form-control" autocomplete="username" aria-required="true" />
                    <label asp-for="Email" class="form-label">Имейл</label>
                    <span asp-validation-for="Email" class="text-danger"></span>
                </div>
                <div class="form-floating">
                    <input asp-for="Password" class="form-control" autocomplete="current-password" aria-required="true" />
                    <label asp-for="Password" class="form-label"></label>
                    <span asp-validation-for="Password" class="text-danger"></span>
                </div>
                <div>
                    <div class="checkbox">
                        <label asp-for="RememberMe" class="form-label">
                            <input class="form-check-input" asp-for="RememberMe" />
                            @Html.DisplayNameFor(m => m.RememberMe)
                        </label>
                    </div>
                </div>
                <div>
                    <button id="login-submit" type="submit" class="w-100 btn btn-lg btn-primary">Влез</button>
                </div>
                <div>
                    <p>
                        <a asp-controller="User" asp-action="Register" asp-route-retunUlr ="@Model.ReturnUrl">Създаване на нов профил</a>
                    </p>
                </div>
            </form>
        </section>
    </div>
</div>

@section Scripts {
            <partial name="_ValidationScriptsPartial" />
}
