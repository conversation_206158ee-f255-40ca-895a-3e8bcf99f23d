namespace TaskManager.Data.Models
{
    using System.ComponentModel.DataAnnotations;
    using System.ComponentModel.DataAnnotations.Schema;
    using static TaskManager.Common.EntityValidationConstants.KanbanCard;

    /// <summary>
    /// Represents a card in a Kanban board that can be linked to a GeoTask
    /// </summary>
    public class KanbanCard
    {
        public KanbanCard()
        {
            this.Id = Guid.NewGuid();
            this.CreatedOn = DateTime.UtcNow;
            this.IsActive = true;
        }

        [Key]
        public Guid Id { get; set; }

        [Required]
        [MaxLength(TitleMaxLength)]
        public string Title { get; set; } = null!;

        [MaxLength(DescriptionMaxLength)]
        public string? Description { get; set; }

        [Required]
        public int Position { get; set; }

        [MaxLength(ColorMaxLength)]
        public string? Color { get; set; }

        [MaxLength(LabelsMaxLength)]
        public string? Labels { get; set; }

        public DateTime? DueDate { get; set; }

        public DateTime? StartTime { get; set; }

        [Required]
        public DateTime CreatedOn { get; set; }

        public DateTime? UpdatedOn { get; set; }

        [Required]
        public bool IsActive { get; set; }

        // Foreign Keys
        [Required]
        public Guid ColumnId { get; set; }

        public Guid? AssignedToId { get; set; }

        public Guid? CreatedById { get; set; }

        // Optional link to GeoTask (one-to-one relationship)
        public Guid? GeoTaskId { get; set; }

        // Navigation properties
        [ForeignKey(nameof(ColumnId))]
        public virtual KanbanColumn Column { get; set; } = null!;

        [ForeignKey(nameof(AssignedToId))]
        public virtual ApplicationUser? AssignedTo { get; set; }

        [ForeignKey(nameof(CreatedById))]
        public virtual ApplicationUser? CreatedBy { get; set; }

        [ForeignKey(nameof(GeoTaskId))]
        public virtual GeoTask? GeoTask { get; set; }
    }
}
