﻿@using static TaskManager.Common.NotificationMessages;
@model ComentarViewModel

 <div class="row">
   <div class="col-sm-12 offset-lg-2 col-lg-8 offset-xl-3 col-xl-6">
      <form method="post">
         <div class="form-group">
            <label asp-for="Description"></label>
                <textarea asp-for="Description" rows="4" class="form-control" placeholder="Коментар..."></textarea>
                <span asp-validation-for="Description" class="text-danger"></span>
         </div>
          <div class="text-center">
            <input class="btn btn-primary mt-3" type="submit" name="Add" value="Запази" />
            <input class="btn btn-danger mt-3" type="submit" name="Close" value="Откажи" />
         </div>
      </form>
   </div>
</div>

@section Scripts
    {
                <partial name ="_ValidationScriptsPartial">
}
