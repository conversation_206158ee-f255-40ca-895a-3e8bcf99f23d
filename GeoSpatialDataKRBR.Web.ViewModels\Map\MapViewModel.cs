namespace GeoSpatialDataKRBR.Web.ViewModels.Map
{
    using GeoSpatialDataKRBR.Web.ViewModels.GeoLayer;

    public class MapViewModel
    {
        public MapViewModel()
        {
            this.Layers = new List<GeoLayerListViewModel>();
            this.BaseLayers = new List<GeoLayerListViewModel>();
        }

        public double CenterLatitude { get; set; } = 42.7339; // Bulgaria center
        public double CenterLongitude { get; set; } = 25.4858; // Bulgaria center
        public int ZoomLevel { get; set; } = 7;
        public int MinZoom { get; set; } = 1;
        public int MaxZoom { get; set; } = 18;

        public ICollection<GeoLayerListViewModel> Layers { get; set; }
        public ICollection<GeoLayerListViewModel> BaseLayers { get; set; }

        public string? SelectedBaseLayerId { get; set; }
        public bool ShowLayerControl { get; set; } = true;
        public bool ShowScaleControl { get; set; } = true;
        public bool ShowZoomControl { get; set; } = true;
    }
}
