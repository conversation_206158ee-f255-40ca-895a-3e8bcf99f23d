﻿function CreateMonthAndProjectCountChartOptions(MonthAndProjectCount) {
    var chartData = {
        series: [{
            name: 'Брой проекти',
            data: MonthAndProjectCount.map(function (item) { return item.projectCount; })
        }],
        xaxis: {
            categories: MonthAndProjectCount.map(function (item) { return item.monthName; })
        }
    };

    var chartOptions = {
        chart: {
            type: 'bar',
            height: 400,
            toolbar: {
                show: true,
                tools: {
                    download: true,
                    selection: false,
                    zoom: false,
                    zoomin: false,
                    zoomout: false,
                    pan: false,
                    reset: false
                }
            },
            animations: {
                enabled: true,
                easing: 'easeinout',
                speed: 800,
                animateGradually: {
                    enabled: true,
                    delay: 150
                },
                dynamicAnimation: {
                    enabled: true,
                    speed: 350
                }
            }
        },
        colors: ['#667eea'],
        series: chartData.series,
        xaxis: {
            ...chartData.xaxis,
            labels: {
                style: {
                    colors: '#64748b',
                    fontSize: '12px',
                    fontWeight: 500
                }
            }
        },
        yaxis: {
            labels: {
                style: {
                    colors: '#64748b',
                    fontSize: '12px',
                    fontWeight: 500
                }
            }
        },
        plotOptions: {
            bar: {
                borderRadius: 8,
                columnWidth: '60%',
                dataLabels: {
                    position: 'top'
                }
            }
        },
        dataLabels: {
            enabled: true,
            offsetY: -20,
            style: {
                fontSize: '12px',
                colors: ['#304758']
            }
        },
        grid: {
            borderColor: '#e2e8f0',
            strokeDashArray: 4,
            xaxis: {
                lines: {
                    show: false
                }
            }
        },
        tooltip: {
            theme: 'dark',
            style: {
                fontSize: '12px'
            },
            y: {
                formatter: function (val) {
                    return val + " проекта"
                }
            }
        }
    };
    return chartOptions;
}

function CreateMonthAndProjectPriceSumChartOptions(MonthAndProjectCount) {
    var chartData = {
        series: [{
            name: 'Стойност на проектите',
            data: MonthAndProjectCount.map(function (item) { return item.price;})
        }],
        xaxis: {
            categories: MonthAndProjectCount.map(function (item) { return item.monthName; })
        }
    };

    var chartOptions = {
        chart: {
            type: 'area',
            height: 400,
            toolbar: {
                show: true,
                tools: {
                    download: true,
                    selection: false,
                    zoom: false,
                    zoomin: false,
                    zoomout: false,
                    pan: false,
                    reset: false
                }
            },
            animations: {
                enabled: true,
                easing: 'easeinout',
                speed: 800
            }
        },
        colors: ['#764ba2'],
        fill: {
            type: 'gradient',
            gradient: {
                shadeIntensity: 1,
                opacityFrom: 0.7,
                opacityTo: 0.3,
                stops: [0, 90, 100]
            }
        },
        series: chartData.series,
        xaxis: {
            ...chartData.xaxis,
            labels: {
                style: {
                    colors: '#64748b',
                    fontSize: '12px',
                    fontWeight: 500
                }
            }
        },
        yaxis: {
            labels: {
                style: {
                    colors: '#64748b',
                    fontSize: '12px',
                    fontWeight: 500
                },
                formatter: function (val) {
                    return val.toLocaleString() + ' лв.'
                }
            }
        },
        dataLabels: {
            enabled: false
        },
        stroke: {
            curve: 'smooth',
            width: 3
        },
        grid: {
            borderColor: '#e2e8f0',
            strokeDashArray: 4
        },
        tooltip: {
            theme: 'dark',
            style: {
                fontSize: '12px'
            },
            y: {
                formatter: function (val) {
                    return val.toLocaleString() + " лв."
                }
            }
        }
    };
    return chartOptions;
}

function CreatePieChartForTaskTypes(TypeProjectCountData) {
    var PieData = {
        series: [TypeProjectCountData.map(function (item) { return item.projectCount; })],
        labels: [TypeProjectCountData.map(function (item) { return item.type; })]
    };

    var PieOptions = {
        series: PieData.series.join(',').split(',').map(Number),
        chart: {
            width: '100%',
            type: 'donut',
            height: 400,
            animations: {
                enabled: true,
                easing: 'easeinout',
                speed: 800
            }
        },
        labels: PieData.labels.join(',').split(','),
        colors: ['#667eea', '#764ba2', '#f093fb', '#f5576c', '#4facfe', '#00f2fe', '#43e97b', '#38f9d7'],
        plotOptions: {
            pie: {
                donut: {
                    size: '60%',
                    labels: {
                        show: true,
                        name: {
                            show: true,
                            fontSize: '16px',
                            fontWeight: 600,
                            color: '#374151'
                        },
                        value: {
                            show: true,
                            fontSize: '24px',
                            fontWeight: 700,
                            color: '#1f2937',
                            formatter: function (val) {
                                return val
                            }
                        },
                        total: {
                            show: true,
                            showAlways: true,
                            label: 'Общо',
                            fontSize: '16px',
                            fontWeight: 600,
                            color: '#374151',
                            formatter: function (w) {
                                return w.globals.seriesTotals.reduce((a, b) => {
                                    return a + b
                                }, 0)
                            }
                        }
                    }
                },
                dataLabels: {
                    offset: -10
                }
            }
        },
        dataLabels: {
            enabled: true,
            style: {
                fontSize: '12px',
                fontWeight: 600,
                colors: ['#fff']
            },
            formatter(val, opts) {
                const name = opts.w.globals.labels[opts.seriesIndex]
                return [name, val.toFixed(1) + '%']
            },
            dropShadow: {
                enabled: true,
                top: 1,
                left: 1,
                blur: 1,
                color: '#000',
                opacity: 0.45
            }
        },
        legend: {
            show: true,
            position: 'bottom',
            horizontalAlign: 'center',
            fontSize: '14px',
            fontWeight: 500,
            labels: {
                colors: '#374151'
            },
            markers: {
                width: 12,
                height: 12,
                radius: 6
            },
            itemMargin: {
                horizontal: 10,
                vertical: 5
            }
        },
        tooltip: {
            theme: 'dark',
            style: {
                fontSize: '12px'
            },
            y: {
                formatter: function (val) {
                    return val + " проекта"
                }
            }
        },
        responsive: [{
            breakpoint: 768,
            options: {
                chart: {
                    height: 300
                },
                legend: {
                    position: 'bottom'
                }
            }
        }]
    };
    return PieOptions;
}

function ChangeMinWidth(count) {
    var MonthProjectCountArticle = document.getElementById('MonthProjectCountArticle');
    var MonthProjectSumPriceArticle = document.getElementById('MonthProjectSumPriceArticle');

    if (MonthProjectCountArticle) {
        MonthProjectCountArticle.style.minWidth = count + 'em';
    }
    if (MonthProjectSumPriceArticle) {
        MonthProjectSumPriceArticle.style.minWidth = count + 'em';
    }
}

// Modern Performance Metrics Chart
function CreatePerformanceMetricsChart(monthlyData) {
    var options = {
        series: [{
            name: 'Проекти',
            type: 'column',
            data: monthlyData.map(item => item.projectCount)
        }, {
            name: 'Стойност (хил. лв.)',
            type: 'line',
            data: monthlyData.map(item => Math.round(item.price / 1000))
        }],
        chart: {
            height: 400,
            type: 'line',
            toolbar: {
                show: true,
                tools: {
                    download: true,
                    selection: false,
                    zoom: false,
                    zoomin: false,
                    zoomout: false,
                    pan: false,
                    reset: false
                }
            },
            animations: {
                enabled: true,
                easing: 'easeinout',
                speed: 800
            }
        },
        colors: ['#667eea', '#764ba2'],
        stroke: {
            width: [0, 4],
            curve: 'smooth'
        },
        plotOptions: {
            bar: {
                borderRadius: 8,
                columnWidth: '50%'
            }
        },
        fill: {
            opacity: [0.85, 1],
            gradient: {
                inverseColors: false,
                shade: 'light',
                type: "vertical",
                opacityFrom: 0.85,
                opacityTo: 0.55,
                stops: [0, 100, 100, 100]
            }
        },
        labels: monthlyData.map(item => item.monthName),
        markers: {
            size: 6,
            colors: ['#764ba2'],
            strokeColors: '#fff',
            strokeWidth: 2,
            hover: {
                size: 8
            }
        },
        xaxis: {
            type: 'category',
            labels: {
                style: {
                    colors: '#64748b',
                    fontSize: '12px',
                    fontWeight: 500
                }
            }
        },
        yaxis: [{
            title: {
                text: 'Брой проекти',
                style: {
                    color: '#667eea',
                    fontSize: '14px',
                    fontWeight: 600
                }
            },
            labels: {
                style: {
                    colors: '#64748b',
                    fontSize: '12px'
                }
            }
        }, {
            opposite: true,
            title: {
                text: 'Стойност (хил. лв.)',
                style: {
                    color: '#764ba2',
                    fontSize: '14px',
                    fontWeight: 600
                }
            },
            labels: {
                style: {
                    colors: '#64748b',
                    fontSize: '12px'
                }
            }
        }],
        tooltip: {
            theme: 'dark',
            shared: true,
            intersect: false,
            y: [{
                formatter: function (y) {
                    if (typeof y !== "undefined") {
                        return y.toFixed(0) + " проекта";
                    }
                    return y;
                }
            }, {
                formatter: function (y) {
                    if (typeof y !== "undefined") {
                        return y.toFixed(0) + " хил. лв.";
                    }
                    return y;
                }
            }]
        },
        legend: {
            show: true,
            position: 'top',
            horizontalAlign: 'right',
            fontSize: '14px',
            fontWeight: 500,
            labels: {
                colors: '#374151'
            }
        },
        grid: {
            borderColor: '#e2e8f0',
            strokeDashArray: 4
        }
    };
    return options;
}

// Productivity Trend Chart
function CreateProductivityTrendChart(monthlyData) {
    var productivityData = monthlyData.map(item => {
        return item.projectCount > 0 ? Math.round(item.price / item.projectCount) : 0;
    });

    var options = {
        series: [{
            name: 'Средна стойност на проект',
            data: productivityData
        }],
        chart: {
            height: 350,
            type: 'line',
            zoom: {
                enabled: false
            },
            toolbar: {
                show: true,
                tools: {
                    download: true,
                    selection: false,
                    zoom: false,
                    zoomin: false,
                    zoomout: false,
                    pan: false,
                    reset: false
                }
            },
            animations: {
                enabled: true,
                easing: 'easeinout',
                speed: 800
            }
        },
        colors: ['#f093fb'],
        stroke: {
            curve: 'smooth',
            width: 4
        },
        fill: {
            type: 'gradient',
            gradient: {
                shade: 'dark',
                gradientToColors: ['#f5576c'],
                shadeIntensity: 1,
                type: 'horizontal',
                opacityFrom: 1,
                opacityTo: 1,
                stops: [0, 100, 100, 100]
            }
        },
        markers: {
            size: 6,
            colors: ['#f5576c'],
            strokeColors: '#fff',
            strokeWidth: 2,
            hover: {
                size: 8
            }
        },
        xaxis: {
            categories: monthlyData.map(item => item.monthName),
            labels: {
                style: {
                    colors: '#64748b',
                    fontSize: '12px',
                    fontWeight: 500
                }
            }
        },
        yaxis: {
            title: {
                text: 'Средна стойност (лв.)',
                style: {
                    color: '#374151',
                    fontSize: '14px',
                    fontWeight: 600
                }
            },
            labels: {
                style: {
                    colors: '#64748b',
                    fontSize: '12px'
                },
                formatter: function (val) {
                    return val.toLocaleString() + ' лв.'
                }
            }
        },
        tooltip: {
            theme: 'dark',
            y: {
                formatter: function (val) {
                    return val.toLocaleString() + " лв."
                }
            }
        },
        grid: {
            borderColor: '#e2e8f0',
            strokeDashArray: 4
        }
    };
    return options;
}