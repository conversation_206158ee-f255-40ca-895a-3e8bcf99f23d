/* Global Styles */
html {
  font-size: 14px;
  height: 100%;
}

@media (min-width: 768px) {
  html {
    font-size: 16px;
  }
}

body {
  margin: 0;
  padding: 0;
  font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  min-height: 100vh;
  color: #333;
}

.btn:focus, .btn:active:focus, .btn-link.nav-link:focus, .form-control:focus, .form-check-input:focus {
  box-shadow: 0 0 0 0.1rem white, 0 0 0 0.25rem #258cfb;
}

/* Navigation */
.navbar {
  background: rgba(255, 255, 255, 0.95) !important;
  backdrop-filter: blur(10px);
  box-shadow: 0 2px 20px rgba(0, 0, 0, 0.1);
  border: none !important;
}

.navbar-brand {
  font-weight: 600;
  color: #667eea !important;
  font-size: 1.5rem;
}

.nav-link {
  font-weight: 500;
  transition: color 0.3s ease;
}

.nav-link:hover {
  color: #667eea !important;
}

/* Container */
.container {
  background: rgba(255, 255, 255, 0.95);
  border-radius: 15px;
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.2);
  padding: 40px;
  margin-top: 30px;
  backdrop-filter: blur(10px);
}

/* Footer */
.footer {
  background: rgba(255, 255, 255, 0.9);
  backdrop-filter: blur(10px);
  border-top: 1px solid rgba(255, 255, 255, 0.2);
  margin-top: 50px;
}

/* Map Styles */
.map-container {
  height: 100vh;
  width: 100%;
  position: relative;
  margin: 0;
  padding: 0;
}

.map-header {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(10px);
  padding: 15px 30px;
  z-index: 1000;
  display: flex;
  justify-content: space-between;
  align-items: center;
  box-shadow: 0 2px 20px rgba(0, 0, 0, 0.1);
}

.map-header h1 {
  margin: 0;
  font-size: 24px;
  color: #667eea;
  font-weight: 600;
}

.map-content {
  height: 100vh;
  padding-top: 80px;
}

#leaflet-map {
  height: 100%;
  width: 100%;
}

/* Layer Control */
.layer-control {
  position: absolute;
  top: 100px;
  right: 20px;
  background: rgba(255, 255, 255, 0.95);
  border-radius: 12px;
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.2);
  padding: 20px;
  max-width: 350px;
  z-index: 1000;
  max-height: 70vh;
  overflow-y: auto;
}

.layer-control h3 {
  margin: 0 0 20px 0;
  font-size: 18px;
  color: #667eea;
  border-bottom: 2px solid #667eea;
  padding-bottom: 10px;
}

.layer-item {
  margin-bottom: 15px;
  padding: 12px;
  border-radius: 8px;
  border: 1px solid #f0f0f0;
  background: rgba(255, 255, 255, 0.7);
}

.layer-item:hover {
  background: rgba(255, 255, 255, 0.9);
  border-color: #667eea;
}

.layer-name {
  font-weight: 600;
  color: #333;
  margin-bottom: 5px;
}

.layer-description {
  font-size: 12px;
  color: #666;
  margin-bottom: 8px;
}

/* Welcome Section */
.welcome-section {
  text-align: center;
  padding: 60px 20px;
  color: white;
}

.welcome-section h1 {
  font-size: 3rem;
  font-weight: 700;
  margin-bottom: 20px;
  text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.3);
}

.btn-primary {
  background: linear-gradient(135deg, #667eea, #764ba2);
  border: none;
  padding: 12px 30px;
  border-radius: 25px;
}

/* Map Styles */
.map-container {
  height: 100vh;
  width: 100%;
  position: relative;
  margin: 0;
  padding: 0;
}

.map-header {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(10px);
  padding: 15px 30px;
  z-index: 1000;
  display: flex;
  justify-content: space-between;
  align-items: center;
  box-shadow: 0 2px 20px rgba(0, 0, 0, 0.1);
}

.map-header h1 {
  margin: 0;
  font-size: 24px;
  color: #667eea;
  font-weight: 600;
}

.map-header .user-info {
  display: flex;
  align-items: center;
  gap: 20px;
  color: #555;
  font-weight: 500;
}

.map-content {
  height: 100vh;
  padding-top: 80px;
}

#leaflet-map {
  height: 100%;
  width: 100%;
  border-radius: 0;
}

/* Layer Control */
.layer-control {
  position: absolute;
  top: 100px;
  right: 20px;
  background: rgba(255, 255, 255, 0.95);
  border-radius: 12px;
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.2);
  padding: 20px;
  max-width: 350px;
  z-index: 1000;
  max-height: 70vh;
  overflow-y: auto;
  backdrop-filter: blur(10px);
}

.layer-control h3 {
  margin: 0 0 20px 0;
  font-size: 18px;
  color: #667eea;
  border-bottom: 2px solid #667eea;
  padding-bottom: 10px;
  font-weight: 600;
}

.layer-section {
  margin-bottom: 25px;
}

.layer-section h4 {
  margin: 0 0 15px 0;
  font-size: 16px;
  color: #555;
  font-weight: 600;
  padding: 8px 0;
  border-bottom: 1px solid #eee;
}

.layer-item {
  margin-bottom: 15px;
  padding: 12px;
  border-radius: 8px;
  transition: all 0.3s ease;
  border: 1px solid #f0f0f0;
  background: rgba(255, 255, 255, 0.7);
}

.layer-item:hover {
  background: rgba(255, 255, 255, 0.9);
  border-color: #667eea;
  transform: translateY(-2px);
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
}

.layer-item input[type="checkbox"],
.layer-item input[type="radio"] {
  margin-right: 12px;
  transform: scale(1.2);
}

.layer-info {
  flex: 1;
}

.layer-name {
  font-weight: 600;
  color: #333;
  margin-bottom: 5px;
  font-size: 14px;
}

.layer-description {
  font-size: 12px;
  color: #666;
  margin-bottom: 8px;
  line-height: 1.4;
}

.layer-details {
  font-size: 11px;
  color: #888;
}

.layer-type {
  background: linear-gradient(135deg, #667eea, #764ba2);
  color: white;
  padding: 2px 8px;
  border-radius: 12px;
  font-weight: 500;
  font-size: 10px;
}

.layer-workspace {
  font-family: 'Courier New', monospace;
  background: #f8f9fa;
  padding: 2px 6px;
  border-radius: 4px;
  margin-left: 8px;
}

.opacity-control {
  display: flex;
  align-items: center;
  margin-top: 10px;
  font-size: 12px;
  color: #666;
  gap: 10px;
}

.opacity-control span:first-child {
  min-width: 80px;
  font-weight: 500;
}

.opacity-control input[type="range"] {
  flex: 1;
  height: 6px;
  background: #ddd;
  border-radius: 3px;
  outline: none;
  -webkit-appearance: none;
}

.opacity-control input[type="range"]::-webkit-slider-thumb {
  -webkit-appearance: none;
  width: 18px;
  height: 18px;
  background: linear-gradient(135deg, #667eea, #764ba2);
  border-radius: 50%;
  cursor: pointer;
}

.opacity-value {
  min-width: 40px;
  text-align: right;
  font-weight: 600;
  color: #667eea;
}

/* Welcome Section */
.welcome-section {
  text-align: center;
  padding: 60px 20px;
  color: white;
}

.welcome-section h1 {
  font-size: 3rem;
  font-weight: 700;
  margin-bottom: 20px;
  text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.3);
}

.welcome-section p {
  font-size: 1.2rem;
  margin-bottom: 30px;
  opacity: 0.9;
}

.welcome-section .btn {
  padding: 12px 30px;
  font-size: 1.1rem;
  font-weight: 600;
  border-radius: 25px;
  transition: all 0.3s ease;
}

.btn-primary {
  background: linear-gradient(135deg, #667eea, #764ba2);
  border: none;
}

.btn-primary:hover {
  transform: translateY(-2px);
  box-shadow: 0 10px 25px rgba(0, 0, 0, 0.2);
}

/* Responsive */
@media (max-width: 768px) {
  .layer-control {
    top: 90px;
    right: 10px;
    left: 10px;
    max-width: none;
    max-height: 50vh;
  }

  .map-header {
    padding: 10px 15px;
  }

  .map-header h1 {
    font-size: 18px;
  }

  .welcome-section h1 {
    font-size: 2rem;
  }
}