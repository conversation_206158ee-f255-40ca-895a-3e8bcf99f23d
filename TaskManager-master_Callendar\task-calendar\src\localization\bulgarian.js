// Bulgarian localization for Task Calendar
export const BULGARIAN_LOCALE = {
  // UI Text
  ui: {
    appTitle: 'Календар за Задачи',
    teamMembers: 'Членове на Екипа',
    createTask: 'Създай Задача',
    editTask: 'Редактирай Задача',
    deleteTask: 'Изтрий Задача',
    cancel: 'Отказ',
    save: 'Запази',
    update: 'Обнови',
    delete: 'Изтрий',
    close: 'Затвори',
    create: 'Създай',
    
    // Form labels
    title: 'Заглавие',
    description: 'Описание',
    date: 'Дата',
    startTime: 'Начален час',
    endTime: 'Краен час',
    assignToTeamMembers: 'Назначи на членове от екипа',
    selectColor: 'Избери цвят',
    
    // Calendar views
    day: 'Ден',
    week: 'Седмица',
    month: 'Месец',
    year: 'Година',
    
    // Time
    time: 'Час',
    
    // Validation messages
    validation: {
      titleRequired: 'Заглавието е задължително',
      invalidTimeRange: 'Крайният час трябва да е след началния',
      conflictDetected: 'Открит е конфликт в разписанието'
    },
    
    // Conflict dialog
    conflict: {
      title: 'Конфликт в Разписанието',
      message: 'Следните членове на екипа вече имат задачи по това време:',
      continueAnyway: 'Продължи въпреки това',
      changeTime: 'Промени времето'
    }
  },
  
  // Days of the week (Monday to Sunday)
  days: {
    short: ['Пн', 'Вт', 'Ср', 'Чт', 'Пт', 'Сб', 'Нд'],
    long: ['Понеделник', 'Вторник', 'Сряда', 'Четвъртък', 'Петък', 'Събота', 'Неделя'],
    // For date headers
    abbreviated: ['Пон', 'Вто', 'Сря', 'Чет', 'Пет', 'Съб', 'Нед']
  },
  
  // Months
  months: {
    short: ['Ян', 'Фев', 'Мар', 'Апр', 'Май', 'Юни', 'Юли', 'Авг', 'Сеп', 'Окт', 'Ное', 'Дек'],
    long: ['Януари', 'Февруари', 'Март', 'Април', 'Май', 'Юни', 'Юли', 'Август', 'Септември', 'Октомври', 'Ноември', 'Декември']
  },
  
  // Date formatting
  dateFormat: {
    short: 'dd.MM.yyyy',
    long: 'dd MMMM yyyy',
    dayMonth: 'dd MMM',
    monthYear: 'MMMM yyyy'
  }
}

// Bulgarian holidays and non-working days
export const BULGARIAN_HOLIDAYS = {
  // Fixed holidays (same date every year)
  fixed: [
    { month: 1, day: 1, name: 'Нова година' },
    { month: 3, day: 3, name: 'Ден на Освобождението на България' },
    { month: 5, day: 1, name: 'Ден на труда' },
    { month: 5, day: 6, name: 'Гергьовден, Ден на храбростта' },
    { month: 5, day: 24, name: 'Ден на българската просвета и култура' },
    { month: 9, day: 6, name: 'Ден на Съединението' },
    { month: 9, day: 22, name: 'Ден на Независимостта' },
    { month: 11, day: 1, name: 'Ден на народните будители' },
    { month: 12, day: 24, name: 'Бъдни вечер' },
    { month: 12, day: 25, name: 'Коледа' },
    { month: 12, day: 26, name: 'Втори ден на Коледа' }
  ],
  
  // Variable holidays (calculated based on Easter)
  // These need to be calculated each year
  variable: [
    { name: 'Великден', offset: 0 }, // Easter Sunday
    { name: 'Втори ден на Великден', offset: 1 }, // Easter Monday
    { name: 'Велики петък', offset: -2 } // Good Friday
  ]
}

// Helper function to check if a date is a weekend
export const isWeekend = (date) => {
  const day = date.getDay()
  return day === 0 || day === 6 // Sunday (0) or Saturday (6)
}

// Helper function to check if a date is a Bulgarian holiday
export const isBulgarianHoliday = (date) => {
  const month = date.getMonth() + 1 // getMonth() returns 0-11
  const day = date.getDate()
  
  // Check fixed holidays
  const isFixedHoliday = BULGARIAN_HOLIDAYS.fixed.some(holiday => 
    holiday.month === month && holiday.day === day
  )
  
  if (isFixedHoliday) return true
  
  // Check variable holidays (Easter-based)
  const easterDate = calculateEasterDate(date.getFullYear())
  const isVariableHoliday = BULGARIAN_HOLIDAYS.variable.some(holiday => {
    const holidayDate = new Date(easterDate)
    holidayDate.setDate(easterDate.getDate() + holiday.offset)
    return holidayDate.toDateString() === date.toDateString()
  })
  
  return isVariableHoliday
}

// Helper function to check if a date is a non-working day
export const isNonWorkingDay = (date) => {
  return isWeekend(date) || isBulgarianHoliday(date)
}

// Calculate Orthodox Easter date (Bulgaria follows Orthodox calendar)
export const calculateEasterDate = (year) => {
  // Orthodox Easter calculation (Julian calendar based)
  const a = year % 4
  const b = year % 7
  const c = year % 19
  const d = (19 * c + 15) % 30
  const e = (2 * a + 4 * b - d + 34) % 7
  const month = Math.floor((d + e + 114) / 31)
  const day = ((d + e + 114) % 31) + 1
  
  // Convert from Julian to Gregorian calendar (add 13 days for 20th-21st century)
  const julianDate = new Date(year, month - 1, day)
  const gregorianDate = new Date(julianDate.getTime() + (13 * 24 * 60 * 60 * 1000))
  
  return gregorianDate
}

// Format date in Bulgarian style
export const formatBulgarianDate = (date, format = 'short') => {
  const day = date.getDate().toString().padStart(2, '0')
  const month = (date.getMonth() + 1).toString().padStart(2, '0')
  const year = date.getFullYear()
  const monthName = BULGARIAN_LOCALE.months.long[date.getMonth()]
  const monthShort = BULGARIAN_LOCALE.months.short[date.getMonth()]
  
  switch (format) {
    case 'short':
      return `${day}.${month}.${year}`
    case 'long':
      return `${day} ${monthName} ${year}`
    case 'dayMonth':
      return `${day} ${monthShort}`
    case 'monthYear':
      return `${monthName} ${year}`
    default:
      return `${day}.${month}.${year}`
  }
}

// Get day name in Bulgarian
export const getBulgarianDayName = (date, format = 'short') => {
  const dayIndex = (date.getDay() + 6) % 7 // Convert Sunday=0 to Monday=0
  return BULGARIAN_LOCALE.days[format][dayIndex]
}

// Get month name in Bulgarian
export const getBulgarianMonthName = (date, format = 'long') => {
  return BULGARIAN_LOCALE.months[format][date.getMonth()]
}
