﻿@model EditGeoTaskViewModel;
@using TaskManager.Services.Data.Interfaces;

@inject IWorkerService WorkerService;
@inject IStatusService StatusService;
@inject IClientService ClientService;
@{
	AllWorkersViewModel currentWorker = await WorkerService.GetWorkerFromTaskWorkerId(Model.WorkerId);
	string currentCheckEr = Model.CheckerId;
	int statusTaskId = Model.StatusId;
	ClientViewModel currentClient = await ClientService.GetClientByIdAsync(Model.ClientId);
	ViewData["Title"] = $"Проект №{Model.ProjectNumber}";
}

<div class="modern-container">
	<!-- Project Header -->
	<div class="modern-project-header">
		<div class="project-info">
			<h1 class="project-title">
				<i class="fas fa-project-diagram"></i>
				Проект №@Model.ProjectNumber
			</h1>
			<p class="project-date">
				<i class="fas fa-calendar-alt"></i>
				Създаден на: @Model.CreateDate.ToString("dd-MM-yyyy")
			</p>
		</div>
		<div class="project-actions">
			<!-- Calendar Integration Button -->
			<div class="calendar-integration">
				<button type="button" id="calendarBtn" class="modern-btn modern-btn-info" onclick="toggleCalendarModal()">
					<i class="fas fa-calendar-plus"></i>
					<span id="calendarBtnText">Добави в календар</span>
				</button>
				<span id="calendarStatus" class="calendar-status" style="display: none;">
					<i class="fas fa-calendar-check"></i>
					В календара
				</span>
			</div>
		</div>
	</div>

	<form method="post" class="modern-project-form">
		<!-- Main Data Section -->
		<div class="modern-card">
			<div class="modern-card-header">
				<h3>
					<i class="fas fa-info-circle"></i>
					Основни данни
				</h3>
			</div>
			<div class="modern-card-body">
				<div class="modern-form-grid">
					<div class="form-group">
						<label asp-for="Adrress" class="modern-form-label">
							<i class="fas fa-map-marker-alt"></i>
							Адрес <span class="required">*</span>
						</label>
						<input asp-for="Adrress" class="modern-form-control" placeholder="Въведете адрес..."/>
						<span asp-validation-for="Adrress" class="modern-validation-error"></span>
					</div>

					<div class="form-group">
						<label asp-for="StatusId" class="modern-form-label">
							<i class="fas fa-flag"></i>
							Статус <span class="required">*</span>
						</label>
						<select asp-for="StatusId" class="modern-form-control">
							@foreach (StatusViewModel status in Model.Statuses)
							{
								@if (status.Id == statusTaskId)
								{
									<option selected="selected" value="@status.Id">@status.Name</option>
								}
								else
								{
									<option value="@status.Id">@status.Name</option>
								}
							}
						</select>
					</div>

					<div class="form-group">
						<label asp-for="WorkerId" class="modern-form-label">
							<i class="fas fa-user-hard-hat"></i>
							Отговорник <span class="required">*</span>
						</label>
						<select asp-for="WorkerId" class="modern-form-control">
							@foreach (AllWorkersViewModel worker in Model.Workers)
							{
								@if(worker.Id == currentWorker.Id.ToUpper())
								{
									<option selected="selected" value="@worker.Id">@($"{worker.FirstName} {worker.LastName}")</option>
								}
								else
								{
									<option value="@worker.Id">@($"{worker.FirstName} {worker.LastName}")</option>
								}
							}
						</select>
					</div>

					<div class="form-group">
						<label asp-for="CheckerId" class="modern-form-label">
							<i class="fas fa-user-check"></i>
							Проверяващ <span class="required">*</span>
						</label>
						<select asp-for="CheckerId" class="modern-form-control">
							@foreach (AllWorkersViewModel checker in Model.Checkers)
							{
								@if(checker.Id == currentCheckEr)
								{
									<option selected="selected" value="@checker.Id">@($"{checker.FirstName} {checker.LastName}")</option>
								}
								else
								{
									<option value="@checker.Id">@($"{checker.FirstName} {checker.LastName}")</option>
								}
							}
						</select>
					</div>

					<div class="form-group">
						<label asp-for="IdKKKR" class="modern-form-label">
							<i class="fas fa-id-card"></i>
							Идентификатор <span class="required">*</span>
						</label>
						<input asp-for="IdKKKR" class="modern-form-control" placeholder="Въведете идентификатор..."/>
						<span asp-validation-for="IdKKKR" class="modern-validation-error"></span>
					</div>

					<div class="form-group">
						<label asp-for="CreateDate" class="modern-form-label">
							<i class="fas fa-calendar-plus"></i>
							Създадена на <span class="required">*</span>
						</label>
						<input asp-for="CreateDate" type="date" class="modern-form-control"/>
						<span asp-validation-for="CreateDate" class="modern-validation-error"></span>
					</div>

					<div class="form-group">
						<label asp-for="EndDate" class="modern-form-label">
							<i class="fas fa-calendar-times"></i>
							Крайна дата <span class="required">*</span>
						</label>
						<input asp-for="EndDate" type="date" class="modern-form-control"/>
						<span asp-validation-for="EndDate" class="modern-validation-error"></span>
					</div>

					<div class="form-group">
						<label asp-for="TypeId" class="modern-form-label">
							<i class="fas fa-tag"></i>
							Тип <span class="required">*</span>
						</label>
						<select asp-for="TypeId" class="modern-form-control">
							@foreach (TypeViewModel type in Model.Types)
							{
								@if (type.Id == Model.TypeId)
								{
									<option selected="selected" value="@type.Id">@type.Name</option>
								}
								else
								{
									<option value="@type.Id">@type.Name</option>
								}
							}
						</select>
					</div>

					<div class="form-group">
						<label asp-for="Price" class="modern-form-label">
							<i class="fas fa-euro-sign"></i>
							Цена <span class="required">*</span>
						</label>
						<input asp-for="Price" type="text" class="modern-form-control" value="@Model.Price.ToString("F2")"/>
						<span asp-validation-for="Price" class="modern-validation-error"></span>
					</div>

					<div class="form-group">
						<label asp-for="quantity" class="modern-form-label">
							<i class="fas fa-sort-numeric-up"></i>
							Количество <span class="required">*</span>
						</label>
						<input asp-for="quantity" type="number" class="modern-form-control"/>
						<span asp-validation-for="quantity" class="modern-validation-error"></span>
					</div>
				</div>

				<!-- Total Price Display -->
				<div class="total-price-display">
					<div class="price-calculation">
						<i class="fas fa-calculator"></i>
						<span class="price-label">Крайна цена:</span>
						<span class="price-value">@((Model.Price * Model.quantity).ToString("F2")) лв.</span>
					</div>
				</div>
			</div>
		</div>
		<!-- Client Data Section -->
		<div class="modern-card">
			<div class="modern-card-header">
				<h3>
					<i class="fas fa-user"></i>
					Данни за клиента
				</h3>
			</div>
			<div class="modern-card-body">
				<div class="client-selector-section">
					<div class="form-group">
						<label asp-for="ClientId" class="modern-form-label">
							<i class="fas fa-users"></i>
							Избери клиент <span class="required">*</span>
						</label>
						<select asp-for="ClientId" class="modern-form-control">
							@foreach (ClientViewModel client in Model.Clients)
							{
								@if (client.Id == Model.ClientId)
								{
									<option selected="selected" value="@client.Id">@client.Name</option>
								}
								else
								{
									<option value="@client.Id">@client.Name</option>
								}
							}
						</select>
					</div>
				</div>

				<div class="client-info-grid">
					<div class="client-info-item">
						<div class="info-icon">
							<i class="fas fa-building"></i>
						</div>
						<div class="info-content">
							<span class="info-label">Име:</span>
							<span class="info-value">@currentClient.Name</span>
						</div>
					</div>

					<div class="client-info-item">
						<div class="info-icon">
							<i class="fas fa-user-tie"></i>
						</div>
						<div class="info-content">
							<span class="info-label">Представител:</span>
							<span class="info-value">@currentClient.CustomerRepresentative</span>
						</div>
					</div>

					<div class="client-info-item">
						<div class="info-icon">
							<i class="fas fa-phone"></i>
						</div>
						<div class="info-content">
							<span class="info-label">Телефон:</span>
							<span class="info-value">@currentClient.PhoneNumber</span>
						</div>
					</div>

					<div class="client-info-item">
						<div class="info-icon">
							<i class="fas fa-envelope"></i>
						</div>
						<div class="info-content">
							<span class="info-label">Имейл:</span>
							<span class="info-value">@currentClient.Email</span>
						</div>
					</div>
				</div>

				<div class="form-group notes-section">
					<label asp-for="Note" class="modern-form-label">
						<i class="fas fa-sticky-note"></i>
						Забележка
					</label>
					<textarea asp-for="Note" class="modern-form-control modern-textarea" rows="4" placeholder="Добавете забележка за проекта..."></textarea>
					<span asp-validation-for="Note" class="modern-validation-error"></span>
				</div>
			</div>
		</div>
		<!-- Comments Section -->
		<div class="modern-card">
			<div class="modern-card-header">
				<h3>
					<i class="fas fa-comments"></i>
					Коментари
				</h3>
				<div class="card-header-actions">
					<a class="modern-btn modern-btn-primary" asp-controller="Comentar" asp-action="Add" asp-route-Id="@Model.Id">
						<i class="fas fa-plus"></i>
						Добави коментар
					</a>
				</div>
			</div>
			<div class="modern-card-body">
				@if (Model.Comentars.Any())
				{
					<div class="comments-list">
						@foreach (ComentarViewModel comentar in Model.Comentars)
						{
							var workerInitals = comentar.WorkerFullName.Split(" ").ToArray();
							string Intials = workerInitals[0].Substring(0, 1) + workerInitals[1].Substring(0, 1);

							<div class="comment-card">
								<div class="comment-header">
									<div class="comment-author">
										<div class="author-avatar">
											@Intials
										</div>
										<div class="author-info">
											<h5 class="author-name">@comentar.WorkerFullName</h5>
											<span class="comment-date">@comentar.CreateDate</span>
										</div>
									</div>
									<div class="comment-actions">
										<a class="comment-edit-btn" asp-controller="Comentar" asp-action="Edit" asp-route-id="@comentar.Id">
											<i class="fas fa-edit"></i>
											Редактиране
										</a>
									</div>
								</div>
								<div class="comment-content">
									<p>@comentar.Description</p>
								</div>
							</div>
						}
					</div>
				}
				else
				{
					<div class="empty-comments-state">
						<i class="fas fa-comment-slash"></i>
						<h4>Няма коментари</h4>
						<p>Все още няма добавени коментари за този проект.</p>
						<a class="modern-btn modern-btn-primary" asp-controller="Comentar" asp-action="Add" asp-route-Id="@Model.Id">
							<i class="fas fa-plus"></i>
							Добави първия коментар
						</a>
					</div>
				}
			</div>
		</div>

		<!-- Form Actions -->
		<div class="modern-card">
			<div class="modern-card-body">
				<div class="form-actions">
					<button type="submit" name="Edit" value="Edit" class="modern-btn modern-btn-primary">
						<i class="fas fa-save"></i>
						Запиши
					</button>
					<button type="submit" name="SaveAndClose" value="SaveAndClose" class="modern-btn modern-btn-success">
						<i class="fas fa-check-circle"></i>
						Запиши и затвори
					</button>
					<a class="modern-btn modern-btn-secondary" asp-controller="GeoTask" asp-action="AllTasks">
						<i class="fas fa-arrow-left"></i>
						Назад към списъка
					</a>
				</div>
			</div>
		</div>
	</form>
</div>

<!-- Calendar Modal -->
<div class="modal fade" id="calendarModal" tabindex="-1" aria-labelledby="calendarModalLabel" aria-hidden="true">
	<div class="modal-dialog">
		<div class="modal-content">
			<div class="modal-header">
				<h5 class="modal-title" id="calendarModalLabel">Добави в календар</h5>
				<button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
			</div>
			<div class="modal-body">
				@Html.AntiForgeryToken()
				<form id="calendarForm">
					<div class="mb-3">
						<label for="calendarTitle" class="form-label">Заглавие</label>
						<input type="text" class="form-control" id="calendarTitle" value="@($"Проект №{Model.ProjectNumber}")" required>
					</div>
					<div class="mb-3">
						<label for="calendarDescription" class="form-label">Описание</label>
						<textarea class="form-control" id="calendarDescription" rows="3">@Model.Note</textarea>
					</div>
					<div class="row">
						<div class="col-md-4">
							<label for="calendarDate" class="form-label">Дата</label>
							<input type="date" class="form-control" id="calendarDate" value="@Model.CreateDate.ToString("yyyy-MM-dd")" required>
						</div>
						<div class="col-md-4">
							<label for="calendarStartTime" class="form-label">Начален час</label>
							<input type="time" class="form-control" id="calendarStartTime" value="09:00" required>
						</div>
						<div class="col-md-4">
							<label for="calendarEndTime" class="form-label">Краен час</label>
							<input type="time" class="form-control" id="calendarEndTime" value="17:00" required>
						</div>
					</div>
					<div class="mb-3 mt-3">
						<label for="calendarColor" class="form-label">Цвят</label>
						<input type="color" class="form-control form-control-color" id="calendarColor" value="#3b82f6">
					</div>
				</form>
			</div>
			<div class="modal-footer">
				<button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Отказ</button>
				<button type="button" class="btn btn-primary" onclick="addToCalendar()">Добави в календар</button>
				<button type="button" class="btn btn-danger" id="removeFromCalendarBtn" onclick="removeFromCalendar()" style="display: none;">Премахни от календар</button>
			</div>
		</div>
	</div>
</div>

@section Scripts
	{
				<partial name ="_ValidationScriptsPartial">

				<script>
					let isLinkedToCalendar = false;
					let calendarTaskId = null;

					// Check calendar status on page load
					$(document).ready(function() {
						checkCalendarStatus();
					});

					function checkCalendarStatus() {
						console.log('Checking calendar status for GeoTask:', '@Model.Id');
						$.get('@Url.Action("CheckCalendarStatus", "GeoTask")', { geoTaskId: '@Model.Id' })
							.done(function(data) {
								console.log('Calendar status response:', data);
								isLinkedToCalendar = data.isLinked;
								calendarTaskId = data.calendarTaskId;
								updateCalendarButton();
							})
							.fail(function(xhr, status, error) {
								console.error('Failed to check calendar status:', error, xhr.responseText);
							});
					}

					function updateCalendarButton() {
						const btn = document.getElementById('calendarBtn');
						const btnText = document.getElementById('calendarBtnText');
						const status = document.getElementById('calendarStatus');
						const removeBtn = document.getElementById('removeFromCalendarBtn');

						if (isLinkedToCalendar) {
							btn.className = 'btn btn-success';
							btnText.textContent = 'В календара';
							status.style.display = 'inline';
							if (removeBtn) removeBtn.style.display = 'inline-block';
						} else {
							btn.className = 'btn btn-info';
							btnText.textContent = 'Добави в календар';
							status.style.display = 'none';
							if (removeBtn) removeBtn.style.display = 'none';
						}
					}

					function toggleCalendarModal() {
						if (isLinkedToCalendar) {
							// If already linked, show options to view or remove
							if (confirm('Тази задача вече е в календара. Искате ли да я премахнете?')) {
								removeFromCalendar();
							}
						} else {
							// Show modal to add to calendar
							$('#calendarModal').modal('show');
						}
					}

					function addToCalendar() {
						const postData = {
							geoTaskId: '@Model.Id',
							title: document.getElementById('calendarTitle').value,
							description: document.getElementById('calendarDescription').value,
							date: document.getElementById('calendarDate').value,
							startTime: document.getElementById('calendarStartTime').value,
							endTime: document.getElementById('calendarEndTime').value,
							color: document.getElementById('calendarColor').value,
							assignedMemberIds: ['@Model.WorkerId', '@Model.CheckerId']
						};

						$.ajaxSetup({
							beforeSend: function(xhr, settings) {
								if (settings.type == 'POST' || settings.type == 'PUT' || settings.type == 'DELETE') {
									xhr.setRequestHeader("RequestVerificationToken", $('input[name="__RequestVerificationToken"]').val());
								}
							}
						});

						console.log('Sending calendar task data:', postData);
						$.post('@Url.Action("AddToCalendar", "GeoTask")', postData)
						.done(function(data) {
							console.log('Calendar task created successfully:', data);
							$('#calendarModal').modal('hide');
							// Wait a moment for the database to update, then check status
							setTimeout(function() {
								checkCalendarStatus();
							}, 500);
							location.reload(); // Reload to show success message
						})
						.fail(function(xhr) {
							console.error('Failed to create calendar task:', xhr.responseText);
							alert('Грешка при добавяне в календара: ' + (xhr.responseText || 'Неизвестна грешка'));
						});
					}

					function removeFromCalendar() {
						if (confirm('Сигурни ли сте, че искате да премахнете тази задача от календара?')) {
							$.post('@Url.Action("RemoveFromCalendar", "GeoTask")', { geoTaskId: '@Model.Id' })
							.done(function(data) {
								checkCalendarStatus();
								location.reload(); // Reload to show success message
							})
							.fail(function(xhr) {
								alert('Грешка при премахване от календара: ' + (xhr.responseText || 'Неизвестна грешка'));
							});
						}
					}
				</script>
}
