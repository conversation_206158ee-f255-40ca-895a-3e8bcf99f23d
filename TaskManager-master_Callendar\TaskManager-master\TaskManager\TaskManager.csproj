﻿<Project Sdk="Microsoft.NET.Sdk.Web">

  <PropertyGroup>
    <TargetFramework>net6.0</TargetFramework>
    <Nullable>enable</Nullable>
    <ImplicitUsings>enable</ImplicitUsings>
    <UserSecretsId>aspnet-TaskManager-74430038-ab3d-4d5d-9ee4-e0957111f6f5</UserSecretsId>
  </PropertyGroup>

  <ItemGroup>
    <Content Include="Content\Images\FrontImage.jpg" />
    <Content Include="Content\Images\home-button.png" />
  </ItemGroup>

  <ItemGroup>
    <PackageReference Include="Microsoft.AspNetCore.Diagnostics.EntityFrameworkCore" Version="6.0.10" />
    <PackageReference Include="Microsoft.AspNetCore.Identity.EntityFrameworkCore" Version="6.0.10" />
    <PackageReference Include="Microsoft.AspNetCore.Identity.UI" Version="6.0.10" />
    <PackageReference Include="Microsoft.EntityFrameworkCore.Design" Version="6.0.10">
      <PrivateAssets>all</PrivateAssets>
      <IncludeAssets>runtime; build; native; contentfiles; analyzers; buildtransitive</IncludeAssets>
    </PackageReference>
    <PackageReference Include="Microsoft.EntityFrameworkCore.SqlServer" Version="6.0.10" />
    <PackageReference Include="Microsoft.EntityFrameworkCore.Tools" Version="6.0.10" />
    <PackageReference Include="Microsoft.VisualStudio.Web.CodeGeneration.Design" Version="6.0.16" />
  </ItemGroup>

  <ItemGroup>
    <ProjectReference Include="..\TaskManager.Common\TaskManager.Common.csproj" />
    <ProjectReference Include="..\TaskManager.Data.Models\TaskManager.Data.Models.csproj" />
    <ProjectReference Include="..\TaskManager.Data\TaskManager.Data.csproj" />
    <ProjectReference Include="..\TaskManager.Services.Data\TaskManager.Services.Data.csproj" />
    <ProjectReference Include="..\TaskManager.Web.Infrastructure\TaskManager.Web.Infrastructure.csproj" />
    <ProjectReference Include="..\TaskManager.Web.ViewModels\TaskManager.Web.ViewModels.csproj" />
  </ItemGroup>

  <ItemGroup>
    <Folder Include="Areas\Admin\Controllers\" />
    <Folder Include="Areas\Admin\Data\" />
    <Folder Include="Areas\Admin\Models\" />
    <Folder Include="Areas\Admin\Views\" />
    <Folder Include="Areas\Identity\" />
  </ItemGroup>

</Project>
