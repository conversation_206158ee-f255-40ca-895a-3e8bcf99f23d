﻿using System;
using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace TaskManager.Data.Migrations
{
    public partial class AddColorToWorker : Migration
    {
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.AddColumn<string>(
                name: "Color",
                table: "Workers",
                type: "nvarchar(7)",
                maxLength: 7,
                nullable: false,
                defaultValue: "");

            migrationBuilder.UpdateData(
                table: "AspNetUsers",
                keyColumn: "Id",
                keyValue: new Guid("296193d2-f02c-4410-b8fe-9eab4ffb4801"),
                columns: new[] { "ConcurrencyStamp", "PasswordHash" },
                values: new object[] { "fd026052-**************-08bc34f1e083", "AQAAAAEAACcQAAAAEF09tJIx9ByCAVc+9WKPJ5np1+TF3gSlLsfZeg+tlCGmWqGDNPHMlUzJuz/Hqb07/Q==" });

            migrationBuilder.UpdateData(
                table: "AspNetUsers",
                keyColumn: "Id",
                keyValue: new Guid("35679ef5-bb0b-4d6a-bb37-6b0bfb32e801"),
                columns: new[] { "ConcurrencyStamp", "PasswordHash" },
                values: new object[] { "fc93b80f-002b-4058-a5b3-c7e6ac805157", "AQAAAAEAACcQAAAAEBW8+Y76NUvPpTDHtl85+bowwgdpdj4hjpCdMquzGuQ/eJHgUIy1LTmBNGMK9Ewx0Q==" });

            migrationBuilder.UpdateData(
                table: "AspNetUsers",
                keyColumn: "Id",
                keyValue: new Guid("6cef6e21-5151-4e88-be5c-f4f78953bf84"),
                columns: new[] { "ConcurrencyStamp", "PasswordHash" },
                values: new object[] { "3a6b9e08-e4a8-4473-be84-61d8c2a01339", "AQAAAAEAACcQAAAAEM/kSoaNrfLiPEgzYtWT8Cv3KriNWBqhlcukpKdb58KcFbWo2BZ4XEfWV7JagEpZXA==" });

            migrationBuilder.UpdateData(
                table: "AspNetUsers",
                keyColumn: "Id",
                keyValue: new Guid("6e70d07b-eaa7-4384-b233-c5ef9c0bf8de"),
                columns: new[] { "ConcurrencyStamp", "PasswordHash" },
                values: new object[] { "2f52e977-2eb3-4b97-9151-1aace1fcaf06", "AQAAAAEAACcQAAAAEDOkaDfdOLbpXamlBEgej4eZXOqrYDL2Ut1R/WKm5Xe4XdauSvULxS0SIJ6ZsD01lA==" });

            migrationBuilder.UpdateData(
                table: "AspNetUsers",
                keyColumn: "Id",
                keyValue: new Guid("bcb4f072-ecca-43c9-ab26-c060c6f364e4"),
                columns: new[] { "ConcurrencyStamp", "PasswordHash" },
                values: new object[] { "3d54dd52-7415-409a-8837-ca37f1bcc9b4", "AQAAAAEAACcQAAAAECa2Waybi/ch1fNsHgW3aW26xopsI3YHTQ/SxTXPSI14rYTNDKsc45tDyqmAupj/Yg==" });

            migrationBuilder.UpdateData(
                table: "Comentars",
                keyColumn: "Id",
                keyValue: 1,
                column: "CreateDate",
                value: new DateTime(2025, 6, 28, 20, 56, 47, 473, DateTimeKind.Local).AddTicks(449));

            migrationBuilder.UpdateData(
                table: "Comentars",
                keyColumn: "Id",
                keyValue: 2,
                column: "CreateDate",
                value: new DateTime(2025, 6, 28, 20, 56, 47, 473, DateTimeKind.Local).AddTicks(487));

            migrationBuilder.UpdateData(
                table: "Comentars",
                keyColumn: "Id",
                keyValue: 3,
                column: "CreateDate",
                value: new DateTime(2025, 6, 28, 20, 56, 47, 473, DateTimeKind.Local).AddTicks(490));

            migrationBuilder.UpdateData(
                table: "Comentars",
                keyColumn: "Id",
                keyValue: 4,
                column: "CreateDate",
                value: new DateTime(2025, 6, 28, 20, 56, 47, 473, DateTimeKind.Local).AddTicks(493));

            migrationBuilder.UpdateData(
                table: "GeoTasks",
                keyColumn: "Id",
                keyValue: new Guid("6e6de662-426f-4ff2-9886-984fa1c46a81"),
                columns: new[] { "CreateDate", "EndDate" },
                values: new object[] { new DateTime(2025, 6, 28, 20, 56, 47, 473, DateTimeKind.Local).AddTicks(4585), new DateTime(2025, 6, 28, 20, 56, 47, 473, DateTimeKind.Local).AddTicks(4586) });

            migrationBuilder.UpdateData(
                table: "GeoTasks",
                keyColumn: "Id",
                keyValue: new Guid("*************-4962-9c31-d32e86f6ec0c"),
                columns: new[] { "CreateDate", "EndDate" },
                values: new object[] { new DateTime(2025, 6, 28, 20, 56, 47, 473, DateTimeKind.Local).AddTicks(4560), new DateTime(2025, 6, 28, 20, 56, 47, 473, DateTimeKind.Local).AddTicks(4574) });

            migrationBuilder.UpdateData(
                table: "Salaries",
                keyColumn: "Id",
                keyValue: 1,
                column: "ChangeDate",
                value: new DateTime(2025, 6, 28, 20, 56, 47, 473, DateTimeKind.Local).AddTicks(4806));

            migrationBuilder.UpdateData(
                table: "Salaries",
                keyColumn: "Id",
                keyValue: 2,
                column: "ChangeDate",
                value: new DateTime(2025, 6, 28, 20, 56, 47, 473, DateTimeKind.Local).AddTicks(4811));

            migrationBuilder.UpdateData(
                table: "Salaries",
                keyColumn: "Id",
                keyValue: 3,
                column: "ChangeDate",
                value: new DateTime(2025, 6, 28, 20, 56, 47, 473, DateTimeKind.Local).AddTicks(4814));

            migrationBuilder.UpdateData(
                table: "Workers",
                keyColumn: "Id",
                keyValue: new Guid("a13c085f-4d96-4244-8ea4-607c4b6989f6"),
                column: "Color",
                value: "#3B82F6");

            migrationBuilder.UpdateData(
                table: "Workers",
                keyColumn: "Id",
                keyValue: new Guid("acda7da9-d7a0-4fd0-9aca-f6000da77582"),
                column: "Color",
                value: "#3B82F6");

            migrationBuilder.UpdateData(
                table: "Workers",
                keyColumn: "Id",
                keyValue: new Guid("c6d1253b-2695-4a7f-98d5-a2a87eb3b97a"),
                column: "Color",
                value: "#3B82F6");
        }

        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropColumn(
                name: "Color",
                table: "Workers");

            migrationBuilder.UpdateData(
                table: "AspNetUsers",
                keyColumn: "Id",
                keyValue: new Guid("296193d2-f02c-4410-b8fe-9eab4ffb4801"),
                columns: new[] { "ConcurrencyStamp", "PasswordHash" },
                values: new object[] { "af89139d-8772-4575-87da-0145914c9aa9", "AQAAAAEAACcQAAAAEJ99V9gn/86i2M/pMxU+bDrwM+8fFRRFf78M2XWXiFr51udvIINgGcxcxcfeHDci3A==" });

            migrationBuilder.UpdateData(
                table: "AspNetUsers",
                keyColumn: "Id",
                keyValue: new Guid("35679ef5-bb0b-4d6a-bb37-6b0bfb32e801"),
                columns: new[] { "ConcurrencyStamp", "PasswordHash" },
                values: new object[] { "2b09f8ee-a9d9-4ab3-9bcd-e6a61e6a6a38", "AQAAAAEAACcQAAAAEKKJde4/QPY1LzHf46Io+0PQNODACjPwypdwVZJX/VVS5eVvaKGBHyvh0rJ/xzDq7Q==" });

            migrationBuilder.UpdateData(
                table: "AspNetUsers",
                keyColumn: "Id",
                keyValue: new Guid("6cef6e21-5151-4e88-be5c-f4f78953bf84"),
                columns: new[] { "ConcurrencyStamp", "PasswordHash" },
                values: new object[] { "7410dde3-969d-478a-87eb-ce928121e5c5", "AQAAAAEAACcQAAAAEDbJ6FhsBsL5yzxjFATcNdyi4gad0vrNfshL8mvIr2+rLb0g6P/5sr5lIotKefqt/A==" });

            migrationBuilder.UpdateData(
                table: "AspNetUsers",
                keyColumn: "Id",
                keyValue: new Guid("6e70d07b-eaa7-4384-b233-c5ef9c0bf8de"),
                columns: new[] { "ConcurrencyStamp", "PasswordHash" },
                values: new object[] { "30b3875a-41f7-42f7-b8fd-37668db485ef", "AQAAAAEAACcQAAAAEIPSK5UkF+5mP2/OOjjcmi203mRvVlGXZkFHqeV0sXIiBmgjmvhftzGkluQPjNOgXg==" });

            migrationBuilder.UpdateData(
                table: "AspNetUsers",
                keyColumn: "Id",
                keyValue: new Guid("bcb4f072-ecca-43c9-ab26-c060c6f364e4"),
                columns: new[] { "ConcurrencyStamp", "PasswordHash" },
                values: new object[] { "f04c99f0-4416-435a-847b-cff7ccf45495", "AQAAAAEAACcQAAAAEGZGXiXxvBHJpaWnfIX/87DcYh1CudYEUYQekczI10UdPxC8BA1l5VBmq55CeAhguA==" });

            migrationBuilder.UpdateData(
                table: "Comentars",
                keyColumn: "Id",
                keyValue: 1,
                column: "CreateDate",
                value: new DateTime(2025, 6, 28, 9, 41, 25, 843, DateTimeKind.Utc).AddTicks(7133));

            migrationBuilder.UpdateData(
                table: "Comentars",
                keyColumn: "Id",
                keyValue: 2,
                column: "CreateDate",
                value: new DateTime(2025, 6, 28, 9, 41, 25, 843, DateTimeKind.Utc).AddTicks(7139));

            migrationBuilder.UpdateData(
                table: "Comentars",
                keyColumn: "Id",
                keyValue: 3,
                column: "CreateDate",
                value: new DateTime(2025, 6, 28, 9, 41, 25, 843, DateTimeKind.Utc).AddTicks(7141));

            migrationBuilder.UpdateData(
                table: "Comentars",
                keyColumn: "Id",
                keyValue: 4,
                column: "CreateDate",
                value: new DateTime(2025, 6, 28, 9, 41, 25, 843, DateTimeKind.Utc).AddTicks(7142));

            migrationBuilder.UpdateData(
                table: "GeoTasks",
                keyColumn: "Id",
                keyValue: new Guid("6e6de662-426f-4ff2-9886-984fa1c46a81"),
                columns: new[] { "CreateDate", "EndDate" },
                values: new object[] { new DateTime(2025, 6, 28, 12, 41, 25, 844, DateTimeKind.Local).AddTicks(537), new DateTime(2025, 6, 28, 12, 41, 25, 844, DateTimeKind.Local).AddTicks(538) });

            migrationBuilder.UpdateData(
                table: "GeoTasks",
                keyColumn: "Id",
                keyValue: new Guid("*************-4962-9c31-d32e86f6ec0c"),
                columns: new[] { "CreateDate", "EndDate" },
                values: new object[] { new DateTime(2025, 6, 28, 12, 41, 25, 844, DateTimeKind.Local).AddTicks(502), new DateTime(2025, 6, 28, 12, 41, 25, 844, DateTimeKind.Local).AddTicks(527) });

            migrationBuilder.UpdateData(
                table: "Salaries",
                keyColumn: "Id",
                keyValue: 1,
                column: "ChangeDate",
                value: new DateTime(2025, 6, 28, 12, 41, 25, 844, DateTimeKind.Local).AddTicks(789));

            migrationBuilder.UpdateData(
                table: "Salaries",
                keyColumn: "Id",
                keyValue: 2,
                column: "ChangeDate",
                value: new DateTime(2025, 6, 28, 12, 41, 25, 844, DateTimeKind.Local).AddTicks(794));

            migrationBuilder.UpdateData(
                table: "Salaries",
                keyColumn: "Id",
                keyValue: 3,
                column: "ChangeDate",
                value: new DateTime(2025, 6, 28, 12, 41, 25, 844, DateTimeKind.Local).AddTicks(797));
        }
    }
}
