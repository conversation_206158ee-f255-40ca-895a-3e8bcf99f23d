{"format": 1, "restore": {"D:\\C#Web\\TaskManager-master_Callendar\\TaskManager-master\\TaskManager.Web.Infrastructure\\TaskManager.Web.Infrastructure.csproj": {}}, "projects": {"D:\\C#Web\\TaskManager-master_Callendar\\TaskManager-master\\TaskManager.Common\\TaskManager.Common.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "D:\\C#Web\\TaskManager-master_Callendar\\TaskManager-master\\TaskManager.Common\\TaskManager.Common.csproj", "projectName": "TaskManager.Common", "projectPath": "D:\\C#Web\\TaskManager-master_Callendar\\TaskManager-master\\TaskManager.Common\\TaskManager.Common.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "D:\\C#Web\\TaskManager-master_Callendar\\TaskManager-master\\TaskManager.Common\\obj\\", "projectStyle": "PackageReference", "fallbackFolders": ["C:\\Program Files (x86)\\Microsoft Visual Studio\\Shared\\NuGetPackages"], "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.FallbackLocation.config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config"], "originalTargetFrameworks": ["net6.0"], "sources": {"C:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\": {}, "https://api.nuget.org/v3/index.json": {}}, "frameworks": {"net6.0": {"targetAlias": "net6.0", "projectReferences": {}}}, "warningProperties": {"warnAsError": ["NU1605"]}}, "frameworks": {"net6.0": {"targetAlias": "net6.0", "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "frameworkReferences": {"Microsoft.NETCore.App": {"privateAssets": "all"}}, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\7.0.100\\RuntimeIdentifierGraph.json"}}}, "D:\\C#Web\\TaskManager-master_Callendar\\TaskManager-master\\TaskManager.Data.Models\\TaskManager.Data.Models.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "D:\\C#Web\\TaskManager-master_Callendar\\TaskManager-master\\TaskManager.Data.Models\\TaskManager.Data.Models.csproj", "projectName": "TaskManager.Data.Models", "projectPath": "D:\\C#Web\\TaskManager-master_Callendar\\TaskManager-master\\TaskManager.Data.Models\\TaskManager.Data.Models.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "D:\\C#Web\\TaskManager-master_Callendar\\TaskManager-master\\TaskManager.Data.Models\\obj\\", "projectStyle": "PackageReference", "fallbackFolders": ["C:\\Program Files (x86)\\Microsoft Visual Studio\\Shared\\NuGetPackages"], "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.FallbackLocation.config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config"], "originalTargetFrameworks": ["net6.0"], "sources": {"C:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\": {}, "https://api.nuget.org/v3/index.json": {}}, "frameworks": {"net6.0": {"targetAlias": "net6.0", "projectReferences": {"D:\\C#Web\\TaskManager-master_Callendar\\TaskManager-master\\TaskManager.Common\\TaskManager.Common.csproj": {"projectPath": "D:\\C#Web\\TaskManager-master_Callendar\\TaskManager-master\\TaskManager.Common\\TaskManager.Common.csproj"}}}}, "warningProperties": {"warnAsError": ["NU1605"]}}, "frameworks": {"net6.0": {"targetAlias": "net6.0", "dependencies": {"Microsoft.AspNetCore.Identity.EntityFrameworkCore": {"target": "Package", "version": "[6.0.10, )"}, "Microsoft.EntityFrameworkCore.SqlServer": {"target": "Package", "version": "[6.0.10, )"}, "Microsoft.EntityFrameworkCore.Tools": {"include": "Runtime, Build, Native, ContentFiles, Analyzers, BuildTransitive", "suppressParent": "All", "target": "Package", "version": "[6.0.10, )"}}, "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "frameworkReferences": {"Microsoft.NETCore.App": {"privateAssets": "all"}}, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\7.0.100\\RuntimeIdentifierGraph.json"}}}, "D:\\C#Web\\TaskManager-master_Callendar\\TaskManager-master\\TaskManager.Data\\TaskManager.Data.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "D:\\C#Web\\TaskManager-master_Callendar\\TaskManager-master\\TaskManager.Data\\TaskManager.Data.csproj", "projectName": "TaskManager.Data", "projectPath": "D:\\C#Web\\TaskManager-master_Callendar\\TaskManager-master\\TaskManager.Data\\TaskManager.Data.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "D:\\C#Web\\TaskManager-master_Callendar\\TaskManager-master\\TaskManager.Data\\obj\\", "projectStyle": "PackageReference", "fallbackFolders": ["C:\\Program Files (x86)\\Microsoft Visual Studio\\Shared\\NuGetPackages"], "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.FallbackLocation.config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config"], "originalTargetFrameworks": ["net6.0"], "sources": {"C:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\": {}, "https://api.nuget.org/v3/index.json": {}}, "frameworks": {"net6.0": {"targetAlias": "net6.0", "projectReferences": {"D:\\C#Web\\TaskManager-master_Callendar\\TaskManager-master\\TaskManager.Data.Models\\TaskManager.Data.Models.csproj": {"projectPath": "D:\\C#Web\\TaskManager-master_Callendar\\TaskManager-master\\TaskManager.Data.Models\\TaskManager.Data.Models.csproj"}}}}, "warningProperties": {"warnAsError": ["NU1605"]}}, "frameworks": {"net6.0": {"targetAlias": "net6.0", "dependencies": {"Microsoft.AspNetCore.Identity.EntityFrameworkCore": {"target": "Package", "version": "[6.0.10, )"}, "Microsoft.EntityFrameworkCore.Design": {"include": "Runtime, Build, Native, ContentFiles, Analyzers, BuildTransitive", "suppressParent": "All", "target": "Package", "version": "[6.0.10, )"}, "Microsoft.EntityFrameworkCore.SqlServer": {"target": "Package", "version": "[6.0.10, )"}, "Microsoft.EntityFrameworkCore.Tools": {"include": "Runtime, Build, Native, ContentFiles, Analyzers, BuildTransitive", "suppressParent": "All", "target": "Package", "version": "[6.0.10, )"}}, "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "frameworkReferences": {"Microsoft.NETCore.App": {"privateAssets": "all"}}, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\7.0.100\\RuntimeIdentifierGraph.json"}}}, "D:\\C#Web\\TaskManager-master_Callendar\\TaskManager-master\\TaskManager.Services.Data\\TaskManager.Services.Data.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "D:\\C#Web\\TaskManager-master_Callendar\\TaskManager-master\\TaskManager.Services.Data\\TaskManager.Services.Data.csproj", "projectName": "TaskManager.Services.Data", "projectPath": "D:\\C#Web\\TaskManager-master_Callendar\\TaskManager-master\\TaskManager.Services.Data\\TaskManager.Services.Data.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "D:\\C#Web\\TaskManager-master_Callendar\\TaskManager-master\\TaskManager.Services.Data\\obj\\", "projectStyle": "PackageReference", "fallbackFolders": ["C:\\Program Files (x86)\\Microsoft Visual Studio\\Shared\\NuGetPackages"], "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.FallbackLocation.config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config"], "originalTargetFrameworks": ["net6.0"], "sources": {"C:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\": {}, "https://api.nuget.org/v3/index.json": {}}, "frameworks": {"net6.0": {"targetAlias": "net6.0", "projectReferences": {"D:\\C#Web\\TaskManager-master_Callendar\\TaskManager-master\\TaskManager.Common\\TaskManager.Common.csproj": {"projectPath": "D:\\C#Web\\TaskManager-master_Callendar\\TaskManager-master\\TaskManager.Common\\TaskManager.Common.csproj"}, "D:\\C#Web\\TaskManager-master_Callendar\\TaskManager-master\\TaskManager.Data.Models\\TaskManager.Data.Models.csproj": {"projectPath": "D:\\C#Web\\TaskManager-master_Callendar\\TaskManager-master\\TaskManager.Data.Models\\TaskManager.Data.Models.csproj"}, "D:\\C#Web\\TaskManager-master_Callendar\\TaskManager-master\\TaskManager.Data\\TaskManager.Data.csproj": {"projectPath": "D:\\C#Web\\TaskManager-master_Callendar\\TaskManager-master\\TaskManager.Data\\TaskManager.Data.csproj"}, "D:\\C#Web\\TaskManager-master_Callendar\\TaskManager-master\\TaskManager.Web.ViewModels\\TaskManager.Web.ViewModels.csproj": {"projectPath": "D:\\C#Web\\TaskManager-master_Callendar\\TaskManager-master\\TaskManager.Web.ViewModels\\TaskManager.Web.ViewModels.csproj"}}}}, "warningProperties": {"warnAsError": ["NU1605"]}}, "frameworks": {"net6.0": {"targetAlias": "net6.0", "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "frameworkReferences": {"Microsoft.NETCore.App": {"privateAssets": "all"}}, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\7.0.100\\RuntimeIdentifierGraph.json"}}}, "D:\\C#Web\\TaskManager-master_Callendar\\TaskManager-master\\TaskManager.Web.Infrastructure\\TaskManager.Web.Infrastructure.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "D:\\C#Web\\TaskManager-master_Callendar\\TaskManager-master\\TaskManager.Web.Infrastructure\\TaskManager.Web.Infrastructure.csproj", "projectName": "TaskManager.Web.Infrastructure", "projectPath": "D:\\C#Web\\TaskManager-master_Callendar\\TaskManager-master\\TaskManager.Web.Infrastructure\\TaskManager.Web.Infrastructure.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "D:\\C#Web\\TaskManager-master_Callendar\\TaskManager-master\\TaskManager.Web.Infrastructure\\obj\\", "projectStyle": "PackageReference", "fallbackFolders": ["C:\\Program Files (x86)\\Microsoft Visual Studio\\Shared\\NuGetPackages"], "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.FallbackLocation.config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config"], "originalTargetFrameworks": ["net6.0"], "sources": {"C:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\": {}, "https://api.nuget.org/v3/index.json": {}}, "frameworks": {"net6.0": {"targetAlias": "net6.0", "projectReferences": {"D:\\C#Web\\TaskManager-master_Callendar\\TaskManager-master\\TaskManager.Services.Data\\TaskManager.Services.Data.csproj": {"projectPath": "D:\\C#Web\\TaskManager-master_Callendar\\TaskManager-master\\TaskManager.Services.Data\\TaskManager.Services.Data.csproj"}}}}, "warningProperties": {"warnAsError": ["NU1605"]}}, "frameworks": {"net6.0": {"targetAlias": "net6.0", "dependencies": {"Microsoft.AspNetCore.Mvc.Core": {"target": "Package", "version": "[2.2.5, )"}}, "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "frameworkReferences": {"Microsoft.NETCore.App": {"privateAssets": "all"}}, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\7.0.100\\RuntimeIdentifierGraph.json"}}}, "D:\\C#Web\\TaskManager-master_Callendar\\TaskManager-master\\TaskManager.Web.ViewModels\\TaskManager.Web.ViewModels.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "D:\\C#Web\\TaskManager-master_Callendar\\TaskManager-master\\TaskManager.Web.ViewModels\\TaskManager.Web.ViewModels.csproj", "projectName": "TaskManager.Web.ViewModels", "projectPath": "D:\\C#Web\\TaskManager-master_Callendar\\TaskManager-master\\TaskManager.Web.ViewModels\\TaskManager.Web.ViewModels.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "D:\\C#Web\\TaskManager-master_Callendar\\TaskManager-master\\TaskManager.Web.ViewModels\\obj\\", "projectStyle": "PackageReference", "fallbackFolders": ["C:\\Program Files (x86)\\Microsoft Visual Studio\\Shared\\NuGetPackages"], "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.FallbackLocation.config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config"], "originalTargetFrameworks": ["net6.0"], "sources": {"C:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\": {}, "https://api.nuget.org/v3/index.json": {}}, "frameworks": {"net6.0": {"targetAlias": "net6.0", "projectReferences": {"D:\\C#Web\\TaskManager-master_Callendar\\TaskManager-master\\TaskManager.Common\\TaskManager.Common.csproj": {"projectPath": "D:\\C#Web\\TaskManager-master_Callendar\\TaskManager-master\\TaskManager.Common\\TaskManager.Common.csproj"}}}}, "warningProperties": {"warnAsError": ["NU1605"]}}, "frameworks": {"net6.0": {"targetAlias": "net6.0", "dependencies": {"Microsoft.AspNetCore.Mvc.Core": {"target": "Package", "version": "[2.2.5, )"}}, "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "frameworkReferences": {"Microsoft.NETCore.App": {"privateAssets": "all"}}, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\7.0.100\\RuntimeIdentifierGraph.json"}}}}}