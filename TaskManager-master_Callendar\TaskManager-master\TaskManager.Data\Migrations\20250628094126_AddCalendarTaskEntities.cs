﻿using System;
using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace TaskManager.Data.Migrations
{
    public partial class AddCalendarTaskEntities : Migration
    {
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.AddColumn<Guid>(
                name: "CalendarTaskId",
                table: "GeoTasks",
                type: "uniqueidentifier",
                nullable: true);

            migrationBuilder.CreateTable(
                name: "CalendarTasks",
                columns: table => new
                {
                    Id = table.Column<Guid>(type: "uniqueidentifier", nullable: false),
                    Title = table.Column<string>(type: "nvarchar(200)", maxLength: 200, nullable: false),
                    Description = table.Column<string>(type: "nvarchar(1000)", maxLength: 1000, nullable: false),
                    Date = table.Column<DateTime>(type: "datetime2", nullable: false),
                    StartTime = table.Column<TimeSpan>(type: "time", nullable: false),
                    EndTime = table.Column<TimeSpan>(type: "time", nullable: false),
                    Color = table.Column<string>(type: "nvarchar(7)", maxLength: 7, nullable: false, defaultValue: "#3b82f6"),
                    CreatedDate = table.Column<DateTime>(type: "datetime2", nullable: false, defaultValueSql: "GETUTCDATE()"),
                    ModifiedDate = table.Column<DateTime>(type: "datetime2", nullable: true),
                    GeoTaskId = table.Column<Guid>(type: "uniqueidentifier", nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_CalendarTasks", x => x.Id);
                    table.ForeignKey(
                        name: "FK_CalendarTasks_GeoTasks_GeoTaskId",
                        column: x => x.GeoTaskId,
                        principalTable: "GeoTasks",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.SetNull);
                });

            migrationBuilder.CreateTable(
                name: "CalendarTaskWorkers",
                columns: table => new
                {
                    CalendarTaskId = table.Column<Guid>(type: "uniqueidentifier", nullable: false),
                    WorkerId = table.Column<Guid>(type: "uniqueidentifier", nullable: false),
                    AssignedDate = table.Column<DateTime>(type: "datetime2", nullable: false, defaultValueSql: "GETUTCDATE()")
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_CalendarTaskWorkers", x => new { x.CalendarTaskId, x.WorkerId });
                    table.ForeignKey(
                        name: "FK_CalendarTaskWorkers_CalendarTasks_CalendarTaskId",
                        column: x => x.CalendarTaskId,
                        principalTable: "CalendarTasks",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Cascade);
                    table.ForeignKey(
                        name: "FK_CalendarTaskWorkers_Workers_WorkerId",
                        column: x => x.WorkerId,
                        principalTable: "Workers",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Cascade);
                });

            migrationBuilder.UpdateData(
                table: "AspNetUsers",
                keyColumn: "Id",
                keyValue: new Guid("296193d2-f02c-4410-b8fe-9eab4ffb4801"),
                columns: new[] { "ConcurrencyStamp", "PasswordHash" },
                values: new object[] { "af89139d-8772-4575-87da-0145914c9aa9", "AQAAAAEAACcQAAAAEJ99V9gn/86i2M/pMxU+bDrwM+8fFRRFf78M2XWXiFr51udvIINgGcxcxcfeHDci3A==" });

            migrationBuilder.UpdateData(
                table: "AspNetUsers",
                keyColumn: "Id",
                keyValue: new Guid("35679ef5-bb0b-4d6a-bb37-6b0bfb32e801"),
                columns: new[] { "ConcurrencyStamp", "PasswordHash" },
                values: new object[] { "2b09f8ee-a9d9-4ab3-9bcd-e6a61e6a6a38", "AQAAAAEAACcQAAAAEKKJde4/QPY1LzHf46Io+0PQNODACjPwypdwVZJX/VVS5eVvaKGBHyvh0rJ/xzDq7Q==" });

            migrationBuilder.UpdateData(
                table: "AspNetUsers",
                keyColumn: "Id",
                keyValue: new Guid("6cef6e21-5151-4e88-be5c-f4f78953bf84"),
                columns: new[] { "ConcurrencyStamp", "PasswordHash" },
                values: new object[] { "7410dde3-969d-478a-87eb-ce928121e5c5", "AQAAAAEAACcQAAAAEDbJ6FhsBsL5yzxjFATcNdyi4gad0vrNfshL8mvIr2+rLb0g6P/5sr5lIotKefqt/A==" });

            migrationBuilder.UpdateData(
                table: "AspNetUsers",
                keyColumn: "Id",
                keyValue: new Guid("6e70d07b-eaa7-4384-b233-c5ef9c0bf8de"),
                columns: new[] { "ConcurrencyStamp", "PasswordHash" },
                values: new object[] { "30b3875a-41f7-42f7-b8fd-37668db485ef", "AQAAAAEAACcQAAAAEIPSK5UkF+5mP2/OOjjcmi203mRvVlGXZkFHqeV0sXIiBmgjmvhftzGkluQPjNOgXg==" });

            migrationBuilder.UpdateData(
                table: "AspNetUsers",
                keyColumn: "Id",
                keyValue: new Guid("bcb4f072-ecca-43c9-ab26-c060c6f364e4"),
                columns: new[] { "ConcurrencyStamp", "PasswordHash" },
                values: new object[] { "f04c99f0-4416-435a-847b-cff7ccf45495", "AQAAAAEAACcQAAAAEGZGXiXxvBHJpaWnfIX/87DcYh1CudYEUYQekczI10UdPxC8BA1l5VBmq55CeAhguA==" });

            migrationBuilder.UpdateData(
                table: "Comentars",
                keyColumn: "Id",
                keyValue: 1,
                column: "CreateDate",
                value: new DateTime(2025, 6, 28, 9, 41, 25, 843, DateTimeKind.Utc).AddTicks(7133));

            migrationBuilder.UpdateData(
                table: "Comentars",
                keyColumn: "Id",
                keyValue: 2,
                column: "CreateDate",
                value: new DateTime(2025, 6, 28, 9, 41, 25, 843, DateTimeKind.Utc).AddTicks(7139));

            migrationBuilder.UpdateData(
                table: "Comentars",
                keyColumn: "Id",
                keyValue: 3,
                column: "CreateDate",
                value: new DateTime(2025, 6, 28, 9, 41, 25, 843, DateTimeKind.Utc).AddTicks(7141));

            migrationBuilder.UpdateData(
                table: "Comentars",
                keyColumn: "Id",
                keyValue: 4,
                column: "CreateDate",
                value: new DateTime(2025, 6, 28, 9, 41, 25, 843, DateTimeKind.Utc).AddTicks(7142));

            migrationBuilder.UpdateData(
                table: "GeoTasks",
                keyColumn: "Id",
                keyValue: new Guid("6e6de662-426f-4ff2-9886-984fa1c46a81"),
                columns: new[] { "CreateDate", "EndDate" },
                values: new object[] { new DateTime(2025, 6, 28, 12, 41, 25, 844, DateTimeKind.Local).AddTicks(537), new DateTime(2025, 6, 28, 12, 41, 25, 844, DateTimeKind.Local).AddTicks(538) });

            migrationBuilder.UpdateData(
                table: "GeoTasks",
                keyColumn: "Id",
                keyValue: new Guid("*************-4962-9c31-d32e86f6ec0c"),
                columns: new[] { "CreateDate", "EndDate" },
                values: new object[] { new DateTime(2025, 6, 28, 12, 41, 25, 844, DateTimeKind.Local).AddTicks(502), new DateTime(2025, 6, 28, 12, 41, 25, 844, DateTimeKind.Local).AddTicks(527) });

            migrationBuilder.UpdateData(
                table: "Salaries",
                keyColumn: "Id",
                keyValue: 1,
                column: "ChangeDate",
                value: new DateTime(2025, 6, 28, 12, 41, 25, 844, DateTimeKind.Local).AddTicks(789));

            migrationBuilder.UpdateData(
                table: "Salaries",
                keyColumn: "Id",
                keyValue: 2,
                column: "ChangeDate",
                value: new DateTime(2025, 6, 28, 12, 41, 25, 844, DateTimeKind.Local).AddTicks(794));

            migrationBuilder.UpdateData(
                table: "Salaries",
                keyColumn: "Id",
                keyValue: 3,
                column: "ChangeDate",
                value: new DateTime(2025, 6, 28, 12, 41, 25, 844, DateTimeKind.Local).AddTicks(797));

            migrationBuilder.CreateIndex(
                name: "IX_CalendarTask_Date",
                table: "CalendarTasks",
                column: "Date");

            migrationBuilder.CreateIndex(
                name: "IX_CalendarTask_GeoTaskId",
                table: "CalendarTasks",
                column: "GeoTaskId",
                unique: true,
                filter: "[GeoTaskId] IS NOT NULL");

            migrationBuilder.CreateIndex(
                name: "IX_CalendarTaskWorker_CalendarTaskId",
                table: "CalendarTaskWorkers",
                column: "CalendarTaskId");

            migrationBuilder.CreateIndex(
                name: "IX_CalendarTaskWorker_WorkerId",
                table: "CalendarTaskWorkers",
                column: "WorkerId");
        }

        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropTable(
                name: "CalendarTaskWorkers");

            migrationBuilder.DropTable(
                name: "CalendarTasks");

            migrationBuilder.DropColumn(
                name: "CalendarTaskId",
                table: "GeoTasks");

            migrationBuilder.UpdateData(
                table: "AspNetUsers",
                keyColumn: "Id",
                keyValue: new Guid("296193d2-f02c-4410-b8fe-9eab4ffb4801"),
                columns: new[] { "ConcurrencyStamp", "PasswordHash" },
                values: new object[] { "b87d687b-9d99-47f7-8b75-8f2961ce4ee9", "AQAAAAEAACcQAAAAEFDlIS4RFbNYLDRiIwv1gvT6tgNSLmb3p8Xa2k6WmpkkeDSObBt9/zj/FjTKTChxtg==" });

            migrationBuilder.UpdateData(
                table: "AspNetUsers",
                keyColumn: "Id",
                keyValue: new Guid("35679ef5-bb0b-4d6a-bb37-6b0bfb32e801"),
                columns: new[] { "ConcurrencyStamp", "PasswordHash" },
                values: new object[] { "6309854d-0f2c-4162-a560-d6ad23a07ae8", "AQAAAAEAACcQAAAAEDgTAf+bwBAJelZLsLw7kFlTBIjCzHtF7x+naYtmt+YLAeL5Ikl/iBuJpbkL56G6Yg==" });

            migrationBuilder.UpdateData(
                table: "AspNetUsers",
                keyColumn: "Id",
                keyValue: new Guid("6cef6e21-5151-4e88-be5c-f4f78953bf84"),
                columns: new[] { "ConcurrencyStamp", "PasswordHash" },
                values: new object[] { "411d3a61-8ceb-48a3-a29e-50b47c2e962c", "AQAAAAEAACcQAAAAEIk+cRKQTgOXGp69hqulhRe3Q+JrCd+qRs0faKgAC5M9DFHw7PTxuggHU/1ob5UBRA==" });

            migrationBuilder.UpdateData(
                table: "AspNetUsers",
                keyColumn: "Id",
                keyValue: new Guid("6e70d07b-eaa7-4384-b233-c5ef9c0bf8de"),
                columns: new[] { "ConcurrencyStamp", "PasswordHash" },
                values: new object[] { "2b3bc8e5-cef1-4909-9ee3-5060ffd6de27", "AQAAAAEAACcQAAAAECK880AIcd1UPwJmGn4+ccjVBXDKgsPnkAu1vBoigXNBLwkSgZ6swcUtmruQtPsuhg==" });

            migrationBuilder.UpdateData(
                table: "AspNetUsers",
                keyColumn: "Id",
                keyValue: new Guid("bcb4f072-ecca-43c9-ab26-c060c6f364e4"),
                columns: new[] { "ConcurrencyStamp", "PasswordHash" },
                values: new object[] { "0bff4f8f-c491-4fe3-9ea8-2b83ee75db2b", "AQAAAAEAACcQAAAAEB2lv7xUEcmUAB2ndPuyZwtYYL/EaxUThpqP7fWeh4zJP56yWI46HgnqaT+ghFqkYQ==" });

            migrationBuilder.UpdateData(
                table: "Comentars",
                keyColumn: "Id",
                keyValue: 1,
                column: "CreateDate",
                value: new DateTime(2024, 1, 14, 17, 57, 1, 599, DateTimeKind.Utc).AddTicks(3153));

            migrationBuilder.UpdateData(
                table: "Comentars",
                keyColumn: "Id",
                keyValue: 2,
                column: "CreateDate",
                value: new DateTime(2024, 1, 14, 17, 57, 1, 599, DateTimeKind.Utc).AddTicks(3159));

            migrationBuilder.UpdateData(
                table: "Comentars",
                keyColumn: "Id",
                keyValue: 3,
                column: "CreateDate",
                value: new DateTime(2024, 1, 14, 17, 57, 1, 599, DateTimeKind.Utc).AddTicks(3161));

            migrationBuilder.UpdateData(
                table: "Comentars",
                keyColumn: "Id",
                keyValue: 4,
                column: "CreateDate",
                value: new DateTime(2024, 1, 14, 17, 57, 1, 599, DateTimeKind.Utc).AddTicks(3163));

            migrationBuilder.UpdateData(
                table: "GeoTasks",
                keyColumn: "Id",
                keyValue: new Guid("6e6de662-426f-4ff2-9886-984fa1c46a81"),
                columns: new[] { "CreateDate", "EndDate" },
                values: new object[] { new DateTime(2024, 1, 14, 19, 57, 1, 599, DateTimeKind.Local).AddTicks(6181), new DateTime(2024, 1, 14, 19, 57, 1, 599, DateTimeKind.Local).AddTicks(6182) });

            migrationBuilder.UpdateData(
                table: "GeoTasks",
                keyColumn: "Id",
                keyValue: new Guid("*************-4962-9c31-d32e86f6ec0c"),
                columns: new[] { "CreateDate", "EndDate" },
                values: new object[] { new DateTime(2024, 1, 14, 19, 57, 1, 599, DateTimeKind.Local).AddTicks(6144), new DateTime(2024, 1, 14, 19, 57, 1, 599, DateTimeKind.Local).AddTicks(6168) });

            migrationBuilder.UpdateData(
                table: "Salaries",
                keyColumn: "Id",
                keyValue: 1,
                column: "ChangeDate",
                value: new DateTime(2024, 1, 14, 19, 57, 1, 599, DateTimeKind.Local).AddTicks(6378));

            migrationBuilder.UpdateData(
                table: "Salaries",
                keyColumn: "Id",
                keyValue: 2,
                column: "ChangeDate",
                value: new DateTime(2024, 1, 14, 19, 57, 1, 599, DateTimeKind.Local).AddTicks(6383));

            migrationBuilder.UpdateData(
                table: "Salaries",
                keyColumn: "Id",
                keyValue: 3,
                column: "ChangeDate",
                value: new DateTime(2024, 1, 14, 19, 57, 1, 599, DateTimeKind.Local).AddTicks(6386));
        }
    }
}
