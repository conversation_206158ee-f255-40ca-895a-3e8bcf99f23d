is_global = true
build_property.TargetFramework = net7.0
build_property.TargetPlatformMinVersion = 
build_property.UsingMicrosoftNETSdkWeb = true
build_property.ProjectTypeGuids = 
build_property.InvariantGlobalization = 
build_property.PlatformNeutralAssembly = 
build_property.EnforceExtendedAnalyzerRules = 
build_property._SupportedPlatformList = Linux,macOS,Windows
build_property.RootNamespace = GeoSpatialDataKRBR
build_property.RootNamespace = GeoSpatialDataKRBR
build_property.ProjectDir = D:\C#Web\IKRB_real\GeoSpatialDataKRBR\
build_property.RazorLangVersion = 7.0
build_property.SupportLocalizedComponentNames = 
build_property.GenerateRazorMetadataSourceChecksumAttributes = 
build_property.MSBuildProjectDirectory = D:\C#Web\IKRB_real\GeoSpatialDataKRBR
build_property._RazorSourceGeneratorDebug = 

[D:/C\#Web/IKRB_real/GeoSpatialDataKRBR/Areas/Identity/Pages/_ViewStart.cshtml]
build_metadata.AdditionalFiles.TargetPath = QXJlYXNcSWRlbnRpdHlcUGFnZXNcX1ZpZXdTdGFydC5jc2h0bWw=
build_metadata.AdditionalFiles.CssScope = 

[D:/C\#Web/IKRB_real/GeoSpatialDataKRBR/Views/Admin/Layers.cshtml]
build_metadata.AdditionalFiles.TargetPath = Vmlld3NcQWRtaW5cTGF5ZXJzLmNzaHRtbA==
build_metadata.AdditionalFiles.CssScope = 

[D:/C\#Web/IKRB_real/GeoSpatialDataKRBR/Views/Home/Index.cshtml]
build_metadata.AdditionalFiles.TargetPath = Vmlld3NcSG9tZVxJbmRleC5jc2h0bWw=
build_metadata.AdditionalFiles.CssScope = 

[D:/C\#Web/IKRB_real/GeoSpatialDataKRBR/Views/Home/Privacy.cshtml]
build_metadata.AdditionalFiles.TargetPath = Vmlld3NcSG9tZVxQcml2YWN5LmNzaHRtbA==
build_metadata.AdditionalFiles.CssScope = 

[D:/C\#Web/IKRB_real/GeoSpatialDataKRBR/Views/Map/Index.cshtml]
build_metadata.AdditionalFiles.TargetPath = Vmlld3NcTWFwXEluZGV4LmNzaHRtbA==
build_metadata.AdditionalFiles.CssScope = 

[D:/C\#Web/IKRB_real/GeoSpatialDataKRBR/Views/Shared/Error.cshtml]
build_metadata.AdditionalFiles.TargetPath = Vmlld3NcU2hhcmVkXEVycm9yLmNzaHRtbA==
build_metadata.AdditionalFiles.CssScope = 

[D:/C\#Web/IKRB_real/GeoSpatialDataKRBR/Views/Shared/_LoginPartial.cshtml]
build_metadata.AdditionalFiles.TargetPath = Vmlld3NcU2hhcmVkXF9Mb2dpblBhcnRpYWwuY3NodG1s
build_metadata.AdditionalFiles.CssScope = 

[D:/C\#Web/IKRB_real/GeoSpatialDataKRBR/Views/Shared/_ValidationScriptsPartial.cshtml]
build_metadata.AdditionalFiles.TargetPath = Vmlld3NcU2hhcmVkXF9WYWxpZGF0aW9uU2NyaXB0c1BhcnRpYWwuY3NodG1s
build_metadata.AdditionalFiles.CssScope = 

[D:/C\#Web/IKRB_real/GeoSpatialDataKRBR/Views/_ViewImports.cshtml]
build_metadata.AdditionalFiles.TargetPath = Vmlld3NcX1ZpZXdJbXBvcnRzLmNzaHRtbA==
build_metadata.AdditionalFiles.CssScope = 

[D:/C\#Web/IKRB_real/GeoSpatialDataKRBR/Views/_ViewStart.cshtml]
build_metadata.AdditionalFiles.TargetPath = Vmlld3NcX1ZpZXdTdGFydC5jc2h0bWw=
build_metadata.AdditionalFiles.CssScope = 

[D:/C\#Web/IKRB_real/GeoSpatialDataKRBR/Views/Shared/_Layout.cshtml]
build_metadata.AdditionalFiles.TargetPath = Vmlld3NcU2hhcmVkXF9MYXlvdXQuY3NodG1s
build_metadata.AdditionalFiles.CssScope = b-zu8bfs4g8e
