﻿@{
    ViewData["Title"] = "Административен панел";
}

<div class="admin-panel-container">
    <!-- Header Section -->
    <div class="admin-header">
        <div class="admin-header-content">
            <h1 class="admin-title">
                <i class="fas fa-cogs"></i>
                Административен панел
            </h1>
            <p class="admin-subtitle">Управление на системата и потребителите</p>
        </div>
    </div>

    <!-- Quick Stats Section -->
    <div class="admin-stats">
        <div class="stat-card">
            <div class="stat-icon users">
                <i class="fas fa-users"></i>
            </div>
            <div class="stat-content">
                <h3>Потребители</h3>
                <p>Управление на всички потребители</p>
            </div>
        </div>
        <div class="stat-card">
            <div class="stat-icon workers">
                <i class="fas fa-hard-hat"></i>
            </div>
            <div class="stat-content">
                <h3>Работници</h3>
                <p>Управление на работниците</p>
            </div>
        </div>
        <div class="stat-card">
            <div class="stat-icon tasks">
                <i class="fas fa-tasks"></i>
            </div>
            <div class="stat-content">
                <h3>Задачи</h3>
                <p>Типове задачи и услуги</p>
            </div>
        </div>
    </div>

    <!-- Main Admin Cards -->
    <div class="admin-grid">
        <!-- User Management Section -->
        <div class="admin-card">
            <div class="card-header">
                <div class="card-icon users-icon">
                    <i class="fas fa-users-cog"></i>
                </div>
                <h3>Управление на потребители</h3>
            </div>
            <div class="card-content">
                <p>Преглед, редактиране и управление на всички потребители в системата</p>
                <div class="card-actions">
                    <a class="admin-btn primary" asp-controller="Admin" asp-action="AllUsers">
                        <i class="fas fa-list"></i>
                        Всички потребители
                    </a>
                    <a class="admin-btn secondary" asp-controller="Admin" asp-action="AllWorkers">
                        <i class="fas fa-hard-hat"></i>
                        Всички работници
                    </a>
                </div>
            </div>
        </div>

        <!-- Task Management Section -->
        <div class="admin-card">
            <div class="card-header">
                <div class="card-icon tasks-icon">
                    <i class="fas fa-project-diagram"></i>
                </div>
                <h3>Управление на задачи</h3>
            </div>
            <div class="card-content">
                <p>Създаване и управление на типове задачи в системата</p>
                <div class="card-actions">
                    <a class="admin-btn primary" asp-controller="Type" asp-action="Add">
                        <i class="fas fa-plus"></i>
                        Добави тип задача
                    </a>
                    <a class="admin-btn secondary" asp-controller="Type" asp-action="All">
                        <i class="fas fa-list-alt"></i>
                        Всички типове
                    </a>
                </div>
            </div>
        </div>

        <!-- Services Management Section -->
        <div class="admin-card">
            <div class="card-header">
                <div class="card-icon services-icon">
                    <i class="fas fa-concierge-bell"></i>
                </div>
                <h3>Управление на услуги</h3>
            </div>
            <div class="card-content">
                <p>Добавяне и управление на услуги, предлагани от компанията</p>
                <div class="card-actions">
                    <a class="admin-btn primary" asp-controller="FrontDescription" asp-action="Add">
                        <i class="fas fa-plus"></i>
                        Добави услуга
                    </a>
                    <a class="admin-btn secondary" asp-controller="FrontDescription" asp-action="All">
                        <i class="fas fa-list"></i>
                        Всички услуги
                    </a>
                </div>
            </div>
        </div>

        <!-- Calendar Management Section -->
        <div class="admin-card">
            <div class="card-header">
                <div class="card-icon calendar-icon">
                    <i class="fas fa-calendar-alt"></i>
                </div>
                <h3>Календар</h3>
            </div>
            <div class="card-content">
                <p>Преглед и управление на календара с задачи</p>
                <div class="card-actions">
                    <a class="admin-btn primary" asp-controller="Calendar" asp-action="Calendar">
                        <i class="fas fa-calendar"></i>
                        Отвори календар
                    </a>
                </div>
            </div>
        </div>

        <!-- Reports Section -->
        <div class="admin-card">
            <div class="card-header">
                <div class="card-icon reports-icon">
                    <i class="fas fa-chart-bar"></i>
                </div>
                <h3>Отчети и статистики</h3>
            </div>
            <div class="card-content">
                <p>Преглед на отчети и статистики за работата</p>
                <div class="card-actions">
                    <a class="admin-btn primary" asp-controller="Admin" asp-action="AllWorkers">
                        <i class="fas fa-chart-line"></i>
                        Статистики
                    </a>
                </div>
            </div>
        </div>


    </div>
</div>