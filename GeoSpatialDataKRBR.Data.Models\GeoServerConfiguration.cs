namespace GeoSpatialDataKRBR.Data.Models
{
    using System.ComponentModel.DataAnnotations;
    using static Common.EntityValidationConstants.GeoServerConfiguration;

    public class GeoServerConfiguration
    {
        public GeoServerConfiguration()
        {
            this.Id = Guid.NewGuid();
            this.GeoLayers = new HashSet<GeoLayer>();
        }

        [Key]
        public Guid Id { get; set; }

        [Required]
        [MaxLength(NameMaxLength)]
        public string Name { get; set; } = null!;

        [Required]
        [MaxLength(BaseUrlMaxLength)]
        public string BaseUrl { get; set; } = null!;

        [Required]
        [MaxLength(UsernameMaxLength)]
        public string Username { get; set; } = null!;

        [Required]
        [MaxLength(PasswordMaxLength)]
        public string Password { get; set; } = null!;

        [Required]
        [MaxLength(WorkspaceMaxLength)]
        public string DefaultWorkspace { get; set; } = null!;

        public bool IsActive { get; set; } = true;

        public DateTime CreatedOn { get; set; } = DateTime.UtcNow;

        public DateTime? ModifiedOn { get; set; }

        // Navigation properties
        public virtual ICollection<GeoLayer> GeoLayers { get; set; }
    }
}
