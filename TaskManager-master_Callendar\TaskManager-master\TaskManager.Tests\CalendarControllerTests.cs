using Microsoft.AspNetCore.Mvc;
using Moq;
using TaskManager.Controllers;
using TaskManager.Services.Data.Interfaces;
using TaskManager.Web.ViewModels.Calendar;
using Xunit;

namespace TaskManager.Tests
{
    public class CalendarControllerTests
    {
        private readonly Mock<ICalendarService> mockCalendarService;
        private readonly CalendarController controller;

        public CalendarControllerTests()
        {
            mockCalendarService = new Mock<ICalendarService>();
            controller = new CalendarController(mockCalendarService.Object);
        }

        [Fact]
        public async Task GetTasks_ShouldReturnJsonResult_WhenTasksExist()
        {
            // Arrange
            var tasks = new List<CalendarTaskViewModel>
            {
                new CalendarTaskViewModel
                {
                    Id = "task1",
                    Title = "Test Task 1",
                    Date = DateTime.Today,
                    StartTime = "09:00",
                    EndTime = "10:00",
                    Color = "#3b82f6",
                    AssignedMemberIds = new List<string> { "worker1" }
                },
                new CalendarTaskViewModel
                {
                    Id = "task2",
                    Title = "Test Task 2",
                    Date = DateTime.Today.AddDays(1),
                    StartTime = "14:00",
                    EndTime = "15:00",
                    Color = "#ef4444",
                    AssignedMemberIds = new List<string> { "worker2" }
                }
            };

            mockCalendarService.Setup(s => s.GetTasksAsync(It.IsAny<DateTime?>(), It.IsAny<DateTime?>()))
                .ReturnsAsync(tasks);

            // Act
            var result = await controller.GetTasks();

            // Assert
            var jsonResult = Assert.IsType<JsonResult>(result);
            var returnedTasks = Assert.IsAssignableFrom<IEnumerable<CalendarTaskViewModel>>(jsonResult.Value);
            Assert.Equal(2, returnedTasks.Count());
        }

        [Fact]
        public async Task GetTasks_ShouldReturnEmptyArray_WhenNoTasksExist()
        {
            // Arrange
            mockCalendarService.Setup(s => s.GetTasksAsync(It.IsAny<DateTime?>(), It.IsAny<DateTime?>()))
                .ReturnsAsync(new List<CalendarTaskViewModel>());

            // Act
            var result = await controller.GetTasks();

            // Assert
            var jsonResult = Assert.IsType<JsonResult>(result);
            var returnedTasks = Assert.IsAssignableFrom<IEnumerable<CalendarTaskViewModel>>(jsonResult.Value);
            Assert.Empty(returnedTasks);
        }

        [Fact]
        public async Task GetTasks_ShouldReturnBadRequest_WhenServiceThrowsException()
        {
            // Arrange
            mockCalendarService.Setup(s => s.GetTasksAsync(It.IsAny<DateTime?>(), It.IsAny<DateTime?>()))
                .ThrowsAsync(new Exception("Database error"));

            // Act
            var result = await controller.GetTasks();

            // Assert
            var badRequestResult = Assert.IsType<BadRequestObjectResult>(result);
            Assert.NotNull(badRequestResult.Value);
        }

        [Fact]
        public async Task CreateTask_ShouldReturnJsonResult_WhenTaskCreatedSuccessfully()
        {
            // Arrange
            var createModel = new CreateCalendarTaskViewModel
            {
                Title = "New Task",
                Description = "Test Description",
                Date = DateTime.Today.AddDays(1),
                StartTime = "09:00",
                EndTime = "10:00",
                Color = "#3b82f6",
                AssignedMemberIds = new List<string> { "worker1" }
            };

            var createdTask = new CalendarTaskViewModel
            {
                Id = "new-task-id",
                Title = "New Task",
                Description = "Test Description",
                Date = DateTime.Today.AddDays(1),
                StartTime = "09:00",
                EndTime = "10:00",
                Color = "#3b82f6",
                AssignedMemberIds = new List<string> { "worker1" }
            };

            mockCalendarService.Setup(s => s.CreateTaskAsync(It.IsAny<CreateCalendarTaskViewModel>()))
                .ReturnsAsync(createdTask);

            // Act
            var result = await controller.CreateTask(createModel);

            // Assert
            var jsonResult = Assert.IsType<JsonResult>(result);
            var returnedTask = Assert.IsType<CalendarTaskViewModel>(jsonResult.Value);
            Assert.Equal("new-task-id", returnedTask.Id);
            Assert.Equal("New Task", returnedTask.Title);
        }

        [Fact]
        public async Task CreateTask_ShouldReturnBadRequest_WhenModelIsInvalid()
        {
            // Arrange
            controller.ModelState.AddModelError("Title", "Title is required");
            var createModel = new CreateCalendarTaskViewModel();

            // Act
            var result = await controller.CreateTask(createModel);

            // Assert
            var badRequestResult = Assert.IsType<BadRequestObjectResult>(result);
            Assert.NotNull(badRequestResult.Value);
        }

        [Fact]
        public async Task CreateTask_ShouldReturnBadRequest_WhenServiceThrowsException()
        {
            // Arrange
            var createModel = new CreateCalendarTaskViewModel
            {
                Title = "New Task",
                Description = "Test Description",
                Date = DateTime.Today.AddDays(1),
                StartTime = "09:00",
                EndTime = "10:00",
                Color = "#3b82f6",
                AssignedMemberIds = new List<string> { "worker1" }
            };

            mockCalendarService.Setup(s => s.CreateTaskAsync(It.IsAny<CreateCalendarTaskViewModel>()))
                .ThrowsAsync(new Exception("Database error"));

            // Act
            var result = await controller.CreateTask(createModel);

            // Assert
            var badRequestResult = Assert.IsType<BadRequestObjectResult>(result);
            Assert.NotNull(badRequestResult.Value);
        }

        [Fact]
        public async Task RemoveTask_ShouldReturnJsonResult_WhenTaskRemovedSuccessfully()
        {
            // Arrange
            mockCalendarService.Setup(s => s.RemoveTaskAsync(It.IsAny<string>()))
                .ReturnsAsync(true);

            // Act
            var result = await controller.RemoveTask("task-id");

            // Assert
            var jsonResult = Assert.IsType<JsonResult>(result);
            var response = jsonResult.Value;
            Assert.NotNull(response);
        }

        [Fact]
        public async Task RemoveTask_ShouldReturnBadRequest_WhenServiceThrowsException()
        {
            // Arrange
            mockCalendarService.Setup(s => s.RemoveTaskAsync(It.IsAny<string>()))
                .ThrowsAsync(new Exception("Database error"));

            // Act
            var result = await controller.RemoveTask("task-id");

            // Assert
            var badRequestResult = Assert.IsType<BadRequestObjectResult>(result);
            Assert.NotNull(badRequestResult.Value);
        }

        [Fact]
        public async Task IsGeoTaskLinked_ShouldReturnJsonResult_WhenGeoTaskExists()
        {
            // Arrange
            mockCalendarService.Setup(s => s.IsGeoTaskLinkedToCalendarAsync(It.IsAny<string>()))
                .ReturnsAsync(true);

            // Act
            var result = await controller.IsGeoTaskLinked("geotask-id");

            // Assert
            var jsonResult = Assert.IsType<JsonResult>(result);
            var response = jsonResult.Value;
            Assert.NotNull(response);
        }

        [Fact]
        public async Task GetCalendarTaskByGeoTaskId_ShouldReturnJsonResult_WhenTaskExists()
        {
            // Arrange
            mockCalendarService.Setup(s => s.GetCalendarTaskIdByGeoTaskIdAsync(It.IsAny<string>()))
                .ReturnsAsync("calendar-task-id");

            // Act
            var result = await controller.GetCalendarTaskByGeoTaskId("geotask-id");

            // Assert
            var jsonResult = Assert.IsType<JsonResult>(result);
            var response = jsonResult.Value;
            Assert.NotNull(response);
        }
    }
}
