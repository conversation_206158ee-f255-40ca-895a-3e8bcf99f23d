﻿@model RegisterFormModel
@{
    ViewData["Title"] = "Регистрация";
}

<h1 class="text-center">@ViewData["Title"]</h1>

<div class="row text-center">
    <div class="col-md-4 offset-md-4">
        <form id="registerForm" method="post" class="justify-content-center">
            <h2>Създаване на нов профил</h2>
            <hr />
            <div asp-validation-summary="ModelOnly" class="text-danger"></div>
            <div class="form-floating">
                <input asp-for="Email" class="form-control" autocomplete="username" aria-required="true" />
                <label asp-for="Email"></label>
                <span asp-validation-for="Email" class="text-danger"></span>
            </div>
            <div class="form-floating">
                <input asp-for="Password" class="form-control" autocomplete="new-password" aria-required="true" />
                <label asp-for="Password"></label>
                <span asp-validation-for="Password" class="text-danger"></span>
            </div>
            <div class="form-floating">
                <input asp-for="ConfirmPassword" class="form-control" autocomplete="new-password" aria-required="true" />
                <label asp-for="ConfirmPassword"></label>
                <span asp-validation-for="ConfirmPassword" class="text-danger"></span>
            </div>
            <div class="form-floating">
                <input asp-for="FirstName" class="form-control" autocomplete="new-password" aria-required="true" />
                <label asp-for="FirstName"></label>
                <span asp-validation-for="FirstName" class="text-danger"></span>
            </div>
             <div class="form-floating">
                <input asp-for="LastName" class="form-control" autocomplete="new-password" aria-required="true" />
                <label asp-for="LastName"></label>
                <span asp-validation-for="LastName" class="text-danger"></span>
            </div>

            <button id="registerSubmit" type="submit" class="w-100 btn btn-lg btn-primary">Регистрация</button>
        </form>
    </div>
</div>

@section Scripts {
            <partial name="_ValidationScriptsPartial" />
}
