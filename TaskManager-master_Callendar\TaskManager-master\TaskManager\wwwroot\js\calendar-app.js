// Bulgarian localization
console.log('🔧 DEBUGGING VERSION - Calendar app script loaded with task edit debugging!');
const BULGARIAN_LOCALE = {
  ui: {
    appTitle: 'Календар за Задачи',
    teamMembers: 'Членове на Екипа',
    createTask: 'Създай Задача',
    editTask: 'Редактирай Задача',
    deleteTask: 'Изтрий Задача',
    cancel: 'Отказ',
    save: 'Запази',
    update: 'Обнови',
    delete: 'Изтрий',
    close: 'Затвори',
    title: 'Заглавие',
    description: 'Описание',
    date: 'Дата',
    startTime: 'Начален час',
    endTime: 'Краен час',
    assignToTeamMembers: 'Назначи на членове от екипа',
    selectColor: 'Избери цвят',
    day: 'Ден',
    week: 'Седмица',
    month: 'Месец',
    year: 'Година',
    time: 'Час'
  },
  days: {
    short: ['Пн', 'Вт', 'Ср', 'Чт', 'Пт', 'Сб', 'Нд'],
    long: ['Понеделник', 'Вторник', 'Сряда', 'Четвъртък', 'Петък', 'Събота', 'Неделя'],
    abbreviated: ['Пон', 'Вто', 'Сря', 'Чет', 'Пет', 'Съб', 'Нед']
  },
  months: {
    short: ['Ян', 'Фев', 'Мар', 'Апр', 'Май', 'Юни', 'Юли', 'Авг', 'Сеп', 'Окт', 'Ное', 'Дек'],
    long: ['Януари', 'Февруари', 'Март', 'Април', 'Май', 'Юни', 'Юли', 'Август', 'Септември', 'Октомври', 'Ноември', 'Декември']
  }
};

// Calendar view types
const VIEW_TYPES = {
  DAY: 'day',
  WEEK: 'week',
  MONTH: 'month',
  YEAR: 'year'
};

// Predefined color palette for tasks
const TASK_COLORS = [
  '#3B82F6', // Blue
  '#10B981', // Green
  '#F59E0B', // Yellow
  '#EF4444', // Red
  '#8B5CF6', // Purple
  '#F97316', // Orange
  '#06B6D4', // Cyan
  '#84CC16', // Lime
  '#EC4899', // Pink
  '#6B7280'  // Gray
];

// Time slots for day/week view (08:00 to 18:00 with 30-min intervals)
const generateTimeSlots = () => {
  const slots = [];
  for (let hour = 8; hour <= 18; hour++) {
    slots.push(`${hour.toString().padStart(2, '0')}:00`);
    if (hour < 18) {
      slots.push(`${hour.toString().padStart(2, '0')}:30`);
    }
  }
  return slots;
};

const TIME_SLOTS = generateTimeSlots();

// Helper functions
const isWeekend = (date) => {
  const day = date.getDay();
  return day === 0 || day === 6; // Sunday (0) or Saturday (6)
};

const isNonWorkingDay = (date) => {
  return isWeekend(date);
};

const formatBulgarianDate = (date, format = 'short') => {
  const day = date.getDate().toString().padStart(2, '0');
  const month = (date.getMonth() + 1).toString().padStart(2, '0');
  const year = date.getFullYear();
  const monthName = BULGARIAN_LOCALE.months.long[date.getMonth()];
  const monthShort = BULGARIAN_LOCALE.months.short[date.getMonth()];
  
  switch (format) {
    case 'short':
      return `${day}.${month}.${year}`;
    case 'long':
      return `${day} ${monthName} ${year}`;
    case 'dayMonth':
      return `${day} ${monthShort}`;
    case 'monthYear':
      return `${monthName} ${year}`;
    default:
      return `${day}.${month}.${year}`;
  }
};

const getBulgarianDayName = (date, format = 'short') => {
  const dayIndex = (date.getDay() + 6) % 7; // Convert Sunday=0 to Monday=0
  return BULGARIAN_LOCALE.days[format][dayIndex];
};

// API functions
const api = {
  async getWorkers() {
    const response = await fetch('/Calendar/GetWorkers');
    if (!response.ok) throw new Error('Failed to fetch workers');
    return response.json();
  },

  async getTasks(startDate, endDate) {
    const params = new URLSearchParams();
    if (startDate) params.append('startDate', startDate.toISOString());
    if (endDate) params.append('endDate', endDate.toISOString());
    
    const response = await fetch(`/Calendar/GetTasks?${params}`);
    if (!response.ok) throw new Error('Failed to fetch tasks');
    return response.json();
  },

  async createTask(task) {
    // Process date the same way as update to ensure consistency
    let dateValue = task.date;

    // If date is a Date object, convert to YYYY-MM-DD string
    if (dateValue instanceof Date) {
      dateValue = dateValue.toISOString().split('T')[0];
    }

    // If date string contains time, extract just the date part
    if (typeof dateValue === 'string' && dateValue.includes('T')) {
      dateValue = dateValue.split('T')[0];
    }

    // Ensure it's in YYYY-MM-DD format
    if (typeof dateValue === 'string' && dateValue.includes('.')) {
      // Convert DD.MM.YYYY to YYYY-MM-DD
      const parts = dateValue.replace(' г.', '').split('.');
      if (parts.length === 3) {
        dateValue = `${parts[2]}-${parts[1].padStart(2, '0')}-${parts[0].padStart(2, '0')}`;
      }
    }

    // Transform the task data to match the API expectations
    const apiTask = {
      title: task.title,
      description: task.description || '',
      assignedMemberIds: Array.isArray(task.assignedMemberIds)
        ? task.assignedMemberIds.filter(id => id && typeof id === 'string')
        : [],
      date: dateValue, // Send as simple date string YYYY-MM-DD
      startTime: task.startTime,
      endTime: task.endTime,
      color: task.color || '#3b82f6'
    };

    console.log('Creating task with data:', apiTask);

    const response = await fetch('/Calendar/CreateTask', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'RequestVerificationToken': document.querySelector('input[name="__RequestVerificationToken"]')?.value || ''
      },
      body: JSON.stringify(apiTask)
    });

    if (!response.ok) {
      const errorText = await response.text();
      console.error('Create task failed:', response.status, errorText);
      throw new Error(`Failed to create task: ${response.status} - ${errorText}`);
    }

    return response.json();
  },

  async updateTask(taskId, task) {
    // Ensure date is in correct format to prevent timezone issues
    let dateValue = task.date;

    // If date is a Date object, convert to YYYY-MM-DD string
    if (dateValue instanceof Date) {
      dateValue = dateValue.toISOString().split('T')[0];
    }

    // If date string contains time, extract just the date part
    if (typeof dateValue === 'string' && dateValue.includes('T')) {
      dateValue = dateValue.split('T')[0];
    }

    // Ensure it's in YYYY-MM-DD format
    if (typeof dateValue === 'string' && dateValue.includes('.')) {
      // Convert DD.MM.YYYY to YYYY-MM-DD
      const parts = dateValue.replace(' г.', '').split('.');
      if (parts.length === 3) {
        dateValue = `${parts[2]}-${parts[1].padStart(2, '0')}-${parts[0].padStart(2, '0')}`;
      }
    }

    // FIX: Send just the date string to avoid any timezone conversion
    // Let the server handle the date parsing without timezone issues
    console.log('🕐 Original date:', dateValue);
    console.log('🕐 Sending date as string:', dateValue);

    const taskData = {
      ...task,
      date: dateValue // Send as simple date string YYYY-MM-DD
    };

    console.log('🔄 API updateTask - original date:', task.date);
    console.log('🔄 API updateTask - processed date:', dateValue);
    console.log('🔄 API updateTask - final date being sent:', taskData.date);
    console.log('🔄 API updateTask - full data being sent:', taskData);

    const response = await fetch(`/Calendar/UpdateTask?id=${taskId}`, {
      method: 'PUT',
      headers: {
        'Content-Type': 'application/json',
        'RequestVerificationToken': document.querySelector('input[name="__RequestVerificationToken"]')?.value || ''
      },
      body: JSON.stringify(taskData)
    });
    if (!response.ok) throw new Error('Failed to update task');
    return response.json();
  },

  async deleteTask(taskId) {
    const response = await fetch(`/Calendar/DeleteTask?id=${taskId}`, {
      method: 'DELETE',
      headers: {
        'RequestVerificationToken': document.querySelector('input[name="__RequestVerificationToken"]')?.value || ''
      }
    });
    if (!response.ok) throw new Error('Failed to delete task');
    return response.json();
  }
};

// Main Calendar App Component
function CalendarApp() {
  // Check if current user is admin (you can modify this logic based on your auth system)
  const [isAdmin, setIsAdmin] = React.useState(false);

  // Check admin status on component mount
  React.useEffect(() => {
    const checkAdminStatus = async () => {
      try {
        console.log('🔑 Checking admin status...');
        const response = await fetch('/Calendar/IsAdmin');
        if (response.ok) {
          const data = await response.json();
          setIsAdmin(data.isAdmin);
          console.log('🔑 Admin status set to:', data.isAdmin);
        } else {
          console.warn('⚠️ Failed to check admin status, defaulting to false');
          setIsAdmin(false);
        }
      } catch (error) {
        console.error('❌ Error checking admin status:', error);
        setIsAdmin(false);
      }
    };

    checkAdminStatus();
  }, []);

  const [currentView, setCurrentView] = React.useState(VIEW_TYPES.WEEK);
  const [currentDate, setCurrentDate] = React.useState(() => {
    // Ensure we start with today's date
    const today = new Date();
    console.log('🗓️ Initializing calendar with date:', today.toISOString().split('T')[0]);
    return today;
  });
  const [teamMembers, setTeamMembers] = React.useState([]);
  const [tasks, setTasks] = React.useState([]);
  const [selectedSlot, setSelectedSlot] = React.useState(null);
  const [showTaskModal, setShowTaskModal] = React.useState(false);
  const [editingTask, setEditingTask] = React.useState(null);
  const [isLoading, setIsLoading] = React.useState(false);
  const [selectedWorkers, setSelectedWorkers] = React.useState(new Set());

  // Load data on component mount
  React.useEffect(() => {
    const loadData = async () => {
      await loadWorkers(); // Wait for workers to load first
      await loadTasks();   // Then load tasks
    };
    loadData();
  }, []);

  // Reload tasks when current date changes
  React.useEffect(() => {
    loadTasks();
  }, [currentDate]);

  const loadWorkers = async () => {
    try {
      console.log('🔄 Starting to load workers...');
      const workers = await api.getWorkers();
      console.log('📥 Raw workers from API:', workers);
      console.log('📊 Workers count:', workers?.length || 0);

      if (!workers || workers.length === 0) {
        console.error('❌ No workers returned from API!');
        return;
      }

      // Set all workers as visible by default and normalize IDs to lowercase
      const workersWithVisibility = workers.map(worker => ({
        ...worker,
        id: worker.id.toLowerCase(), // Normalize to lowercase for consistent matching
        visible: true
      }));
      setTeamMembers(workersWithVisibility);

      // Update global variables for conflict detection
      window.teamMembers = workersWithVisibility;

      console.log('✅ Loaded workers with visibility:', workersWithVisibility);
      console.log('🆔 Worker IDs loaded:', workersWithVisibility.map(w => ({ id: w.id, name: w.name })));
      console.log('🔍 All worker IDs as array:', workersWithVisibility.map(w => w.id));
    } catch (error) {
      console.error('💥 Error loading workers:', error);
      console.error('💥 Error details:', error.message, error.stack);
    }
  };

  const loadTasks = async () => {
    try {
      // Get a wide date range to ensure we capture all relevant tasks
      const startDate = new Date(currentDate);
      startDate.setMonth(startDate.getMonth() - 1); // 1 month before

      const endDate = new Date(currentDate);
      endDate.setMonth(endDate.getMonth() + 1); // 1 month after

      console.log('Loading tasks from', startDate.toISOString(), 'to', endDate.toISOString());
      const tasks = await api.getTasks(startDate, endDate);
      console.log('🔍 Loaded tasks from API:', tasks);

      // Convert date strings to Date objects and format times
      const formattedTasks = tasks.map(task => ({
        ...task,
        date: new Date(task.date).toISOString().split('T')[0],
        assignedMembers: task.assignedMemberIds || [],
        // Ensure assignedMemberIds is preserved for filtering and normalize to lowercase
        assignedMemberIds: (task.assignedMemberIds || []).map(id => id.toLowerCase())
      }));
      console.log('📋 Formatted tasks:', formattedTasks);
      console.log('👥 Current team members:', teamMembers);
      console.log('👁️ Team members visible status:', teamMembers.map(m => ({ id: m.id, name: m.name, visible: m.visible })));

      // Debug: Check assignedMemberIds for each task
      formattedTasks.forEach(task => {
        if (task.assignedMemberIds && task.assignedMemberIds.length > 0) {
          console.log(`🔍 Task "${task.title}" has assignedMemberIds:`, task.assignedMemberIds);
          console.log(`🔍 Checking visibility for each assigned member:`);
          task.assignedMemberIds.forEach(memberId => {
            const member = teamMembers.find(m => m.id === memberId);
            console.log(`   - Member ID ${memberId}: ${member ? `Found (${member.name}, visible: ${member.visible})` : 'NOT FOUND'}`);
          });
        }
      });

      // Debug: Show task dates and current date
      console.log('📅 Current date:', currentDate);
      console.log('📅 Current date ISO:', currentDate.toISOString().split('T')[0]);
      console.log('📅 Today actual:', new Date().toISOString().split('T')[0]);
      console.log('📅 Task dates:', formattedTasks.map(t => ({ title: t.title, date: t.date, startTime: t.startTime })));

      // Check if any tasks match today's date
      const todayStr = new Date().toISOString().split('T')[0];
      const todayTasks = formattedTasks.filter(t => t.date === todayStr);
      console.log('📅 Tasks for today (' + todayStr + '):', todayTasks.length);

      setTasks(formattedTasks);

      // Update global variables for conflict detection
      window.calendarTasks = formattedTasks;
      window.teamMembers = teamMembers;

      // Expose data globally for debugging
      window.calendarTasks = formattedTasks;
      window.calendarTeamMembers = teamMembers;
      window.calendarWorkers = teamMembers;

      // Debug function to check current state
      window.debugCalendarState = () => {
        console.log('=== CALENDAR DEBUG STATE ===');
        console.log('Team Members:', teamMembers.map(m => ({ id: m.id, name: m.name, visible: m.visible })));
        console.log('Tasks with assignments:', formattedTasks.filter(t => t.assignedMemberIds?.length > 0).map(t => ({
          title: t.title,
          assignedMemberIds: t.assignedMemberIds
        })));
        console.log('=== END DEBUG ===');
      };

      // Debug function to manually reload workers
      window.reloadWorkers = async () => {
        console.log('🔄 Manually reloading workers...');
        await loadWorkers();
        console.log('✅ Manual worker reload complete');
      };
    } catch (error) {
      console.error('Error loading tasks:', error);
    }
  };

  // Toggle team member visibility
  const toggleMemberVisibility = (memberId) => {
    setTeamMembers(prev =>
      prev.map(member =>
        member.id === memberId
          ? { ...member, visible: !member.visible }
          : member
      )
    );
  };

  // Change team member color
  const changeMemberColor = async (memberId, newColor) => {
    console.log(`🎨 changeMemberColor called: memberId=${memberId}, newColor=${newColor}, isAdmin=${isAdmin}`);

    if (!isAdmin) {
      alert('Само администратори могат да променят цветовете на работниците.');
      return;
    }

    try {
      console.log(`📡 Making API call to update color...`);
      // Update color in database using the Calendar controller endpoint
      const response = await fetch(`/Calendar/UpdateWorkerColor?workerId=${memberId}`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ color: newColor })
      });

      console.log(`📡 API response status: ${response.status}`);

      if (response.ok) {
        const responseData = await response.json();
        console.log(`📡 API response data:`, responseData);

        // Update local state only if database update was successful
        setTeamMembers(prev =>
          prev.map(member =>
            member.id === memberId
              ? { ...member, color: newColor }
              : member
          )
        );
        console.log(`✅ Color updated for member ${memberId} to ${newColor}`);
      } else {
        const errorText = await response.text();
        console.error('Failed to update color in database:', errorText);
        alert('Грешка при запазване на цвета в базата данни.');
      }
    } catch (error) {
      console.error('Error updating member color:', error);
      alert('Грешка при запазване на цвета.');
    }
  };

  // Toggle worker selection for task highlighting
  const toggleWorkerSelection = (workerId) => {
    setSelectedWorkers(prev => {
      const newSelected = new Set(prev);
      if (newSelected.has(workerId)) {
        newSelected.delete(workerId);
      } else {
        newSelected.add(workerId);
      }
      return newSelected;
    });
  };

  // Check if a task should be highlighted (has selected workers)
  const shouldHighlightTask = (task) => {
    if (selectedWorkers.size === 0) return false;
    return task.assignedMembers.some(memberId => selectedWorkers.has(memberId));
  };

  // Get tasks for a specific date (for month view)
  const getTasksForSlot = (date, time) => {
    const dateStr = date instanceof Date ? date.toISOString().split('T')[0] : date;
    return tasks.filter(task => {
      const taskDate = task.date.split('T')[0];
      return taskDate === dateStr;
    });
  };

  // Navigation functions
  const navigateDate = (direction) => {
    const newDate = new Date(currentDate);

    switch (currentView) {
      case VIEW_TYPES.DAY:
        newDate.setDate(newDate.getDate() + (direction === 'next' ? 1 : -1));
        break;
      case VIEW_TYPES.WEEK:
        newDate.setDate(newDate.getDate() + (direction === 'next' ? 7 : -7));
        break;
      case VIEW_TYPES.MONTH:
        newDate.setMonth(newDate.getMonth() + (direction === 'next' ? 1 : -1));
        break;
      case VIEW_TYPES.YEAR:
        newDate.setFullYear(newDate.getFullYear() + (direction === 'next' ? 1 : -1));
        break;
    }

    setCurrentDate(newDate);
  };

  const handleSlotClick = (date, time, memberId) => {
    setSelectedSlot({ date, time, memberId });
    setShowTaskModal(true);
    setEditingTask(null);
  };

  const handleTaskClick = (task) => {
    setEditingTask(task);
    setShowTaskModal(true);
    setSelectedSlot(null);
  };

  const createTask = async (taskData) => {
    setIsLoading(true);
    try {
      console.log('🚀 === CREATE TASK START ===');
      console.log('📝 Original taskData:', taskData);

      // Step 1: Validate input data
      if (!taskData) {
        console.error('❌ No task data provided');
        alert('Грешка: Няма данни за задачата');
        setIsLoading(false);
        return;
      }

      if (!taskData.title || taskData.title.trim() === '') {
        console.error('❌ Task title is required');
        alert('Грешка: Заглавието на задачата е задължително');
        setIsLoading(false);
        return;
      }

      // Step 2: Normalize task data for consistent processing
      const normalizedTaskData = {
        title: (taskData.title || '').trim(),
        description: (taskData.description || '').trim(),
        date: taskData.date ? taskData.date.split('T')[0] : '', // Ensure YYYY-MM-DD format
        startTime: taskData.startTime || '09:00',
        endTime: taskData.endTime || '10:00',
        color: taskData.color || '#3b82f6',
        // Normalize worker IDs to lowercase for consistent matching
        assignedMemberIds: (taskData.assignedMemberIds || [])
          .filter(id => id && id.toString().trim() !== '') // Remove empty IDs
          .map(id => id.toString().toLowerCase().trim()) // Normalize to lowercase
      };

      console.log('🔍 Normalized taskData:', normalizedTaskData);

      // Step 3: Check for worker time conflicts (only if date is provided)
      if (normalizedTaskData.date && normalizedTaskData.date.trim() !== '') {
        console.log('🔍 Starting conflict detection...');

        const conflicts = checkWorkerTimeConflicts(normalizedTaskData);

        if (conflicts.length > 0) {
          console.log('🚨 Conflicts detected:', conflicts);

          const conflictMessages = conflicts.map(conflict => {
            const workerName = conflict.member?.name || 'Неизвестен работник';
            const overlapStart = new Date(conflict.timeOverlap.start).toLocaleTimeString('bg-BG', {
              hour: '2-digit',
              minute: '2-digit'
            });
            const overlapEnd = new Date(conflict.timeOverlap.end).toLocaleTimeString('bg-BG', {
              hour: '2-digit',
              minute: '2-digit'
            });
            return `${workerName}: ${overlapStart} - ${overlapEnd}`;
          });

          const confirmMessage = `⚠️ КОНФЛИКТ ВЪВ ВРЕМЕТО!\n\nСледните работници имат припокриващи се задачи:\n${conflictMessages.join('\n')}\n\nИскате ли да продължите въпреки конфликта?`;

          if (!confirm(confirmMessage)) {
            console.log('🚫 User cancelled due to conflicts');
            setIsLoading(false);
            return;
          }

          console.log('✅ User confirmed to proceed despite conflicts');
        } else {
          console.log('✅ No conflicts detected');
        }
      } else {
        console.log('⏭️ Skipping conflict check - no date provided');
      }

      // Step 4: Send normalized data to API
      console.log('📤 Sending create request to server...');
      const newTask = await api.createTask(normalizedTaskData);
      console.log('✅ Task created successfully:', newTask);

      await loadTasks(); // Reload tasks to get the latest data
      setShowTaskModal(false);
      setSelectedSlot(null);
      alert('Задачата е създадена успешно!');

    } catch (error) {
      console.error('❌ Exception in createTask:', error);
      alert('Грешка при създаване на задача: ' + error.message);
    } finally {
      setIsLoading(false);
    }

    console.log('🚀 === CREATE TASK END ===');
  };

  const updateTask = async (taskId, taskData) => {
    setIsLoading(true);
    try {
      console.log('🔄 === UPDATE TASK START ===');
      console.log('📝 TaskId:', taskId, 'type:', typeof taskId);
      console.log('📝 Original taskData:', taskData);

      // Step 1: Validate input data
      if (!taskId) {
        console.error('❌ No task ID provided');
        alert('Грешка: Няма ID на задачата');
        setIsLoading(false);
        return;
      }

      if (!taskData) {
        console.error('❌ No task data provided');
        alert('Грешка: Няма данни за задачата');
        setIsLoading(false);
        return;
      }

      // Step 2: Normalize task data for consistent processing
      const normalizedTaskData = {
        title: (taskData.title || '').trim(),
        description: (taskData.description || '').trim(),
        date: taskData.date ? taskData.date.split('T')[0] : '', // Ensure YYYY-MM-DD format
        startTime: taskData.startTime || '09:00',
        endTime: taskData.endTime || '10:00',
        color: taskData.color || '#3b82f6',
        // Normalize worker IDs to lowercase for consistent matching
        assignedMemberIds: (taskData.assignedMemberIds || [])
          .filter(id => id && id.toString().trim() !== '') // Remove empty IDs
          .map(id => id.toString().toLowerCase().trim()) // Normalize to lowercase
      };

      console.log('🔍 Normalized taskData:', normalizedTaskData);

      // Step 3: Check for worker time conflicts (only if date is provided)
      if (normalizedTaskData.date && normalizedTaskData.date.trim() !== '') {
        console.log('🔍 Starting conflict detection for update...');
        console.log('🔍 Excluding task ID from conflict check:', taskId);

        // Use excludeTaskId to prevent self-conflict during updates
        const conflicts = checkWorkerTimeConflicts(normalizedTaskData, taskId);

        if (conflicts.length > 0) {
          console.log('🚨 Conflicts detected:', conflicts);

          const conflictMessages = conflicts.map(conflict => {
            const workerName = conflict.member?.name || 'Неизвестен работник';
            const overlapStart = new Date(conflict.timeOverlap.start).toLocaleTimeString('bg-BG', {
              hour: '2-digit',
              minute: '2-digit'
            });
            const overlapEnd = new Date(conflict.timeOverlap.end).toLocaleTimeString('bg-BG', {
              hour: '2-digit',
              minute: '2-digit'
            });
            return `${workerName}: ${overlapStart} - ${overlapEnd}`;
          });

          const confirmMessage = `⚠️ КОНФЛИКТ ВЪВ ВРЕМЕТО!\n\nСледните работници имат припокриващи се задачи:\n${conflictMessages.join('\n')}\n\nИскате ли да продължите въпреки конфликта?`;

          if (!confirm(confirmMessage)) {
            console.log('🚫 User cancelled due to conflicts');
            setIsLoading(false);
            return;
          }

          console.log('✅ User confirmed to proceed despite conflicts');
        } else {
          console.log('✅ No conflicts detected');
        }
      } else {
        console.log('⏭️ Skipping conflict check - no date provided');
      }

      // Step 4: Send normalized data to API
      console.log('📤 Sending update request to server...');
      const updatedTask = await api.updateTask(taskId, normalizedTaskData);
      console.log('✅ Task updated successfully:', updatedTask);

      await loadTasks(); // Reload tasks to get the latest data
      setShowTaskModal(false);
      setEditingTask(null);

    } catch (error) {
      console.error('❌ Exception in updateTask:', error);
      alert('Грешка при обновяване на задача: ' + error.message);
    } finally {
      setIsLoading(false);
    }

    console.log('🔄 === UPDATE TASK END ===');
  };

  const deleteTask = async (taskId) => {
    if (!confirm('Сигурни ли сте, че искате да изтриете тази задача?')) {
      return;
    }

    setIsLoading(true);
    try {
      await api.deleteTask(taskId);
      await loadTasks(); // Reload tasks
      setShowTaskModal(false);
      setEditingTask(null);
    } catch (error) {
      console.error('Error deleting task:', error);
      alert('Грешка при изтриване на задача: ' + error.message);
    } finally {
      setIsLoading(false);
    }
  };

  return React.createElement('div', { className: `app ${isAdmin ? 'admin-user' : 'non-admin'}` },
    // Header
    React.createElement('header', { className: 'app-header' },
      React.createElement('h1', null, BULGARIAN_LOCALE.ui.appTitle),
      
      // View Type Selector
      React.createElement('div', { className: 'view-selector' },
        Object.values(VIEW_TYPES).map(view =>
          React.createElement('button', {
            key: view,
            className: `view-btn ${currentView === view ? 'active' : ''}`,
            onClick: () => setCurrentView(view)
          }, BULGARIAN_LOCALE.ui[view])
        )
      ),

      // Date Navigation
      React.createElement('div', { className: 'date-navigation' },
        React.createElement('button', {
          onClick: () => navigateDate('prev'),
          title: 'Предишен период'
        }, '←'),
        React.createElement('span', { className: 'current-date' },
          formatBulgarianDate(currentDate, 'long')
        ),
        React.createElement('button', {
          onClick: () => navigateDate('next'),
          title: 'Следващ период'
        }, '→'),
        React.createElement('button', {
          onClick: () => setCurrentDate(new Date()),
          className: 'today-btn',
          title: 'Днес'
        }, 'Днес')
      )
    ),

    // Calendar Container
    React.createElement('div', { className: 'calendar-container' },
      // Team Members Sidebar
      React.createElement('div', { className: 'team-sidebar' },
        React.createElement('h3', null, BULGARIAN_LOCALE.ui.teamMembers),
        teamMembers.map(member =>
          React.createElement('div', { key: member.id, className: 'team-member' },
            React.createElement('div', { className: 'member-controls' },
              React.createElement('button', {
                className: 'visibility-toggle',
                onClick: () => toggleMemberVisibility(member.id),
                title: member.visible ? "Скрий работника" : "Покажи работника"
              }, member.visible ? '👁️' : '🙈'),
              React.createElement('div', {
                className: 'member-avatar',
                style: { backgroundColor: member.color }
              }, member.initials),
              React.createElement('button', {
                className: `member-name-button ${selectedWorkers.has(member.id) ? 'highlighted' : ''}`,
                style: { color: member.color },
                onClick: () => toggleWorkerSelection(member.id),
                title: selectedWorkers.has(member.id) ? "Скрий задачите" : "Покажи задачите"
              }, member.name),
              React.createElement('div', {
                className: 'member-color-indicator',
                style: {
                  backgroundColor: member.color,
                  width: '20px',
                  height: '20px',
                  borderRadius: '4px',
                  border: '2px solid white',
                  boxShadow: '0 1px 3px rgba(0, 0, 0, 0.2)',
                  flexShrink: 0,
                  transition: 'all 0.2s ease'
                },
                title: `Цвят на ${member.name}`
              })
            ),
            // Color palette (only show if user is admin) - positioned below the member controls
            isAdmin && React.createElement('div', {
              className: 'worker-color-palette admin-only',
              title: 'Изберете цвят (само за администратори)'
            },
              // Predefined color options
              ['#3B82F6', '#10B981', '#F59E0B', '#EF4444', '#8B5CF6', '#06B6D4', '#84CC16', '#F97316'].map(color => {
                console.log(`🎨 Rendering color ${color} for member ${member.name}, isAdmin: ${isAdmin}`);
                return React.createElement('div', {
                  key: color,
                  className: `worker-color-option ${member.color === color ? 'selected' : ''}`,
                  onClick: () => {
                    console.log(`🖱️ Color clicked: ${color} for member ${member.id}`);
                    changeMemberColor(member.id, color);
                  },
                  style: {
                    backgroundColor: color
                  },
                  title: `Промени цвета на ${member.name} на ${color}`
                });
              })
            )
          )
        )
      ),

      // Calendar Content
      React.createElement('div', { className: 'calendar-content' },
        (currentView === VIEW_TYPES.DAY || currentView === VIEW_TYPES.WEEK) &&
        React.createElement(TimeGridView, {
          currentView,
          currentDate,
          tasks,
          teamMembers,
          onSlotClick: handleSlotClick,
          onTaskClick: handleTaskClick,
          shouldHighlightTask
        }),

        currentView === VIEW_TYPES.MONTH &&
        React.createElement(MonthView, {
          currentDate,
          tasks,
          teamMembers,
          onSlotClick: handleSlotClick,
          onTaskClick: handleTaskClick,
          getTasksForSlot
        }),

        currentView === VIEW_TYPES.YEAR &&
        React.createElement('div', { style: { padding: '2rem', textAlign: 'center' } },
          'Year view - Coming soon...'
        )
      ),

      // Task Modal
      showTaskModal && React.createElement(TaskModal, {
        isOpen: showTaskModal,
        onClose: () => {
          setShowTaskModal(false);
          setSelectedSlot(null);
          setEditingTask(null);
        },
        onSave: editingTask ? updateTask : createTask,
        onDelete: editingTask ? deleteTask : null,
        task: editingTask,
        selectedSlot: selectedSlot,
        teamMembers: teamMembers,
        isLoading: isLoading
      })
    )
  );
}

// Global helper functions for conflict detection and task positioning
// Check for time conflicts - adapted from working task-calendar
function checkWorkerTimeConflicts(taskData, excludeTaskId = null) {
  console.log('🔍 === CONFLICT DETECTION START (New Simple Logic) ===');
  console.log('📝 Input parameters:', {
    taskData: taskData,
    excludeTaskId: excludeTaskId
  });

  const conflicts = [];

  // Validate inputs
  if (!taskData) {
    console.log('❌ Invalid input: missing taskData');
    return conflicts;
  }

  // Skip conflict check if no date provided
  if (!taskData.date || taskData.date.trim() === '') {
    console.log('⏭️ No conflict check needed - no date provided');
    return conflicts;
  }

  // Skip conflict check if no assigned members
  if (!taskData.assignedMemberIds || taskData.assignedMemberIds.length === 0) {
    console.log('⏭️ No conflict check needed - no assigned members');
    return conflicts;
  }

  // Get global data
  const tasks = window.calendarTasks || [];
  const teamMembers = window.teamMembers || [];

  console.log('🔍 Using global data:', {
    tasksCount: tasks.length,
    teamMembersCount: teamMembers.length,
    assignedMembers: taskData.assignedMemberIds
  });

  // Check each assigned member for conflicts
  for (const memberId of taskData.assignedMemberIds) {
    console.log(`🔍 Checking conflicts for member: ${memberId}`);

    // Find all tasks for this member on the same date (excluding the current task being updated)
    const memberTasks = tasks.filter(task => {
      const isSameTask = excludeTaskId && (task.id === excludeTaskId || task.id?.toString() === excludeTaskId?.toString());
      const isSameDate = task.date === taskData.date;
      const isAssignedToMember = task.assignedMemberIds && task.assignedMemberIds.includes(memberId);

      console.log(`  📋 Task "${task.title}": isSameTask=${isSameTask}, isSameDate=${isSameDate}, isAssignedToMember=${isAssignedToMember}`);

      return !isSameTask && isSameDate && isAssignedToMember;
    });

    console.log(`🔍 Found ${memberTasks.length} existing tasks for member ${memberId} on ${taskData.date}`);

    // Check each existing task for time overlap
    for (const existingTask of memberTasks) {
      console.log(`🔍 Comparing with existing task: "${existingTask.title}" (${existingTask.startTime}-${existingTask.endTime})`);

      try {
        // Parse times for comparison
        const newStart = new Date(`2000-01-01T${taskData.startTime}:00`);
        const newEnd = new Date(`2000-01-01T${taskData.endTime}:00`);
        const existingStart = new Date(`2000-01-01T${existingTask.startTime}:00`);
        const existingEnd = new Date(`2000-01-01T${existingTask.endTime}:00`);

        // Check for overlap: tasks overlap if newStart < existingEnd AND newEnd > existingStart
        const hasOverlap = newStart < existingEnd && newEnd > existingStart;

        if (hasOverlap) {
          // Find the team member info
          const member = teamMembers.find(m => {
            const memberIdStr = m.id?.toString().toLowerCase();
            const targetIdStr = memberId?.toString().toLowerCase();
            return memberIdStr === targetIdStr;
          });

          const conflict = {
            member: member || { id: memberId, name: `Unknown Worker (${memberId})` },
            existingTask: existingTask,
            timeOverlap: {
              start: Math.max(newStart.getTime(), existingStart.getTime()),
              end: Math.min(newEnd.getTime(), existingEnd.getTime())
            }
          };

          conflicts.push(conflict);

          console.log(`🚨 CONFLICT DETECTED: "${existingTask.title}" conflicts with new task for ${member?.name || memberId}`);
          console.log(`   Overlap: ${new Date(conflict.timeOverlap.start).toLocaleTimeString('bg-BG', { hour: '2-digit', minute: '2-digit' })} - ${new Date(conflict.timeOverlap.end).toLocaleTimeString('bg-BG', { hour: '2-digit', minute: '2-digit' })}`);
        } else {
          console.log(`✅ No conflict with "${existingTask.title}"`);
        }
      } catch (error) {
        console.error(`❌ Error parsing times for task "${existingTask.title}":`, error);
      }
    }
  }

  console.log(`🔍 === CONFLICT DETECTION END === Found ${conflicts.length} conflicts`);
  return conflicts;
}

function calculateTaskPositions(dayTasks, time) {
  // Only render tasks that START at this time slot, not tasks that just overlap
  const tasksStartingAtTime = dayTasks.filter(task => {
    // Check if task starts exactly at this time slot
    return task.startTime === time;
  });

  if (tasksStartingAtTime.length === 0) {
    return [];
  }

  // Find all tasks that overlap with any of the starting tasks to determine positioning
  const allOverlappingTasks = [];

  tasksStartingAtTime.forEach(startingTask => {
    const taskStart = new Date(`2000-01-01T${startingTask.startTime}:00`);
    const taskEnd = new Date(`2000-01-01T${startingTask.endTime}:00`);

    // Find all tasks that overlap with this starting task
    const overlapping = dayTasks.filter(task => {
      const otherStart = new Date(`2000-01-01T${task.startTime}:00`);
      const otherEnd = new Date(`2000-01-01T${task.endTime}:00`);

      // Check if tasks overlap
      return taskStart < otherEnd && taskEnd > otherStart;
    });

    // Add to overlapping list if not already there
    overlapping.forEach(task => {
      if (!allOverlappingTasks.find(t => t.id === task.id)) {
        allOverlappingTasks.push(task);
      }
    });
  });

  // Sort all overlapping tasks by start time for consistent positioning
  allOverlappingTasks.sort((a, b) => a.startTime.localeCompare(b.startTime));

  const positions = [];
  const totalOverlapping = allOverlappingTasks.length;

  tasksStartingAtTime.forEach(startingTask => {
    // Find the index of this task in the overlapping tasks list
    const index = allOverlappingTasks.findIndex(t => t.id === startingTask.id);

    if (totalOverlapping === 1) {
      positions.push({
        task: startingTask,
        width: '95%',
        left: '0%',
        zIndex: 10
      });
    } else {
      const taskWidth = 95 / totalOverlapping;
      positions.push({
        task: startingTask,
        width: `${taskWidth}%`,
        left: `${(index * taskWidth)}%`,
        zIndex: 10 + index
      });
    }
  });

  return positions;
}

// TimeGridView Component
function TimeGridView({ currentView, currentDate, tasks, teamMembers, onSlotClick, onTaskClick, shouldHighlightTask }) {
  // Get tasks for a specific time slot
  const getTasksForSlot = (date, time) => {
    const dateStr = new Date(date).toISOString().split('T')[0];

    // Debug: Log what we're looking for
    if (tasks.length > 0) {
      console.log(`🔍 Looking for tasks on ${dateStr} at ${time}`);
      console.log(`📋 Available task dates:`, tasks.map(t => t.date));
      console.log(`🎯 Matching tasks:`, tasks.filter(t => t.date === dateStr).map(t => ({ title: t.title, startTime: t.startTime, endTime: t.endTime })));
    }

    const filteredTasks = tasks.filter(task => {
      if (task.date !== dateStr) return false;

      // Check if task is active during this time slot
      const taskStart = new Date(`2000-01-01T${task.startTime}:00`);
      const taskEnd = new Date(`2000-01-01T${task.endTime}:00`);
      const slotTime = new Date(`2000-01-01T${time}:00`);
      const slotEndTime = new Date(slotTime.getTime() + 30 * 60000); // 30 minutes later

      const isOverlapping = taskStart < slotEndTime && taskEnd > slotTime;

      // Show task if:
      // 1. It has no assigned members (show all unassigned tasks)
      // 2. It has assigned members and at least one is visible
      // 3. teamMembers is not loaded yet (show all tasks)
      if (!task.assignedMemberIds || task.assignedMemberIds.length === 0) {
        return isOverlapping; // Show unassigned tasks
      }

      if (!teamMembers || teamMembers.length === 0) {
        console.log('⚠️ Team members not loaded yet, showing all tasks');
        return isOverlapping; // Show all tasks if team members not loaded
      }

      const hasVisibleMember = task.assignedMemberIds.some(memberId => {
        const member = teamMembers.find(m => m.id === memberId && m.visible);
        if (!member) {
          console.log(`❌ Member ID ${memberId} not found in team members for task "${task.title}"`);
          console.log(`🔍 Available team member IDs:`, teamMembers.map(m => m.id));
          console.log(`🔍 Looking for exact match:`, memberId);
          // Check if member exists but is not visible
          const memberExists = teamMembers.find(m => m.id === memberId);
          if (memberExists) {
            console.log(`👁️ Member exists but is not visible:`, memberExists);
          }
        }
        return member;
      });

      if (!hasVisibleMember) {
        console.log(`🙈 Task "${task.title}" hidden - no visible assigned members`);
      }

      return isOverlapping && hasVisibleMember;
    });

    // Debug logging for task filtering
    if (filteredTasks.length > 0) {
      console.log(`🎯 Found ${filteredTasks.length} tasks for slot ${dateStr} ${time}:`,
        filteredTasks.map(t => ({ title: t.title, startTime: t.startTime, endTime: t.endTime })));
    }

    return filteredTasks;
  };

  // Calculate task height based on duration
  const calculateTaskHeight = (startTime, endTime) => {
    const [startHour, startMin] = startTime.split(':').map(Number);
    const [endHour, endMin] = endTime.split(':').map(Number);

    const startMinutes = startHour * 60 + startMin;
    const endMinutes = endHour * 60 + endMin;
    const durationMinutes = endMinutes - startMinutes;

    const slotHeight = 35;
    const thirtyMinuteSlots = durationMinutes / 30;
    return Math.max(slotHeight, slotHeight * thirtyMinuteSlots + 30);
  };

  return React.createElement('div', { className: 'time-grid' },
    // Time header
    React.createElement('div', { className: 'time-header' },
      React.createElement('div', { className: 'time-label' }, 'Час'),
      currentView === VIEW_TYPES.WEEK ? (
        // Week view - show 7 days starting from Monday
        Array.from({ length: 7 }, (_, i) => {
          const date = new Date(currentDate);
          const dayOfWeek = date.getDay();
          const daysFromMonday = dayOfWeek === 0 ? 6 : dayOfWeek - 1;
          const startOfWeek = new Date(date.setDate(date.getDate() - daysFromMonday));
          const dayDate = new Date(startOfWeek.setDate(startOfWeek.getDate() + i));
          const isNonWorking = isNonWorkingDay(dayDate);

          return React.createElement('div', {
            key: i,
            className: `day-header ${isNonWorking ? 'non-working-day' : ''}`
          }, `${getBulgarianDayName(dayDate, 'abbreviated')}, ${formatBulgarianDate(dayDate, 'dayMonth')}`);
        })
      ) : (
        // Day view - show single day
        React.createElement('div', {
          className: `day-header ${isNonWorkingDay(currentDate) ? 'non-working-day' : ''}`
        }, `${getBulgarianDayName(currentDate, 'long')}, ${formatBulgarianDate(currentDate, 'dayMonth')}`)
      )
    ),

    // Time slots
    TIME_SLOTS.map(time =>
      React.createElement('div', { key: time, className: 'time-row' },
        React.createElement('div', { className: 'time-label' }, time),
        currentView === VIEW_TYPES.WEEK ? (
          // Week view - 7 columns
          Array.from({ length: 7 }, (_, dayIndex) => {
            const date = new Date(currentDate);
            const dayOfWeek = date.getDay();
            const daysFromMonday = dayOfWeek === 0 ? 6 : dayOfWeek - 1;
            const startOfWeek = new Date(date.setDate(date.getDate() - daysFromMonday));
            const dayDate = new Date(startOfWeek.setDate(startOfWeek.getDate() + dayIndex));
            const dayTasks = getTasksForSlot(dayDate, time);
            const isNonWorking = isNonWorkingDay(dayDate);

            return React.createElement('div', {
              key: dayIndex,
              className: `time-slot ${isNonWorking ? 'non-working-day' : ''}`
            },
              React.createElement('div', {
                className: `unified-slot ${isNonWorking ? 'non-working-day' : ''}`,
                onClick: () => onSlotClick(dayDate, time, 1)
              },
                React.createElement('div', { className: 'tasks-container' },
                  (() => {
                    const taskPositions = calculateTaskPositions(dayTasks, time);
                    return taskPositions.map((position) => {
                      const task = position.task;
                      const assignedMembers = task.assignedMembers
                        .map(memberId => teamMembers.find(m => m.id === memberId))
                        .filter(Boolean);

                      const taskHeight = calculateTaskHeight(task.startTime, task.endTime);

                      return React.createElement('div', {
                        key: task.id,
                        className: `task-item task-spanning ${shouldHighlightTask(task) ? 'highlighted-task' : ''}`,
                        style: {
                          backgroundColor: task.color,
                          height: `${taskHeight}px`,
                          position: 'absolute',
                          width: position.width,
                          left: position.left,
                          zIndex: position.zIndex,
                          top: '0px',
                          margin: '0px'
                        },
                        onClick: (e) => {
                          e.stopPropagation();
                          onTaskClick(task);
                        }
                      },
                        React.createElement('div', { className: 'task-time' },
                          `${task.startTime} - ${task.endTime}`
                        ),
                        React.createElement('div', { className: 'task-title' }, task.title),
                        React.createElement('div', { className: 'task-description' }, task.description),
                        React.createElement('div', { className: 'task-members' },
                          assignedMembers.map(member =>
                            React.createElement('span', {
                              key: member.id,
                              className: 'member-initial',
                              style: {
                                backgroundColor: member.color,
                                color: 'black',
                                borderRadius: '50%',
                                width: '24px',
                                height: '24px',
                                display: 'inline-flex',
                                alignItems: 'center',
                                justifyContent: 'center',
                                fontSize: '0.7rem',
                                fontWeight: '600',
                                margin: '0 2px'
                              }
                            }, member.initials)
                          )
                        )
                      );
                    });
                  })()
                )
              )
            );
          })
        ) : (
          // Day view - single column
          React.createElement('div', {
            className: `time-slot ${isNonWorkingDay(currentDate) ? 'non-working-day' : ''}`
          },
            React.createElement('div', {
              className: `unified-slot ${isNonWorkingDay(currentDate) ? 'non-working-day' : ''}`,
              onClick: () => onSlotClick(currentDate, time, 1)
            },
              React.createElement('div', { className: 'tasks-container' },
                (() => {
                  const dayTasks = getTasksForSlot(currentDate, time);
                  const taskPositions = calculateTaskPositions(dayTasks, time);
                  return taskPositions.map((position) => {
                    const task = position.task;
                    const assignedMembers = task.assignedMembers
                      .map(memberId => teamMembers.find(m => m.id === memberId))
                      .filter(Boolean);

                    const taskHeight = calculateTaskHeight(task.startTime, task.endTime);

                    return React.createElement('div', {
                      key: task.id,
                      className: `task-item task-spanning ${shouldHighlightTask(task) ? 'highlighted-task' : ''}`,
                      style: {
                        backgroundColor: task.color,
                        height: `${taskHeight}px`,
                        position: 'absolute',
                        width: position.width,
                        left: position.left,
                        zIndex: position.zIndex,
                        top: '0px',
                        margin: '0px'
                      },
                      onClick: (e) => {
                        e.stopPropagation();
                        onTaskClick(task);
                      }
                    },
                      React.createElement('div', { className: 'task-time' },
                        `${task.startTime} - ${task.endTime}`
                      ),
                      React.createElement('div', { className: 'task-title' }, task.title),
                      React.createElement('div', { className: 'task-description' }, task.description),
                      React.createElement('div', { className: 'task-members' },
                        assignedMembers.map(member =>
                          React.createElement('span', {
                            key: member.id,
                            className: 'member-initial',
                            style: {
                              backgroundColor: member.color,
                              color: 'black',
                              borderRadius: '50%',
                              width: '24px',
                              height: '24px',
                              display: 'inline-flex',
                              alignItems: 'center',
                              justifyContent: 'center',
                              fontSize: '0.7rem',
                              fontWeight: '600',
                              margin: '0 2px'
                            }
                          }, member.initials)
                        )
                      )
                    );
                  });
                })()
              )
            )
          )
        )
      )
    )
  );
}

// Task Modal Component
function TaskModal({ isOpen, onClose, onSave, onDelete, task, selectedSlot, teamMembers, isLoading }) {
  const [formData, setFormData] = React.useState({
    title: '',
    description: '',
    assignedMemberIds: [],
    date: '',
    startTime: '09:00',
    endTime: '10:00',
    color: '#3b82f6'
  });

  const [comments, setComments] = React.useState([]);
  const [newComment, setNewComment] = React.useState('');
  const [loadingComments, setLoadingComments] = React.useState(false);

  // Load comments when task changes
  React.useEffect(() => {
    if (task && task.geoTaskId) {
      // Load existing GeoTask comments from the task data
      setComments(task.comments || []);
    } else {
      setComments([]);
    }
  }, [task]);

  const addComment = async () => {
    if (!newComment.trim() || !task?.geoTaskId) return;

    try {
      // Use existing GeoTask comment API
      const formData = new FormData();
      formData.append('Description', newComment.trim());
      formData.append('Add', 'Add');
      formData.append('__RequestVerificationToken', document.querySelector('input[name="__RequestVerificationToken"]')?.value || '');

      const response = await fetch(`/Comentar/Add/${task.geoTaskId}`, {
        method: 'POST',
        body: formData
      });

      if (response.ok) {
        // Reload the task to get updated comments
        window.location.reload();
      } else {
        console.error('Failed to add comment');
      }
    } catch (error) {
      console.error('Error adding comment:', error);
    }
  };

  // Initialize form data when modal opens
  React.useEffect(() => {
    if (task) {
      // Editing existing task
      console.log('🔍 DEBUGGING: Original task.date from server:', task.date);

      // Extract date directly from the string to avoid timezone conversion issues
      const dateOnly = task.date.split('T')[0]; // Gets "2025-06-28"
      console.log('🔍 DEBUGGING: Using date directly from split:', dateOnly);

      setFormData({
        title: task.title,
        description: task.description,
        assignedMemberIds: (task.assignedMemberIds || []).map(id => id.toLowerCase()), // Normalize to lowercase
        date: dateOnly, // Use the date string directly without any Date object conversion
        startTime: task.startTime,
        endTime: task.endTime,
        color: task.color
      });
    } else if (selectedSlot) {
      // Creating new task
      setFormData({
        title: '',
        description: '',
        assignedMemberIds: selectedSlot.memberId && typeof selectedSlot.memberId === 'string'
          ? [selectedSlot.memberId.toLowerCase()] // Normalize to lowercase
          : [],
        date: selectedSlot.date instanceof Date ? selectedSlot.date.toISOString().split('T')[0] : selectedSlot.date,
        startTime: selectedSlot.time,
        endTime: selectedSlot.time === '18:00' ? '18:30' : TIME_SLOTS[TIME_SLOTS.indexOf(selectedSlot.time) + 1] || '18:00',
        color: '#3b82f6'
      });
    }
  }, [task, selectedSlot]);

  const handleSubmit = (e) => {
    e.preventDefault();
    console.log('🚨 FORM SUBMITTED! handleSubmit called');
    console.log('📝 Form submission - formData.date:', formData.date);
    console.log('📝 Form submission - date type:', typeof formData.date);
    console.log('📝 Full formData:', formData);
    if (task) {
      onSave(task.id, formData);
    } else {
      onSave(formData);
    }
  };

  const handleMemberToggle = (memberId) => {
    // Normalize memberId to lowercase for consistency
    const normalizedMemberId = memberId.toLowerCase();
    console.log('🔄 Member toggle - Original ID:', memberId, 'Normalized ID:', normalizedMemberId);

    setFormData(prev => ({
      ...prev,
      assignedMemberIds: prev.assignedMemberIds.includes(normalizedMemberId)
        ? prev.assignedMemberIds.filter(id => id !== normalizedMemberId)
        : [...prev.assignedMemberIds, normalizedMemberId]
    }));
  };

  if (!isOpen) return null;

  return React.createElement('div', {
    className: 'modal-overlay',
    onClick: onClose
  },
    React.createElement('div', {
      className: 'modal-content',
      onClick: (e) => e.stopPropagation()
    },
      React.createElement('div', { className: 'modal-header' },
        React.createElement('h2', null, task ? BULGARIAN_LOCALE.ui.editTask : BULGARIAN_LOCALE.ui.createTask),
        React.createElement('button', {
          className: 'close-btn',
          onClick: onClose
        }, '×')
      ),

      React.createElement('form', {
        onSubmit: handleSubmit,
        className: 'task-form'
      },
        React.createElement('div', { className: 'form-group' },
          React.createElement('label', null, BULGARIAN_LOCALE.ui.title),
          React.createElement('input', {
            type: 'text',
            value: formData.title,
            onChange: (e) => setFormData(prev => ({ ...prev, title: e.target.value })),
            required: true
          })
        ),

        React.createElement('div', { className: 'form-group' },
          React.createElement('label', null, BULGARIAN_LOCALE.ui.description),
          React.createElement('textarea', {
            value: formData.description,
            onChange: (e) => setFormData(prev => ({ ...prev, description: e.target.value })),
            rows: 3
          })
        ),

        React.createElement('div', { className: 'form-group' },
          React.createElement('label', null, BULGARIAN_LOCALE.ui.date),
          React.createElement('input', {
            type: 'date',
            value: formData.date,
            onChange: (e) => {
              // Ensure date is always in YYYY-MM-DD format without timezone issues
              const dateValue = e.target.value;
              console.log('📅 Date input changed to:', dateValue);
              console.log('📅 Date input type:', typeof dateValue);
              console.log('📅 Setting formData.date to:', dateValue);
              setFormData(prev => ({ ...prev, date: dateValue }));
            },
            required: true
          })
        ),

        React.createElement('div', { className: 'form-row' },
          React.createElement('div', { className: 'form-group' },
            React.createElement('label', null, BULGARIAN_LOCALE.ui.startTime),
            React.createElement('select', {
              value: formData.startTime,
              onChange: (e) => setFormData(prev => ({ ...prev, startTime: e.target.value })),
              required: true
            },
              TIME_SLOTS.map(time =>
                React.createElement('option', { key: time, value: time }, time)
              )
            )
          ),

          React.createElement('div', { className: 'form-group' },
            React.createElement('label', null, BULGARIAN_LOCALE.ui.endTime),
            React.createElement('select', {
              value: formData.endTime,
              onChange: (e) => setFormData(prev => ({ ...prev, endTime: e.target.value })),
              required: true
            },
              TIME_SLOTS.map(time =>
                React.createElement('option', { key: time, value: time }, time)
              )
            )
          )
        ),

        React.createElement('div', { className: 'form-group' },
          React.createElement('label', null, BULGARIAN_LOCALE.ui.selectColor),
          React.createElement('div', { className: 'color-palette' },
            TASK_COLORS.map(color =>
              React.createElement('button', {
                key: color,
                type: 'button',
                className: `color-option ${formData.color === color ? 'selected' : ''}`,
                style: { backgroundColor: color },
                onClick: () => setFormData(prev => ({ ...prev, color })),
                title: color
              })
            )
          )
        ),

        React.createElement('div', { className: 'form-group' },
          React.createElement('label', null, BULGARIAN_LOCALE.ui.assignToTeamMembers),
          React.createElement('div', { className: 'member-dropdown' },
            teamMembers.map(member =>
              React.createElement('label', {
                key: member.id,
                className: 'member-checkbox-item'
              },
                React.createElement('input', {
                  type: 'checkbox',
                  checked: formData.assignedMemberIds.includes(member.id.toLowerCase()),
                  onChange: () => handleMemberToggle(member.id)
                }),
                React.createElement('div', {
                  className: 'member-avatar-small',
                  style: { backgroundColor: member.color }
                }, member.initials),
                React.createElement('span', null, member.name)
              )
            )
          )
        ),

        // GeoTask Comments Section (only show if task is connected to GeoTask)
        task && task.geoTaskId && React.createElement('div', { className: 'form-group geotask-comments-section' },
          React.createElement('h6', { className: 'comments-title' }, 'Коментари:'),

          // Comments list
          React.createElement('div', { className: 'comments-list' },
            loadingComments ?
              React.createElement('div', { className: 'text-center p-3' }, 'Зареждане...') :
              comments.length === 0 ?
                React.createElement('div', { className: 'no-comments' }, 'Няма коментари') :
                comments.map((comment, index) =>
                  React.createElement('div', {
                    key: comment.id || index,
                    className: 'comment-item'
                  },
                    React.createElement('div', { className: 'comment-header' },
                      React.createElement('strong', null, comment.workerFullName),
                      React.createElement('span', { className: 'comment-date' }, comment.createDate)
                    ),
                    React.createElement('div', { className: 'comment-text' }, comment.description)
                  )
                )
          ),

          // Add comment form
          React.createElement('div', { className: 'add-comment-form' },
            React.createElement('textarea', {
              className: 'form-control comment-input',
              placeholder: 'Добави коментар...',
              rows: 3,
              value: newComment,
              onChange: (e) => setNewComment(e.target.value)
            }),
            React.createElement('button', {
              type: 'button',
              className: 'btn btn-sm btn-primary mt-2',
              onClick: addComment,
              disabled: !newComment.trim()
            }, 'Добави коментар')
          )
        ),

        React.createElement('div', { className: 'modal-actions' },
          task && onDelete && React.createElement('button', {
            type: 'button',
            className: 'delete-btn',
            onClick: () => onDelete(task.id)
          }, BULGARIAN_LOCALE.ui.delete),

          React.createElement('button', {
            type: 'button',
            className: 'cancel-btn',
            onClick: onClose
          }, BULGARIAN_LOCALE.ui.cancel),

          React.createElement('button', {
            type: 'submit',
            className: 'save-btn',
            disabled: isLoading
          }, isLoading ? 'Запазване...' : (task ? BULGARIAN_LOCALE.ui.update : BULGARIAN_LOCALE.ui.save))
        )
      )
    )
  );
}

// Month View Component
function MonthView({ currentDate, tasks, teamMembers, onSlotClick, onTaskClick, getTasksForSlot }) {
  const getDaysInMonth = (date) => {
    const year = date.getFullYear();
    const month = date.getMonth();
    const firstDay = new Date(year, month, 1);
    const lastDay = new Date(year, month + 1, 0);
    const daysInMonth = lastDay.getDate();
    const startingDayOfWeek = firstDay.getDay();

    const days = [];

    // Add empty cells for days before the first day of the month
    // Convert Sunday (0) to 6, Monday (1) to 0, etc. for Monday-first week
    const mondayBasedStartDay = startingDayOfWeek === 0 ? 6 : startingDayOfWeek - 1;
    for (let i = 0; i < mondayBasedStartDay; i++) {
      days.push(null);
    }

    // Add all days of the month
    for (let day = 1; day <= daysInMonth; day++) {
      days.push(new Date(year, month, day));
    }

    return days;
  };

  const days = getDaysInMonth(currentDate);

  return React.createElement('div', { className: 'month-view' },
    // Month header with day names
    React.createElement('div', { className: 'month-header' },
      ['Пн', 'Вт', 'Ср', 'Чт', 'Пт', 'Сб', 'Нд'].map(day =>
        React.createElement('div', { key: day, className: 'month-day-header' }, day)
      )
    ),

    // Month grid
    React.createElement('div', { className: 'month-grid' },
      days.map((date, index) => {
        if (!date) {
          return React.createElement('div', { key: index, className: 'month-cell empty' });
        }

        const dateStr = date.toISOString().split('T')[0];
        const dayTasks = getTasksForSlot ? getTasksForSlot(date, '09:00') : [];
        const isToday = date.toDateString() === new Date().toDateString();

        return React.createElement('div', {
          key: index,
          className: `month-cell ${isToday ? 'today' : ''}`,
          onClick: () => onSlotClick(date, '09:00', null)
        },
          React.createElement('div', { className: 'month-day-number' }, date.getDate()),
          React.createElement('div', { className: 'month-tasks' },
            dayTasks.slice(0, 3).map(task =>
              React.createElement('div', {
                key: task.id,
                className: 'month-task',
                style: { backgroundColor: task.color },
                onClick: (e) => {
                  e.stopPropagation();
                  onTaskClick(task);
                }
              }, task.title)
            ),
            dayTasks.length > 3 &&
            React.createElement('div', { className: 'month-task-more' },
              `+${dayTasks.length - 3} more`
            )
          )
        );
      })
    )
  );
}

// Render the app
ReactDOM.render(React.createElement(CalendarApp), document.getElementById('react-calendar-root'));
