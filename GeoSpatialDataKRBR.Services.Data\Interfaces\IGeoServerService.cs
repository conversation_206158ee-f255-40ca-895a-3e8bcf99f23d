namespace GeoSpatialDataKRBR.Services.Data.Interfaces
{
    using GeoSpatialDataKRBR.Data.Models;

    public interface IGeoServerService
    {
        Task<bool> TestConnectionAsync(GeoServerConfiguration configuration);
        Task<IEnumerable<string>> GetWorkspacesAsync(GeoServerConfiguration configuration);
        Task<IEnumerable<string>> GetLayersAsync(GeoServerConfiguration configuration, string workspace);
        Task<string> GetLayerCapabilitiesAsync(GeoServerConfiguration configuration, string layerName);
        Task<string> GetFeatureInfoAsync(GeoServerConfiguration configuration, string layerName, 
            double x, double y, string srs, int width, int height, string bbox);
        Task<byte[]> GetMapImageAsync(GeoServerConfiguration configuration, string layerName, 
            string bbox, int width, int height, string srs, string format = "image/png");
        Task<string> GetWfsFeatureAsync(GeoServerConfiguration configuration, string layerName, 
            string filter = "", int maxFeatures = 100);
    }
}
