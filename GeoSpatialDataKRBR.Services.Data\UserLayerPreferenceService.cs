namespace GeoSpatialDataKRBR.Services.Data
{
    using Microsoft.EntityFrameworkCore;
    using GeoSpatialDataKRBR.Data;
    using GeoSpatialDataKRBR.Data.Models;
    using GeoSpatialDataKRBR.Services.Data.Interfaces;

    public class UserLayerPreferenceService : IUserLayerPreferenceService
    {
        private readonly GeoSpatialDbContext dbContext;

        public UserLayerPreferenceService(GeoSpatialDbContext dbContext)
        {
            this.dbContext = dbContext;
        }

        public async Task<IEnumerable<UserLayerPreference>> GetUserPreferencesAsync(Guid userId)
        {
            return await this.dbContext.UserLayerPreferences
                .Include(ulp => ulp.GeoLayer)
                .Where(ulp => ulp.UserId == userId)
                .OrderBy(ulp => ulp.DisplayOrder)
                .ToListAsync();
        }

        public async Task<UserLayerPreference?> GetUserLayerPreferenceAsync(Guid userId, Guid layerId)
        {
            return await this.dbContext.UserLayerPreferences
                .Include(ulp => ulp.GeoLayer)
                .FirstOrDefaultAsync(ulp => ulp.UserId == userId && ulp.GeoLayerId == layerId);
        }

        public async Task<bool> SaveUserPreferenceAsync(UserLayerPreference preference)
        {
            try
            {
                await this.dbContext.UserLayerPreferences.AddAsync(preference);
                int result = await this.dbContext.SaveChangesAsync();
                return result > 0;
            }
            catch
            {
                return false;
            }
        }

        public async Task<bool> UpdateUserPreferenceAsync(UserLayerPreference preference)
        {
            try
            {
                preference.ModifiedOn = DateTime.UtcNow;
                this.dbContext.UserLayerPreferences.Update(preference);
                int result = await this.dbContext.SaveChangesAsync();
                return result > 0;
            }
            catch
            {
                return false;
            }
        }

        public async Task<bool> DeleteUserPreferenceAsync(Guid userId, Guid layerId)
        {
            try
            {
                var preference = await this.dbContext.UserLayerPreferences
                    .FirstOrDefaultAsync(ulp => ulp.UserId == userId && ulp.GeoLayerId == layerId);
                
                if (preference == null)
                {
                    return false;
                }

                this.dbContext.UserLayerPreferences.Remove(preference);
                int result = await this.dbContext.SaveChangesAsync();
                return result > 0;
            }
            catch
            {
                return false;
            }
        }

        public async Task<bool> ToggleLayerVisibilityAsync(Guid userId, Guid layerId)
        {
            try
            {
                var preference = await this.dbContext.UserLayerPreferences
                    .FirstOrDefaultAsync(ulp => ulp.UserId == userId && ulp.GeoLayerId == layerId);

                if (preference == null)
                {
                    // Create new preference if it doesn't exist - default to visible when first toggled
                    preference = new UserLayerPreference
                    {
                        UserId = userId,
                        GeoLayerId = layerId,
                        IsVisible = true, // Make visible when first toggled
                        DisplayOrder = 0,
                        Opacity = 1.0,
                        CreatedOn = DateTime.UtcNow
                    };
                    await this.dbContext.UserLayerPreferences.AddAsync(preference);
                }
                else
                {
                    preference.IsVisible = !preference.IsVisible;
                    preference.ModifiedOn = DateTime.UtcNow;
                }

                int result = await this.dbContext.SaveChangesAsync();
                return result > 0;
            }
            catch
            {
                return false;
            }
        }

        public async Task<bool> UpdateLayerOpacityAsync(Guid userId, Guid layerId, double opacity)
        {
            try
            {
                var preference = await this.dbContext.UserLayerPreferences
                    .FirstOrDefaultAsync(ulp => ulp.UserId == userId && ulp.GeoLayerId == layerId);

                if (preference == null)
                {
                    preference = new UserLayerPreference
                    {
                        UserId = userId,
                        GeoLayerId = layerId,
                        Opacity = opacity,
                        CreatedOn = DateTime.UtcNow
                    };
                    await this.dbContext.UserLayerPreferences.AddAsync(preference);
                }
                else
                {
                    preference.Opacity = opacity;
                    preference.ModifiedOn = DateTime.UtcNow;
                }

                int result = await this.dbContext.SaveChangesAsync();
                return result > 0;
            }
            catch
            {
                return false;
            }
        }

        public async Task<bool> UpdateLayerOrderAsync(Guid userId, Guid layerId, int displayOrder)
        {
            try
            {
                var preference = await this.dbContext.UserLayerPreferences
                    .FirstOrDefaultAsync(ulp => ulp.UserId == userId && ulp.GeoLayerId == layerId);

                if (preference == null)
                {
                    preference = new UserLayerPreference
                    {
                        UserId = userId,
                        GeoLayerId = layerId,
                        DisplayOrder = displayOrder,
                        CreatedOn = DateTime.UtcNow
                    };
                    await this.dbContext.UserLayerPreferences.AddAsync(preference);
                }
                else
                {
                    preference.DisplayOrder = displayOrder;
                    preference.ModifiedOn = DateTime.UtcNow;
                }

                int result = await this.dbContext.SaveChangesAsync();
                return result > 0;
            }
            catch
            {
                return false;
            }
        }

        public async Task<bool> ResetUserPreferencesAsync(Guid userId)
        {
            try
            {
                var preferences = await this.dbContext.UserLayerPreferences
                    .Where(ulp => ulp.UserId == userId)
                    .ToListAsync();

                this.dbContext.UserLayerPreferences.RemoveRange(preferences);
                int result = await this.dbContext.SaveChangesAsync();
                return result >= 0;
            }
            catch
            {
                return false;
            }
        }
    }
}
