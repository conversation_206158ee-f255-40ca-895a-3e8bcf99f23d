# 📅 Task Calendar - Team Scheduling Application

A modern, responsive task calendar application for team scheduling and task management. Features drag-and-drop functionality, team member assignment, and mobile-friendly design.

## 🚀 Quick Start

### Prerequisites
- Node.js (version 14 or higher)
- npm or yarn package manager

### Local Development
1. **Open Terminal/Command Prompt**
   - Windows: Press `Win + R`, type `cmd`, press Enter
   - Mac: Press `Cmd + Space`, type "Terminal", press Enter
   - Linux: Press `Ctrl + Alt + T`

2. **Navigate to Project Directory**
   ```bash
   cd "d:\JSadvatage\Calendar\task-calendar"
   ```

3. **Install Dependencies** (first time only)
   ```bash
   npm install
   ```

4. **Start Development Server**
   ```bash
   npm run dev
   ```t

5. **Open in Browser**
   - Go to `http://localhost:5173/`
   - Application should open automatically

### Stopping the Application
- Press `Ctrl + C` in terminal to stop server

## 🌐 HOSTING OPTIONS (1-Day Usage)

### Option 1: Netlify Drop (EASIEST - No Account Required) ⭐
**Perfect for 1-day hosting - Auto-deletes after 24 hours!**

1. **Build the project:**
   ```bash
   npm run build
   ```

2. **Go to Netlify Drop:**
   - Visit: [netlify.com/drop](https://netlify.com/drop)
   - Drag and drop the `dist` folder from your project
   - Get instant URL (e.g., `https://amazing-name-123456.netlify.app`)

3. **Access from any device:**
   - Share the URL with anyone
   - Works on Android, iPhone, tablets, computers
   - **Automatically deletes after 24 hours** ✅

### Option 2: Local Network Access (Free)
**Access from phones/tablets on same WiFi**

1. **Start with network access:**
   ```bash
   npm run dev -- --host
   ```

2. **Find your computer's IP address:**
   - Windows: Open cmd, type `ipconfig`, look for IPv4 Address
   - Mac/Linux: Open terminal, type `ifconfig`, look for inet

3. **Access from any device on same WiFi:**
   - Go to `http://YOUR_IP_ADDRESS:5173`
   - Example: `http://*************:5173`

### Option 3: Vercel (Free Account Required)
**Professional hosting with custom domain**

1. **Install Vercel CLI:**
   ```bash
   npm install -g vercel
   ```

2. **Build and deploy:**
   ```bash
   npm run build
   vercel --prod
   ```

3. **Follow prompts for instant deployment**

## 🗑️ HOW TO DROP/REMOVE HOSTING

### Netlify Drop
- **Automatic**: Files auto-delete after 24 hours ✅
- **Manual**: No account to delete, just lose the URL

### Vercel
1. Go to [vercel.com/dashboard](https://vercel.com/dashboard)
2. Find your project → Settings → Advanced → Delete Project

### Local Network
- Simply stop the server with `Ctrl + C`

## 📱 ANDROID PHONE ACCESS

### Method 1: Internet Access (Recommended)
1. Use Netlify Drop (Option 1 above)
2. Share the URL with your phone
3. Open in any browser (Chrome, Firefox, etc.)
4. Works perfectly on Android! ✅

### Method 2: Same WiFi Access
1. Use Local Network option (Option 2 above)
2. Connect phone to same WiFi as computer
3. Open browser and go to `http://YOUR_IP:5173`

### Mobile Features
- ✅ **Touch-friendly**: Optimized for touch screens
- ✅ **Responsive**: Adapts to phone screen size
- ✅ **Full functionality**: All features work on mobile
- ✅ **Fast loading**: Optimized for mobile networks

## ✨ Features

### 🎯 Task Management
- **Drag & Drop**: Move tasks between time slots and days
- **Simple Colors**: 10 predefined colors for easy selection
- **Team Assignment**: Checkboxes on the right for team members
- **Visual Layout**: Time at top, description in middle, team initials at bottom

### 👥 Team Management
- **Team Sidebar**: View all team members with colored initials
- **Checkbox Selection**: Clean interface with checkboxes on the right
- **Multiple Assignment**: Assign multiple team members to tasks
- **Color Coding**: Each team member has a unique color

### 📅 Calendar Views
- **Day/Week/Month/Year**: Multiple view options
- **Centered Layout**: Calendar centered with wider columns
- **Mobile Responsive**: Works perfectly on all devices
- **Conflict Detection**: Alerts for scheduling conflicts

## 🔧 Quick Configuration

### Add Team Members
Edit `src/App.jsx`:
```javascript
const [teamMembers, setTeamMembers] = useState([
  { id: 1, name: 'Your Name', initials: 'YN', color: '#3B82F6' },
  // Add more team members here
])
```

### Change Working Hours
Modify time range in `generateTimeSlots()`:
```javascript
for (let hour = 8; hour <= 18; hour++) { // 8 AM to 6 PM
```

## 🔗 C# MVC Integration Available!
Ready to connect with your existing C# MVC project:
- API endpoint integration
- Database synchronization
- User authentication
- Real worker data

## 📞 Need Help?
- Check this README for common issues
- All hosting options are FREE
- Perfect for 1-day events and testing

---
**🎯 Perfect for team scheduling, project management, and temporary event coordination!**
