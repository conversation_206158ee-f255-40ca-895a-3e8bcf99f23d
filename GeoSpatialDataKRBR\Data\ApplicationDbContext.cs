﻿using Microsoft.AspNetCore.Identity;
using Microsoft.AspNetCore.Identity.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore;
using GeoSpatialDataKRBR.Data.Models;

namespace GeoSpatialDataKRBR.Data
{
    public class ApplicationDbContext : IdentityDbContext<ApplicationUser, IdentityRole<Guid>, Guid>
    {
        public ApplicationDbContext(DbContextOptions<ApplicationDbContext> options)
            : base(options)
        {
        }

        public DbSet<GeoLayer> GeoLayers { get; set; } = null!;
        public DbSet<GeoServerConfiguration> GeoServerConfigurations { get; set; } = null!;
        public DbSet<GeoLayerGroup> GeoLayerGroups { get; set; } = null!;
        public DbSet<GeoLayerGroupLayer> GeoLayerGroupLayers { get; set; } = null!;
        public DbSet<UserLayerPreference> UserLayerPreferences { get; set; } = null!;
    }
}