namespace TaskManager.Web.ViewModels.Kanban
{
    using System.ComponentModel.DataAnnotations;
    using static TaskManager.Common.EntityValidationConstants.KanbanCard;

    /// <summary>
    /// View model for updating an existing Kanban card
    /// </summary>
    public class UpdateKanbanCardViewModel
    {
        [Required]
        public string Id { get; set; } = null!;

        [Required(ErrorMessage = "Заглавието е задължително")]
        [StringLength(TitleMaxLength, MinimumLength = TitleMinLength, 
            ErrorMessage = "Заглавието трябва да бъде между {2} и {1} символа")]
        [Display(Name = "Заглавие")]
        public string Title { get; set; } = null!;

        [StringLength(DescriptionMaxLength, MinimumLength = DescriptionMinLength,
            ErrorMessage = "Описанието трябва да бъде между {2} и {1} символа")]
        [Display(Name = "Описание")]
        public string? Description { get; set; }

        [Display(Name = "Цвят")]
        [StringLength(ColorMaxLength, MinimumLength = ColorMinLength)]
        public string? Color { get; set; }

        [Display(Name = "Етикети")]
        [StringLength(LabelsMaxLength, MinimumLength = LabelsMinLength)]
        public string? Labels { get; set; }

        [Display(Name = "Краен срок")]
        [DataType(DataType.DateTime)]
        public DateTime? DueDate { get; set; }

        [Display(Name = "Възложено на")]
        public string? AssignedToId { get; set; }

        [Display(Name = "Начало на работа")]
        public string? StartTime { get; set; }

        // For worker reassignment
        [Display(Name = "Целева колона")]
        public string? TargetColumnId { get; set; }

        // Current column (for reference)
        public string ColumnId { get; set; } = null!;
    }
}
