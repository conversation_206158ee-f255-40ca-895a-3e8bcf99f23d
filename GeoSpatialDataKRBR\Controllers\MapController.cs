namespace GeoSpatialDataKRBR.Controllers
{
    using Microsoft.AspNetCore.Authorization;
    using Microsoft.AspNetCore.Mvc;
    using GeoSpatialDataKRBR.Services.Data.Interfaces;
    using GeoSpatialDataKRBR.Web.ViewModels.Map;
    using GeoSpatialDataKRBR.Web.ViewModels.GeoLayer;
    using System.Security.Claims;

    [Authorize]
    public class MapController : Controller
    {
        private readonly IGeoLayerService geoLayerService;
        private readonly IUserLayerPreferenceService userLayerPreferenceService;

        public MapController(
            IGeoLayerService geoLayerService,
            IUserLayerPreferenceService userLayerPreferenceService)
        {
            this.geoLayerService = geoLayerService;
            this.userLayerPreferenceService = userLayerPreferenceService;
        }

        public async Task<IActionResult> Index()
        {
            try
            {
                var userId = GetCurrentUserId();
                var allLayers = await this.geoLayerService.GetAllLayersAsync();

                // Debug: Log all layers
                Console.WriteLine($"=== MAP CONTROLLER DEBUG ===");
                Console.WriteLine($"Total layers from service: {allLayers.Count()}");
                foreach (var layer in allLayers)
                {
                    Console.WriteLine($"Layer: {layer.Name}, Workspace: {layer.Workspace}, IsBaseLayer: {layer.IsBaseLayer}");
                }
                
                var mapViewModel = new MapViewModel
                {
                    CenterLatitude = 42.7339, // Bulgaria center
                    CenterLongitude = 25.4858, // Bulgaria center
                    ZoomLevel = 7,
                    MinZoom = 1,
                    MaxZoom = 18,
                    ShowLayerControl = true,
                    ShowScaleControl = true,
                    ShowZoomControl = true
                };

                // Get user preferences for all layers
                var userPreferences = await this.userLayerPreferenceService.GetUserPreferencesAsync(userId);
                var userPrefDict = userPreferences.ToDictionary(p => p.GeoLayerId, p => p);

                // Separate base layers from regular layers
                var baseLayers = allLayers.Where(l => l.IsBaseLayer).ToList();
                var regularLayers = allLayers.Where(l => !l.IsBaseLayer).ToList();

                // Debug: Log layer separation
                Console.WriteLine($"Base layers count: {baseLayers.Count}");
                Console.WriteLine($"Regular layers count: {regularLayers.Count}");
                foreach (var layer in regularLayers)
                {
                    Console.WriteLine($"Regular layer: {layer.Name}, Workspace: {layer.Workspace}");
                }

                mapViewModel.BaseLayers = baseLayers.Select(l =>
                {
                    var userPref = userPrefDict.ContainsKey(l.Id) ? userPrefDict[l.Id] : null;
                    return new GeoLayerListViewModel
                    {
                        Id = l.Id,
                        Name = l.Name,
                        Description = l.Description,
                        LayerName = l.LayerName,
                        Workspace = l.Workspace,
                        WmsUrl = l.WmsUrl,
                        WfsUrl = l.WfsUrl,
                        LayerType = l.LayerType,
                        IsVisible = userPref?.IsVisible ?? l.IsVisible,
                        IsBaseLayer = l.IsBaseLayer,
                        DisplayOrder = userPref?.DisplayOrder ?? l.DisplayOrder,
                        Opacity = userPref?.Opacity ?? l.Opacity,
                        GeoServerConfigurationName = l.GeoServerConfiguration?.Name,
                        CreatedOn = l.CreatedOn
                    };
                }).ToList();

                mapViewModel.Layers = regularLayers.Select(l =>
                {
                    var userPref = userPrefDict.ContainsKey(l.Id) ? userPrefDict[l.Id] : null;
                    return new GeoLayerListViewModel
                    {
                        Id = l.Id,
                        Name = l.Name,
                        Description = l.Description,
                        LayerName = l.LayerName,
                        Workspace = l.Workspace,
                        WmsUrl = l.WmsUrl,
                        WfsUrl = l.WfsUrl,
                        LayerType = l.LayerType,
                        IsVisible = userPref?.IsVisible ?? false, // Default to false for regular layers
                        IsBaseLayer = l.IsBaseLayer,
                        DisplayOrder = userPref?.DisplayOrder ?? l.DisplayOrder,
                        Opacity = userPref?.Opacity ?? l.Opacity,
                        GeoServerConfigurationName = l.GeoServerConfiguration?.Name,
                        CreatedOn = l.CreatedOn
                    };
                }).ToList();

                // Set default base layer
                if (mapViewModel.BaseLayers.Any())
                {
                    mapViewModel.SelectedBaseLayerId = mapViewModel.BaseLayers.First().Id.ToString();
                }

                return View(mapViewModel);
            }
            catch (Exception ex)
            {
                TempData["ErrorMessage"] = "Грешка при зареждане на картата: " + ex.Message;
                return RedirectToAction("Index", "Home");
            }
        }

        [HttpGet]
        public async Task<IActionResult> LayerManager()
        {
            try
            {
                var layers = await this.geoLayerService.GetAllLayersAsync();
                var layerViewModels = layers.Select(l => new GeoLayerListViewModel
                {
                    Id = l.Id,
                    Name = l.Name,
                    Description = l.Description,
                    LayerName = l.LayerName,
                    Workspace = l.Workspace,
                    WmsUrl = l.WmsUrl,
                    WfsUrl = l.WfsUrl,
                    LayerType = l.LayerType,
                    IsVisible = l.IsVisible,
                    IsBaseLayer = l.IsBaseLayer,
                    DisplayOrder = l.DisplayOrder,
                    Opacity = l.Opacity,
                    GeoServerConfigurationName = l.GeoServerConfiguration?.Name,
                    CreatedOn = l.CreatedOn
                }).ToList();

                return View(layerViewModels);
            }
            catch (Exception ex)
            {
                TempData["ErrorMessage"] = "Грешка при зареждане на слоевете: " + ex.Message;
                return RedirectToAction("Index");
            }
        }

        private Guid GetCurrentUserId()
        {
            var userIdClaim = User.FindFirst(ClaimTypes.NameIdentifier)?.Value;
            if (Guid.TryParse(userIdClaim, out var userId))
            {
                return userId;
            }
            throw new UnauthorizedAccessException("Невалиден потребител");
        }
    }
}
