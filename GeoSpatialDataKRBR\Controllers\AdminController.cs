namespace GeoSpatialDataKRBR.Controllers
{
    using Microsoft.AspNetCore.Mvc;
    using Microsoft.EntityFrameworkCore;
    using GeoSpatialDataKRBR.Data;
    using GeoSpatialDataKRBR.Data.Models;
    using GeoSpatialDataKRBR.Services.Data.Interfaces;
    using GeoSpatialDataKRBR.Web.ViewModels.GeoLayer;
    using GeoSpatialDataKRBR.Web.Infrastructure.Attributes;

    [AdminOnly]
    public class AdminController : Controller
    {
        private readonly IGeoLayerService geoLayerService;
        private readonly IGeoServerService geoServerService;
        private readonly GeoSpatialDbContext dbContext;

        public AdminController(
            IGeoLayerService geoLayerService,
            IGeoServerService geoServerService,
            GeoSpatialDbContext dbContext)
        {
            this.geoLayerService = geoLayerService;
            this.geoServerService = geoServerService;
            this.dbContext = dbContext;
        }

        public IActionResult Index()
        {
            return View();
        }

        [HttpGet]
        public async Task<IActionResult> Layers()
        {
            try
            {
                var layers = await this.geoLayerService.GetAllLayersAsync();
                var layerViewModels = layers.Select(l => new GeoLayerListViewModel
                {
                    Id = l.Id,
                    Name = l.Name,
                    Description = l.Description,
                    LayerName = l.LayerName,
                    Workspace = l.Workspace,
                    WmsUrl = l.WmsUrl,
                    WfsUrl = l.WfsUrl,
                    LayerType = l.LayerType,
                    IsVisible = l.IsVisible,
                    IsBaseLayer = l.IsBaseLayer,
                    DisplayOrder = l.DisplayOrder,
                    Opacity = l.Opacity,
                    GeoServerConfigurationName = l.GeoServerConfiguration?.Name,
                    CreatedOn = l.CreatedOn
                }).ToList();

                return View(layerViewModels);
            }
            catch (Exception ex)
            {
                TempData["ErrorMessage"] = "Грешка при зареждане на слоевете: " + ex.Message;
                return View(new List<GeoLayerListViewModel>());
            }
        }

        [HttpGet]
        public async Task<IActionResult> CreateLayer()
        {
            var viewModel = new GeoLayerViewModel();
            await PopulateGeoServerConfigurations(viewModel);
            return View(viewModel);
        }

        [HttpPost]
        [ValidateAntiForgeryToken]
        public async Task<IActionResult> CreateLayer(GeoLayerViewModel model)
        {
            if (!ModelState.IsValid)
            {
                await PopulateGeoServerConfigurations(model);
                return View(model);
            }

            try
            {
                // Check if layer already exists
                var existingLayer = await this.geoLayerService.LayerExistsAsync(model.LayerName, model.Workspace);
                if (existingLayer)
                {
                    ModelState.AddModelError("", "Слой с това име вече съществува в този workspace");
                    await PopulateGeoServerConfigurations(model);
                    return View(model);
                }

                var layer = new GeoLayer
                {
                    Name = model.Name,
                    Description = model.Description,
                    LayerName = model.LayerName,
                    Workspace = model.Workspace,
                    WmsUrl = model.WmsUrl,
                    WfsUrl = model.WfsUrl,
                    StyleName = model.StyleName,
                    LayerType = model.LayerType,
                    IsVisible = model.IsVisible,
                    IsBaseLayer = model.IsBaseLayer,
                    DisplayOrder = model.DisplayOrder,
                    Opacity = model.Opacity,
                    GeoServerConfigurationId = model.GeoServerConfigurationId,
                    CreatedOn = DateTime.UtcNow
                };

                var success = await this.geoLayerService.CreateLayerAsync(layer);
                if (success)
                {
                    TempData["SuccessMessage"] = "Слоят беше създаден успешно!";
                    return RedirectToAction(nameof(Layers));
                }
                else
                {
                    ModelState.AddModelError("", "Грешка при създаване на слоя");
                }
            }
            catch (Exception ex)
            {
                ModelState.AddModelError("", "Грешка при създаване на слоя: " + ex.Message);
            }

            await PopulateGeoServerConfigurations(model);
            return View(model);
        }

        [HttpGet]
        public async Task<IActionResult> EditLayer(Guid id)
        {
            try
            {
                var layer = await this.geoLayerService.GetLayerByIdAsync(id);
                if (layer == null)
                {
                    TempData["ErrorMessage"] = "Слоят не беше намерен";
                    return RedirectToAction(nameof(Layers));
                }

                var viewModel = new GeoLayerViewModel
                {
                    Id = layer.Id,
                    Name = layer.Name,
                    Description = layer.Description,
                    LayerName = layer.LayerName,
                    Workspace = layer.Workspace,
                    WmsUrl = layer.WmsUrl,
                    WfsUrl = layer.WfsUrl,
                    StyleName = layer.StyleName,
                    LayerType = layer.LayerType,
                    IsVisible = layer.IsVisible,
                    IsBaseLayer = layer.IsBaseLayer,
                    DisplayOrder = layer.DisplayOrder,
                    Opacity = layer.Opacity,
                    GeoServerConfigurationId = layer.GeoServerConfigurationId,
                    CreatedOn = layer.CreatedOn,
                    ModifiedOn = layer.ModifiedOn
                };

                await PopulateGeoServerConfigurations(viewModel);
                return View(viewModel);
            }
            catch (Exception ex)
            {
                TempData["ErrorMessage"] = "Грешка при зареждане на слоя: " + ex.Message;
                return RedirectToAction(nameof(Layers));
            }
        }

        [HttpPost]
        [ValidateAntiForgeryToken]
        public async Task<IActionResult> EditLayer(GeoLayerViewModel model)
        {
            if (!ModelState.IsValid)
            {
                await PopulateGeoServerConfigurations(model);
                return View(model);
            }

            try
            {
                var layer = await this.geoLayerService.GetLayerByIdAsync(model.Id);
                if (layer == null)
                {
                    TempData["ErrorMessage"] = "Слоят не беше намерен";
                    return RedirectToAction(nameof(Layers));
                }

                // Update layer properties
                layer.Name = model.Name;
                layer.Description = model.Description;
                layer.LayerName = model.LayerName;
                layer.Workspace = model.Workspace;
                layer.WmsUrl = model.WmsUrl;
                layer.WfsUrl = model.WfsUrl;
                layer.StyleName = model.StyleName;
                layer.LayerType = model.LayerType;
                layer.IsVisible = model.IsVisible;
                layer.IsBaseLayer = model.IsBaseLayer;
                layer.DisplayOrder = model.DisplayOrder;
                layer.Opacity = model.Opacity;
                layer.GeoServerConfigurationId = model.GeoServerConfigurationId;
                layer.ModifiedOn = DateTime.UtcNow;

                var success = await this.geoLayerService.UpdateLayerAsync(layer);
                if (success)
                {
                    TempData["SuccessMessage"] = "Слоят беше обновен успешно!";
                    return RedirectToAction(nameof(Layers));
                }
                else
                {
                    ModelState.AddModelError("", "Грешка при обновяване на слоя");
                }
            }
            catch (Exception ex)
            {
                ModelState.AddModelError("", "Грешка при обновяване на слоя: " + ex.Message);
            }

            await PopulateGeoServerConfigurations(model);
            return View(model);
        }

        [HttpPost]
        [ValidateAntiForgeryToken]
        public async Task<IActionResult> DeleteLayer(Guid id)
        {
            try
            {
                var success = await this.geoLayerService.DeleteLayerAsync(id);
                if (success)
                {
                    TempData["SuccessMessage"] = "Слоят беше изтрит успешно!";
                }
                else
                {
                    TempData["ErrorMessage"] = "Грешка при изтриване на слоя";
                }
            }
            catch (Exception ex)
            {
                TempData["ErrorMessage"] = "Грешка при изтриване на слоя: " + ex.Message;
            }

            return RedirectToAction(nameof(Layers));
        }

        [HttpGet]
        public async Task<IActionResult> GeoServerConfigurations()
        {
            try
            {
                var configurations = await this.dbContext.GeoServerConfigurations
                    .OrderBy(c => c.Name)
                    .ToListAsync();

                return View(configurations);
            }
            catch (Exception ex)
            {
                TempData["ErrorMessage"] = "Грешка при зареждане на конфигурациите: " + ex.Message;
                return View(new List<GeoServerConfiguration>());
            }
        }

        private async Task PopulateGeoServerConfigurations(GeoLayerViewModel model)
        {
            var configurations = await this.dbContext.GeoServerConfigurations
                .Where(c => c.IsActive)
                .OrderBy(c => c.Name)
                .Select(c => new { c.Id, c.Name })
                .ToListAsync();

            ViewBag.GeoServerConfigurations = configurations;
        }
    }
}
