namespace TaskManager.Data.Configurations
{
    using Microsoft.EntityFrameworkCore;
    using Microsoft.EntityFrameworkCore.Metadata.Builders;
    using TaskManager.Data.Models;

    public class CalendarTaskEntityConfigurations : IEntityTypeConfiguration<CalendarTask>
    {
        public void Configure(EntityTypeBuilder<CalendarTask> builder)
        {
            // Configure one-to-one relationship with GeoTask
            builder
                .HasOne(ct => ct.GeoTask)
                .WithOne(gt => gt.CalendarTask)
                .HasForeignKey<CalendarTask>(ct => ct.GeoTaskId)
                .OnDelete(DeleteBehavior.SetNull);

            // Configure many-to-many relationship with Workers through CalendarTaskWorker
            builder
                .HasMany(ct => ct.AssignedWorkers)
                .WithOne(ctw => ctw.CalendarTask)
                .HasForeignKey(ctw => ctw.CalendarTaskId)
                .OnDelete(DeleteBehavior.Cascade);

            // Configure properties
            builder
                .Property(ct => ct.Title)
                .IsRequired()
                .HasMaxLength(200);

            builder
                .Property(ct => ct.Description)
                .HasMaxLength(1000);

            builder
                .Property(ct => ct.Color)
                .HasMaxLength(7)
                .HasDefaultValue("#3b82f6");

            builder
                .Property(ct => ct.CreatedDate)
                .HasDefaultValueSql("GETUTCDATE()");

            // Index for better performance
            builder
                .HasIndex(ct => ct.Date)
                .HasDatabaseName("IX_CalendarTask_Date");

            builder
                .HasIndex(ct => ct.GeoTaskId)
                .IsUnique()
                .HasDatabaseName("IX_CalendarTask_GeoTaskId");
        }
    }
}
