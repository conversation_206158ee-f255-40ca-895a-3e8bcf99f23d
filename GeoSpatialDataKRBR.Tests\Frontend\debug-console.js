// Debug script for browser console testing
// Copy and paste this into browser console after logging in

console.log('🚀 Starting GeoSpatial Debug Session...');

// Debug helper functions
window.geoDebug = {
    // Test API endpoints
    async testToggleVisibility(layerId, isVisible) {
        console.log(`🔄 Testing toggle visibility: ${layerId} -> ${isVisible}`);
        try {
            const response = await fetch(`/api/geolayerapi/toggle-visibility/${layerId}`, {
                method: 'POST',
                credentials: 'include',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify({ isVisible: isVisible })
            });
            
            const result = await response.json();
            console.log(`✅ Toggle response:`, result);
            return result;
        } catch (error) {
            console.error(`❌ Toggle error:`, error);
            return null;
        }
    },

    async testUpdateOpacity(layerId, opacity) {
        console.log(`🎨 Testing update opacity: ${layerId} -> ${opacity}`);
        try {
            const response = await fetch(`/api/geolayerapi/update-opacity/${layerId}`, {
                method: 'POST',
                credentials: 'include',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify({ opacity: opacity })
            });
            
            const result = await response.json();
            console.log(`✅ Opacity response:`, result);
            return result;
        } catch (error) {
            console.error(`❌ Opacity error:`, error);
            return null;
        }
    },

    async getAllLayers() {
        console.log(`📋 Getting all layers...`);
        try {
            const response = await fetch('/api/geolayerapi', {
                method: 'GET',
                credentials: 'include'
            });
            
            const layers = await response.json();
            console.log(`✅ All layers:`, layers);
            return layers;
        } catch (error) {
            console.error(`❌ Get layers error:`, error);
            return null;
        }
    },

    async getUserPreferences() {
        console.log(`👤 Getting user preferences...`);
        try {
            const response = await fetch('/api/geolayerapi/user-preferences', {
                method: 'GET',
                credentials: 'include'
            });
            
            const preferences = await response.json();
            console.log(`✅ User preferences:`, preferences);
            return preferences;
        } catch (error) {
            console.error(`❌ Get preferences error:`, error);
            return null;
        }
    },

    // Test layer functionality
    testLayerFunctions() {
        console.log(`🧪 Testing layer functions...`);
        
        // Check if mapLayers exists
        if (typeof mapLayers !== 'undefined') {
            console.log(`✅ mapLayers found:`, Object.keys(mapLayers));
        } else {
            console.error(`❌ mapLayers not found`);
        }
        
        // Check if map exists
        if (typeof map !== 'undefined') {
            console.log(`✅ map found:`, map);
        } else {
            console.error(`❌ map not found`);
        }
        
        // Check if toggleLayerVisibility exists
        if (typeof toggleLayerVisibility !== 'undefined') {
            console.log(`✅ toggleLayerVisibility function found`);
        } else {
            console.error(`❌ toggleLayerVisibility function not found`);
        }
        
        // Check if toggleBaseLayer exists
        if (typeof toggleBaseLayer !== 'undefined') {
            console.log(`✅ toggleBaseLayer function found`);
        } else {
            console.error(`❌ toggleBaseLayer function not found`);
        }
    },

    // Test all checkboxes
    testAllCheckboxes() {
        console.log(`☑️ Testing all checkboxes...`);
        
        const checkboxes = document.querySelectorAll('input[type="checkbox"][data-layer-id]');
        console.log(`Found ${checkboxes.length} layer checkboxes`);
        
        checkboxes.forEach((checkbox, index) => {
            const layerId = checkbox.getAttribute('data-layer-id');
            const isChecked = checkbox.checked;
            console.log(`Checkbox ${index + 1}: Layer ${layerId} - Checked: ${isChecked}`);
        });
        
        return checkboxes;
    },

    // Test all radio buttons (base layers)
    testAllRadioButtons() {
        console.log(`📻 Testing all radio buttons...`);
        
        const radioButtons = document.querySelectorAll('input[type="radio"][name="baseLayer"]');
        console.log(`Found ${radioButtons.length} base layer radio buttons`);
        
        radioButtons.forEach((radio, index) => {
            const layerId = radio.value;
            const isChecked = radio.checked;
            console.log(`Radio ${index + 1}: Layer ${layerId} - Checked: ${isChecked}`);
        });
        
        return radioButtons;
    },

    // Simulate clicking a checkbox
    async simulateCheckboxClick(layerId) {
        console.log(`🖱️ Simulating checkbox click for layer: ${layerId}`);
        
        const checkbox = document.querySelector(`input[type="checkbox"][data-layer-id="${layerId}"]`);
        if (checkbox) {
            const wasChecked = checkbox.checked;
            checkbox.checked = !wasChecked;
            
            // Trigger the change event
            const event = new Event('change', { bubbles: true });
            checkbox.dispatchEvent(event);
            
            console.log(`✅ Checkbox clicked: ${wasChecked} -> ${!wasChecked}`);
            return !wasChecked;
        } else {
            console.error(`❌ Checkbox not found for layer: ${layerId}`);
            return null;
        }
    },

    // Simulate clicking a radio button
    async simulateRadioClick(layerId) {
        console.log(`🖱️ Simulating radio click for base layer: ${layerId}`);
        
        const radio = document.querySelector(`input[type="radio"][value="${layerId}"]`);
        if (radio) {
            radio.checked = true;
            
            // Trigger the change event
            const event = new Event('change', { bubbles: true });
            radio.dispatchEvent(event);
            
            console.log(`✅ Radio button clicked for layer: ${layerId}`);
            return true;
        } else {
            console.error(`❌ Radio button not found for layer: ${layerId}`);
            return null;
        }
    },

    // Run full test suite
    async runFullTest() {
        console.log(`🧪 Running full test suite...`);
        
        // 1. Test basic functions
        this.testLayerFunctions();
        
        // 2. Get all layers
        const layers = await this.getAllLayers();
        
        // 3. Get user preferences
        const preferences = await this.getUserPreferences();
        
        // 4. Test checkboxes
        const checkboxes = this.testAllCheckboxes();
        
        // 5. Test radio buttons
        const radioButtons = this.testAllRadioButtons();
        
        // 6. Test API calls if we have layers
        if (layers && layers.length > 0) {
            const firstLayer = layers[0];
            console.log(`🧪 Testing API with first layer: ${firstLayer.id}`);
            
            // Test toggle visibility
            await this.testToggleVisibility(firstLayer.id, true);
            await new Promise(resolve => setTimeout(resolve, 1000)); // Wait 1 second
            await this.testToggleVisibility(firstLayer.id, false);
            
            // Test opacity
            await this.testUpdateOpacity(firstLayer.id, 0.5);
            await new Promise(resolve => setTimeout(resolve, 1000)); // Wait 1 second
            await this.testUpdateOpacity(firstLayer.id, 1.0);
        }
        
        console.log(`✅ Full test suite completed!`);
    }
};

// Auto-run basic tests
console.log('🔍 Running basic diagnostics...');
geoDebug.testLayerFunctions();
geoDebug.testAllCheckboxes();
geoDebug.testAllRadioButtons();

console.log(`
🎯 Debug commands available:
- geoDebug.runFullTest() - Run complete test suite
- geoDebug.getAllLayers() - Get all layers from API
- geoDebug.getUserPreferences() - Get user preferences
- geoDebug.testToggleVisibility(layerId, isVisible) - Test toggle API
- geoDebug.testUpdateOpacity(layerId, opacity) - Test opacity API
- geoDebug.simulateCheckboxClick(layerId) - Simulate checkbox click
- geoDebug.simulateRadioClick(layerId) - Simulate radio click
- geoDebug.testLayerFunctions() - Check if functions exist
`);
