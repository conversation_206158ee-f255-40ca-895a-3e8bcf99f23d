﻿namespace TaskManager.Controllers
{
    using Microsoft.AspNetCore.Authorization;
    using Microsoft.AspNetCore.Mvc;
    using TaskManager.Services.Data.Interfaces;
    using TaskManager.Web.Infrastructure.Extentions;
    using TaskManager.Web.ViewModels.GeoTask;
    using TaskManager.Web.ViewModels.Calendar;
    using static Common.NotificationMessages;
    using static Common.ErrorMessageBulgarian;

    [Authorize]
    public class GeoTaskController : Controller
    {
        private readonly IUserService userService;
        private readonly IGeoTaskService geoTaskService;
        private readonly IComentarService comentarService;
        private readonly ITypeService typeService;
        private readonly IStatusService statusService;
        private readonly IClientService clientService;
        private readonly ICalendarService calendarService;


        public GeoTaskController(IUserService userService, IGeoTaskService geoTaskService, IComentarService comentarService, ITypeService typeService, IStatusService statusService, IClientService clientService, ICalendarService calendarService)
        {
            this.userService = userService;
            this.geoTaskService = geoTaskService;
            this.comentarService = comentarService;
            this.typeService = typeService;
            this.statusService = statusService;
            this.clientService = clientService;
            this.calendarService = calendarService;
        }

        [HttpGet]
        public async Task<IActionResult> AllTasks([FromQuery]AllTaskQueryModel queryModel)
        {
            try
            {
                string Id = User.GetId();
                bool isUserWorker = await this.userService.IsUserWorkerByIdAsync(Id);

                if (!isUserWorker)
                {
                    return this.ErrorIfUserIsNotWorker();
                }

                AllGeoTaskFilteredAndPageServiceModel serviceModel = await this.geoTaskService.GetAllGeoTaskFilteredAsync(queryModel);
                queryModel.TotalTaskss = serviceModel.TotalTasks;
                queryModel.Tasks = serviceModel.Tasks;
                queryModel.Types = await this.typeService.GetAllTypeNamesAsync();

                return this.View(queryModel);
            }
            catch (Exception)
            {
                return this.GeneralError();
            }

        }
        [HttpGet]
        public async Task<IActionResult> MyTasks()
        {
            string workerId = "";
            try
            {
                string UserId = User.GetId();
                bool isUserWorker = await this.userService.IsUserWorkerByIdAsync(UserId);
                workerId = await this.userService.GetWorkerIdByUserIdAsync(UserId);

                if (!isUserWorker)
                {
                    return this.ErrorIfUserIsNotWorker();
                }
            }
            catch (Exception)
            {
                return this.GeneralError();
            }
            try
            {

                IEnumerable<TaskViewModel> taskViewModels = await this.geoTaskService.GetMyTaskByWorkerIdAsync(workerId);
                return this.View(taskViewModels);
            }
            catch (Exception)
            {
                return this.GeneralError();
            }
        }
        [HttpGet]
        public async Task<IActionResult> Checked()
        {
            string UserId = User.GetId();
            string workerId = "";

            try
            {
                bool isUserWorker = await this.userService.IsUserWorkerByIdAsync(UserId);
                workerId = await this.userService.GetWorkerIdByUserIdAsync(UserId);
                if (!isUserWorker)
                {
                    return this.ErrorIfUserIsNotWorker();
                }
            }
            catch (Exception)
            {
                return this.GeneralError();
            }
            try
            {

                IEnumerable<TaskViewModel> taskViewModels = await this.geoTaskService.GeoTaskForCheckByWorkerIdAsync(workerId);
                return this.View(taskViewModels);
            }
            catch (Exception)
            {
                return this.GeneralError();
            }
        }
        [HttpGet]
        public async Task<IActionResult> Add()
        {
			try
			{
				string Id = User.GetId();
				bool isUserWorker = await this.userService.IsUserWorkerByIdAsync(Id);

				if (!isUserWorker)
				{
					return this.ErrorIfUserIsNotWorker();
				}
			}
			catch (Exception)
			{
				return this.GeneralError();
			}
            try
            {
                // Create a new draft task view model without saving to database
                var createViewModel = await this.geoTaskService.CreateNewTaskDraft();
                return this.View("Create", createViewModel);
            }
            catch (Exception)
            {
                return this.GeneralError();
            }
		}

        [HttpPost]
        public async Task<IActionResult> Create(EditGeoTaskViewModel createGeoTaskViewModel)
        {
            try
            {
                string UserId = User.GetId();
                bool isUserWorker = await this.userService.IsUserWorkerByIdAsync(UserId);

                if (!isUserWorker)
                {
                    return this.ErrorIfUserIsNotWorker();
                }

                if (!ModelState.IsValid)
                {
                    return this.View(createGeoTaskViewModel);
                }
            }
            catch (Exception)
            {
                return this.GeneralError();
            }

            // Detect which button was pressed
            var buttonPressed = TaskManager.Web.Infrastructure.Helpers.FormButtonHelper.DetectButtonPressed(Request);
            bool isButtonDiscard = buttonPressed == TaskManager.Web.Infrastructure.Helpers.ButtonType.Discard;
            bool isButtonSave = buttonPressed == TaskManager.Web.Infrastructure.Helpers.ButtonType.Save;

            if (isButtonDiscard)
            {
                // User chose to discard - redirect back to task list without saving
                return this.RedirectToAction("AllTasks", "GeoTask");
            }
            else if (isButtonSave)
            {
                try
                {
                    // Save the new task to database
                    string newTaskId = await this.geoTaskService.CreateGeoTaskAsync(createGeoTaskViewModel);
                    this.TempData[SuccsessMessage] = "Задачата беше създадена успешно.";
                    return this.RedirectToAction("Edit", "GeoTask", new { Id = newTaskId });
                }
                catch (Exception)
                {
                    return this.GeneralError();
                }
            }

            // Default fallback
            return this.View(createGeoTaskViewModel);
        }

        [HttpGet]
        public async Task<IActionResult> Edit(string Id)
        {
            try
            {
                string UserId = User.GetId();
                bool isUserWorker = await this.userService.IsUserWorkerByIdAsync(UserId);
                bool isTaskExist = await this.geoTaskService.IsTaskExistByIdAsync(Id);
                if (!isUserWorker)
                {
                    return this.ErrorIfUserIsNotWorker();
                }
                if(!isTaskExist)
                {
                    return this.ErrorIfTaskDontExist();
                }
            }
            catch (Exception)
            {
                return this.GeneralError();
            }
            try
            {
                EditGeoTaskViewModel editGeoTaskViewModel = await this.geoTaskService.GetGeoTaskByIdAsync(Id);
                 
                editGeoTaskViewModel.Comentars = await this.comentarService.GetComentarByTaskIdAsync(Id);
                editGeoTaskViewModel.Types = await this.typeService.GetAllTypesAsync();
                editGeoTaskViewModel.Statuses = await this.statusService.GetAllStatusAsync();
                editGeoTaskViewModel.Checkers = await this.userService.GetAllWorkersAsync();
                editGeoTaskViewModel.Workers = await this.userService.GetAllWorkersAsync();
                editGeoTaskViewModel.Clients = await this.clientService.GetAllClientsAsync();
               
				return this.View(editGeoTaskViewModel);
            }
            catch (Exception)
            {
                return this.GeneralError();
            }
        }

        [HttpPost]
		public async Task<IActionResult> Edit(EditGeoTaskViewModel editGeoTaskViewModel)
        {
			try
			{
				string UserId = User.GetId();
				bool isUserWorker = await this.userService.IsUserWorkerByIdAsync(UserId);
				bool isTaskExist = await this.geoTaskService.IsTaskExistByIdAsync(editGeoTaskViewModel.Id);

				if (!isUserWorker)
				{
					return this.ErrorIfUserIsNotWorker();
				}
				if (!isTaskExist)
				{
					return this.ErrorIfTaskDontExist();
				}
                if (!ModelState.IsValid)
                {
                    return this.View(editGeoTaskViewModel);
                }
            }
			catch (Exception)
			{
				return this.GeneralError();
			}

            // ✅ FIXED: Use helper for reliable button detection
            var buttonPressed = TaskManager.Web.Infrastructure.Helpers.FormButtonHelper.DetectButtonPressed(Request);
            bool isButtonSaveAndClose = buttonPressed == TaskManager.Web.Infrastructure.Helpers.ButtonType.SaveAndClose;
            bool isButtonSave = buttonPressed == TaskManager.Web.Infrastructure.Helpers.ButtonType.Save;

			if (isButtonSaveAndClose)
            {
                try
                {
					await this.geoTaskService.EditGeoTaskByIdAsync(editGeoTaskViewModel);
                    return this.RedirectToAction("AllTasks", "GeoTask");
				}
				catch (Exception)
                {
                    return this.GeneralError();
                }
            }
            else if (isButtonSave)
            {
                try
                {
					await this.geoTaskService.EditGeoTaskByIdAsync(editGeoTaskViewModel);

					EditGeoTaskViewModel editGeoTaskViewModelLoad = await this.geoTaskService.GetGeoTaskByIdAsync(editGeoTaskViewModel.Id);

					editGeoTaskViewModelLoad.Comentars = await this.comentarService.GetComentarByTaskIdAsync(editGeoTaskViewModel.Id);
					editGeoTaskViewModelLoad.Types = await this.typeService.GetAllTypesAsync();
					editGeoTaskViewModelLoad.Statuses = await this.statusService.GetAllStatusAsync();
					editGeoTaskViewModelLoad.Checkers = await this.userService.GetAllWorkersAsync();
					editGeoTaskViewModelLoad.Workers = await this.userService.GetAllWorkersAsync();
					editGeoTaskViewModelLoad.Clients = await this.clientService.GetAllClientsAsync();

					return this.View(editGeoTaskViewModelLoad);

				}
				catch (Exception)
                {
					return this.GeneralError();
				}
            }
            return GeneralError();
		}
        private IActionResult GeneralError()
        {
            this.TempData[ErrorMessage] = GeneralErrorMessage;

            return RedirectToAction("Index", "Home");
        }
        private IActionResult ErrorIfTaskDontExist()
        {
            this.TempData[ErrorMessage] = ErrorIfTaskDontExistMessage;

            return RedirectToAction("Index", "Home");
        }
        private IActionResult ErrorIfUserIsNotWorker()
        {
            this.TempData[ErrorMessage] = ErrorIfUserIsNotWorkerMessage;

            return RedirectToAction("Index", "Home");
        }

        // Calendar integration methods
        [HttpPost]
        [ValidateAntiForgeryToken]
        public async Task<IActionResult> AddToCalendar(string geoTaskId, string title, string description, string date, string startTime, string endTime, string color, List<string> assignedMemberIds)
        {
            try
            {
                string userId = User.GetId();
                bool isUserWorker = await this.userService.IsUserWorkerByIdAsync(userId);

                if (!isUserWorker)
                {
                    return this.ErrorIfUserIsNotWorker();
                }

                bool isTaskExist = await this.geoTaskService.IsTaskExistByIdAsync(geoTaskId);
                if (!isTaskExist)
                {
                    return this.ErrorIfTaskDontExist();
                }

                bool isAlreadyLinked = await this.calendarService.IsGeoTaskLinkedToCalendarAsync(geoTaskId);
                if (isAlreadyLinked)
                {
                    this.TempData[ErrorMessage] = "Тази задача вече е добавена в календара.";
                    return RedirectToAction("Edit", new { Id = geoTaskId });
                }

                var model = new CreateCalendarTaskViewModel
                {
                    Title = title,
                    Description = description,
                    Date = DateTime.Parse(date),
                    StartTime = startTime,
                    EndTime = endTime,
                    Color = color,
                    AssignedMemberIds = assignedMemberIds ?? new List<string>()
                };

                var calendarTask = await this.calendarService.CreateTaskFromGeoTaskAsync(geoTaskId, model);
                this.TempData[SuccsessMessage] = "Задачата беше успешно добавена в календара.";

                return RedirectToAction("Edit", new { Id = geoTaskId });
            }
            catch (Exception)
            {
                return this.GeneralError();
            }
        }

        [HttpPost]
        [ValidateAntiForgeryToken]
        public async Task<IActionResult> RemoveFromCalendar(string geoTaskId)
        {
            try
            {
                string userId = User.GetId();
                bool isUserWorker = await this.userService.IsUserWorkerByIdAsync(userId);

                if (!isUserWorker)
                {
                    return this.ErrorIfUserIsNotWorker();
                }

                bool isTaskExist = await this.geoTaskService.IsTaskExistByIdAsync(geoTaskId);
                if (!isTaskExist)
                {
                    return this.ErrorIfTaskDontExist();
                }

                bool success = await this.calendarService.UnlinkGeoTaskFromCalendarAsync(geoTaskId);
                if (success)
                {
                    this.TempData[SuccsessMessage] = "Задачата беше премахната от календара.";
                }
                else
                {
                    this.TempData[ErrorMessage] = "Задачата не е намерена в календара.";
                }

                return RedirectToAction("Edit", new { Id = geoTaskId });
            }
            catch (Exception)
            {
                return this.GeneralError();
            }
        }

        [HttpGet]
        public async Task<IActionResult> CheckCalendarStatus(string geoTaskId)
        {
            try
            {
                bool isLinked = await this.calendarService.IsGeoTaskLinkedToCalendarAsync(geoTaskId);
                string? calendarTaskId = null;

                if (isLinked)
                {
                    calendarTaskId = await this.calendarService.GetCalendarTaskIdByGeoTaskIdAsync(geoTaskId);
                }

                return Json(new { isLinked, calendarTaskId });
            }
            catch (Exception)
            {
                return Json(new { isLinked = false, calendarTaskId = (string?)null });
            }
        }

    }
}
