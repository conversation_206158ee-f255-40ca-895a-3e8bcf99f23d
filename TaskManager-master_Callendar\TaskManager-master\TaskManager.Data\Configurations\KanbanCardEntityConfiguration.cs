namespace TaskManager.Data.Configurations
{
    using Microsoft.EntityFrameworkCore;
    using Microsoft.EntityFrameworkCore.Metadata.Builders;
    using TaskManager.Data.Models;

    public class KanbanCardEntityConfiguration : IEntityTypeConfiguration<KanbanCard>
    {
        public void Configure(EntityTypeBuilder<KanbanCard> builder)
        {
            builder.HasKey(kc => kc.Id);

            builder.Property(kc => kc.Title)
                .IsRequired()
                .HasMaxLength(200);

            builder.Property(kc => kc.Description)
                .HasMaxLength(1000);

            builder.Property(kc => kc.Position)
                .IsRequired();

            builder.Property(kc => kc.Color)
                .HasMaxLength(7);

            builder.Property(kc => kc.Labels)
                .HasMaxLength(500);

            builder.Property(kc => kc.CreatedOn)
                .IsRequired();

            builder.Property(kc => kc.IsActive)
                .IsRequired()
                .HasDefaultValue(true);

            builder.Property(kc => kc.ColumnId)
                .IsRequired();

            // Configure relationships
            builder.HasOne(kc => kc.Column)
                .WithMany(col => col.Cards)
                .HasForeignKey(kc => kc.ColumnId)
                .OnDelete(DeleteBehavior.Cascade);

            builder.HasOne(kc => kc.AssignedTo)
                .WithMany()
                .HasForeignKey(kc => kc.AssignedToId)
                .OnDelete(DeleteBehavior.NoAction);

            builder.HasOne(kc => kc.CreatedBy)
                .WithMany()
                .HasForeignKey(kc => kc.CreatedById)
                .OnDelete(DeleteBehavior.NoAction);

            // One-to-one relationship with GeoTask
            builder.HasOne(kc => kc.GeoTask)
                .WithOne(gt => gt.KanbanCard)
                .HasForeignKey<KanbanCard>(kc => kc.GeoTaskId)
                .OnDelete(DeleteBehavior.SetNull);
        }
    }
}
