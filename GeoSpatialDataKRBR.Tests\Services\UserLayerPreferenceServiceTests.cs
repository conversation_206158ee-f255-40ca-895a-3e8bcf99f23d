using Microsoft.EntityFrameworkCore;
using Xunit;
using FluentAssertions;
using GeoSpatialDataKRBR.Data;
using GeoSpatialDataKRBR.Data.Models;
using GeoSpatialDataKRBR.Services.Data;
using GeoSpatialDataKRBR.Services.Data.Interfaces;

namespace GeoSpatialDataKRBR.Tests.Services
{
    public class UserLayerPreferenceServiceTests : IDisposable
    {
        private readonly GeoSpatialDbContext _context;
        private readonly IUserLayerPreferenceService _userLayerPreferenceService;

        public UserLayerPreferenceServiceTests()
        {
            var options = new DbContextOptionsBuilder<GeoSpatialDbContext>()
                .UseInMemoryDatabase(databaseName: Guid.NewGuid().ToString())
                .Options;

            _context = new GeoSpatialDbContext(options);
            _userLayerPreferenceService = new UserLayerPreferenceService(_context);
        }

        [Fact]
        public async Task ToggleLayerVisibilityAsync_WithNewPreference_ShouldCreatePreference()
        {
            // Arrange
            var userId = Guid.NewGuid();
            var layerId = Guid.NewGuid();
            var isVisible = true;

            // Act
            var result = await _userLayerPreferenceService.ToggleLayerVisibilityAsync(userId, layerId, isVisible);

            // Assert
            result.Should().BeTrue();

            var preference = await _context.UserLayerPreferences
                .FirstOrDefaultAsync(p => p.UserId == userId && p.GeoLayerId == layerId);

            preference.Should().NotBeNull();
            preference!.IsVisible.Should().Be(isVisible);
        }

        [Fact]
        public async Task ToggleLayerVisibilityAsync_WithExistingPreference_ShouldUpdatePreference()
        {
            // Arrange
            var userId = Guid.NewGuid();
            var layerId = Guid.NewGuid();
            var existingPreference = new UserLayerPreference
            {
                UserId = userId,
                GeoLayerId = layerId,
                IsVisible = false,
                DisplayOrder = 1
            };

            await _context.UserLayerPreferences.AddAsync(existingPreference);
            await _context.SaveChangesAsync();

            // Act
            var result = await _userLayerPreferenceService.ToggleLayerVisibilityAsync(userId, layerId, true);

            // Assert
            result.Should().BeTrue();

            var updatedPreference = await _context.UserLayerPreferences
                .FirstOrDefaultAsync(p => p.UserId == userId && p.GeoLayerId == layerId);

            updatedPreference.Should().NotBeNull();
            updatedPreference!.IsVisible.Should().BeTrue();
        }

        [Fact]
        public async Task UpdateLayerOpacityAsync_WithExistingPreference_ShouldUpdateOpacity()
        {
            // Arrange
            var userId = Guid.NewGuid();
            var layerId = Guid.NewGuid();
            var newOpacity = 0.5;
            var existingPreference = new UserLayerPreference
            {
                UserId = userId,
                GeoLayerId = layerId,
                IsVisible = true,
                Opacity = 1.0,
                DisplayOrder = 1
            };

            await _context.UserLayerPreferences.AddAsync(existingPreference);
            await _context.SaveChangesAsync();

            // Act
            var result = await _userLayerPreferenceService.UpdateLayerOpacityAsync(userId, layerId, newOpacity);

            // Assert
            result.Should().BeTrue();

            var updatedPreference = await _context.UserLayerPreferences
                .FirstOrDefaultAsync(p => p.UserId == userId && p.GeoLayerId == layerId);

            updatedPreference.Should().NotBeNull();
            updatedPreference!.Opacity.Should().Be(newOpacity);
        }

        [Fact]
        public async Task UpdateLayerOpacityAsync_WithNewPreference_ShouldCreatePreference()
        {
            // Arrange
            var userId = Guid.NewGuid();
            var layerId = Guid.NewGuid();
            var opacity = 0.7;

            // Act
            var result = await _userLayerPreferenceService.UpdateLayerOpacityAsync(userId, layerId, opacity);

            // Assert
            result.Should().BeTrue();

            var preference = await _context.UserLayerPreferences
                .FirstOrDefaultAsync(p => p.UserId == userId && p.GeoLayerId == layerId);

            preference.Should().NotBeNull();
            preference!.Opacity.Should().Be(opacity);
            preference.IsVisible.Should().BeTrue(); // Default value
        }

        [Fact]
        public async Task GetUserLayerPreferencesAsync_ShouldReturnUserPreferences()
        {
            // Arrange
            var userId = Guid.NewGuid();
            var layerId1 = Guid.NewGuid();
            var layerId2 = Guid.NewGuid();

            var preferences = new List<UserLayerPreference>
            {
                new UserLayerPreference
                {
                    UserId = userId,
                    GeoLayerId = layerId1,
                    IsVisible = true,
                    Opacity = 0.8,
                    DisplayOrder = 1
                },
                new UserLayerPreference
                {
                    UserId = userId,
                    GeoLayerId = layerId2,
                    IsVisible = false,
                    Opacity = 0.5,
                    DisplayOrder = 2
                }
            };

            await _context.UserLayerPreferences.AddRangeAsync(preferences);
            await _context.SaveChangesAsync();

            // Act
            var result = await _userLayerPreferenceService.GetUserLayerPreferencesAsync(userId);

            // Assert
            result.Should().NotBeNull();
            result.Should().HaveCount(2);
            result.Should().Contain(p => p.GeoLayerId == layerId1 && p.IsVisible == true);
            result.Should().Contain(p => p.GeoLayerId == layerId2 && p.IsVisible == false);
        }

        [Fact]
        public async Task GetUserLayerPreferenceAsync_WithExistingPreference_ShouldReturnPreference()
        {
            // Arrange
            var userId = Guid.NewGuid();
            var layerId = Guid.NewGuid();
            var preference = new UserLayerPreference
            {
                UserId = userId,
                GeoLayerId = layerId,
                IsVisible = true,
                Opacity = 0.9,
                DisplayOrder = 1
            };

            await _context.UserLayerPreferences.AddAsync(preference);
            await _context.SaveChangesAsync();

            // Act
            var result = await _userLayerPreferenceService.GetUserLayerPreferenceAsync(userId, layerId);

            // Assert
            result.Should().NotBeNull();
            result!.UserId.Should().Be(userId);
            result.GeoLayerId.Should().Be(layerId);
            result.IsVisible.Should().BeTrue();
            result.Opacity.Should().Be(0.9);
        }

        [Fact]
        public async Task GetUserLayerPreferenceAsync_WithNonExistingPreference_ShouldReturnNull()
        {
            // Arrange
            var userId = Guid.NewGuid();
            var layerId = Guid.NewGuid();

            // Act
            var result = await _userLayerPreferenceService.GetUserLayerPreferenceAsync(userId, layerId);

            // Assert
            result.Should().BeNull();
        }

        public void Dispose()
        {
            _context.Dispose();
        }
    }
}
