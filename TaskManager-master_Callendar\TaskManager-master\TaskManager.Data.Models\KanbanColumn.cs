namespace TaskManager.Data.Models
{
    using System.ComponentModel.DataAnnotations;
    using System.ComponentModel.DataAnnotations.Schema;
    using static TaskManager.Common.EntityValidationConstants.KanbanColumn;

    /// <summary>
    /// Represents a column in a Kanban board (e.g., "В процес на работа", "За проверка")
    /// </summary>
    public class KanbanColumn
    {
        public KanbanColumn()
        {
            this.Id = Guid.NewGuid();
            this.Cards = new HashSet<KanbanCard>();
            this.CreatedOn = DateTime.UtcNow;
            this.IsActive = true;
        }

        [Key]
        public Guid Id { get; set; }

        [Required]
        [MaxLength(NameMaxLength)]
        public string Name { get; set; } = null!;

        [MaxLength(DescriptionMaxLength)]
        public string? Description { get; set; }

        [Required]
        public int Position { get; set; }

        [MaxLength(ColorMaxLength)]
        public string? Color { get; set; }

        [Required]
        public DateTime CreatedOn { get; set; }

        public DateTime? UpdatedOn { get; set; }

        [Required]
        public bool IsActive { get; set; }

        // Foreign Keys
        [Required]
        public Guid BoardId { get; set; }

        // Navigation properties
        [ForeignKey(nameof(BoardId))]
        public virtual KanbanBoard Board { get; set; } = null!;

        public virtual ICollection<KanbanCard> Cards { get; set; }
    }
}
