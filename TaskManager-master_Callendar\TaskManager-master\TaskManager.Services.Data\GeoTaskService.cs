﻿namespace TaskManager.Services.Data
{
    using Microsoft.EntityFrameworkCore;
    using System.Threading.Tasks;
    using TaskManager.Data;
    using TaskManager.Data.Models;
    using TaskManager.Services.Data.Interfaces;
    using TaskManager.Web.ViewModels.GeoTask;
    using TaskManager.Web.ViewModels.Client;
    using TaskManager.Web.ViewModels.Admin;
    using TaskManager.Web.ViewModels.Status;
    using TaskManager.Web.ViewModels.Type;
    using TaskManager.Web.ViewModels.Comentar;
    using static Common.EntityValidationConstants.GeoTask;
    public class GeoTaskService : IGeoTaskService
    {
        private readonly TaskManagerDbContext dbContext;
        private readonly IKanbanService kanbanService;

        public GeoTaskService(TaskManagerDbContext dbContext, IKanbanService kanbanService)
        {
            this.dbContext = dbContext;
            this.kanbanService = kanbanService;
        }

		public async Task<IEnumerable<TaskViewModel>> GetAllGeoTaskAsync()
        {
            IEnumerable<TaskViewModel> tasks = await this.dbContext
                .GeoTasks
                .Include(t => t.Client)
                .Include(t => t.Status)
                .Include(t => t.Type)
                .Select(t => new TaskViewModel
                {
                    Id = t.Id.ToString(),
                    Number = t.ProjectNumber,
                    EndDate = t.EndDate,
                    ClientName = t.Client.Name,
                    price = t.Price,
                    quantity = t.quantity,
                    status = t.Status.Name,
                    TaskType = t.Type.Name                   
              })
                .OrderBy(t => t.Number)
                .ToArrayAsync();

            return tasks;
        }

        public async Task<EditGeoTaskViewModel> GetGeoTaskByIdAsync(string Id)
        {
            GeoTask geoTask = await this.dbContext
                .GeoTasks
                .FirstAsync(gt => gt.Id.ToString() == Id);

            EditGeoTaskViewModel editGeoTaskViewModel = new EditGeoTaskViewModel()
            {
                Id = geoTask.Id.ToString(),
                Adrress = geoTask.Adrress,
                ProjectNumber = geoTask.ProjectNumber,
                Price = geoTask.Price,
                CreateDate = geoTask.CreateDate,
                EndDate = geoTask.EndDate,
                IdKKKR = geoTask.IdKKKR,
                quantity = geoTask.quantity,
                Note = geoTask.Note,
                ClientId = geoTask.ClientId.ToString(),
                WorkerId = geoTask.WorkerId.ToString(),
                CheckerId = geoTask.CheckerId.ToString(),
                StatusId = geoTask.StatusId,
                TypeId = geoTask.TypeId,
            };

            return editGeoTaskViewModel;
        }

        public async Task<bool> IsTaskExistByIdAsync(string Id)
        {
            return await this.dbContext
                .GeoTasks
                .AnyAsync(gt => gt.Id.ToString() == Id);
        }
		public async Task EditGeoTaskByIdAsync(EditGeoTaskViewModel editGeoTaskViewModel)
		{
			string taskId = editGeoTaskViewModel.Id;

            GeoTask geoTask = await this.dbContext
                .GeoTasks
                .FirstAsync(gt => gt.Id.ToString() == taskId);

            geoTask.Adrress = editGeoTaskViewModel.Adrress; // ✅ FIXED: Added missing Address update
            geoTask.IdKKKR = editGeoTaskViewModel.IdKKKR;
            geoTask.StatusId = editGeoTaskViewModel.StatusId;
            geoTask.WorkerId = Guid.Parse(editGeoTaskViewModel.WorkerId);
            geoTask.CheckerId= Guid.Parse(editGeoTaskViewModel.CheckerId);
            geoTask.CreateDate= editGeoTaskViewModel.CreateDate;
            geoTask.EndDate= editGeoTaskViewModel.EndDate;
            geoTask.TypeId = editGeoTaskViewModel.TypeId;
            geoTask.Note= editGeoTaskViewModel.Note;
            geoTask.quantity= editGeoTaskViewModel.quantity;
            geoTask.ClientId =Guid.Parse(editGeoTaskViewModel.ClientId);
            geoTask.Price= editGeoTaskViewModel.Price;

            await this.dbContext.SaveChangesAsync();

            // Update associated Kanban card
            try
            {
                var cardTitle = $"Проект #{geoTask.ProjectNumber}";
                // Get the worker's UserId for card assignment
                var worker = await this.dbContext.Workers
                    .FirstOrDefaultAsync(w => w.Id == geoTask.WorkerId);
                var userIdForCard = worker?.UserId.ToString();

                await kanbanService.UpdateCardForGeoTaskAsync(geoTask.Id.ToString(), cardTitle, userIdForCard);
            }
            catch (Exception)
            {
                // Log error but don't fail the GeoTask update
            }
		}

        public async Task<EditGeoTaskViewModel> AddNewTask()
        {
            int projectNumberr = await this.dbContext.GeoTasks.CountAsync();
            GeoTask geoTask = new GeoTask()
            {
                Id = Guid.NewGuid(),
                IdKKKR = IkkrDefautValue,
                StatusId = 1,
                WorkerId = Guid.Parse("A13C085F-4D96-4244-8EA4-607C4B6989F6"),
                CheckerId= Guid.Parse("A13C085F-4D96-4244-8EA4-607C4B6989F6"),
                CreateDate = DateTime.UtcNow,
                EndDate = DateTime.UtcNow,
                TypeId = 1,
                Note = NoteDefautValue,
                quantity = 1,
                ClientId = Guid.Parse("3E05C605-D947-4C9E-AC82-E4746822BA7D"),
                Price = 100,
                ProjectNumber = projectNumberr + 1,
                Adrress=AdresDefautValue
        };
            await this.dbContext.GeoTasks.AddAsync(geoTask);
            await this.dbContext.SaveChangesAsync();

            // Automatically create Kanban card for the new GeoTask
            try
            {
                var cardTitle = $"Проект #{geoTask.ProjectNumber}";
                // Get the worker's UserId for card assignment
                var worker = await this.dbContext.Workers
                    .FirstOrDefaultAsync(w => w.Id == geoTask.WorkerId);
                var userIdForCard = worker?.UserId.ToString();

                await kanbanService.CreateCardForGeoTaskAsync(geoTask.Id.ToString(), cardTitle, userIdForCard);
            }
            catch (Exception)
            {
                // Log error but don't fail the GeoTask creation
                // The card can be created later manually if needed
            }

            EditGeoTaskViewModel editGeoTaskViewModel = new EditGeoTaskViewModel()
            {
				Id = geoTask.Id.ToString(),
				Adrress = geoTask.Adrress,
				ProjectNumber = geoTask.ProjectNumber,
				Price = geoTask.Price,
				CreateDate = geoTask.CreateDate,
				EndDate = geoTask.EndDate,
				IdKKKR = geoTask.IdKKKR,
				quantity = geoTask.quantity,
				Note = geoTask.Note,
				ClientId = geoTask.ClientId.ToString(),
				WorkerId = geoTask.WorkerId.ToString(),
				CheckerId = geoTask.CheckerId.ToString(),
				StatusId = geoTask.StatusId,
				TypeId = geoTask.TypeId,
			};

            return editGeoTaskViewModel;
		}

        public async Task<EditGeoTaskViewModel> CreateNewTaskDraft()
        {
            // Create a draft view model without saving to database
            int projectNumber = await this.dbContext.GeoTasks.CountAsync() + 1;

            EditGeoTaskViewModel draftViewModel = new EditGeoTaskViewModel()
            {
                Id = Guid.NewGuid().ToString(), // Temporary ID for the draft
                IdKKKR = IkkrDefautValue,
                WorkerId = "A13C085F-4D96-4244-8EA4-607C4B6989F6", // Default worker
                CheckerId = "A13C085F-4D96-4244-8EA4-607C4B6989F6", // Default checker
                CreateDate = DateTime.UtcNow,
                EndDate = DateTime.UtcNow,
                TypeId = 1,
                Note = NoteDefautValue,
                quantity = 1,
                ClientId = "3E05C605-D947-4C9E-AC82-E4746822BA7D", // Default client
                Price = 100,
                ProjectNumber = projectNumber,
                Adrress = AdresDefautValue,
                StatusId = 1,

                // Load dropdown data
                Clients = await this.dbContext.Clients
                    .Select(c => new ClientViewModel
                    {
                        Id = c.Id.ToString(),
                        Name = c.Name
                    })
                    .ToArrayAsync(),

                Workers = await this.dbContext.Workers
                    .Include(w => w.User)
                    .Select(w => new AllWorkersViewModel
                    {
                        Id = w.Id.ToString(),
                        FirstName = w.User.FirstName,
                        LastName = w.User.LastName
                    })
                    .ToArrayAsync(),

                Checkers = await this.dbContext.Workers
                    .Include(w => w.User)
                    .Select(w => new AllWorkersViewModel
                    {
                        Id = w.Id.ToString(),
                        FirstName = w.User.FirstName,
                        LastName = w.User.LastName
                    })
                    .ToArrayAsync(),

                Statuses = await this.dbContext.Statuses
                    .Select(s => new StatusViewModel
                    {
                        Id = s.Id,
                        Name = s.Name
                    })
                    .ToArrayAsync(),

                Types = await this.dbContext.Types
                    .Select(t => new TypeViewModel
                    {
                        Id = t.Id,
                        Name = t.Name
                    })
                    .ToArrayAsync(),

                Comentars = new List<ComentarViewModel>() // Empty for new task
            };

            return draftViewModel;
        }

        public async Task<string> CreateGeoTaskAsync(EditGeoTaskViewModel editGeoTaskViewModel)
        {
            // Create the actual GeoTask entity and save to database
            GeoTask geoTask = new GeoTask()
            {
                Id = Guid.NewGuid(), // Generate new ID for database
                IdKKKR = editGeoTaskViewModel.IdKKKR,
                StatusId = editGeoTaskViewModel.StatusId,
                WorkerId = Guid.Parse(editGeoTaskViewModel.WorkerId),
                CheckerId = Guid.Parse(editGeoTaskViewModel.CheckerId),
                CreateDate = editGeoTaskViewModel.CreateDate,
                EndDate = editGeoTaskViewModel.EndDate,
                TypeId = editGeoTaskViewModel.TypeId,
                Note = editGeoTaskViewModel.Note,
                quantity = editGeoTaskViewModel.quantity,
                ClientId = Guid.Parse(editGeoTaskViewModel.ClientId),
                Price = editGeoTaskViewModel.Price,
                ProjectNumber = editGeoTaskViewModel.ProjectNumber,
                Adrress = editGeoTaskViewModel.Adrress
            };

            await this.dbContext.GeoTasks.AddAsync(geoTask);
            await this.dbContext.SaveChangesAsync();

            // Automatically create Kanban card for the new GeoTask
            try
            {
                var cardTitle = $"Проект #{geoTask.ProjectNumber}";
                // Get the worker's UserId for card assignment
                var worker = await this.dbContext.Workers
                    .FirstOrDefaultAsync(w => w.Id == geoTask.WorkerId);
                var userIdForCard = worker?.UserId.ToString();

                await kanbanService.CreateCardForGeoTaskAsync(geoTask.Id.ToString(), cardTitle, userIdForCard);
            }
            catch (Exception)
            {
                // Log error but don't fail the GeoTask creation
                // The card can be created later manually if needed
            }

            return geoTask.Id.ToString();
        }

        public async Task<IEnumerable<TaskViewModel>> GetMyTaskByWorkerIdAsync(string workerId)
        {
            IEnumerable<TaskViewModel> taskViewModels = await this.dbContext
                .GeoTasks
                .Include(t => t.Client)
                .Include(t => t.Status)
                .Include(t => t.Type)
                .Where(gt => gt.WorkerId.ToString() == workerId && gt.Status.Name != "Приключена")
                .Select(t => new TaskViewModel
                {
                    Id = t.Id.ToString(),
                    Number = t.ProjectNumber,
                    EndDate = t.EndDate,
                    ClientName = t.Client.Name,
                    price = t.Price,
                    quantity = t.quantity,
                    status = t.Status.Name,
                    TaskType = t.Type.Name
                })
                .OrderByDescending(t => t.Number)
                .ToArrayAsync();

            return taskViewModels;
        }

        public async Task<AllGeoTaskFilteredAndPageServiceModel> GetAllGeoTaskFilteredAsync(AllTaskQueryModel allTaskQueryModel)
        {
            IQueryable<GeoTask> geoTasksQuery = this.dbContext
                .GeoTasks
                .Include(t => t.Client)
                .Include(t => t.Status)
                .Include(t => t.Type)
                .OrderByDescending(t => t.ProjectNumber)
                .AsQueryable();

            if (!string.IsNullOrWhiteSpace(allTaskQueryModel.Type))
            {
                geoTasksQuery = geoTasksQuery
                    .Where(h => h.Type.Name == allTaskQueryModel.Type);
            }
            if(!string.IsNullOrWhiteSpace(allTaskQueryModel.SearchString))
            {
                geoTasksQuery = geoTasksQuery
                    .Where(t => t.Client.Name.ToLower().Contains(allTaskQueryModel.SearchString.ToLower())
                    || t.Adrress.ToLower().Contains(allTaskQueryModel.SearchString.ToLower())
                    || t.Note.ToLower().Contains(allTaskQueryModel.SearchString.ToLower())
                    || t.ProjectNumber.ToString().ToLower().Contains(allTaskQueryModel.SearchString.ToLower())
                    );
            }

            IEnumerable<TaskViewModel> allGeoTasksModel = await geoTasksQuery
                .Skip((allTaskQueryModel.CurrentPage - 1) * allTaskQueryModel.TaskPerPage)
                .Take(allTaskQueryModel.TaskPerPage)
                .Select(t => new TaskViewModel
                {
                    Id = t.Id.ToString(),
                    Number = t.ProjectNumber,
                    EndDate = t.EndDate,
                    ClientName = t.Client.Name,
                    price = t.Price,
                    quantity = t.quantity,
                    status = t.Status.Name,
                    TaskType = t.Type.Name
                })
                .ToArrayAsync();

            int totalTasks = geoTasksQuery.Count();

            return new AllGeoTaskFilteredAndPageServiceModel()
            {
                Tasks = allGeoTasksModel,
                TotalTasks = totalTasks,
            };
        }

        public async Task<IEnumerable<TaskViewModel>> GeoTaskForCheckByWorkerIdAsync(string workerId)
        {
            IEnumerable<TaskViewModel> taskViewModels = await this.dbContext
                .GeoTasks
                .Include(t => t.Client)
                .Include(t => t.Status)
                .Include(t => t.Type)
                .Where(gt => gt.CheckerId.ToString() == workerId && gt.Status.Name != "Приключена")
                .Select(t => new TaskViewModel
                {
                    Id = t.Id.ToString(),
                    Number = t.ProjectNumber,
                    EndDate = t.EndDate,
                    ClientName = t.Client.Name,
                    price = t.Price,
                    quantity = t.quantity,
                    status = t.Status.Name,
                    TaskType = t.Type.Name
                })
                .OrderByDescending(t => t.Number)
                .ToArrayAsync();

            return taskViewModels;
        }
    }
}

