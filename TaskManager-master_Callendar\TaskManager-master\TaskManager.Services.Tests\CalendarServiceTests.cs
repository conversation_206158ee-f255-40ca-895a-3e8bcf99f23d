namespace TaskManager.Services.Tests
{
    using Microsoft.EntityFrameworkCore;
    using TaskManager.Data;
    using TaskManager.Data.Models;
    using TaskManager.Services.Data;
    using TaskManager.Services.Data.Interfaces;
    using TaskManager.Web.ViewModels.Calendar;

    [TestFixture]
    public class CalendarServiceTests
    {
        private DbContextOptions<TaskManagerDbContext> dbContextOptions;
        private TaskManagerDbContext dbContext;
        private ICalendarService calendarService;

        [SetUp]
        public void SetUp()
        {
            this.dbContextOptions = new DbContextOptionsBuilder<TaskManagerDbContext>()
                .UseInMemoryDatabase("CalendarTestDb" + Guid.NewGuid().ToString())
                .Options;

            this.dbContext = new TaskManagerDbContext(this.dbContextOptions);

            // Don't use EnsureCreated() as it applies seed data from configurations
            // Instead, manually create the schema without seed data
            this.dbContext.Database.EnsureDeleted();
            this.dbContext.Database.EnsureCreated();

            // Clear any seed data that might have been added
            this.dbContext.Workers.RemoveRange(this.dbContext.Workers);
            this.dbContext.Users.RemoveRange(this.dbContext.Users);
            this.dbContext.SaveChanges();

            SeedTestData();

            this.calendarService = new CalendarService(this.dbContext);
        }

        [TearDown]
        public void TearDown()
        {
            this.dbContext.Database.EnsureDeleted();
            this.dbContext.Dispose();
        }



        private void SeedTestData()
        {
            // Create test users
            var user1 = new ApplicationUser
            {
                Id = Guid.NewGuid(),
                FirstName = "Георги",
                LastName = "Петров",
                Email = "<EMAIL>",
                UserName = "<EMAIL>"
            };

            var user2 = new ApplicationUser
            {
                Id = Guid.NewGuid(),
                FirstName = "Мария",
                LastName = "Иванова",
                Email = "<EMAIL>",
                UserName = "<EMAIL>"
            };

            // Create test workers
            var worker1 = new Worker
            {
                Id = Guid.NewGuid(),
                UserId = user1.Id,
                User = user1,
                Position = "Геодезист",
                PhoneNumber = "0888123456",
                Color = "#3B82F6" // Default blue
            };

            var worker2 = new Worker
            {
                Id = Guid.NewGuid(),
                UserId = user2.Id,
                User = user2,
                Position = "Инженер",
                PhoneNumber = "0888654321",
                Color = "#EF4444" // Red
            };

            // Add to context
            this.dbContext.Users.AddRange(user1, user2);
            this.dbContext.Workers.AddRange(worker1, worker2);
            this.dbContext.SaveChanges();
        }

        [Test]
        public async Task GetAllWorkersAsync_ShouldReturnAllWorkers()
        {
            // Act
            var result = await this.calendarService.GetAllWorkersAsync();
            var workers = result.ToList();

            // Assert
            Assert.That(workers.Count, Is.EqualTo(2));
            Assert.That(workers.All(w => !string.IsNullOrEmpty(w.Id)), Is.True);
            Assert.That(workers.All(w => !string.IsNullOrEmpty(w.Name)), Is.True);
            Assert.That(workers.All(w => !string.IsNullOrEmpty(w.Color)), Is.True);
        }

        [Test]
        public async Task GetAllWorkersAsync_ShouldReturnWorkersWithCorrectColors()
        {
            // Act
            var result = await this.calendarService.GetAllWorkersAsync();
            var workers = result.ToList();

            // Assert
            var georgiWorker = workers.FirstOrDefault(w => w.Name.Contains("Георги"));
            var mariaWorker = workers.FirstOrDefault(w => w.Name.Contains("Мария"));

            Assert.That(georgiWorker, Is.Not.Null);
            Assert.That(mariaWorker, Is.Not.Null);
            Assert.That(georgiWorker.Color, Is.EqualTo("#3B82F6"));
            Assert.That(mariaWorker.Color, Is.EqualTo("#EF4444"));
        }

        [Test]
        public async Task UpdateWorkerColorAsync_ShouldUpdateWorkerColor()
        {
            // Arrange
            var worker = await this.dbContext.Workers.FirstAsync();
            var workerId = worker.Id.ToString();
            var newColor = "#10B981"; // Green

            // Act
            await this.calendarService.UpdateWorkerColorAsync(workerId, newColor);

            // Assert
            var updatedWorker = await this.dbContext.Workers.FindAsync(worker.Id);
            Assert.That(updatedWorker.Color, Is.EqualTo(newColor));
        }

        [Test]
        public async Task UpdateWorkerColorAsync_WithInvalidWorkerId_ShouldThrowException()
        {
            // Arrange
            var invalidWorkerId = Guid.NewGuid().ToString();
            var newColor = "#10B981";

            // Act & Assert
            var exception = Assert.ThrowsAsync<ArgumentException>(async () =>
                await this.calendarService.UpdateWorkerColorAsync(invalidWorkerId, newColor));

            Assert.That(exception.Message, Is.EqualTo("Worker not found"));
        }

        [Test]
        public async Task UpdateWorkerColorAsync_WithValidHexColor_ShouldSucceed()
        {
            // Arrange
            var worker = await this.dbContext.Workers.FirstAsync();
            var workerId = worker.Id.ToString();
            var validColors = new[] { "#FF0000", "#00FF00", "#0000FF", "#FFFFFF", "#000000" };

            foreach (var color in validColors)
            {
                // Act
                await this.calendarService.UpdateWorkerColorAsync(workerId, color);

                // Assert
                var updatedWorker = await this.dbContext.Workers.FindAsync(worker.Id);
                Assert.That(updatedWorker.Color, Is.EqualTo(color));
            }
        }

        [Test]
        public async Task UpdateWorkerColorAsync_ShouldPersistChanges()
        {
            // Arrange
            var worker = await this.dbContext.Workers.FirstAsync();
            var workerId = worker.Id.ToString();
            var originalColor = worker.Color;
            var newColor = "#F59E0B"; // Amber

            // Act
            await this.calendarService.UpdateWorkerColorAsync(workerId, newColor);

            // Create new context to verify persistence
            using var newContext = new TaskManagerDbContext(this.dbContextOptions);
            var persistedWorker = await newContext.Workers.FindAsync(worker.Id);

            // Assert
            Assert.That(persistedWorker.Color, Is.EqualTo(newColor));
            Assert.That(persistedWorker.Color, Is.Not.EqualTo(originalColor));
        }

        [Test]
        public async Task GetAllWorkersAsync_AfterColorUpdate_ShouldReturnUpdatedColor()
        {
            // Arrange
            var worker = await this.dbContext.Workers.FirstAsync();
            var workerId = worker.Id.ToString();
            var newColor = "#8B5CF6"; // Purple

            // Act
            await this.calendarService.UpdateWorkerColorAsync(workerId, newColor);
            var workers = await this.calendarService.GetAllWorkersAsync();

            // Assert
            var updatedWorker = workers.FirstOrDefault(w => w.Id == workerId);
            Assert.That(updatedWorker, Is.Not.Null);
            Assert.That(updatedWorker.Color, Is.EqualTo(newColor));
        }

        [Test]
        public async Task UpdateWorkerColorAsync_MultipleUpdates_ShouldKeepLatestColor()
        {
            // Arrange
            var worker = await this.dbContext.Workers.FirstAsync();
            var workerId = worker.Id.ToString();
            var colors = new[] { "#FF0000", "#00FF00", "#0000FF" };

            // Act
            foreach (var color in colors)
            {
                await this.calendarService.UpdateWorkerColorAsync(workerId, color);
            }

            // Assert
            var finalWorker = await this.dbContext.Workers.FindAsync(worker.Id);
            Assert.That(finalWorker.Color, Is.EqualTo(colors.Last()));
        }
    }
}
