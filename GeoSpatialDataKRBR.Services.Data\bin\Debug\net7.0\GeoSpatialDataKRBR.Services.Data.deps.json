{"runtimeTarget": {"name": ".NETCoreApp,Version=v7.0", "signature": ""}, "compilationOptions": {}, "targets": {".NETCoreApp,Version=v7.0": {"GeoSpatialDataKRBR.Services.Data/1.0.0": {"dependencies": {"GeoSpatialDataKRBR.Common": "1.0.0", "GeoSpatialDataKRBR.Data": "1.0.0", "GeoSpatialDataKRBR.Data.Models": "1.0.0", "Microsoft.EntityFrameworkCore": "7.0.0", "Microsoft.Extensions.DependencyInjection.Abstractions": "7.0.0", "Microsoft.Extensions.Http": "7.0.0", "System.Text.Json": "7.0.0"}, "runtime": {"GeoSpatialDataKRBR.Services.Data.dll": {}}}, "Humanizer.Core/2.14.1": {"runtime": {"lib/net6.0/Humanizer.dll": {"assemblyVersion": "2.14.0.0", "fileVersion": "2.14.1.48190"}}}, "Microsoft.AspNetCore.Cryptography.Internal/7.0.0": {"runtime": {"lib/net7.0/Microsoft.AspNetCore.Cryptography.Internal.dll": {"assemblyVersion": "7.0.0.0", "fileVersion": "7.0.22.51819"}}}, "Microsoft.AspNetCore.Cryptography.KeyDerivation/7.0.0": {"dependencies": {"Microsoft.AspNetCore.Cryptography.Internal": "7.0.0"}, "runtime": {"lib/net7.0/Microsoft.AspNetCore.Cryptography.KeyDerivation.dll": {"assemblyVersion": "7.0.0.0", "fileVersion": "7.0.22.51819"}}}, "Microsoft.AspNetCore.Identity.EntityFrameworkCore/7.0.0": {"dependencies": {"Microsoft.EntityFrameworkCore.Relational": "7.0.0", "Microsoft.Extensions.Identity.Stores": "7.0.0"}, "runtime": {"lib/net7.0/Microsoft.AspNetCore.Identity.EntityFrameworkCore.dll": {"assemblyVersion": "7.0.0.0", "fileVersion": "7.0.22.51819"}}}, "Microsoft.EntityFrameworkCore/7.0.0": {"dependencies": {"Microsoft.EntityFrameworkCore.Abstractions": "7.0.0", "Microsoft.EntityFrameworkCore.Analyzers": "7.0.0", "Microsoft.Extensions.Caching.Memory": "7.0.0", "Microsoft.Extensions.DependencyInjection": "7.0.0", "Microsoft.Extensions.Logging": "7.0.0"}, "runtime": {"lib/net6.0/Microsoft.EntityFrameworkCore.dll": {"assemblyVersion": "7.0.0.0", "fileVersion": "7.0.22.51807"}}}, "Microsoft.EntityFrameworkCore.Abstractions/7.0.0": {"runtime": {"lib/net6.0/Microsoft.EntityFrameworkCore.Abstractions.dll": {"assemblyVersion": "7.0.0.0", "fileVersion": "7.0.22.51807"}}}, "Microsoft.EntityFrameworkCore.Analyzers/7.0.0": {}, "Microsoft.EntityFrameworkCore.Design/7.0.0": {"dependencies": {"Humanizer.Core": "2.14.1", "Microsoft.EntityFrameworkCore.Relational": "7.0.0", "Microsoft.Extensions.DependencyModel": "7.0.0", "Mono.TextTemplating": "2.2.1"}, "runtime": {"lib/net6.0/Microsoft.EntityFrameworkCore.Design.dll": {"assemblyVersion": "7.0.0.0", "fileVersion": "7.0.22.51807"}}}, "Microsoft.EntityFrameworkCore.Relational/7.0.0": {"dependencies": {"Microsoft.EntityFrameworkCore": "7.0.0", "Microsoft.Extensions.Configuration.Abstractions": "7.0.0"}, "runtime": {"lib/net6.0/Microsoft.EntityFrameworkCore.Relational.dll": {"assemblyVersion": "7.0.0.0", "fileVersion": "7.0.22.51807"}}}, "Microsoft.EntityFrameworkCore.Tools/7.0.0": {"dependencies": {"Microsoft.EntityFrameworkCore.Design": "7.0.0"}}, "Microsoft.Extensions.Caching.Abstractions/7.0.0": {"dependencies": {"Microsoft.Extensions.Primitives": "7.0.0"}, "runtime": {"lib/net7.0/Microsoft.Extensions.Caching.Abstractions.dll": {"assemblyVersion": "7.0.0.0", "fileVersion": "7.0.22.51805"}}}, "Microsoft.Extensions.Caching.Memory/7.0.0": {"dependencies": {"Microsoft.Extensions.Caching.Abstractions": "7.0.0", "Microsoft.Extensions.DependencyInjection.Abstractions": "7.0.0", "Microsoft.Extensions.Logging.Abstractions": "7.0.0", "Microsoft.Extensions.Options": "7.0.0", "Microsoft.Extensions.Primitives": "7.0.0"}, "runtime": {"lib/net7.0/Microsoft.Extensions.Caching.Memory.dll": {"assemblyVersion": "7.0.0.0", "fileVersion": "7.0.22.51805"}}}, "Microsoft.Extensions.Configuration.Abstractions/7.0.0": {"dependencies": {"Microsoft.Extensions.Primitives": "7.0.0"}, "runtime": {"lib/net7.0/Microsoft.Extensions.Configuration.Abstractions.dll": {"assemblyVersion": "7.0.0.0", "fileVersion": "7.0.22.51805"}}}, "Microsoft.Extensions.DependencyInjection/7.0.0": {"dependencies": {"Microsoft.Extensions.DependencyInjection.Abstractions": "7.0.0"}, "runtime": {"lib/net7.0/Microsoft.Extensions.DependencyInjection.dll": {"assemblyVersion": "7.0.0.0", "fileVersion": "7.0.22.51805"}}}, "Microsoft.Extensions.DependencyInjection.Abstractions/7.0.0": {"runtime": {"lib/net7.0/Microsoft.Extensions.DependencyInjection.Abstractions.dll": {"assemblyVersion": "7.0.0.0", "fileVersion": "7.0.22.51805"}}}, "Microsoft.Extensions.DependencyModel/7.0.0": {"dependencies": {"System.Text.Encodings.Web": "7.0.0", "System.Text.Json": "7.0.0"}, "runtime": {"lib/net7.0/Microsoft.Extensions.DependencyModel.dll": {"assemblyVersion": "7.0.0.0", "fileVersion": "7.0.22.51805"}}}, "Microsoft.Extensions.Http/7.0.0": {"dependencies": {"Microsoft.Extensions.DependencyInjection.Abstractions": "7.0.0", "Microsoft.Extensions.Logging": "7.0.0", "Microsoft.Extensions.Logging.Abstractions": "7.0.0", "Microsoft.Extensions.Options": "7.0.0"}, "runtime": {"lib/net7.0/Microsoft.Extensions.Http.dll": {"assemblyVersion": "7.0.0.0", "fileVersion": "7.0.22.51805"}}}, "Microsoft.Extensions.Identity.Core/7.0.0": {"dependencies": {"Microsoft.AspNetCore.Cryptography.KeyDerivation": "7.0.0", "Microsoft.Extensions.Logging": "7.0.0", "Microsoft.Extensions.Options": "7.0.0"}, "runtime": {"lib/net7.0/Microsoft.Extensions.Identity.Core.dll": {"assemblyVersion": "7.0.0.0", "fileVersion": "7.0.22.51819"}}}, "Microsoft.Extensions.Identity.Stores/7.0.0": {"dependencies": {"Microsoft.Extensions.Caching.Abstractions": "7.0.0", "Microsoft.Extensions.Identity.Core": "7.0.0", "Microsoft.Extensions.Logging": "7.0.0"}, "runtime": {"lib/net7.0/Microsoft.Extensions.Identity.Stores.dll": {"assemblyVersion": "7.0.0.0", "fileVersion": "7.0.22.51819"}}}, "Microsoft.Extensions.Logging/7.0.0": {"dependencies": {"Microsoft.Extensions.DependencyInjection": "7.0.0", "Microsoft.Extensions.DependencyInjection.Abstractions": "7.0.0", "Microsoft.Extensions.Logging.Abstractions": "7.0.0", "Microsoft.Extensions.Options": "7.0.0"}, "runtime": {"lib/net7.0/Microsoft.Extensions.Logging.dll": {"assemblyVersion": "7.0.0.0", "fileVersion": "7.0.22.51805"}}}, "Microsoft.Extensions.Logging.Abstractions/7.0.0": {"runtime": {"lib/net7.0/Microsoft.Extensions.Logging.Abstractions.dll": {"assemblyVersion": "7.0.0.0", "fileVersion": "7.0.22.51805"}}}, "Microsoft.Extensions.Options/7.0.0": {"dependencies": {"Microsoft.Extensions.DependencyInjection.Abstractions": "7.0.0", "Microsoft.Extensions.Primitives": "7.0.0"}, "runtime": {"lib/net7.0/Microsoft.Extensions.Options.dll": {"assemblyVersion": "7.0.0.0", "fileVersion": "7.0.22.51805"}}}, "Microsoft.Extensions.Primitives/7.0.0": {"runtime": {"lib/net7.0/Microsoft.Extensions.Primitives.dll": {"assemblyVersion": "7.0.0.0", "fileVersion": "7.0.22.51805"}}}, "Mono.TextTemplating/2.2.1": {"dependencies": {"System.CodeDom": "4.4.0"}, "runtime": {"lib/netstandard2.0/Mono.TextTemplating.dll": {"assemblyVersion": "2.2.0.0", "fileVersion": "2.2.1.1"}}}, "Npgsql/7.0.0": {"dependencies": {"Microsoft.Extensions.Logging.Abstractions": "7.0.0", "System.Runtime.CompilerServices.Unsafe": "6.0.0"}, "runtime": {"lib/net7.0/Npgsql.dll": {"assemblyVersion": "7.0.0.0", "fileVersion": "7.0.0.0"}}}, "Npgsql.EntityFrameworkCore.PostgreSQL/7.0.0": {"dependencies": {"Microsoft.EntityFrameworkCore": "7.0.0", "Microsoft.EntityFrameworkCore.Abstractions": "7.0.0", "Microsoft.EntityFrameworkCore.Relational": "7.0.0", "Npgsql": "7.0.0"}, "runtime": {"lib/net7.0/Npgsql.EntityFrameworkCore.PostgreSQL.dll": {"assemblyVersion": "7.0.0.0", "fileVersion": "7.0.0.0"}}}, "System.CodeDom/4.4.0": {"runtime": {"lib/netstandard2.0/System.CodeDom.dll": {"assemblyVersion": "4.0.0.0", "fileVersion": "4.6.25519.3"}}}, "System.ComponentModel.Annotations/5.0.0": {}, "System.Runtime.CompilerServices.Unsafe/6.0.0": {}, "System.Text.Encodings.Web/7.0.0": {}, "System.Text.Json/7.0.0": {"dependencies": {"System.Text.Encodings.Web": "7.0.0"}}, "GeoSpatialDataKRBR.Common/1.0.0": {"runtime": {"GeoSpatialDataKRBR.Common.dll": {}}}, "GeoSpatialDataKRBR.Data/1.0.0": {"dependencies": {"GeoSpatialDataKRBR.Common": "1.0.0", "GeoSpatialDataKRBR.Data.Models": "1.0.0", "Microsoft.AspNetCore.Identity.EntityFrameworkCore": "7.0.0", "Microsoft.EntityFrameworkCore": "7.0.0", "Microsoft.EntityFrameworkCore.Design": "7.0.0", "Microsoft.EntityFrameworkCore.Tools": "7.0.0", "Npgsql.EntityFrameworkCore.PostgreSQL": "7.0.0"}, "runtime": {"GeoSpatialDataKRBR.Data.dll": {}}}, "GeoSpatialDataKRBR.Data.Models/1.0.0": {"dependencies": {"GeoSpatialDataKRBR.Common": "1.0.0", "Microsoft.AspNetCore.Identity.EntityFrameworkCore": "7.0.0", "Microsoft.EntityFrameworkCore": "7.0.0", "System.ComponentModel.Annotations": "5.0.0"}, "runtime": {"GeoSpatialDataKRBR.Data.Models.dll": {}}}}}, "libraries": {"GeoSpatialDataKRBR.Services.Data/1.0.0": {"type": "project", "serviceable": false, "sha512": ""}, "Humanizer.Core/2.14.1": {"type": "package", "serviceable": true, "sha512": "sha512-lQKvtaTDOXnoVJ20ibTuSIOf2i0uO0MPbDhd1jm238I+U/2ZnRENj0cktKZhtchBMtCUSRQ5v4xBCUbKNmyVMw==", "path": "humanizer.core/2.14.1", "hashPath": "humanizer.core.2.14.1.nupkg.sha512"}, "Microsoft.AspNetCore.Cryptography.Internal/7.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-hFF+HOqtiNrGtO5ZxLVAFo1ksDLQWf8IHEmGRmcF9azlUWvDLZp8+W8gDyLBcGcY5m3ugEvKy/ncElxO4d0NtQ==", "path": "microsoft.aspnetcore.cryptography.internal/7.0.0", "hashPath": "microsoft.aspnetcore.cryptography.internal.7.0.0.nupkg.sha512"}, "Microsoft.AspNetCore.Cryptography.KeyDerivation/7.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-rCQddWkUxGmObeftM0YVyFOPcXkXDEWKGCc4F1viRLEL4ojIbdKwbOYBSf5hfWDR+NO0aGq8r3a8COvNYN/bZA==", "path": "microsoft.aspnetcore.cryptography.keyderivation/7.0.0", "hashPath": "microsoft.aspnetcore.cryptography.keyderivation.7.0.0.nupkg.sha512"}, "Microsoft.AspNetCore.Identity.EntityFrameworkCore/7.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-mtomuG24wGpvdblVQUj/JHIZ1i8oNhRNHr0V0re8fTkv15hz+AQLdtwbdd6FdINNeXiKi3kGmzZ7PE1KOyzoSg==", "path": "microsoft.aspnetcore.identity.entityframeworkcore/7.0.0", "hashPath": "microsoft.aspnetcore.identity.entityframeworkcore.7.0.0.nupkg.sha512"}, "Microsoft.EntityFrameworkCore/7.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-9W+IfmAzMrp2ZpKZLhgTlWljSBM9Erldis1us61DAGi+L7Q6vilTbe1G2zDxtYO8F2H0I0Qnupdx5Cp4s2xoZw==", "path": "microsoft.entityframeworkcore/7.0.0", "hashPath": "microsoft.entityframeworkcore.7.0.0.nupkg.sha512"}, "Microsoft.EntityFrameworkCore.Abstractions/7.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-Pfu3Zjj5+d2Gt27oE9dpGiF/VobBB+s5ogrfI9sBsXQE1SG49RqVz5+IyeNnzhyejFrPIQsPDRMchhcojy4Hbw==", "path": "microsoft.entityframeworkcore.abstractions/7.0.0", "hashPath": "microsoft.entityframeworkcore.abstractions.7.0.0.nupkg.sha512"}, "Microsoft.EntityFrameworkCore.Analyzers/7.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-Qkd2H+jLe37o5ku+LjT6qf7kAHY75Yfn2bBDQgqr13DTOLYpEy1Mt93KPFjaZvIu/srEcbfGGMRL7urKm5zN8Q==", "path": "microsoft.entityframeworkcore.analyzers/7.0.0", "hashPath": "microsoft.entityframeworkcore.analyzers.7.0.0.nupkg.sha512"}, "Microsoft.EntityFrameworkCore.Design/7.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-fEEU/zZ/VblZRQxHNZxgGKVtEtOGqEAmuHkACV1i0H031bM8PQKTS7PlKPVOgg0C1v+6yeHoIAGGgbAvG9f7kw==", "path": "microsoft.entityframeworkcore.design/7.0.0", "hashPath": "microsoft.entityframeworkcore.design.7.0.0.nupkg.sha512"}, "Microsoft.EntityFrameworkCore.Relational/7.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-eQiYygtR2xZ0Uy7KtiFRHpoEx/U8xNwbNRgu1pEJgSxbJLtg6tDL1y2YcIbSuIRSNEljXIIHq/apEhGm1QL70g==", "path": "microsoft.entityframeworkcore.relational/7.0.0", "hashPath": "microsoft.entityframeworkcore.relational.7.0.0.nupkg.sha512"}, "Microsoft.EntityFrameworkCore.Tools/7.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-DtLJ0usm8NdPbRDxvNUBAYgnvqhodr/HPb461I+jrgHw5ZKF0vRTaokNth2Zy9xiw1ZTpT4c+S40f7AHWakODg==", "path": "microsoft.entityframeworkcore.tools/7.0.0", "hashPath": "microsoft.entityframeworkcore.tools.7.0.0.nupkg.sha512"}, "Microsoft.Extensions.Caching.Abstractions/7.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-IeimUd0TNbhB4ded3AbgBLQv2SnsiVugDyGV1MvspQFVlA07nDC7Zul7kcwH5jWN3JiTcp/ySE83AIJo8yfKjg==", "path": "microsoft.extensions.caching.abstractions/7.0.0", "hashPath": "microsoft.extensions.caching.abstractions.7.0.0.nupkg.sha512"}, "Microsoft.Extensions.Caching.Memory/7.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-xpidBs2KCE2gw1JrD0quHE72kvCaI3xFql5/Peb2GRtUuZX+dYPoK/NTdVMiM67Svym0M0Df9A3xyU0FbMQhHw==", "path": "microsoft.extensions.caching.memory/7.0.0", "hashPath": "microsoft.extensions.caching.memory.7.0.0.nupkg.sha512"}, "Microsoft.Extensions.Configuration.Abstractions/7.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-f34u2eaqIjNO9YLHBz8rozVZ+TcFiFs0F3r7nUJd7FRkVSxk8u4OpoK226mi49MwexHOR2ibP9MFvRUaLilcQQ==", "path": "microsoft.extensions.configuration.abstractions/7.0.0", "hashPath": "microsoft.extensions.configuration.abstractions.7.0.0.nupkg.sha512"}, "Microsoft.Extensions.DependencyInjection/7.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-elNeOmkeX3eDVG6pYVeV82p29hr+UKDaBhrZyWvWLw/EVZSYEkZlQdkp0V39k/Xehs2Qa0mvoCvkVj3eQxNQ1Q==", "path": "microsoft.extensions.dependencyinjection/7.0.0", "hashPath": "microsoft.extensions.dependencyinjection.7.0.0.nupkg.sha512"}, "Microsoft.Extensions.DependencyInjection.Abstractions/7.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-h3j/QfmFN4S0w4C2A6X7arXij/M/OVw3uQHSOFxnND4DyAzO1F9eMX7Eti7lU/OkSthEE0WzRsfT/Dmx86jzCw==", "path": "microsoft.extensions.dependencyinjection.abstractions/7.0.0", "hashPath": "microsoft.extensions.dependencyinjection.abstractions.7.0.0.nupkg.sha512"}, "Microsoft.Extensions.DependencyModel/7.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-oONNYd71J3LzkWc4fUHl3SvMfiQMYUCo/mDHDEu76hYYxdhdrPYv6fvGv9nnKVyhE9P0h20AU8RZB5OOWQcAXg==", "path": "microsoft.extensions.dependencymodel/7.0.0", "hashPath": "microsoft.extensions.dependencymodel.7.0.0.nupkg.sha512"}, "Microsoft.Extensions.Http/7.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-9Pq9f/CvOSz0t9yQa6g1uWpxa2sm13daLFm8EZwy9MaQUjKXWdNUXQwIxwhmba5N83UIqURiPHSNqGK1vfWF2w==", "path": "microsoft.extensions.http/7.0.0", "hashPath": "microsoft.extensions.http.7.0.0.nupkg.sha512"}, "Microsoft.Extensions.Identity.Core/7.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-cq11jroq2szFcXLJ0IW5BlI7oqq3ZGCu1mXCnpJ8VIvhvpIzf30AOoWR/w3YRVdAgkYzxbUQpKGZd+oxAKQhLA==", "path": "microsoft.extensions.identity.core/7.0.0", "hashPath": "microsoft.extensions.identity.core.7.0.0.nupkg.sha512"}, "Microsoft.Extensions.Identity.Stores/7.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-feaaluQbzJAMMluwSc7Rebm7IEVAD8/5GWt0dMYLE0tcc6gAsHYjBIBrPzmTstORd7k405Qo18FPF/jTfRsM0A==", "path": "microsoft.extensions.identity.stores/7.0.0", "hashPath": "microsoft.extensions.identity.stores.7.0.0.nupkg.sha512"}, "Microsoft.Extensions.Logging/7.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-Nw2muoNrOG5U5qa2ZekXwudUn2BJcD41e65zwmDHb1fQegTX66UokLWZkJRpqSSHXDOWZ5V0iqhbxOEky91atA==", "path": "microsoft.extensions.logging/7.0.0", "hashPath": "microsoft.extensions.logging.7.0.0.nupkg.sha512"}, "Microsoft.Extensions.Logging.Abstractions/7.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-kmn78+LPVMOWeITUjIlfxUPDsI0R6G0RkeAMBmQxAJ7vBJn4q2dTva7pWi65ceN5vPGjJ9q/Uae2WKgvfktJAw==", "path": "microsoft.extensions.logging.abstractions/7.0.0", "hashPath": "microsoft.extensions.logging.abstractions.7.0.0.nupkg.sha512"}, "Microsoft.Extensions.Options/7.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-lP1yBnTTU42cKpMozuafbvNtQ7QcBjr/CcK3bYOGEMH55Fjt+iecXjT6chR7vbgCMqy3PG3aNQSZgo/EuY/9qQ==", "path": "microsoft.extensions.options/7.0.0", "hashPath": "microsoft.extensions.options.7.0.0.nupkg.sha512"}, "Microsoft.Extensions.Primitives/7.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-um1KU5kxcRp3CNuI8o/GrZtD4AIOXDk+RLsytjZ9QPok3ttLUelLKpilVPuaFT3TFjOhSibUAso0odbOaCDj3Q==", "path": "microsoft.extensions.primitives/7.0.0", "hashPath": "microsoft.extensions.primitives.7.0.0.nupkg.sha512"}, "Mono.TextTemplating/2.2.1": {"type": "package", "serviceable": true, "sha512": "sha512-KZYeKBET/2Z0gY1WlTAK7+RHTl7GSbtvTLDXEZZojUdAPqpQNDL6tHv7VUpqfX5VEOh+uRGKaZXkuD253nEOBQ==", "path": "mono.texttemplating/2.2.1", "hashPath": "mono.texttemplating.2.2.1.nupkg.sha512"}, "Npgsql/7.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-tOBFksJZ2MiEz8xtDUgS5IG19jVO3nSP15QDYWiiGpXHe0PsLoQBts2Sg3hHKrrLTuw+AjsJz9iKvvGNHyKDIg==", "path": "npgsql/7.0.0", "hashPath": "npgsql.7.0.0.nupkg.sha512"}, "Npgsql.EntityFrameworkCore.PostgreSQL/7.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-CyUNlFZmtX2Kmw8XK5Tlx5eVUCzWJ+zJHErxZiMo2Y8zCRuH9+/OMGwG+9Mmp5zD5p3Ifbi5Pp3btsqoDDkSZQ==", "path": "npgsql.entityframeworkcore.postgresql/7.0.0", "hashPath": "npgsql.entityframeworkcore.postgresql.7.0.0.nupkg.sha512"}, "System.CodeDom/4.4.0": {"type": "package", "serviceable": true, "sha512": "sha512-2sCCb7doXEwtYAbqzbF/8UAeDRMNmPaQbU2q50Psg1J9KzumyVVCgKQY8s53WIPTufNT0DpSe9QRvVjOzfDWBA==", "path": "system.codedom/4.4.0", "hashPath": "system.codedom.4.4.0.nupkg.sha512"}, "System.ComponentModel.Annotations/5.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-dMkqfy2el8A8/I76n2Hi1oBFEbG1SfxD2l5nhwXV3XjlnOmwxJlQbYpJH4W51odnU9sARCSAgv7S3CyAFMkpYg==", "path": "system.componentmodel.annotations/5.0.0", "hashPath": "system.componentmodel.annotations.5.0.0.nupkg.sha512"}, "System.Runtime.CompilerServices.Unsafe/6.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-/iUeP3tq1S0XdNNoMz5C9twLSrM/TH+qElHkXWaPvuNOt+99G75NrV0OS2EqHx5wMN7popYjpc8oTjC1y16DLg==", "path": "system.runtime.compilerservices.unsafe/6.0.0", "hashPath": "system.runtime.compilerservices.unsafe.6.0.0.nupkg.sha512"}, "System.Text.Encodings.Web/7.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-OP6umVGxc0Z0MvZQBVigj4/U31Pw72ITihDWP9WiWDm+q5aoe0GaJivsfYGq53o6dxH7DcXWiCTl7+0o2CGdmg==", "path": "system.text.encodings.web/7.0.0", "hashPath": "system.text.encodings.web.7.0.0.nupkg.sha512"}, "System.Text.Json/7.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-DaGSsVqKsn/ia6RG8frjwmJonfos0srquhw09TlT8KRw5I43E+4gs+/bZj4K0vShJ5H9imCuXupb4RmS+dBy3w==", "path": "system.text.json/7.0.0", "hashPath": "system.text.json.7.0.0.nupkg.sha512"}, "GeoSpatialDataKRBR.Common/1.0.0": {"type": "project", "serviceable": false, "sha512": ""}, "GeoSpatialDataKRBR.Data/1.0.0": {"type": "project", "serviceable": false, "sha512": ""}, "GeoSpatialDataKRBR.Data.Models/1.0.0": {"type": "project", "serviceable": false, "sha512": ""}}}