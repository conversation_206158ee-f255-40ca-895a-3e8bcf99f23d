﻿@model PersonalFileFormModel
@{
	int chartWidth = ViewBag.Months;
	string userId = Model.userId;
	ViewData["Title"] = $"Личен файл - {Model.Name}";
}

<div class="modern-container">
	<!-- Employee Header -->
	<div class="employee-header">
		<div class="employee-info">
			<div class="employee-avatar">
				@{
					var initials = Model.Name.Split(' ').Take(2).Select(n => n.FirstOrDefault()).ToArray();
					string employeeInitials = string.Join("", initials).ToUpper();
				}
				@employeeInitials
			</div>
			<div class="employee-details">
				<h1 class="employee-name">@Model.Name</h1>
				<p class="employee-title">
					<i class="fas fa-user-hard-hat"></i>
					Работник
				</p>
			</div>
		</div>
		<div class="employee-actions">
			<a class="modern-btn modern-btn-success" asp-controller="Admin" asp-action="ChangeSalary" asp-route-userId="@Model.userId">
				<i class="fas fa-dollar-sign"></i>
				Смени заплатата
			</a>
			<a class="modern-btn modern-btn-info" asp-controller="Admin" asp-action="UsersEdit" asp-route-id="@Model.userId">
				<i class="fas fa-edit"></i>
				Редактирай профил
			</a>
		</div>
	</div>

	<!-- Salary Information -->
	<div class="modern-card">
		<div class="modern-card-header">
			<h3>
				<i class="fas fa-money-bill-wave"></i>
				Заплата и повишения
			</h3>
		</div>
		<div class="modern-card-body">
			<div class="salary-grid">
				<div class="salary-item">
					<div class="salary-icon">
						<i class="fas fa-euro-sign"></i>
					</div>
					<div class="salary-info">
						<span class="salary-label">Текуща заплата</span>
						<span class="salary-value">@Model.Salary лв.</span>
					</div>
				</div>
				<div class="salary-item">
					<div class="salary-icon">
						<i class="fas fa-chart-line"></i>
					</div>
					<div class="salary-info">
						<span class="salary-label">Брой повишения</span>
						<span class="salary-value">@Model.SalaryUpdates пъти</span>
					</div>
				</div>
			</div>
		</div>
	</div>

	<!-- Analytics Controls -->
	<div class="modern-card">
		<div class="modern-card-header">
			<h3>
				<i class="fas fa-chart-bar"></i>
				Анализ на производителността
			</h3>
		</div>
		<div class="modern-card-body">
			<form method="get" asp-controller="Admin" asp-action="PersonalFile" class="analytics-form">
				<div class="form-group">
					<label class="modern-form-label">
						<i class="fas fa-calendar-alt"></i>
						Период за анализ
					</label>
					<select asp-for="Months" class="modern-form-control">
						<option value="2">2 Месеца</option>
						<option value="3">3 Месеца</option>
						<option value="6">6 Месеца</option>
						<option value="9">9 Месеца</option>
						<option value="12">12 Месеца</option>
						<option value="16">16 Месеца</option>
						<option value="20">20 Месеца</option>
						<option value="24">24 Месеца</option>
						<option value="30">30 Месеца</option>
						<option value="36">36 Месеца</option>
					</select>
				</div>
				<input type="hidden" asp-for="userId" value="@Model.userId"/>
				<button type="submit" class="modern-btn modern-btn-primary">
					<i class="fas fa-search"></i>
					Генерирай отчет
				</button>
			</form>
		</div>
	</div>

	<!-- Modern Charts Section -->
	<div class="charts-container">
		<!-- Combined Performance Chart -->
		<div class="chart-card full-width">
			<div class="chart-header">
				<h4>
					<i class="fas fa-chart-line"></i>
					Обща производителност за последните @chartWidth месеца
				</h4>
				<p class="chart-description">Комбиниран преглед на брой проекти и стойност</p>
			</div>
			<div class="chart-body">
				<div id="PerformanceMetrics"></div>
			</div>
		</div>

		<!-- Project Count Chart -->
		<div class="chart-card">
			<div class="chart-header">
				<h4>
					<i class="fas fa-project-diagram"></i>
					Брой проекти за последните @chartWidth месеца
				</h4>
				<p class="chart-description">Месечно разпределение на завършени проекти</p>
			</div>
			<div class="chart-body">
				<div id="MonthProjectCount"></div>
			</div>
		</div>

		<!-- Project Value Chart -->
		<div class="chart-card">
			<div class="chart-header">
				<h4>
					<i class="fas fa-euro-sign"></i>
					Обща стойност на проектите за последните @chartWidth месеца
				</h4>
				<p class="chart-description">Финансов принос по месеци</p>
			</div>
			<div class="chart-body">
				<div id="MonthProjectSumPrice"></div>
			</div>
		</div>

		<!-- Productivity Trend Chart -->
		<div class="chart-card">
			<div class="chart-header">
				<h4>
					<i class="fas fa-trending-up"></i>
					Тенденция на производителността
				</h4>
				<p class="chart-description">Средна стойност на проект по месеци</p>
			</div>
			<div class="chart-body">
				<div id="ProductivityTrend"></div>
			</div>
		</div>

		<!-- Project Types Chart -->
		<div class="chart-card">
			<div class="chart-header">
				<h4>
					<i class="fas fa-chart-pie"></i>
					Тип на проектите за последните @chartWidth месеца
				</h4>
				<p class="chart-description">Разпределение по тип проекти</p>
			</div>
			<div class="chart-body">
				<div id="TypeProjectCount"></div>
			</div>
		</div>

		<!-- Performance Summary Cards -->
		<div class="chart-card full-width">
			<div class="chart-header">
				<h4>
					<i class="fas fa-analytics"></i>
					Ключови показатели
				</h4>
			</div>
			<div class="chart-body">
				<div class="kpi-grid">
					<div class="kpi-card">
						<div class="kpi-icon">
							<i class="fas fa-tasks"></i>
						</div>
						<div class="kpi-content">
							<div class="kpi-value" id="totalProjects">-</div>
							<div class="kpi-label">Общо проекти</div>
						</div>
					</div>
					<div class="kpi-card">
						<div class="kpi-icon">
							<i class="fas fa-euro-sign"></i>
						</div>
						<div class="kpi-content">
							<div class="kpi-value" id="totalValue">-</div>
							<div class="kpi-label">Обща стойност</div>
						</div>
					</div>
					<div class="kpi-card">
						<div class="kpi-icon">
							<i class="fas fa-chart-bar"></i>
						</div>
						<div class="kpi-content">
							<div class="kpi-value" id="avgValue">-</div>
							<div class="kpi-label">Средна стойност</div>
						</div>
					</div>
					<div class="kpi-card">
						<div class="kpi-icon">
							<i class="fas fa-calendar-alt"></i>
						</div>
						<div class="kpi-content">
							<div class="kpi-value" id="avgMonthly">-</div>
							<div class="kpi-label">Средно месечно</div>
						</div>
					</div>
				</div>
			</div>
		</div>
	</div>

	<!-- Performance Summary -->
	<div class="modern-card">
		<div class="modern-card-header">
			<h3>
				<i class="fas fa-trophy"></i>
				Обобщение на производителността
			</h3>
		</div>
		<div class="modern-card-body">
			<div class="performance-grid">
				<div class="performance-item">
					<div class="performance-icon success">
						<i class="fas fa-check-circle"></i>
					</div>
					<div class="performance-info">
						<span class="performance-label">Статус</span>
						<span class="performance-value">Активен работник</span>
					</div>
				</div>
				<div class="performance-item">
					<div class="performance-icon info">
						<i class="fas fa-calendar-check"></i>
					</div>
					<div class="performance-info">
						<span class="performance-label">Период</span>
						<span class="performance-value">@chartWidth месеца</span>
					</div>
				</div>
			</div>
		</div>
	</div>
</div>

@section Scripts {
	<!-- Add ApexCharts library -->
	<script src="https://cdn.jsdelivr.net/npm/apexcharts"></script>
	<!-- JavaScript code -->
	<script src="~/js/PersonalFileCharts.js"></script>

	<script>
		// Initialize chart width
		ChangeMinWidth(@chartWidth);

		// Data from server
		var MonthAndProjectCountData = @Html.Raw(Json.Serialize(Model.monthlyProjectCounts));
		var TypeProjectCountData = @Html.Raw(Json.Serialize(Model.typeProjectCounts));

		// Calculate KPIs
		function calculateKPIs() {
			var totalProjects = MonthAndProjectCountData.reduce((sum, item) => sum + item.projectCount, 0);
			var totalValue = MonthAndProjectCountData.reduce((sum, item) => sum + item.price, 0);
			var avgValue = totalProjects > 0 ? totalValue / totalProjects : 0;
			var avgMonthly = MonthAndProjectCountData.length > 0 ? totalProjects / MonthAndProjectCountData.length : 0;

			document.getElementById('totalProjects').textContent = totalProjects;
			document.getElementById('totalValue').textContent = totalValue.toLocaleString() + ' лв.';
			document.getElementById('avgValue').textContent = Math.round(avgValue).toLocaleString() + ' лв.';
			document.getElementById('avgMonthly').textContent = avgMonthly.toFixed(1);
		}

		// Create and render charts
		document.addEventListener('DOMContentLoaded', function() {
			// Performance Metrics Chart (Combined)
			var performanceOptions = CreatePerformanceMetricsChart(MonthAndProjectCountData);
			var performanceChart = new ApexCharts(document.querySelector('#PerformanceMetrics'), performanceOptions);

			// Project Count Chart
			var chartMonthProjectCountOptions = CreateMonthAndProjectCountChartOptions(MonthAndProjectCountData);
			var MonthProjectCountChart = new ApexCharts(document.querySelector('#MonthProjectCount'), chartMonthProjectCountOptions);

			// Project Value Chart
			var chartMonthProjectSumPriceOptions = CreateMonthAndProjectPriceSumChartOptions(MonthAndProjectCountData);
			var MonthProjectSumPriceChart = new ApexCharts(document.querySelector('#MonthProjectSumPrice'), chartMonthProjectSumPriceOptions);

			// Productivity Trend Chart
			var productivityOptions = CreateProductivityTrendChart(MonthAndProjectCountData);
			var productivityChart = new ApexCharts(document.querySelector('#ProductivityTrend'), productivityOptions);

			// Project Types Chart
			var ChartPieProjectsTypeCountOptions = CreatePieChartForTaskTypes(TypeProjectCountData);
			var TypeProjectCountPieChart = new ApexCharts(document.querySelector("#TypeProjectCount"), ChartPieProjectsTypeCountOptions);

			// Render all charts with staggered animation
			setTimeout(() => performanceChart.render(), 100);
			setTimeout(() => MonthProjectCountChart.render(), 300);
			setTimeout(() => MonthProjectSumPriceChart.render(), 500);
			setTimeout(() => productivityChart.render(), 700);
			setTimeout(() => TypeProjectCountPieChart.render(), 900);

			// Calculate and display KPIs
			calculateKPIs();

			// Add chart loading animations
			document.querySelectorAll('.chart-card').forEach((card, index) => {
				card.style.opacity = '0';
				card.style.transform = 'translateY(20px)';
				setTimeout(() => {
					card.style.transition = 'all 0.6s ease';
					card.style.opacity = '1';
					card.style.transform = 'translateY(0)';
				}, index * 150);
			});
		});
	</script>
}



																										  