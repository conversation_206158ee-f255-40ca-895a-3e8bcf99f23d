using NUnit.Framework;
using System;
using System.Collections.Generic;
using System.Linq;

namespace TaskManager.FronEndServices.Tests
{
    [TestFixture]
    public class CalendarConflictDetectionTests
    {
        private dynamic[] teamMembers;
        private dynamic[] existingTasks;

        [SetUp]
        public void Setup()
        {
            teamMembers = new[]
            {
                new { id = "worker1-guid", name = "Петър Петров", initials = "ПП", color = "#3B82F6" },
                new { id = "worker2-guid", name = "Георги Георгиев", initials = "ГГ", color = "#10B981" },
                new { id = "worker3-guid", name = "<PERSON><PERSON><PERSON><PERSON> Иванов", initials = "И<PERSON>", color = "#F59E0B" }
            };

            existingTasks = new[]
            {
                new { id = "task1", title = "Morning Task", date = "2024-01-15", startTime = "09:00", endTime = "11:00", assignedMemberIds = new[] { "worker1-guid" } },
                new { id = "task2", title = "Afternoon Task", date = "2024-01-15", startTime = "14:00", endTime = "16:00", assignedMemberIds = new[] { "worker2-guid" } },
                new { id = "task3", title = "Evening Task", date = "2024-01-16", startTime = "09:00", endTime = "11:00", assignedMemberIds = new[] { "worker1-guid" } }
            };
        }

        [Test]
        public void TestConflictDetection_SameWorkerOverlappingTime_ShouldDetectConflict()
        {
            // Arrange - Create new task that overlaps with existing task1 (09:00-11:00)
            var newTaskData = new {
                title = "Conflicting Task",
                date = "2024-01-15",
                startTime = "10:00",
                endTime = "12:00",
                assignedMemberIds = new[] { "worker1-guid" }
            };

            // Act
            var conflicts = SimulateJavaScriptConflictDetection(existingTasks, teamMembers, newTaskData);

            // Assert
            Assert.That(conflicts.Count, Is.GreaterThan(0), "Should detect conflict when same worker has overlapping times");
            Assert.That(conflicts[0].member.id, Is.EqualTo("worker1-guid"), "Should identify correct conflicting worker");
        }

        [Test]
        public void TestConflictDetection_DifferentWorkerSameTime_ShouldNotDetectConflict()
        {
            // Arrange - Create new task for different worker at same time as existing task
            var newTaskData = new {
                title = "Different Worker Task",
                date = "2024-01-15",
                startTime = "09:00",
                endTime = "11:00",
                assignedMemberIds = new[] { "worker2-guid" } // Different worker than task1
            };

            // Act
            var conflicts = SimulateJavaScriptConflictDetection(existingTasks, teamMembers, newTaskData);

            // Assert
            Assert.That(conflicts.Count, Is.EqualTo(0), "Should not detect conflict when different workers have overlapping times");
        }

        [Test]
        public void TestConflictDetection_SameWorkerNoOverlap_ShouldNotDetectConflict()
        {
            // Arrange - Create new task for same worker but non-overlapping time
            var newTaskData = new {
                title = "Non-Overlapping Task",
                date = "2024-01-15",
                startTime = "12:00", // After task1 ends at 11:00
                endTime = "13:00",
                assignedMemberIds = new[] { "worker1-guid" }
            };

            // Act
            var conflicts = SimulateJavaScriptConflictDetection(existingTasks, teamMembers, newTaskData);

            // Assert
            Assert.That(conflicts.Count, Is.EqualTo(0), "Should not detect conflict when same worker has non-overlapping times");
        }

        [Test]
        public void TestConflictDetection_ExactSameTime_ShouldDetectConflict()
        {
            // Arrange - Create new task with exact same time as existing task1
            var newTaskData = new {
                title = "Exact Same Time Task",
                date = "2024-01-15",
                startTime = "09:00", // Exact same as task1
                endTime = "11:00",   // Exact same as task1
                assignedMemberIds = new[] { "worker1-guid" }
            };

            // Act
            var conflicts = SimulateJavaScriptConflictDetection(existingTasks, teamMembers, newTaskData);

            // Assert
            Assert.That(conflicts.Count, Is.GreaterThan(0), "Should detect conflict when same worker has exact same time slot");
            Assert.That(conflicts[0].member.id, Is.EqualTo("worker1-guid"), "Should identify correct conflicting worker");
        }

        [Test]
        public void TestConflictDetection_DifferentDate_ShouldNotDetectConflict()
        {
            // Arrange - Create new task for same worker but different date with no existing tasks
            var newTaskData = new {
                title = "Different Date Task",
                date = "2024-01-17", // Completely different date with no existing tasks
                startTime = "09:00",
                endTime = "11:00",
                assignedMemberIds = new[] { "worker1-guid" }
            };

            // Act
            var conflicts = SimulateJavaScriptConflictDetection(existingTasks, teamMembers, newTaskData);

            // Assert
            Assert.That(conflicts.Count, Is.EqualTo(0), "Should not detect conflict when same worker has task on different date");
        }

        [Test]
        public void TestConflictDetection_UpdateExistingTask_ShouldExcludeSelf()
        {
            // Arrange - Update existing task1 with overlapping time (should exclude itself)
            var updateTaskData = new {
                title = "Updated Morning Task",
                date = "2024-01-15",
                startTime = "08:30", // Slightly earlier than original
                endTime = "11:30",   // Slightly later than original
                assignedMemberIds = new[] { "worker1-guid" }
            };

            // Act - Exclude task1 from conflict check (simulating update)
            var conflicts = SimulateJavaScriptConflictDetection(existingTasks, teamMembers, updateTaskData, "task1");

            // Assert
            Assert.That(conflicts.Count, Is.EqualTo(0), "Should not detect conflict with self when updating existing task");
        }

        [Test]
        public void TestConflictDetection_MultipleWorkers_ShouldDetectAllConflicts()
        {
            // Arrange - Create task with multiple workers, one of which conflicts
            var newTaskData = new {
                title = "Multi-Worker Task",
                date = "2024-01-15",
                startTime = "10:00",
                endTime = "12:00",
                assignedMemberIds = new[] { "worker1-guid", "worker3-guid" } // worker1 conflicts, worker3 doesn't
            };

            // Act
            var conflicts = SimulateJavaScriptConflictDetection(existingTasks, teamMembers, newTaskData);

            // Assert
            Assert.That(conflicts.Count, Is.EqualTo(1), "Should detect conflict for worker1 only");
            Assert.That(conflicts[0].member.id, Is.EqualTo("worker1-guid"), "Should identify worker1 as conflicting");
        }

        // Simulate the JavaScript conflict detection logic from calendar-app.js
        private List<dynamic> SimulateJavaScriptConflictDetection(dynamic[] tasks, dynamic[] teamMembers, dynamic taskData, string excludeTaskId = null)
        {
            var conflicts = new List<dynamic>();

            // Validate inputs (same as JavaScript)
            if (taskData == null) return conflicts;
            if (string.IsNullOrEmpty(taskData.date?.ToString()?.Trim())) return conflicts;
            if (taskData.assignedMemberIds == null || taskData.assignedMemberIds.Length == 0) return conflicts;

            // Check each assigned member for conflicts
            foreach (string memberId in taskData.assignedMemberIds)
            {
                // Find all tasks for this member on the same date (excluding the current task being updated)
                var memberTasks = tasks.Where(task => {
                    bool isSameTask = excludeTaskId != null && (task.id?.ToString() == excludeTaskId?.ToString());
                    bool isSameDate = task.date?.ToString() == taskData.date?.ToString();
                    bool isAssignedToMember = task.assignedMemberIds != null &&
                                            ((string[])task.assignedMemberIds).Contains(memberId);

                    return !isSameTask && isSameDate && isAssignedToMember;
                }).ToArray();

                // Check each existing task for time overlap
                foreach (var existingTask in memberTasks)
                {
                    try
                    {
                        // Parse times for comparison (same logic as JavaScript)
                        var newStart = DateTime.Parse($"2000-01-01T{taskData.startTime}:00");
                        var newEnd = DateTime.Parse($"2000-01-01T{taskData.endTime}:00");
                        var existingStart = DateTime.Parse($"2000-01-01T{existingTask.startTime}:00");
                        var existingEnd = DateTime.Parse($"2000-01-01T{existingTask.endTime}:00");

                        // Check for overlap: tasks overlap if newStart < existingEnd AND newEnd > existingStart
                        bool hasOverlap = newStart < existingEnd && newEnd > existingStart;

                        if (hasOverlap)
                        {
                            // Find the team member info
                            var member = teamMembers.FirstOrDefault(m =>
                                m.id?.ToString().ToLower() == memberId?.ToString().ToLower())
                                ?? new { id = memberId, name = $"Unknown Worker ({memberId})" };

                            var conflict = new {
                                member = member,
                                existingTask = existingTask,
                                timeOverlap = new {
                                    start = Math.Max(newStart.Ticks, existingStart.Ticks),
                                    end = Math.Min(newEnd.Ticks, existingEnd.Ticks)
                                }
                            };

                            conflicts.Add(conflict);
                        }
                    }
                    catch (Exception)
                    {
                        // Skip tasks with invalid time formats
                        continue;
                    }
                }
            }

            return conflicts;
        }
    }
}
