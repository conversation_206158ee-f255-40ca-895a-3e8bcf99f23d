import { describe, it, expect, beforeEach, vi } from 'vitest'
import { render, screen, fireEvent, waitFor } from '@testing-library/react'
import userEvent from '@testing-library/user-event'
import App from '../App'

// Mock data
const mockTeamMembers = [
  { id: 1, name: '<PERSON><PERSON>', initials: 'G<PERSON>', color: '#3B82F6' },
  { id: 2, name: '<PERSON>', initials: 'II', color: '#10B981' },
  { id: 3, name: '<PERSON><PERSON>rov', initials: 'PP', color: '#F59E0B' },
  { id: 4, name: '<PERSON><PERSON><PERSON>', initials: 'DD', color: '#EF4444' }
]

const mockTask = {
  id: 1,
  title: 'Test Task',
  description: 'Test Description',
  date: '2025-06-27',
  startTime: '09:00',
  endTime: '10:00',
  color: '#3B82F6',
  assignedMembers: [1, 2]
}

describe('App Component', () => {
  beforeEach(() => {
    // Reset any mocks before each test
    vi.clearAllMocks()
  })

  describe('Initial Render', () => {
    it('should render the main application', () => {
      render(<App />)
      
      expect(screen.getByText('Task Calendar')).toBeInTheDocument()
      expect(screen.getByText('Team Members')).toBeInTheDocument()
    })

    it('should render calendar view buttons', () => {
      render(<App />)
      
      expect(screen.getByText('Day')).toBeInTheDocument()
      expect(screen.getByText('Week')).toBeInTheDocument()
      expect(screen.getByText('Month')).toBeInTheDocument()
      expect(screen.getByText('Year')).toBeInTheDocument()
    })

    it('should render team members in sidebar', () => {
      render(<App />)
      
      expect(screen.getByText('Georgi Georgiev')).toBeInTheDocument()
      expect(screen.getByText('Ivan Ivanov')).toBeInTheDocument()
      expect(screen.getByText('Petar Petrov')).toBeInTheDocument()
      expect(screen.getByText('Dimitar Dimitrov')).toBeInTheDocument()
    })

    it('should show current date in header', () => {
      render(<App />)
      
      const currentDate = new Date().toLocaleDateString('en-US', { 
        year: 'numeric', 
        month: 'long' 
      })
      expect(screen.getByText(new RegExp(currentDate.split(' ')[1]))).toBeInTheDocument()
    })
  })

  describe('Calendar Navigation', () => {
    it('should switch between calendar views', async () => {
      const user = userEvent.setup()
      render(<App />)
      
      // Test Week view
      await user.click(screen.getByText('Week'))
      expect(screen.getByText('Week')).toHaveClass('active')
      
      // Test Month view
      await user.click(screen.getByText('Month'))
      expect(screen.getByText('Month')).toHaveClass('active')
      
      // Test Day view
      await user.click(screen.getByText('Day'))
      expect(screen.getByText('Day')).toHaveClass('active')
    })

    it('should navigate between dates', async () => {
      const user = userEvent.setup()
      render(<App />)
      
      const prevButton = screen.getByText('←')
      const nextButton = screen.getByText('→')
      
      await user.click(nextButton)
      await user.click(prevButton)
      
      expect(prevButton).toBeInTheDocument()
      expect(nextButton).toBeInTheDocument()
    })
  })

  describe('Team Member Management', () => {
    it('should toggle team member visibility', async () => {
      const user = userEvent.setup()
      render(<App />)
      
      const georgeMember = screen.getByText('Georgi Georgiev')
      await user.click(georgeMember)
      
      // Should toggle visibility (implementation depends on your logic)
      expect(georgeMember).toBeInTheDocument()
    })

    it('should display team member initials and colors', () => {
      render(<App />)

      const georgeInitials = screen.getByTestId('sidebar-avatar-GG')
      const ivanInitials = screen.getByTestId('sidebar-avatar-II')

      expect(georgeInitials).toBeInTheDocument()
      expect(ivanInitials).toBeInTheDocument()
    })
  })

  describe('Task Creation Modal', () => {
    it('should open task creation modal when clicking time slot', async () => {
      const user = userEvent.setup()
      render(<App />)

      // Find and click a time slot using test ID
      const timeSlots = document.querySelectorAll('[data-testid^="day-time-slot-"]')

      if (timeSlots.length > 0) {
        await user.click(timeSlots[0])

        await waitFor(() => {
          expect(screen.getByTestId('task-modal-title')).toBeInTheDocument()
        })
      }
    })

    it('should render task form fields', async () => {
      const user = userEvent.setup()
      render(<App />)

      // Trigger modal opening using time slot
      const timeSlots = document.querySelectorAll('[data-testid^="day-time-slot-"]')
      if (timeSlots.length > 0) {
        await user.click(timeSlots[0])

        await waitFor(() => {
          if (screen.queryByTestId('task-modal-title')) {
            expect(screen.getByTestId('task-title-input')).toBeInTheDocument()
            expect(screen.getByTestId('task-description-input')).toBeInTheDocument()
            expect(screen.getByTestId('task-date-input')).toBeInTheDocument()
          }
        })
      }
    })
  })

  describe('Color Palette', () => {
    it('should render color palette options', async () => {
      const user = userEvent.setup()
      render(<App />)
      
      // Open task creation modal first
      const timeSlots = screen.getAllByRole('button')
      if (timeSlots.length > 0) {
        await user.click(timeSlots[0])
        
        await waitFor(() => {
          const colorOptions = screen.getAllByRole('button')
          const colorButtons = colorOptions.filter(btn => 
            btn.className && btn.className.includes('color-option')
          )
          
          if (colorButtons.length > 0) {
            expect(colorButtons.length).toBeGreaterThan(0)
          }
        })
      }
    })
  })

  describe('Team Member Selection', () => {
    it('should render team member checkboxes in horizontal layout', async () => {
      const user = userEvent.setup()
      render(<App />)
      
      // Open task creation modal
      const buttons = screen.getAllByRole('button')
      if (buttons.length > 0) {
        await user.click(buttons[0])
        
        await waitFor(() => {
          if (screen.queryByText('Assign to Team Members')) {
            const checkboxes = screen.getAllByRole('checkbox')
            expect(checkboxes.length).toBeGreaterThan(0)
          }
        })
      }
    })

    it('should allow multiple team member selection', async () => {
      const user = userEvent.setup()
      render(<App />)
      
      // This test would need the modal to be open
      // Implementation depends on your specific DOM structure
    })
  })

  describe('Responsive Design', () => {
    it('should handle mobile viewport', () => {
      // Mock mobile viewport
      Object.defineProperty(window, 'innerWidth', {
        writable: true,
        configurable: true,
        value: 375,
      })
      
      render(<App />)
      
      expect(screen.getByText('Task Calendar')).toBeInTheDocument()
    })
  })

  describe('Error Handling', () => {
    it('should handle invalid dates gracefully', () => {
      render(<App />)
      
      // The app should render without crashing
      expect(screen.getByText('Task Calendar')).toBeInTheDocument()
    })

    it('should handle empty task list', () => {
      render(<App />)
      
      // Should render calendar even with no tasks
      expect(screen.getByText('Team Members')).toBeInTheDocument()
    })
  })

  describe('Accessibility', () => {
    it('should have proper ARIA labels', () => {
      render(<App />)

      const buttons = screen.getAllByRole('button')
      expect(buttons.length).toBeGreaterThan(0)
    })

    it('should support keyboard navigation', async () => {
      const user = userEvent.setup()
      render(<App />)

      // Test tab navigation
      await user.tab()
      expect(document.activeElement).toBeInTheDocument()
    })
  })

  describe('Task Management', () => {
    it('should handle task creation with all fields', async () => {
      const user = userEvent.setup()
      render(<App />)

      // Mock task creation flow
      const timeSlots = document.querySelectorAll('.unified-slot')
      if (timeSlots.length > 0) {
        fireEvent.click(timeSlots[0])

        await waitFor(() => {
          if (screen.queryByText('Create Task')) {
            // Test form completion
            expect(screen.getByText('Create Task')).toBeInTheDocument()
          }
        })
      }
    })

    it('should handle task deletion', async () => {
      render(<App />)

      // This would test the delete functionality
      // Implementation depends on your delete button/feature
      const deleteButtons = screen.queryAllByText('Delete')
      if (deleteButtons.length > 0) {
        fireEvent.click(deleteButtons[0])
        // Should remove task or show confirmation
      }
    })
  })

  describe('Calendar State Management', () => {
    it('should maintain selected date across view changes', async () => {
      const user = userEvent.setup()
      render(<App />)

      // Navigate to specific date
      const nextButton = screen.getByText('→')
      await user.click(nextButton)

      // Change view
      await user.click(screen.getByText('Month'))
      await user.click(screen.getByText('Week'))

      // Should maintain the same date context
      expect(screen.getByText('Week')).toBeInTheDocument()
    })

    it('should update time slots based on current view', () => {
      render(<App />)

      // Time slots should be present in day/week view
      const timeSlots = document.querySelectorAll('.unified-slot')
      expect(timeSlots.length).toBeGreaterThanOrEqual(0)
    })
  })
})
