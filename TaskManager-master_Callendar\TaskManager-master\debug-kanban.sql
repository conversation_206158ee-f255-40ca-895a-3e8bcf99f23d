-- Debug script to check Kanban board state

-- Check if Kanban boards exist
SELECT 'Kanban Boards' as TableName, COUNT(*) as Count FROM KanbanBoards WHERE IsActive = 1;
SELECT * FROM KanbanBoards WHERE IsActive = 1;

-- Check if Kanban columns exist
SELECT 'Kanban Columns' as TableName, COUNT(*) as Count FROM KanbanColumns WHERE IsActive = 1;
SELECT * FROM KanbanColumns WHERE IsActive = 1 ORDER BY BoardId, Position;

-- Check if Kanban cards exist
SELECT 'Kanban Cards' as TableName, COUNT(*) as Count FROM KanbanCards WHERE IsActive = 1;
SELECT * FROM KanbanCards WHERE IsActive = 1 ORDER BY ColumnId, Position;

-- Check Workers
SELECT 'Workers' as TableName, COUNT(*) as Count FROM Workers;
SELECT w.Id, u.FirstName, u.LastName, u.Email, w.Position 
FROM Workers w 
INNER JOIN AspNetUsers u ON w.UserId = u.Id;

-- Check GeoTasks
SELECT 'GeoTasks' as TableName, COUNT(*) as Count FROM GeoTasks;
SELECT TOP 5 Id, ProjectNumber, Adrress, WorkerId, StatusId FROM GeoTasks ORDER BY CreateDate DESC;

-- Check if any GeoTasks have associated Kanban cards
SELECT 
    gt.Id as GeoTaskId,
    gt.ProjectNumber,
    kc.Id as CardId,
    kc.Title as CardTitle,
    kc.ColumnId
FROM GeoTasks gt
LEFT JOIN KanbanCards kc ON gt.Id = kc.GeoTaskId AND kc.IsActive = 1
ORDER BY gt.CreateDate DESC;
