namespace TaskManager.Web.ViewModels.Team
{
    using TaskManager.Web.ViewModels.GeoTask;
    using TaskManager.Web.ViewModels.Worker;
    using TaskManager.Web.ViewModels.Admin;

    public class TeamBoardViewModel
    {
        public string CurrentUserId { get; set; } = null!;
        public string CurrentUserName { get; set; } = null!;
        public bool IsAdmin { get; set; }

        // Kanban Columns
        public IEnumerable<TaskColumnViewModel> Columns { get; set; } = new List<TaskColumnViewModel>();
        
        // Team Members
        public IEnumerable<AllWorkersViewModel> TeamMembers { get; set; } = new List<AllWorkersViewModel>();
        
        // Quick Stats
        public TeamStatsViewModel Stats { get; set; } = new TeamStatsViewModel();
    }

    public class TaskColumnViewModel
    {
        public int StatusId { get; set; }
        public string StatusName { get; set; } = null!;
        public string StatusColor { get; set; } = "#6b7280"; // Default gray
        public int TaskCount { get; set; }
        public IEnumerable<TeamTaskCardViewModel> Tasks { get; set; } = new List<TeamTaskCardViewModel>();
    }

    public class TeamTaskCardViewModel
    {
        public string Id { get; set; } = null!;
        public int ProjectNumber { get; set; }
        public string Title { get; set; } = null!;
        public string? Description { get; set; }
        public decimal Price { get; set; }
        public DateTime CreateDate { get; set; }
        public DateTime? EndDate { get; set; }
        public string Priority { get; set; } = "Medium";
        
        // Worker Info
        public string WorkerId { get; set; } = null!;
        public string WorkerName { get; set; } = null!;
        public string WorkerInitials { get; set; } = null!;
        public string WorkerColor { get; set; } = "#6b7280";
        
        // Client Info
        public string ClientName { get; set; } = null!;
        
        // Status Info
        public int StatusId { get; set; }
        public string StatusName { get; set; } = null!;
        
        // Type Info
        public string TypeName { get; set; } = null!;
        
        // Progress
        public int ProgressPercentage { get; set; }
        public bool IsOverdue { get; set; }
        public int DaysRemaining { get; set; }
    }

    public class TeamStatsViewModel
    {
        public int TotalTasks { get; set; }
        public int CompletedTasks { get; set; }
        public int InProgressTasks { get; set; }
        public int OverdueTasks { get; set; }
        public decimal TotalValue { get; set; }
        public int ActiveMembers { get; set; }
        public double CompletionRate { get; set; }
    }

    public class CreateTaskCardViewModel
    {
        public string Title { get; set; } = null!;
        public string? Description { get; set; }
        public decimal Price { get; set; }
        public DateTime? EndDate { get; set; }
        public string Priority { get; set; } = "Medium";
        public string WorkerId { get; set; } = null!;
        public string ClientId { get; set; } = null!;
        public int TypeId { get; set; }
        public int StatusId { get; set; } = 1; // Default to first status
    }

    public class UpdateTaskStatusViewModel
    {
        public string TaskId { get; set; } = null!;
        public int NewStatusId { get; set; }
        public int OldStatusId { get; set; }
    }
}
