namespace GeoSpatialDataKRBR.Data.Models
{
    using System.ComponentModel.DataAnnotations;
    using static Common.EntityValidationConstants.GeoLayerGroup;

    public class GeoLayerGroup
    {
        public GeoLayerGroup()
        {
            this.Id = Guid.NewGuid();
            this.GeoLayerGroupLayers = new HashSet<GeoLayerGroupLayer>();
        }

        [Key]
        public Guid Id { get; set; }

        [Required]
        [MaxLength(NameMaxLength)]
        public string Name { get; set; } = null!;

        [MaxLength(DescriptionMaxLength)]
        public string? Description { get; set; }

        public bool IsVisible { get; set; } = true;

        public int DisplayOrder { get; set; } = 0;

        public DateTime CreatedOn { get; set; } = DateTime.UtcNow;

        public DateTime? ModifiedOn { get; set; }

        // Navigation properties
        public virtual ICollection<GeoLayerGroupLayer> GeoLayerGroupLayers { get; set; }
    }
}
