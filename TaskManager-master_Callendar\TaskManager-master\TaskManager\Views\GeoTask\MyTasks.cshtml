﻿@model IEnumerable<TaskViewModel>;

@{
    ViewData["Title"] = "Моите задачи";
}

<div class="modern-container">
    <div class="modern-page-header">
        <h1 class="modern-page-title">
            <i class="fas fa-user-hard-hat"></i>
            Моите задачи
        </h1>
        <p class="modern-page-subtitle">
            Преглед и управление на вашите възложени задачи
        </p>
    </div>

    @if (!Model.Any())
    {
        <!-- Empty State -->
        <div class="modern-card">
            <div class="modern-card-body">
                <div class="empty-tasks-state">
                    <i class="fas fa-clipboard-list"></i>
                    <h3>Няма възложени задачи</h3>
                    <p>Все още нямате възложени задачи за изпълнение.</p>
                    <a class="modern-btn modern-btn-primary" asp-controller="GeoTask" asp-action="AllTasks">
                        <i class="fas fa-search"></i>
                        Разгледай всички задачи
                    </a>
                </div>
            </div>
        </div>
    }
    else
    {
        <!-- Tasks Table -->
        <div class="tasks-table-container">
            <div class="tasks-table-header">
                <h3>
                    <i class="fas fa-list"></i>
                    Списък с моите задачи (@Model.Count())
                </h3>
                <a class="add-task-btn" asp-controller="GeoTask" asp-action="Add">
                    <i class="fas fa-plus"></i>
                    Нова задача
                </a>
            </div>

            <table class="modern-table">
                <thead>
                    <tr>
                        <th><i class="fas fa-flag"></i> Статус</th>
                        <th><i class="fas fa-hashtag"></i> Номер</th>
                        <th><i class="fas fa-user"></i> Клиент</th>
                        <th><i class="fas fa-tag"></i> Услуга</th>
                        <th><i class="fas fa-sort-numeric-up"></i> Брой</th>
                        <th><i class="fas fa-euro-sign"></i> Цена</th>
                        <th><i class="fas fa-calendar-times"></i> Краен срок</th>
                    </tr>
                </thead>
                <tbody>
                    @foreach (TaskViewModel task in Model)
                    {
                        <tr class="task-row">
                            <td>
                                @if(task.status == "Приключена")
                                {
                                    <span class="task-status completed">@task.status</span>
                                }
                                else if(task.status == "В процес на изпълнение")
                                {
                                    <span class="task-status active">@task.status</span>
                                }
                                else
                                {
                                    <span class="task-status pending">@task.status</span>
                                }
                            </td>
                            <td>
                                <a class="task-number-link" asp-controller="GeoTask" asp-action="Edit" asp-route-id="@task.Id">
                                    <span class="task-number">Проект №@task.Number</span>
                                </a>
                            </td>
                            <td>
                                <div class="client-info">
                                    <i class="fas fa-building"></i>
                                    <span>@task.ClientName</span>
                                </div>
                            </td>
                            <td>
                                <div class="service-info">
                                    <i class="fas fa-cogs"></i>
                                    <span>@task.TaskType</span>
                                </div>
                            </td>
                            <td>
                                <span class="quantity-badge">@task.quantity бр.</span>
                            </td>
                            <td>
                                <span class="task-price">@((task.price * task.quantity).ToString("F2")) лв.</span>
                            </td>
                            <td>
                                @if (task.EndDate <= DateTime.UtcNow)
                                {
                                    <span class="task-deadline urgent">
                                        <i class="fas fa-exclamation-triangle"></i>
                                        @task.EndDate.ToString("dd/MM/yyyy")
                                    </span>
                                }
                                else if (task.EndDate.AddDays(-2) <= DateTime.UtcNow)
                                {
                                    <span class="task-deadline warning">
                                        <i class="fas fa-clock"></i>
                                        @task.EndDate.ToString("dd/MM/yyyy")
                                    </span>
                                }
                                else
                                {
                                    <span class="task-deadline normal">
                                        <i class="fas fa-calendar-check"></i>
                                        @task.EndDate.ToString("dd/MM/yyyy")
                                    </span>
                                }
                            </td>
                        </tr>
                    }
                </tbody>
            </table>
        </div>
    }
</div>

