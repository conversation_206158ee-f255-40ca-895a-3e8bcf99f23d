{"format": 1, "restore": {"D:\\C#Web\\TaskManager-master_Callendar\\TaskManager-master\\TaskManager.FronEndServices.Tests\\TaskManager.FronEndServices.Tests.csproj": {}}, "projects": {"D:\\C#Web\\TaskManager-master_Callendar\\TaskManager-master\\TaskManager.FronEndServices.Tests\\TaskManager.FronEndServices.Tests.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "D:\\C#Web\\TaskManager-master_Callendar\\TaskManager-master\\TaskManager.FronEndServices.Tests\\TaskManager.FronEndServices.Tests.csproj", "projectName": "TaskManager.FronEndServices.Tests", "projectPath": "D:\\C#Web\\TaskManager-master_Callendar\\TaskManager-master\\TaskManager.FronEndServices.Tests\\TaskManager.FronEndServices.Tests.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "D:\\C#Web\\TaskManager-master_Callendar\\TaskManager-master\\TaskManager.FronEndServices.Tests\\obj\\", "projectStyle": "PackageReference", "fallbackFolders": ["C:\\Program Files (x86)\\Microsoft Visual Studio\\Shared\\NuGetPackages"], "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.FallbackLocation.config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config"], "originalTargetFrameworks": ["net6.0"], "sources": {"C:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\": {}, "https://api.nuget.org/v3/index.json": {}}, "frameworks": {"net6.0": {"targetAlias": "net6.0", "projectReferences": {}}}, "warningProperties": {"warnAsError": ["NU1605"]}}, "frameworks": {"net6.0": {"targetAlias": "net6.0", "dependencies": {"Microsoft.NET.Test.Sdk": {"target": "Package", "version": "[17.3.2, )"}, "NUnit": {"target": "Package", "version": "[3.13.3, )"}, "NUnit.Analyzers": {"target": "Package", "version": "[3.3.0, )"}, "NUnit3TestAdapter": {"target": "Package", "version": "[4.2.1, )"}, "coverlet.collector": {"target": "Package", "version": "[3.1.2, )"}}, "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "frameworkReferences": {"Microsoft.NETCore.App": {"privateAssets": "all"}}, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\7.0.100\\RuntimeIdentifierGraph.json"}}}}}