@model GeoSpatialDataKRBR.Web.ViewModels.Map.MapViewModel
@{
    ViewData["Title"] = "Геопространствени данни";
    Layout = null;
}

<!DOCTYPE html>
<html lang="bg">
<head>
    <meta charset="utf-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>@ViewData["Title"] - КККР</title>
    <link rel="stylesheet" href="~/lib/bootstrap/dist/css/bootstrap.min.css" />
    <link rel="stylesheet" href="~/css/site.css" asp-append-version="true" />
    <link rel="stylesheet" href="https://unpkg.com/leaflet@1.9.4/dist/leaflet.css" />
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" />
    <style>
        /* Tab styling */
        .nav-tabs {
            border-bottom: 1px solid #dee2e6;
            margin-bottom: 15px;
            background-color: #f8f9fa;
            border-radius: 0.375rem 0.375rem 0 0;
        }

        .nav-tabs .nav-link {
            border: 1px solid transparent;
            border-top-left-radius: 0.375rem;
            border-top-right-radius: 0.375rem;
            color: #495057;
            font-size: 0.9rem;
            padding: 0.75rem 1.25rem;
            font-weight: 500;
            transition: all 0.2s ease-in-out;
        }

        .nav-tabs .nav-link:hover {
            border-color: #e9ecef #e9ecef #dee2e6;
            isolation: isolate;
            background-color: #e9ecef;
            color: #0d6efd;
        }

        .nav-tabs .nav-link.active {
            color: #0d6efd;
            background-color: #fff;
            border-color: #dee2e6 #dee2e6 #fff;
            font-weight: 600;
        }

        .nav-tabs .nav-link i {
            margin-right: 0.5rem;
        }

        /* Tab content */
        .tab-content {
            background-color: #fff;
            border: 1px solid #dee2e6;
            border-top: none;
            border-radius: 0 0 0.375rem 0.375rem;
            padding: 1rem;
        }

        /* Section header with checkbox */
        .section-header {
            padding: 0.75rem 0;
            border-bottom: 2px solid #e9ecef;
            margin-bottom: 1rem;
            background-color: #f8f9fa;
            border-radius: 0.25rem;
            padding: 0.75rem 1rem;
        }

        .section-header .form-check {
            margin-bottom: 0;
        }

        .section-header .form-check-input {
            margin-top: 0.125rem;
        }

        .section-header .form-check-label {
            font-weight: 600;
            color: #495057;
            font-size: 1rem;
        }

        /* Layers content */
        .layers-content {
            max-height: 350px;
            overflow-y: auto;
            padding-right: 8px;
            transition: all 0.3s ease-in-out;
        }

        .layers-content::-webkit-scrollbar {
            width: 8px;
        }

        .layers-content::-webkit-scrollbar-track {
            background: #f1f1f1;
            border-radius: 4px;
        }

        .layers-content::-webkit-scrollbar-thumb {
            background: #c1c1c1;
            border-radius: 4px;
        }

        .layers-content::-webkit-scrollbar-thumb:hover {
            background: #a8a8a8;
        }

        /* Layer item improvements */
        .layer-item {
            padding: 0.75rem 0;
            border-bottom: 1px solid #f8f9fa;
            display: flex;
            align-items: flex-start;
            gap: 0.75rem;
            transition: background-color 0.2s ease-in-out;
        }

        .layer-item:hover {
            background-color: #f8f9fa;
            border-radius: 0.25rem;
            padding-left: 0.5rem;
            padding-right: 0.5rem;
        }

        .layer-item:last-child {
            border-bottom: none;
        }

        .layer-item input[type="checkbox"],
        .layer-item input[type="radio"] {
            margin-top: 0.125rem;
            transform: scale(1.1);
        }

        .layer-item .layer-info {
            flex: 1;
        }

        .layer-item .layer-name {
            font-weight: 500;
            margin-bottom: 0.25rem;
            color: #212529;
            display: flex;
            align-items: center;
            gap: 0.5rem;
        }

        .layer-item .layer-name i {
            color: #6c757d;
            width: 16px;
        }

        .layer-item .layer-details {
            font-size: 0.8rem;
            color: #6c757d;
            margin-top: 0.25rem;
        }

        .layer-item .layer-type {
            background-color: #e9ecef;
            color: #495057;
            padding: 0.125rem 0.5rem;
            border-radius: 0.25rem;
            font-size: 0.75rem;
            font-weight: 500;
        }

        .layer-item .layer-workspace {
            font-family: 'Courier New', monospace;
            font-size: 0.75rem;
        }

        /* Info button styling */
        .btn-outline-info {
            --bs-btn-padding-y: 0.25rem;
            --bs-btn-padding-x: 0.5rem;
            --bs-btn-font-size: 0.75rem;
            border-radius: 50%;
            width: 28px;
            height: 28px;
            display: inline-flex;
            align-items: center;
            justify-content: center;
        }

        /* Opacity control styling */
        .opacity-control {
            margin-top: 0.5rem;
            padding: 0.5rem;
            background-color: #f8f9fa;
            border-radius: 0.25rem;
            border: 1px solid #e9ecef;
            display: flex;
            align-items: center;
            gap: 0.5rem;
            font-size: 0.8rem;
        }

        .opacity-control input[type="range"] {
            flex: 1;
        }

        .opacity-value {
            font-weight: 500;
            color: #495057;
            min-width: 35px;
        }

        /* Hide content when checkbox is unchecked */
        .layers-content.hidden {
            display: none !important;
        }

        /* Animation for showing/hiding content */
        .layers-content {
            opacity: 1;
            transform: translateY(0);
        }

        .layers-content.hidden {
            opacity: 0;
            transform: translateY(-10px);
        }

        /* Layer control panel styling */
        .layer-control {
            background-color: #fff;
            border: 1px solid #dee2e6;
            border-radius: 0.5rem;
            box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075);
        }

        .layer-control h3 {
            background-color: #f8f9fa;
            margin: 0;
            padding: 1rem;
            border-bottom: 1px solid #dee2e6;
            border-radius: 0.5rem 0.5rem 0 0;
            font-size: 1.1rem;
            font-weight: 600;
            color: #495057;
        }

        .layer-control h3 i {
            margin-right: 0.5rem;
            color: #0d6efd;
        }

        /* Coordinate popup styling */
        .coordinate-popup {
            min-width: 250px;
        }

        .coordinate-popup h6 {
            margin-bottom: 0.75rem;
            color: #495057;
            border-bottom: 1px solid #dee2e6;
            padding-bottom: 0.5rem;
        }

        .coordinate-popup h6 i {
            color: #dc3545;
            margin-right: 0.5rem;
        }

        .coordinate-system {
            margin-bottom: 0.75rem;
            padding: 0.5rem;
            background-color: #f8f9fa;
            border-radius: 0.25rem;
            border-left: 3px solid #0d6efd;
        }

        .coordinate-system:last-child {
            margin-bottom: 0;
        }

        .coordinate-system strong {
            color: #495057;
            display: block;
            margin-bottom: 0.25rem;
        }

        /* Coordinate display control */
        .coordinate-display {
            background: rgba(255, 255, 255, 0.95);
            border: 2px solid rgba(0, 0, 0, 0.2);
            border-radius: 5px;
            padding: 8px 12px;
            font-family: 'Courier New', monospace;
            font-size: 12px;
            box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
            min-width: 280px;
        }

        .coordinate-box {
            display: flex;
            flex-direction: column;
            gap: 4px;
        }

        .coordinate-row {
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .coordinate-label {
            font-weight: bold;
            color: #333;
            min-width: 70px;
        }

        .coordinate-display span:last-child {
            color: #0066cc;
            font-weight: normal;
        }

        /* Transport info popup styling */
        .transport-info {
            min-width: 300px;
            font-size: 14px;
        }

        .transport-info h6 {
            margin-bottom: 0.75rem;
            color: #495057;
            border-bottom: 1px solid #dee2e6;
            padding-bottom: 0.5rem;
        }

        .transport-info h6 i {
            color: #0d6efd;
            margin-right: 0.5rem;
        }

        .info-section {
            margin-bottom: 1rem;
            padding: 0.5rem;
            background-color: #f8f9fa;
            border-radius: 0.25rem;
            border-left: 3px solid #0d6efd;
        }

        .info-section ul {
            margin-bottom: 0.5rem;
            padding-left: 1.2rem;
        }

        .info-section li {
            margin-bottom: 0.25rem;
        }

        .coordinate-info {
            margin-top: 0.75rem;
            padding-top: 0.75rem;
        }

        .coordinate-info h6 {
            font-size: 0.9rem;
            margin-bottom: 0.5rem;
        }

        .coordinate-row {
            font-size: 0.85rem;
            margin-bottom: 0.25rem;
        }

        /* Transport markers styling */
        .transport-marker {
            background: white;
            border: 2px solid #333;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 16px;
            font-weight: bold;
            box-shadow: 0 2px 6px rgba(0, 0, 0, 0.3);
            transition: all 0.3s ease;
        }

        .transport-marker:hover {
            transform: scale(1.1);
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.4);
        }

        .metro-stop {
            background: #0066cc;
            color: white;
            border-color: #004499;
        }

        .bus-stop {
            background: #ff6600;
            color: white;
            border-color: #cc5200;
        }

        .tram-stop {
            background: #009900;
            color: white;
            border-color: #006600;
        }

        /* Transport popup styling */
        .transport-popup .leaflet-popup-content {
            margin: 0;
            padding: 0;
        }

        .transport-stop-info {
            padding: 15px;
            min-width: 300px;
        }

        .stop-title {
            margin: 0 0 10px 0;
            color: #333;
            font-size: 16px;
            font-weight: bold;
            border-bottom: 2px solid #0066cc;
            padding-bottom: 8px;
        }

        .stop-title i {
            color: #0066cc;
            margin-right: 8px;
        }

        .stop-description {
            margin: 10px 0 15px 0;
            color: #666;
            font-size: 14px;
            line-height: 1.4;
        }

        .transport-lines {
            margin-bottom: 12px;
            padding: 8px;
            background: #f8f9fa;
            border-radius: 6px;
            border-left: 4px solid #0066cc;
        }

        .line-type {
            margin-bottom: 6px;
            font-size: 14px;
            color: #333;
        }

        .line-type i {
            margin-right: 6px;
            width: 16px;
        }

        .line-numbers {
            display: flex;
            flex-wrap: wrap;
            gap: 4px;
        }

        .line-badge {
            display: inline-block;
            padding: 3px 8px;
            border-radius: 12px;
            font-size: 12px;
            font-weight: bold;
            color: white;
            min-width: 24px;
            text-align: center;
        }

        .metro-line {
            background: #0066cc;
        }

        .bus-line {
            background: #ff6600;
        }

        .tram-line {
            background: #009900;
        }

        .stop-coordinates {
            margin-top: 12px;
            padding-top: 8px;
            border-top: 1px solid #dee2e6;
            color: #666;
            font-size: 12px;
        }
    </style>
</head>
<body style="margin: 0; padding: 0; height: 100vh; overflow: hidden;">

<div id="map-container" class="map-container">
    <div class="map-header">
        <h1><i class="fas fa-globe"></i> @ViewData["Title"] - КККР</h1>
        <div class="user-info">
            <span><i class="fas fa-user"></i> Добре дошли, @User.Identity?.Name</span>
            <a href="/Admin/Layers" class="btn btn-outline-primary btn-sm me-2">
                <i class="fas fa-cog"></i> Управление
            </a>
            <a href="/Identity/Account/Logout" class="btn btn-outline-danger btn-sm">
                <i class="fas fa-sign-out-alt"></i> Изход
            </a>
        </div>
    </div>

    <div class="map-content">
        <div id="leaflet-map" style="height: 100%; width: 100%;"></div>
        
        <div id="layer-control" class="layer-control">
            <h3><i class="fas fa-layer-group"></i> Слоеве</h3>

            <!-- Tab Navigation -->
            <ul class="nav nav-tabs" id="layerTabs" role="tablist">
                <li class="nav-item" role="presentation">
                    <button class="nav-link active" id="base-tab" data-bs-toggle="tab" data-bs-target="#base-layers" type="button" role="tab">
                        <i class="fas fa-map"></i> Базови карти
                    </button>
                </li>
                <li class="nav-item" role="presentation">
                    <button class="nav-link" id="general-tab" data-bs-toggle="tab" data-bs-target="#general-layers" type="button" role="tab">
                        <i class="fas fa-layers"></i> Общи
                    </button>
                </li>
                <li class="nav-item" role="presentation">
                    <button class="nav-link" id="cadastre-tab" data-bs-toggle="tab" data-bs-target="#cadastre-layers" type="button" role="tab">
                        <i class="fas fa-map-marked-alt"></i> Кадастър
                    </button>
                </li>
            </ul>

            <!-- Tab Content -->
            <div class="tab-content" id="layerTabContent">
                <!-- Base Layers Tab -->
                <div class="tab-pane fade show active" id="base-layers" role="tabpanel">
                    <div class="layer-section">
                        <div class="section-header">
                            <div class="form-check">
                                <input class="form-check-input" type="checkbox" id="toggleBaseLayers" checked onchange="toggleAllBaseLayers(this.checked)">
                                <label class="form-check-label" for="toggleBaseLayers">
                                    <strong>Базови карти</strong>
                                </label>
                            </div>
                        </div>
                        <div id="baseLayersContent" class="layers-content">
                            @if (Model.BaseLayers.Any())
                            {
                                @foreach (var baseLayer in Model.BaseLayers)
                                {
                                    <div class="layer-item" data-layer-id="@baseLayer.Id">
                                        <input type="radio"
                                               name="baseLayer"
                                               value="@baseLayer.Id"
                                               @(baseLayer.Id.ToString() == Model.SelectedBaseLayerId ? "checked" : "")
                                               onchange="toggleBaseLayer('@baseLayer.Id')" />
                                        <div class="layer-info">
                                            <div class="layer-name">
                                                <i class="fas fa-globe-americas"></i> @baseLayer.Name
                                                @if (!string.IsNullOrEmpty(baseLayer.Description))
                                                {
                                                    <button type="button" class="btn btn-sm btn-outline-info ms-2"
                                                            onclick="showLayerInfo('@baseLayer.Id', '@Html.Raw(Html.Encode(baseLayer.Name))', '@Html.Raw(Html.Encode(baseLayer.Description))', '@baseLayer.LayerType', '@baseLayer.Workspace:@baseLayer.LayerName')"
                                                            title="Информация за слоя">
                                                        <i class="fas fa-info-circle"></i>
                                                    </button>
                                                }
                                            </div>
                                        </div>
                                    </div>
                                }
                            }
                        </div>
                    </div>
                </div>

                <!-- General Layers Tab -->
                <div class="tab-pane fade" id="general-layers" role="tabpanel">
                    <div class="layer-section">
                        <div class="section-header">
                            <div class="form-check">
                                <input class="form-check-input" type="checkbox" id="toggleGeneralLayers" checked onchange="toggleAllGeneralLayers(this.checked)">
                                <label class="form-check-label" for="toggleGeneralLayers">
                                    <strong>Общи слоеве</strong>
                                </label>
                            </div>
                        </div>
                        <div id="generalLayersContent" class="layers-content">
                            @if (Model.Layers.Any())
                            {
                                @foreach (var layer in Model.Layers.OrderBy(l => l.DisplayOrder).ThenBy(l => l.Name))
                                {
                                    <div class="layer-item" data-layer-id="@layer.Id">
                                        <input type="checkbox"
                                               @(layer.IsVisible ? "checked" : "")
                                               onchange="toggleLayerVisibility('@layer.Id')" />
                                        <div class="layer-info">
                                            <div class="layer-name">
                                                @if (layer.LayerType == "WMS")
                                                {
                                                    <i class="fas fa-map-marked-alt"></i>
                                                }
                                                else if (layer.LayerType == "WFS")
                                                {
                                                    <i class="fas fa-vector-square"></i>
                                                }
                                                else
                                                {
                                                    <i class="fas fa-layer-group"></i>
                                                }
                                                @layer.Name
                                                @if (!string.IsNullOrEmpty(layer.Description))
                                                {
                                                    <button type="button" class="btn btn-sm btn-outline-info ms-2"
                                                            onclick="showLayerInfo('@layer.Id', '@Html.Raw(Html.Encode(layer.Name))', '@Html.Raw(Html.Encode(layer.Description))', '@layer.LayerType', '@layer.Workspace:@layer.LayerName')"
                                                            title="Информация за слоя">
                                                        <i class="fas fa-info-circle"></i>
                                                    </button>
                                                }
                                            </div>
                                            <div class="layer-details">
                                                <span class="layer-type">@layer.LayerType</span>
                                                @if (!string.IsNullOrEmpty(layer.Workspace))
                                                {
                                                    <span class="layer-workspace">• @layer.Workspace:@layer.LayerName</span>
                                                }
                                            </div>
                                        </div>
                                        <div class="opacity-control" style="display: @(layer.IsVisible ? "flex" : "none")">
                                            <span><i class="fas fa-adjust"></i> Прозрачност:</span>
                                            <input type="range"
                                                   min="0"
                                                   max="1"
                                                   step="0.1"
                                                   value="@(layer.Opacity ?? 1.0)"
                                                   onchange="updateLayerOpacity('@layer.Id', this.value)" />
                                            <span class="opacity-value">@(Math.Round((layer.Opacity ?? 1.0) * 100))%</span>
                                        </div>
                                    </div>
                                }
                            }
                        </div>
                    </div>
                </div>
            </div>

            <!-- Cadastre Layers Tab -->
            <div class="tab-pane fade" id="cadastre-layers" role="tabpanel">
                <div class="layer-section">
                    <div class="section-header">
                        <div class="form-check">
                            <input class="form-check-input" type="checkbox" id="toggleCadastreLayers" checked onchange="toggleAllCadastreLayers(this.checked)">
                            <label class="form-check-label" for="toggleCadastreLayers">
                                <strong>Кадастрални слоеве</strong>
                            </label>
                        </div>
                    </div>
                    <div id="cadastreLayersContent" class="layers-content">
                        @if (Model.Layers.Any(l => l.Workspace.Contains("cadastre")))
                        {
                            @foreach (var layer in Model.Layers.Where(l => l.Workspace.Contains("cadastre")).OrderBy(l => l.DisplayOrder))
                            {
                                <div class="layer-item" data-layer-id="@layer.Id">
                                    <input type="checkbox"
                                           id="<EMAIL>"
                                           @(layer.IsVisible ? "checked" : "")
                                           onchange="toggleLayerVisibility('@layer.Id')" />
                                    <div class="layer-info">
                                        <div class="layer-name">
                                            @layer.Name
                                            @if (!string.IsNullOrEmpty(layer.Description))
                                            {
                                                <button type="button" class="btn btn-sm btn-outline-info ms-2"
                                                        onclick="showLayerInfo('@layer.Id', '@Html.Raw(Html.Encode(layer.Name))', '@Html.Raw(Html.Encode(layer.Description))', '@layer.LayerType', '@layer.Workspace:@layer.LayerName')"
                                                        title="Информация за слоя">
                                                    <i class="fas fa-info-circle"></i>
                                                </button>
                                            }
                                        </div>
                                        <div class="layer-details">
                                            <span class="layer-type">@layer.LayerType</span>
                                            @if (!string.IsNullOrEmpty(layer.Workspace))
                                            {
                                                <span class="layer-workspace">• @layer.Workspace:@layer.LayerName</span>
                                            }
                                        </div>
                                    </div>
                                    <div class="opacity-control" style="display: @(layer.IsVisible ? "flex" : "none")">
                                        <span><i class="fas fa-adjust"></i> Прозрачност:</span>
                                        <input type="range"
                                               min="0"
                                               max="1"
                                               step="0.1"
                                               value="@(layer.Opacity ?? 1.0)"
                                               onchange="updateLayerOpacity('@layer.Id', this.value)" />
                                        <span class="opacity-value">@(Math.Round((layer.Opacity ?? 1.0) * 100))%</span>
                                    </div>
                                </div>
                            }
                        }
                        else
                        {
                            <div class="text-center py-3">
                                <i class="fas fa-map-marked-alt fa-2x text-muted mb-2"></i>
                                <p class="text-muted">Няма налични кадастрални слоеве</p>
                            </div>
                        }
                    </div>
                </div>
            </div>

            @if (!Model.Layers.Any() && !Model.BaseLayers.Any())
            {
                <div class="text-center py-4">
                    <i class="fas fa-info-circle fa-2x text-muted mb-3"></i>
                    <p class="text-muted">Няма налични слоеве</p>
                </div>
            }
        </div>
    </div>
</div>

<script src="~/lib/jquery/dist/jquery.min.js"></script>
<script src="~/lib/bootstrap/dist/js/bootstrap.bundle.min.js"></script>
<script src="https://unpkg.com/leaflet@1.9.4/dist/leaflet.js"></script>
<script src="https://cdnjs.cloudflare.com/ajax/libs/proj4js/2.9.2/proj4.min.js"></script>
<script src="~/js/map.js"></script>

    <script>
        // Initialize map with configuration
        document.addEventListener('DOMContentLoaded', function() {
            // Debug: Log Model.Layers count
            console.log('=== VIEW DEBUG ===');
            console.log('Model.Layers count: @Model.Layers.Count()');
            console.log('Model.BaseLayers count: @Model.BaseLayers.Count()');

            const mapConfig = {
                centerLat: @Model.CenterLatitude,
                centerLng: @Model.CenterLongitude,
                zoomLevel: @Model.ZoomLevel,
                showScaleControl: @Model.ShowScaleControl.ToString().ToLower(),
                hasBaseLayers: @(Model.BaseLayers.Any() ? "true" : "false"),
                layers: [
                    @foreach (var layer in Model.Layers)
                    {
                        <text>
                        {
                            id: '@layer.Id',
                            name: '@Html.Raw(layer.Name)',
                            layerName: '@layer.LayerName',
                            workspace: '@layer.Workspace',
                            wmsUrl: '@Html.Raw(layer.WmsUrl)',
                            layerType: '@layer.LayerType',
                            isVisible: @layer.IsVisible.ToString().ToLower(),
                            opacity: @(layer.Opacity ?? 1.0)
                        },
                        </text>
                    }
                ],
                baseLayers: [
                    @foreach (var baseLayer in Model.BaseLayers)
                    {
                        <text>
                        {
                            id: '@baseLayer.Id',
                            name: '@Html.Raw(baseLayer.Name)',
                            layerName: '@baseLayer.LayerName',
                            workspace: '@baseLayer.Workspace',
                            wmsUrl: '@Html.Raw(baseLayer.WmsUrl)',
                            layerType: '@baseLayer.LayerType',
                            isVisible: @baseLayer.IsVisible.ToString().ToLower(),
                            opacity: @(baseLayer.Opacity ?? 1.0)
                        },
                        </text>
                    }
                ]
            };

            // Initialize the map manager
            window.mapManager = new MapManager(mapConfig);
        });

    </script>

    <!-- Layer Info Modal -->
    <div class="modal fade" id="layerInfoModal" tabindex="-1" aria-labelledby="layerInfoModalLabel" aria-hidden="true">
        <div class="modal-dialog modal-lg">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="layerInfoModalLabel">Информация за слоя</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body">
                    <div class="row">
                        <div class="col-md-12">
                            <h6><i class="fas fa-info-circle"></i> Описание</h6>
                            <p id="layerInfoDescription" class="text-muted"></p>
                        </div>
                    </div>
                    <div class="row">
                        <div class="col-md-6">
                            <h6><i class="fas fa-tag"></i> Тип</h6>
                            <p id="layerInfoType" class="badge bg-primary"></p>
                        </div>
                        <div class="col-md-6">
                            <h6><i class="fas fa-layer-group"></i> Workspace</h6>
                            <p id="layerInfoWorkspace" class="font-monospace"></p>
                        </div>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Затвори</button>
                </div>
            </div>
        </div>
    </div>

    <!-- Movable Info Window -->
    <div id="infoWindow" class="info-window">
        <div class="info-window-header" id="infoWindowHeader">
            <span id="infoWindowTitle">Информация за обект</span>
            <button class="info-window-close" onclick="closeInfoWindow()">&times;</button>
        </div>
        <div class="info-window-content" id="infoWindowContent">
            <div class="loading-spinner">Зареждане...</div>
        </div>
    </div>

</body>
</html>
