@model GeoSpatialDataKRBR.Web.ViewModels.Map.MapViewModel
@{
    ViewData["Title"] = "Геопространствени данни";
    Layout = null;
}

<!DOCTYPE html>
<html lang="bg">
<head>
    <meta charset="utf-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>@ViewData["Title"] - КККР</title>
    <link rel="stylesheet" href="~/lib/bootstrap/dist/css/bootstrap.min.css" />
    <link rel="stylesheet" href="~/css/site.css" asp-append-version="true" />
    <link rel="stylesheet" href="https://unpkg.com/leaflet@1.9.4/dist/leaflet.css" />
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" />
    <style>
        /* Tab styling */
        .nav-tabs {
            border-bottom: 1px solid #dee2e6;
            margin-bottom: 15px;
        }

        .nav-tabs .nav-link {
            border: 1px solid transparent;
            border-top-left-radius: 0.375rem;
            border-top-right-radius: 0.375rem;
            color: #495057;
            font-size: 0.9rem;
            padding: 0.5rem 1rem;
        }

        .nav-tabs .nav-link:hover {
            border-color: #e9ecef #e9ecef #dee2e6;
            isolation: isolate;
        }

        .nav-tabs .nav-link.active {
            color: #495057;
            background-color: #fff;
            border-color: #dee2e6 #dee2e6 #fff;
        }

        /* Section header with checkbox */
        .section-header {
            padding: 10px 0;
            border-bottom: 1px solid #e9ecef;
            margin-bottom: 10px;
        }

        .section-header .form-check {
            margin-bottom: 0;
        }

        .section-header .form-check-label {
            font-weight: 600;
            color: #495057;
        }

        /* Layers content */
        .layers-content {
            max-height: 300px;
            overflow-y: auto;
            padding-right: 5px;
        }

        .layers-content::-webkit-scrollbar {
            width: 6px;
        }

        .layers-content::-webkit-scrollbar-track {
            background: #f1f1f1;
            border-radius: 3px;
        }

        .layers-content::-webkit-scrollbar-thumb {
            background: #c1c1c1;
            border-radius: 3px;
        }

        .layers-content::-webkit-scrollbar-thumb:hover {
            background: #a8a8a8;
        }

        /* Layer item improvements */
        .layer-item {
            padding: 8px 0;
            border-bottom: 1px solid #f8f9fa;
        }

        .layer-item:last-child {
            border-bottom: none;
        }

        .layer-item .layer-name {
            font-weight: 500;
            margin-bottom: 2px;
        }

        .layer-item .layer-details {
            font-size: 0.8rem;
            color: #6c757d;
        }

        /* Hide content when checkbox is unchecked */
        .layers-content.hidden {
            display: none;
        }
    </style>
</head>
<body style="margin: 0; padding: 0; height: 100vh; overflow: hidden;">

<div id="map-container" class="map-container">
    <div class="map-header">
        <h1><i class="fas fa-globe"></i> @ViewData["Title"] - КККР</h1>
        <div class="user-info">
            <span><i class="fas fa-user"></i> Добре дошли, @User.Identity?.Name</span>
            <a href="/Admin/Layers" class="btn btn-outline-primary btn-sm me-2">
                <i class="fas fa-cog"></i> Управление
            </a>
            <a href="/Identity/Account/Logout" class="btn btn-outline-danger btn-sm">
                <i class="fas fa-sign-out-alt"></i> Изход
            </a>
        </div>
    </div>

    <div class="map-content">
        <div id="leaflet-map" style="height: 100%; width: 100%;"></div>
        
        <div id="layer-control" class="layer-control">
            <h3><i class="fas fa-layer-group"></i> Слоеве</h3>

            <!-- Tab Navigation -->
            <ul class="nav nav-tabs" id="layerTabs" role="tablist">
                <li class="nav-item" role="presentation">
                    <button class="nav-link active" id="base-tab" data-bs-toggle="tab" data-bs-target="#base-layers" type="button" role="tab">
                        <i class="fas fa-map"></i> Базови карти
                    </button>
                </li>
                <li class="nav-item" role="presentation">
                    <button class="nav-link" id="general-tab" data-bs-toggle="tab" data-bs-target="#general-layers" type="button" role="tab">
                        <i class="fas fa-layers"></i> Общи
                    </button>
                </li>
            </ul>

            <!-- Tab Content -->
            <div class="tab-content" id="layerTabContent">
                <!-- Base Layers Tab -->
                <div class="tab-pane fade show active" id="base-layers" role="tabpanel">
                    <div class="layer-section">
                        <div class="section-header">
                            <div class="form-check">
                                <input class="form-check-input" type="checkbox" id="toggleBaseLayers" checked onchange="toggleAllBaseLayers(this.checked)">
                                <label class="form-check-label" for="toggleBaseLayers">
                                    <strong>Базови карти</strong>
                                </label>
                            </div>
                        </div>
                        <div id="baseLayersContent" class="layers-content">
                            @if (Model.BaseLayers.Any())
                            {
                                @foreach (var baseLayer in Model.BaseLayers)
                                {
                                    <div class="layer-item" data-layer-id="@baseLayer.Id">
                                        <input type="radio"
                                               name="baseLayer"
                                               value="@baseLayer.Id"
                                               @(baseLayer.Id.ToString() == Model.SelectedBaseLayerId ? "checked" : "")
                                               onchange="toggleBaseLayer('@baseLayer.Id')" />
                                        <div class="layer-info">
                                            <div class="layer-name">
                                                <i class="fas fa-globe-americas"></i> @baseLayer.Name
                                                @if (!string.IsNullOrEmpty(baseLayer.Description))
                                                {
                                                    <button type="button" class="btn btn-sm btn-outline-info ms-2"
                                                            onclick="showLayerInfo('@baseLayer.Id', '@Html.Raw(Html.Encode(baseLayer.Name))', '@Html.Raw(Html.Encode(baseLayer.Description))', '@baseLayer.LayerType', '@baseLayer.Workspace:@baseLayer.LayerName')"
                                                            title="Информация за слоя">
                                                        <i class="fas fa-info-circle"></i>
                                                    </button>
                                                }
                                            </div>
                                        </div>
                                    </div>
                                }
                            }
                        </div>
                    </div>
                </div>

                <!-- General Layers Tab -->
                <div class="tab-pane fade" id="general-layers" role="tabpanel">
                    <div class="layer-section">
                        <div class="section-header">
                            <div class="form-check">
                                <input class="form-check-input" type="checkbox" id="toggleGeneralLayers" checked onchange="toggleAllGeneralLayers(this.checked)">
                                <label class="form-check-label" for="toggleGeneralLayers">
                                    <strong>Общи слоеве</strong>
                                </label>
                            </div>
                        </div>
                        <div id="generalLayersContent" class="layers-content">
                            @if (Model.Layers.Any())
                            {
                                @foreach (var layer in Model.Layers.OrderBy(l => l.DisplayOrder).ThenBy(l => l.Name))
                                {
                                    <div class="layer-item" data-layer-id="@layer.Id">
                                        <input type="checkbox"
                                               @(layer.IsVisible ? "checked" : "")
                                               onchange="toggleLayerVisibility('@layer.Id')" />
                                        <div class="layer-info">
                                            <div class="layer-name">
                                                @if (layer.LayerType == "WMS")
                                                {
                                                    <i class="fas fa-map-marked-alt"></i>
                                                }
                                                else if (layer.LayerType == "WFS")
                                                {
                                                    <i class="fas fa-vector-square"></i>
                                                }
                                                else
                                                {
                                                    <i class="fas fa-layer-group"></i>
                                                }
                                                @layer.Name
                                                @if (!string.IsNullOrEmpty(layer.Description))
                                                {
                                                    <button type="button" class="btn btn-sm btn-outline-info ms-2"
                                                            onclick="showLayerInfo('@layer.Id', '@Html.Raw(Html.Encode(layer.Name))', '@Html.Raw(Html.Encode(layer.Description))', '@layer.LayerType', '@layer.Workspace:@layer.LayerName')"
                                                            title="Информация за слоя">
                                                        <i class="fas fa-info-circle"></i>
                                                    </button>
                                                }
                                            </div>
                                            <div class="layer-details">
                                                <span class="layer-type">@layer.LayerType</span>
                                                @if (!string.IsNullOrEmpty(layer.Workspace))
                                                {
                                                    <span class="layer-workspace">• @layer.Workspace:@layer.LayerName</span>
                                                }
                                            </div>
                                        </div>
                                        <div class="opacity-control" style="display: @(layer.IsVisible ? "flex" : "none")">
                                            <span><i class="fas fa-adjust"></i> Прозрачност:</span>
                                            <input type="range"
                                                   min="0"
                                                   max="1"
                                                   step="0.1"
                                                   value="@(layer.Opacity ?? 1.0)"
                                                   onchange="updateLayerOpacity('@layer.Id', this.value)" />
                                            <span class="opacity-value">@(Math.Round((layer.Opacity ?? 1.0) * 100))%</span>
                                        </div>
                                    </div>
                                }
                            }
                        </div>
                    </div>
                </div>
            </div>

            @if (!Model.Layers.Any() && !Model.BaseLayers.Any())
            {
                <div class="text-center py-4">
                    <i class="fas fa-info-circle fa-2x text-muted mb-3"></i>
                    <p class="text-muted">Няма налични слоеве</p>
                </div>
            }
        </div>
    </div>
</div>

<script src="~/lib/jquery/dist/jquery.min.js"></script>
<script src="~/lib/bootstrap/dist/js/bootstrap.bundle.min.js"></script>
<script src="https://unpkg.com/leaflet@1.9.4/dist/leaflet.js"></script>
    
    <script>
        // Initialize map
        const map = L.map('leaflet-map').setView([@Model.CenterLatitude, @Model.CenterLongitude], @Model.ZoomLevel);
        
        // Add base layer (OpenStreetMap)
        const osmLayer = L.tileLayer('https://{s}.tile.openstreetmap.org/{z}/{x}/{y}.png', {
            attribution: '&copy; <a href="https://www.openstreetmap.org/copyright">OpenStreetMap</a> contributors'
        }).addTo(map);
        
        // Store layer references
        const mapLayers = {};
        
        // Initialize all layers (but don't add them to map yet)
        console.log('Starting layer initialization...');
        @foreach (var layer in Model.Layers)
        {
            if (layer.LayerType == "WMS")
            {
                <text>
                try {
                    console.log('Initializing WMS layer: @Html.Raw(layer.Name)');
                    console.log('WMS URL: @Html.Raw(layer.WmsUrl)');
                    console.log('Workspace:LayerName: @layer.Workspace:@layer.LayerName');
                    console.log('IsVisible: @layer.IsVisible');

                    const layer_@(layer.Id.ToString().Replace("-", "_")) = L.tileLayer.wms('@Html.Raw(layer.WmsUrl)', {
                        layers: '@layer.Workspace:@layer.LayerName',
                        format: 'image/png',
                        transparent: true,
                        opacity: @(layer.Opacity ?? 1.0),
                        attribution: 'Layer: @Html.Raw(layer.Name)',
                        version: '1.1.0',
                        crs: L.CRS.EPSG4326
                    });

                    mapLayers['@layer.Id'] = layer_@(layer.Id.ToString().Replace("-", "_"));
                    console.log('Layer stored in mapLayers with ID: @layer.Id');

                    // Add to map if visible
                    @if (layer.IsVisible)
                    {
                        <text>
                        layer_@(layer.Id.ToString().Replace("-", "_")).addTo(map);
                        console.log('Layer @layer.Name added to map (visible by default)');
                        </text>
                    }

                    // Add error handling
                    layer_@(layer.Id.ToString().Replace("-", "_")).on('tileerror', function(error) {
                        console.error('WMS layer error for @Html.Raw(layer.Name):', error);
                    });

                    layer_@(layer.Id.ToString().Replace("-", "_")).on('tileload', function() {
                        console.log('WMS tile loaded successfully for @Html.Raw(layer.Name)');
                    });

                    layer_@(layer.Id.ToString().Replace("-", "_")).on('loading', function() {
                        console.log('WMS layer loading: @Html.Raw(layer.Name)');
                    });
                } catch (error) {
                    console.error('Error initializing layer @layer.Name:', error);
                }
                </text>
            }
            else if (layer.LayerType == "TILE")
            {
                <text>
                try {
                    console.log('Initializing TILE layer: @Html.Raw(layer.Name)');
                    const layer_@(layer.Id.ToString().Replace("-", "_")) = L.tileLayer('@Html.Raw(layer.WmsUrl)', {
                        opacity: @(layer.Opacity ?? 1.0),
                        attribution: 'Layer: @Html.Raw(layer.Name)'
                    });

                    mapLayers['@layer.Id'] = layer_@(layer.Id.ToString().Replace("-", "_"));

                    // Add to map if visible
                    @if (layer.IsVisible)
                    {
                        <text>layer_@(layer.Id.ToString().Replace("-", "_")).addTo(map);</text>
                    }

                    // Add error handling
                    layer_@(layer.Id.ToString().Replace("-", "_")).on('tileerror', function(error) {
                        console.error('TILE layer error for @Html.Raw(layer.Name):', error);
                    });

                    layer_@(layer.Id.ToString().Replace("-", "_")).on('tileload', function() {
                        console.log('TILE loaded successfully for @Html.Raw(layer.Name)');
                    });
                } catch (error) {
                    console.error('Error initializing layer @layer.Name:', error);
                }
                </text>
            }
        }
        
        // Layer control functions
        function toggleLayerVisibility(layerId, forceState = null) {
            console.log('Toggling layer visibility for:', layerId, 'forceState:', forceState);
            console.log('Available layers in mapLayers:', Object.keys(mapLayers));

            const checkbox = document.querySelector(`input[onchange*="${layerId}"]`);
            const isChecked = forceState !== null ? forceState : (checkbox ? checkbox.checked : false);
            console.log('Checkbox checked:', isChecked);

            fetch(`/api/geolayerapi/toggle-visibility/${layerId}`, {
                method: 'POST',
                credentials: 'include',
                headers: {
                    'Content-Type': 'application/json'
                }
            })
            .then(response => {
                console.log('Response status:', response.status);
                if (response.ok) {
                    // Toggle layer on map immediately
                    const layer = mapLayers[layerId];
                    console.log('Found layer in mapLayers:', layer ? 'YES' : 'NO');
                    if (layer) {
                        if (isChecked) {
                            // Add layer to map
                            layer.addTo(map);
                            console.log('Layer added to map:', layerId);
                        } else {
                            // Remove layer from map
                            map.removeLayer(layer);
                            console.log('Layer removed from map:', layerId);
                        }

                        // Show/hide opacity control
                        const layerItem = document.querySelector(`[data-layer-id="${layerId}"]`);
                        const opacityControl = layerItem?.querySelector('.opacity-control');
                        if (opacityControl) {
                            opacityControl.style.display = isChecked ? 'flex' : 'none';
                        }
                    } else {
                        console.error('Layer not found in mapLayers for ID:', layerId);
                        console.log('Available layer IDs:', Object.keys(mapLayers));
                    }
                } else {
                    // Revert checkbox state on error
                    if (checkbox) {
                        checkbox.checked = !isChecked;
                    }
                    response.text().then(text => {
                        console.error('Error response:', text);
                        alert('Грешка при промяна на видимостта: ' + response.status);
                    });
                }
            })
            .catch(error => {
                // Revert checkbox state on error
                if (checkbox) {
                    checkbox.checked = !isChecked;
                }
                console.error('Error:', error);
                alert('Грешка при свързване със сървъра: ' + error.message);
            });
        }
        
        function updateLayerOpacity(layerId, opacity) {
            fetch(`/api/geolayerapi/update-opacity/${layerId}`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                credentials: 'include',
                body: JSON.stringify(parseFloat(opacity))
            })
            .then(response => {
                if (response.ok) {
                    // Update layer opacity on map
                    if (mapLayers[layerId]) {
                        mapLayers[layerId].setOpacity(parseFloat(opacity));
                    }
                    
                    // Update opacity display
                    const opacityValue = document.querySelector(`[data-layer-id="${layerId}"] .opacity-value`);
                    if (opacityValue) {
                        opacityValue.textContent = Math.round(parseFloat(opacity) * 100) + '%';
                    }
                } else {
                    alert('Грешка при промяна на прозрачността');
                }
            })
            .catch(error => {
                console.error('Error:', error);
                alert('Грешка при свързване със сървъра');
            });
        }
        
        function toggleBaseLayer(layerId, show = true) {
            console.log('Selected base layer:', layerId, 'show:', show);

            // Hide all base layers first
            @foreach (var baseLayer in Model.BaseLayers)
            {
                <text>
                const baseLayer_@(baseLayer.Id.ToString().Replace("-", "_")) = mapLayers['@baseLayer.Id'];
                if (baseLayer_@(baseLayer.Id.ToString().Replace("-", "_")) && map.hasLayer(baseLayer_@(baseLayer.Id.ToString().Replace("-", "_")))) {
                    map.removeLayer(baseLayer_@(baseLayer.Id.ToString().Replace("-", "_")));
                }
                </text>
            }

            // Show selected base layer if show is true
            if (show) {
                const selectedLayer = mapLayers[layerId];
                if (selectedLayer) {
                    selectedLayer.addTo(map);
                    console.log('Base layer switched to:', layerId);
                } else {
                    console.error('Base layer not found:', layerId);
                }
            }
        }
        
        // Add map controls
        if (@Model.ShowScaleControl.ToString().ToLower()) {
            L.control.scale().addTo(map);
        }
        
        // Click handler for feature info
        map.on('click', function(e) {
            console.log('Map clicked at:', e.latlng);
            // TODO: Implement GetFeatureInfo request
        });

        // Layer info function
        function showLayerInfo(layerId, layerName, description, layerType, workspace) {
            // Populate modal
            document.getElementById('layerInfoModalLabel').textContent = layerName;
            document.getElementById('layerInfoDescription').textContent = description;
            document.getElementById('layerInfoType').textContent = layerType;
            document.getElementById('layerInfoWorkspace').textContent = workspace;

            // Show modal
            var modal = new bootstrap.Modal(document.getElementById('layerInfoModal'));
            modal.show();
        }

        // Toggle all base layers visibility
        function toggleAllBaseLayers(show) {
            const content = document.getElementById('baseLayersContent');
            const radioButtons = content.querySelectorAll('input[type="radio"]');

            if (show) {
                content.classList.remove('hidden');
                // Enable radio buttons
                radioButtons.forEach(radio => radio.disabled = false);
            } else {
                content.classList.add('hidden');
                // Disable radio buttons and uncheck them
                radioButtons.forEach(radio => {
                    radio.disabled = true;
                    if (radio.checked) {
                        radio.checked = false;
                        // Hide the base layer
                        toggleBaseLayer(radio.value, false);
                    }
                });
            }
        }

        // Toggle all general layers visibility
        function toggleAllGeneralLayers(show) {
            const content = document.getElementById('generalLayersContent');
            const checkboxes = content.querySelectorAll('input[type="checkbox"]');

            if (show) {
                content.classList.remove('hidden');
                // Enable checkboxes
                checkboxes.forEach(checkbox => checkbox.disabled = false);
            } else {
                content.classList.add('hidden');
                // Disable checkboxes and uncheck them
                checkboxes.forEach(checkbox => {
                    checkbox.disabled = true;
                    if (checkbox.checked) {
                        checkbox.checked = false;
                        // Hide the layer
                        const layerItem = checkbox.closest('.layer-item');
                        const layerId = layerItem.getAttribute('data-layer-id');
                        toggleLayerVisibility(layerId, false);
                    }
                });
            }
        }
    </script>

    <!-- Layer Info Modal -->
    <div class="modal fade" id="layerInfoModal" tabindex="-1" aria-labelledby="layerInfoModalLabel" aria-hidden="true">
        <div class="modal-dialog modal-lg">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="layerInfoModalLabel">Информация за слоя</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body">
                    <div class="row">
                        <div class="col-md-12">
                            <h6><i class="fas fa-info-circle"></i> Описание</h6>
                            <p id="layerInfoDescription" class="text-muted"></p>
                        </div>
                    </div>
                    <div class="row">
                        <div class="col-md-6">
                            <h6><i class="fas fa-tag"></i> Тип</h6>
                            <p id="layerInfoType" class="badge bg-primary"></p>
                        </div>
                        <div class="col-md-6">
                            <h6><i class="fas fa-layer-group"></i> Workspace</h6>
                            <p id="layerInfoWorkspace" class="font-monospace"></p>
                        </div>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Затвори</button>
                </div>
            </div>
        </div>
    </div>

</body>
</html>
