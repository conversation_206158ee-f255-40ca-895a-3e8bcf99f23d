@model GeoSpatialDataKRBR.Web.ViewModels.Map.MapViewModel
@{
    ViewData["Title"] = "Геопространствени данни";
    Layout = null;
}

<!DOCTYPE html>
<html lang="bg">
<head>
    <meta charset="utf-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>@ViewData["Title"] - КККР</title>
    <link rel="stylesheet" href="~/lib/bootstrap/dist/css/bootstrap.min.css" />
    <link rel="stylesheet" href="~/css/site.css" asp-append-version="true" />
    <link rel="stylesheet" href="https://unpkg.com/leaflet@1.9.4/dist/leaflet.css" />
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" />
    <style>
        /* Tab styling */
        .nav-tabs {
            border-bottom: 1px solid #dee2e6;
            margin-bottom: 15px;
            background-color: #f8f9fa;
            border-radius: 0.375rem 0.375rem 0 0;
        }

        .nav-tabs .nav-link {
            border: 1px solid transparent;
            border-top-left-radius: 0.375rem;
            border-top-right-radius: 0.375rem;
            color: #495057;
            font-size: 0.9rem;
            padding: 0.75rem 1.25rem;
            font-weight: 500;
            transition: all 0.2s ease-in-out;
        }

        .nav-tabs .nav-link:hover {
            border-color: #e9ecef #e9ecef #dee2e6;
            isolation: isolate;
            background-color: #e9ecef;
            color: #0d6efd;
        }

        .nav-tabs .nav-link.active {
            color: #0d6efd;
            background-color: #fff;
            border-color: #dee2e6 #dee2e6 #fff;
            font-weight: 600;
        }

        .nav-tabs .nav-link i {
            margin-right: 0.5rem;
        }

        /* Tab content */
        .tab-content {
            background-color: #fff;
            border: 1px solid #dee2e6;
            border-top: none;
            border-radius: 0 0 0.375rem 0.375rem;
            padding: 1rem;
        }

        /* Section header with checkbox */
        .section-header {
            padding: 0.75rem 0;
            border-bottom: 2px solid #e9ecef;
            margin-bottom: 1rem;
            background-color: #f8f9fa;
            border-radius: 0.25rem;
            padding: 0.75rem 1rem;
        }

        .section-header .form-check {
            margin-bottom: 0;
        }

        .section-header .form-check-input {
            margin-top: 0.125rem;
        }

        .section-header .form-check-label {
            font-weight: 600;
            color: #495057;
            font-size: 1rem;
        }

        /* Layers content */
        .layers-content {
            max-height: 350px;
            overflow-y: auto;
            padding-right: 8px;
            transition: all 0.3s ease-in-out;
        }

        .layers-content::-webkit-scrollbar {
            width: 8px;
        }

        .layers-content::-webkit-scrollbar-track {
            background: #f1f1f1;
            border-radius: 4px;
        }

        .layers-content::-webkit-scrollbar-thumb {
            background: #c1c1c1;
            border-radius: 4px;
        }

        .layers-content::-webkit-scrollbar-thumb:hover {
            background: #a8a8a8;
        }

        /* Layer item improvements */
        .layer-item {
            padding: 0.75rem 0;
            border-bottom: 1px solid #f8f9fa;
            display: flex;
            align-items: flex-start;
            gap: 0.75rem;
            transition: background-color 0.2s ease-in-out;
        }

        .layer-item:hover {
            background-color: #f8f9fa;
            border-radius: 0.25rem;
            padding-left: 0.5rem;
            padding-right: 0.5rem;
        }

        .layer-item:last-child {
            border-bottom: none;
        }

        .layer-item input[type="checkbox"],
        .layer-item input[type="radio"] {
            margin-top: 0.125rem;
            transform: scale(1.1);
        }

        .layer-item .layer-info {
            flex: 1;
        }

        .layer-item .layer-name {
            font-weight: 500;
            margin-bottom: 0.25rem;
            color: #212529;
            display: flex;
            align-items: center;
            gap: 0.5rem;
        }

        .layer-item .layer-name i {
            color: #6c757d;
            width: 16px;
        }

        .layer-item .layer-details {
            font-size: 0.8rem;
            color: #6c757d;
            margin-top: 0.25rem;
        }

        .layer-item .layer-type {
            background-color: #e9ecef;
            color: #495057;
            padding: 0.125rem 0.5rem;
            border-radius: 0.25rem;
            font-size: 0.75rem;
            font-weight: 500;
        }

        .layer-item .layer-workspace {
            font-family: 'Courier New', monospace;
            font-size: 0.75rem;
        }

        /* Info button styling */
        .btn-outline-info {
            --bs-btn-padding-y: 0.25rem;
            --bs-btn-padding-x: 0.5rem;
            --bs-btn-font-size: 0.75rem;
            border-radius: 50%;
            width: 28px;
            height: 28px;
            display: inline-flex;
            align-items: center;
            justify-content: center;
        }

        /* Opacity control styling */
        .opacity-control {
            margin-top: 0.5rem;
            padding: 0.5rem;
            background-color: #f8f9fa;
            border-radius: 0.25rem;
            border: 1px solid #e9ecef;
            display: flex;
            align-items: center;
            gap: 0.5rem;
            font-size: 0.8rem;
        }

        .opacity-control input[type="range"] {
            flex: 1;
        }

        .opacity-value {
            font-weight: 500;
            color: #495057;
            min-width: 35px;
        }

        /* Hide content when checkbox is unchecked */
        .layers-content.hidden {
            display: none !important;
        }

        /* Animation for showing/hiding content */
        .layers-content {
            opacity: 1;
            transform: translateY(0);
        }

        .layers-content.hidden {
            opacity: 0;
            transform: translateY(-10px);
        }

        /* Layer control panel styling */
        .layer-control {
            background-color: #fff;
            border: 1px solid #dee2e6;
            border-radius: 0.5rem;
            box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075);
        }

        .layer-control h3 {
            background-color: #f8f9fa;
            margin: 0;
            padding: 1rem;
            border-bottom: 1px solid #dee2e6;
            border-radius: 0.5rem 0.5rem 0 0;
            font-size: 1.1rem;
            font-weight: 600;
            color: #495057;
        }

        .layer-control h3 i {
            margin-right: 0.5rem;
            color: #0d6efd;
        }

        /* Coordinate popup styling */
        .coordinate-popup {
            min-width: 250px;
        }

        .coordinate-popup h6 {
            margin-bottom: 0.75rem;
            color: #495057;
            border-bottom: 1px solid #dee2e6;
            padding-bottom: 0.5rem;
        }

        .coordinate-popup h6 i {
            color: #dc3545;
            margin-right: 0.5rem;
        }

        .coordinate-system {
            margin-bottom: 0.75rem;
            padding: 0.5rem;
            background-color: #f8f9fa;
            border-radius: 0.25rem;
            border-left: 3px solid #0d6efd;
        }

        .coordinate-system:last-child {
            margin-bottom: 0;
        }

        .coordinate-system strong {
            color: #495057;
            display: block;
            margin-bottom: 0.25rem;
        }

        /* Coordinate display control */
        .coordinate-display {
            background: rgba(255, 255, 255, 0.95);
            border: 2px solid rgba(0, 0, 0, 0.2);
            border-radius: 5px;
            padding: 8px 12px;
            font-family: 'Courier New', monospace;
            font-size: 12px;
            box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
            min-width: 280px;
        }

        .coordinate-box {
            display: flex;
            flex-direction: column;
            gap: 4px;
        }

        .coordinate-row {
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .coordinate-label {
            font-weight: bold;
            color: #333;
            min-width: 70px;
        }

        .coordinate-display span:last-child {
            color: #0066cc;
            font-weight: normal;
        }
    </style>
</head>
<body style="margin: 0; padding: 0; height: 100vh; overflow: hidden;">

<div id="map-container" class="map-container">
    <div class="map-header">
        <h1><i class="fas fa-globe"></i> @ViewData["Title"] - КККР</h1>
        <div class="user-info">
            <span><i class="fas fa-user"></i> Добре дошли, @User.Identity?.Name</span>
            <a href="/Admin/Layers" class="btn btn-outline-primary btn-sm me-2">
                <i class="fas fa-cog"></i> Управление
            </a>
            <a href="/Identity/Account/Logout" class="btn btn-outline-danger btn-sm">
                <i class="fas fa-sign-out-alt"></i> Изход
            </a>
        </div>
    </div>

    <div class="map-content">
        <div id="leaflet-map" style="height: 100%; width: 100%;"></div>
        
        <div id="layer-control" class="layer-control">
            <h3><i class="fas fa-layer-group"></i> Слоеве</h3>

            <!-- Tab Navigation -->
            <ul class="nav nav-tabs" id="layerTabs" role="tablist">
                <li class="nav-item" role="presentation">
                    <button class="nav-link active" id="base-tab" data-bs-toggle="tab" data-bs-target="#base-layers" type="button" role="tab">
                        <i class="fas fa-map"></i> Базови карти
                    </button>
                </li>
                <li class="nav-item" role="presentation">
                    <button class="nav-link" id="general-tab" data-bs-toggle="tab" data-bs-target="#general-layers" type="button" role="tab">
                        <i class="fas fa-layers"></i> Общи
                    </button>
                </li>
            </ul>

            <!-- Tab Content -->
            <div class="tab-content" id="layerTabContent">
                <!-- Base Layers Tab -->
                <div class="tab-pane fade show active" id="base-layers" role="tabpanel">
                    <div class="layer-section">
                        <div class="section-header">
                            <div class="form-check">
                                <input class="form-check-input" type="checkbox" id="toggleBaseLayers" checked onchange="toggleAllBaseLayers(this.checked)">
                                <label class="form-check-label" for="toggleBaseLayers">
                                    <strong>Базови карти</strong>
                                </label>
                            </div>
                        </div>
                        <div id="baseLayersContent" class="layers-content">
                            @if (Model.BaseLayers.Any())
                            {
                                @foreach (var baseLayer in Model.BaseLayers)
                                {
                                    <div class="layer-item" data-layer-id="@baseLayer.Id">
                                        <input type="radio"
                                               name="baseLayer"
                                               value="@baseLayer.Id"
                                               @(baseLayer.Id.ToString() == Model.SelectedBaseLayerId ? "checked" : "")
                                               onchange="toggleBaseLayer('@baseLayer.Id')" />
                                        <div class="layer-info">
                                            <div class="layer-name">
                                                <i class="fas fa-globe-americas"></i> @baseLayer.Name
                                                @if (!string.IsNullOrEmpty(baseLayer.Description))
                                                {
                                                    <button type="button" class="btn btn-sm btn-outline-info ms-2"
                                                            onclick="showLayerInfo('@baseLayer.Id', '@Html.Raw(Html.Encode(baseLayer.Name))', '@Html.Raw(Html.Encode(baseLayer.Description))', '@baseLayer.LayerType', '@baseLayer.Workspace:@baseLayer.LayerName')"
                                                            title="Информация за слоя">
                                                        <i class="fas fa-info-circle"></i>
                                                    </button>
                                                }
                                            </div>
                                        </div>
                                    </div>
                                }
                            }
                        </div>
                    </div>
                </div>

                <!-- General Layers Tab -->
                <div class="tab-pane fade" id="general-layers" role="tabpanel">
                    <div class="layer-section">
                        <div class="section-header">
                            <div class="form-check">
                                <input class="form-check-input" type="checkbox" id="toggleGeneralLayers" checked onchange="toggleAllGeneralLayers(this.checked)">
                                <label class="form-check-label" for="toggleGeneralLayers">
                                    <strong>Общи слоеве</strong>
                                </label>
                            </div>
                        </div>
                        <div id="generalLayersContent" class="layers-content">
                            @if (Model.Layers.Any())
                            {
                                @foreach (var layer in Model.Layers.OrderBy(l => l.DisplayOrder).ThenBy(l => l.Name))
                                {
                                    <div class="layer-item" data-layer-id="@layer.Id">
                                        <input type="checkbox"
                                               @(layer.IsVisible ? "checked" : "")
                                               onchange="toggleLayerVisibility('@layer.Id')" />
                                        <div class="layer-info">
                                            <div class="layer-name">
                                                @if (layer.LayerType == "WMS")
                                                {
                                                    <i class="fas fa-map-marked-alt"></i>
                                                }
                                                else if (layer.LayerType == "WFS")
                                                {
                                                    <i class="fas fa-vector-square"></i>
                                                }
                                                else
                                                {
                                                    <i class="fas fa-layer-group"></i>
                                                }
                                                @layer.Name
                                                @if (!string.IsNullOrEmpty(layer.Description))
                                                {
                                                    <button type="button" class="btn btn-sm btn-outline-info ms-2"
                                                            onclick="showLayerInfo('@layer.Id', '@Html.Raw(Html.Encode(layer.Name))', '@Html.Raw(Html.Encode(layer.Description))', '@layer.LayerType', '@layer.Workspace:@layer.LayerName')"
                                                            title="Информация за слоя">
                                                        <i class="fas fa-info-circle"></i>
                                                    </button>
                                                }
                                            </div>
                                            <div class="layer-details">
                                                <span class="layer-type">@layer.LayerType</span>
                                                @if (!string.IsNullOrEmpty(layer.Workspace))
                                                {
                                                    <span class="layer-workspace">• @layer.Workspace:@layer.LayerName</span>
                                                }
                                            </div>
                                        </div>
                                        <div class="opacity-control" style="display: @(layer.IsVisible ? "flex" : "none")">
                                            <span><i class="fas fa-adjust"></i> Прозрачност:</span>
                                            <input type="range"
                                                   min="0"
                                                   max="1"
                                                   step="0.1"
                                                   value="@(layer.Opacity ?? 1.0)"
                                                   onchange="updateLayerOpacity('@layer.Id', this.value)" />
                                            <span class="opacity-value">@(Math.Round((layer.Opacity ?? 1.0) * 100))%</span>
                                        </div>
                                    </div>
                                }
                            }
                        </div>
                    </div>
                </div>
            </div>

            @if (!Model.Layers.Any() && !Model.BaseLayers.Any())
            {
                <div class="text-center py-4">
                    <i class="fas fa-info-circle fa-2x text-muted mb-3"></i>
                    <p class="text-muted">Няма налични слоеве</p>
                </div>
            }
        </div>
    </div>
</div>

<script src="~/lib/jquery/dist/jquery.min.js"></script>
<script src="~/lib/bootstrap/dist/js/bootstrap.bundle.min.js"></script>
<script src="https://unpkg.com/leaflet@1.9.4/dist/leaflet.js"></script>
<script src="https://cdnjs.cloudflare.com/ajax/libs/proj4js/2.9.2/proj4.min.js"></script>
    
    <script>
        // Define Bulgarian coordinate systems
        proj4.defs("EPSG:7801", "+proj=tmerc +lat_0=0 +lon_0=25 +k=0.9999 +x_0=500000 +y_0=0 +ellps=GRS80 +towgs84=0,0,0,0,0,0,0 +units=m +no_defs"); // BGS2005 / UTM zone 35N
        proj4.defs("EPSG:4326", "+proj=longlat +datum=WGS84 +no_defs"); // WGS84

        // Initialize map
        const map = L.map('leaflet-map').setView([@Model.CenterLatitude, @Model.CenterLongitude], @Model.ZoomLevel);

        // Add default base layer (OpenStreetMap) only if no base layers are configured
        let osmLayer = null;
        @if (!Model.BaseLayers.Any())
        {
            <text>
            osmLayer = L.tileLayer('https://{s}.tile.openstreetmap.org/{z}/{x}/{y}.png', {
                attribution: '&copy; <a href="https://www.openstreetmap.org/copyright">OpenStreetMap</a> contributors'
            }).addTo(map);
            </text>
        }
        
        // Store layer references
        const mapLayers = {};
        
        // Initialize all layers (but don't add them to map yet)
        console.log('Starting layer initialization...');

        // Initialize regular layers
        @foreach (var layer in Model.Layers)
        {
            if (layer.LayerType == "WMS")
            {
                <text>
                try {
                    console.log('Initializing WMS layer: @Html.Raw(layer.Name)');
                    console.log('WMS URL: @Html.Raw(layer.WmsUrl)');
                    console.log('Workspace:LayerName: @layer.Workspace:@layer.LayerName');
                    console.log('IsVisible: @layer.IsVisible');

                    const layer_@(layer.Id.ToString().Replace("-", "_")) = L.tileLayer.wms('@Html.Raw(layer.WmsUrl)', {
                        layers: '@layer.Workspace:@layer.LayerName',
                        format: 'image/png',
                        transparent: true,
                        opacity: @(layer.Opacity ?? 1.0),
                        attribution: 'Layer: @Html.Raw(layer.Name)',
                        version: '1.1.0',
                        crs: L.CRS.EPSG4326
                    });

                    mapLayers['@layer.Id'] = layer_@(layer.Id.ToString().Replace("-", "_"));
                    console.log('Layer stored in mapLayers with ID: @layer.Id');

                    // Add to map if visible
                    @if (layer.IsVisible)
                    {
                        <text>
                        layer_@(layer.Id.ToString().Replace("-", "_")).addTo(map);
                        console.log('Layer @layer.Name added to map (visible by default)');
                        </text>
                    }

                    // Add error handling
                    layer_@(layer.Id.ToString().Replace("-", "_")).on('tileerror', function(error) {
                        console.error('WMS layer error for @Html.Raw(layer.Name):', error);
                    });

                    layer_@(layer.Id.ToString().Replace("-", "_")).on('tileload', function() {
                        console.log('WMS tile loaded successfully for @Html.Raw(layer.Name)');
                    });

                    layer_@(layer.Id.ToString().Replace("-", "_")).on('loading', function() {
                        console.log('WMS layer loading: @Html.Raw(layer.Name)');
                    });
                } catch (error) {
                    console.error('Error initializing layer @layer.Name:', error);
                }
                </text>
            }
            else if (layer.LayerType == "TILE")
            {
                <text>
                try {
                    console.log('Initializing TILE layer: @Html.Raw(layer.Name)');
                    const layer_@(layer.Id.ToString().Replace("-", "_")) = L.tileLayer('@Html.Raw(layer.WmsUrl)', {
                        opacity: @(layer.Opacity ?? 1.0),
                        attribution: 'Layer: @Html.Raw(layer.Name)'
                    });

                    mapLayers['@layer.Id'] = layer_@(layer.Id.ToString().Replace("-", "_"));

                    // Add to map if visible
                    @if (layer.IsVisible)
                    {
                        <text>layer_@(layer.Id.ToString().Replace("-", "_")).addTo(map);</text>
                    }

                    // Add error handling
                    layer_@(layer.Id.ToString().Replace("-", "_")).on('tileerror', function(error) {
                        console.error('TILE layer error for @Html.Raw(layer.Name):', error);
                    });

                    layer_@(layer.Id.ToString().Replace("-", "_")).on('tileload', function() {
                        console.log('TILE loaded successfully for @Html.Raw(layer.Name)');
                    });
                } catch (error) {
                    console.error('Error initializing layer @layer.Name:', error);
                }
                </text>
            }
        }

        // Initialize base layers
        console.log('Starting base layer initialization...');
        @foreach (var baseLayer in Model.BaseLayers)
        {
            if (baseLayer.LayerType == "WMS")
            {
                <text>
                try {
                    console.log('Initializing base WMS layer: @Html.Raw(baseLayer.Name)');
                    const baseLayer_@(baseLayer.Id.ToString().Replace("-", "_")) = L.tileLayer.wms('@Html.Raw(baseLayer.WmsUrl)', {
                        layers: '@baseLayer.Workspace:@baseLayer.LayerName',
                        format: 'image/png',
                        transparent: false,
                        opacity: @(baseLayer.Opacity ?? 1.0),
                        attribution: 'Base Layer: @Html.Raw(baseLayer.Name)',
                        version: '1.1.0',
                        crs: L.CRS.EPSG4326
                    });

                    mapLayers['@baseLayer.Id'] = baseLayer_@(baseLayer.Id.ToString().Replace("-", "_"));
                    console.log('Base layer stored in mapLayers with ID: @baseLayer.Id');

                    // Add to map if visible
                    @if (baseLayer.IsVisible)
                    {
                        <text>
                        baseLayer_@(baseLayer.Id.ToString().Replace("-", "_")).addTo(map);
                        console.log('Base layer @baseLayer.Name added to map (visible by default)');
                        </text>
                    }
                } catch (error) {
                    console.error('Error initializing base layer @baseLayer.Name:', error);
                }
                </text>
            }
            else if (baseLayer.LayerType == "TILE")
            {
                <text>
                try {
                    console.log('Initializing base TILE layer: @Html.Raw(baseLayer.Name)');
                    const baseLayer_@(baseLayer.Id.ToString().Replace("-", "_")) = L.tileLayer('@Html.Raw(baseLayer.WmsUrl)', {
                        opacity: @(baseLayer.Opacity ?? 1.0),
                        attribution: 'Base Layer: @Html.Raw(baseLayer.Name)'
                    });

                    mapLayers['@baseLayer.Id'] = baseLayer_@(baseLayer.Id.ToString().Replace("-", "_"));

                    // Add to map if visible
                    @if (baseLayer.IsVisible)
                    {
                        <text>baseLayer_@(baseLayer.Id.ToString().Replace("-", "_")).addTo(map);</text>
                    }
                } catch (error) {
                    console.error('Error initializing base layer @baseLayer.Name:', error);
                }
                </text>
            }
        }
        
        // Layer control functions
        function toggleLayerVisibility(layerId, forceState = null) {
            console.log('Toggling layer visibility for:', layerId, 'forceState:', forceState);
            console.log('Available layers in mapLayers:', Object.keys(mapLayers));

            const checkbox = document.querySelector(`input[onchange*="${layerId}"]`);
            const isChecked = forceState !== null ? forceState : (checkbox ? checkbox.checked : false);
            console.log('Checkbox checked:', isChecked);

            fetch(`/api/geolayerapi/toggle-visibility/${layerId}`, {
                method: 'POST',
                credentials: 'include',
                headers: {
                    'Content-Type': 'application/json'
                }
            })
            .then(response => {
                console.log('Response status:', response.status);
                if (response.ok) {
                    // Toggle layer on map immediately
                    const layer = mapLayers[layerId];
                    console.log('Found layer in mapLayers:', layer ? 'YES' : 'NO');
                    if (layer) {
                        if (isChecked) {
                            // Add layer to map
                            layer.addTo(map);
                            console.log('Layer added to map:', layerId);
                        } else {
                            // Remove layer from map
                            map.removeLayer(layer);
                            console.log('Layer removed from map:', layerId);
                        }

                        // Show/hide opacity control
                        const layerItem = document.querySelector(`[data-layer-id="${layerId}"]`);
                        const opacityControl = layerItem?.querySelector('.opacity-control');
                        if (opacityControl) {
                            opacityControl.style.display = isChecked ? 'flex' : 'none';
                        }
                    } else {
                        console.error('Layer not found in mapLayers for ID:', layerId);
                        console.log('Available layer IDs:', Object.keys(mapLayers));
                    }
                } else {
                    // Revert checkbox state on error
                    if (checkbox) {
                        checkbox.checked = !isChecked;
                    }
                    response.text().then(text => {
                        console.error('Error response:', text);
                        alert('Грешка при промяна на видимостта: ' + response.status);
                    });
                }
            })
            .catch(error => {
                // Revert checkbox state on error
                if (checkbox) {
                    checkbox.checked = !isChecked;
                }
                console.error('Error:', error);
                alert('Грешка при свързване със сървъра: ' + error.message);
            });
        }
        
        function updateLayerOpacity(layerId, opacity) {
            fetch(`/api/geolayerapi/update-opacity/${layerId}`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                credentials: 'include',
                body: JSON.stringify(parseFloat(opacity))
            })
            .then(response => {
                if (response.ok) {
                    // Update layer opacity on map
                    if (mapLayers[layerId]) {
                        mapLayers[layerId].setOpacity(parseFloat(opacity));
                    }
                    
                    // Update opacity display
                    const opacityValue = document.querySelector(`[data-layer-id="${layerId}"] .opacity-value`);
                    if (opacityValue) {
                        opacityValue.textContent = Math.round(parseFloat(opacity) * 100) + '%';
                    }
                } else {
                    alert('Грешка при промяна на прозрачността');
                }
            })
            .catch(error => {
                console.error('Error:', error);
                alert('Грешка при свързване със сървъра');
            });
        }
        
        function toggleBaseLayer(layerId, show = true) {
            console.log('Selected base layer:', layerId, 'show:', show);

            // Hide all base layers first
            @foreach (var baseLayer in Model.BaseLayers)
            {
                <text>
                const baseLayer_@(baseLayer.Id.ToString().Replace("-", "_")) = mapLayers['@baseLayer.Id'];
                if (baseLayer_@(baseLayer.Id.ToString().Replace("-", "_")) && map.hasLayer(baseLayer_@(baseLayer.Id.ToString().Replace("-", "_")))) {
                    map.removeLayer(baseLayer_@(baseLayer.Id.ToString().Replace("-", "_")));
                }
                </text>
            }

            // Show selected base layer if show is true
            if (show) {
                const selectedLayer = mapLayers[layerId];
                if (selectedLayer) {
                    selectedLayer.addTo(map);
                    console.log('Base layer switched to:', layerId);
                } else {
                    console.error('Base layer not found:', layerId);
                }
            }
        }
        
        // Add map controls
        if (@Model.ShowScaleControl.ToString().ToLower()) {
            L.control.scale().addTo(map);
        }

        // Create coordinate display control
        const coordinateControl = L.control({position: 'bottomleft'});
        coordinateControl.onAdd = function(map) {
            const div = L.DomUtil.create('div', 'coordinate-display');
            div.innerHTML = `
                <div class="coordinate-box">
                    <div class="coordinate-row">
                        <span class="coordinate-label">BGS2005:</span>
                        <span id="bgs-coords">X: -, Y: -</span>
                    </div>
                    <div class="coordinate-row">
                        <span class="coordinate-label">WGS84:</span>
                        <span id="wgs-coords">Lat: -, Lng: -</span>
                    </div>
                </div>
            `;
            return div;
        };
        coordinateControl.addTo(map);

        // Mouse move handler for coordinate display
        map.on('mousemove', function(e) {
            // Transform coordinates to BGS2005
            const wgs84 = [e.latlng.lng, e.latlng.lat];
            const bgs2005 = proj4('EPSG:4326', 'EPSG:7801', wgs84);

            // Update coordinate display
            document.getElementById('bgs-coords').textContent =
                `X: ${bgs2005[0].toFixed(2)}, Y: ${bgs2005[1].toFixed(2)}`;
            document.getElementById('wgs-coords').textContent =
                `Lat: ${e.latlng.lat.toFixed(6)}, Lng: ${e.latlng.lng.toFixed(6)}`;
        });

        // Click handler for feature info only
        map.on('click', function(e) {
            console.log('Map clicked at:', e.latlng);
            // TODO: Implement GetFeatureInfo request for active layers
        });

        // Layer info function
        function showLayerInfo(layerId, layerName, description, layerType, workspace) {
            // Populate modal
            document.getElementById('layerInfoModalLabel').textContent = layerName;
            document.getElementById('layerInfoDescription').textContent = description;
            document.getElementById('layerInfoType').textContent = layerType;
            document.getElementById('layerInfoWorkspace').textContent = workspace;

            // Show modal
            var modal = new bootstrap.Modal(document.getElementById('layerInfoModal'));
            modal.show();
        }

        // Toggle all base layers visibility
        function toggleAllBaseLayers(show) {
            const content = document.getElementById('baseLayersContent');
            const radioButtons = content.querySelectorAll('input[type="radio"]');

            if (show) {
                content.classList.remove('hidden');
                // Enable radio buttons
                radioButtons.forEach(radio => radio.disabled = false);
                // Restore the previously selected base layer
                const checkedRadio = document.querySelector('input[name="baseLayer"]:checked');
                if (checkedRadio) {
                    toggleBaseLayer(checkedRadio.value, true);
                }
            } else {
                content.classList.add('hidden');
                // Hide all base layers first
                @foreach (var baseLayer in Model.BaseLayers)
                {
                    <text>
                    const baseLayer_@(baseLayer.Id.ToString().Replace("-", "_")) = mapLayers['@baseLayer.Id'];
                    if (baseLayer_@(baseLayer.Id.ToString().Replace("-", "_")) && map.hasLayer(baseLayer_@(baseLayer.Id.ToString().Replace("-", "_")))) {
                        map.removeLayer(baseLayer_@(baseLayer.Id.ToString().Replace("-", "_")));
                    }
                    </text>
                }
                // Disable radio buttons
                radioButtons.forEach(radio => {
                    radio.disabled = true;
                });
            }
        }

        // Toggle all general layers visibility
        function toggleAllGeneralLayers(show) {
            const content = document.getElementById('generalLayersContent');
            const checkboxes = content.querySelectorAll('input[type="checkbox"]');

            if (show) {
                content.classList.remove('hidden');
                // Enable checkboxes
                checkboxes.forEach(checkbox => checkbox.disabled = false);
            } else {
                content.classList.add('hidden');
                // Disable checkboxes and uncheck them
                checkboxes.forEach(checkbox => {
                    checkbox.disabled = true;
                    if (checkbox.checked) {
                        checkbox.checked = false;
                        // Hide the layer
                        const layerItem = checkbox.closest('.layer-item');
                        const layerId = layerItem.getAttribute('data-layer-id');
                        toggleLayerVisibility(layerId, false);
                    }
                });
            }
        }
    </script>

    <!-- Layer Info Modal -->
    <div class="modal fade" id="layerInfoModal" tabindex="-1" aria-labelledby="layerInfoModalLabel" aria-hidden="true">
        <div class="modal-dialog modal-lg">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="layerInfoModalLabel">Информация за слоя</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body">
                    <div class="row">
                        <div class="col-md-12">
                            <h6><i class="fas fa-info-circle"></i> Описание</h6>
                            <p id="layerInfoDescription" class="text-muted"></p>
                        </div>
                    </div>
                    <div class="row">
                        <div class="col-md-6">
                            <h6><i class="fas fa-tag"></i> Тип</h6>
                            <p id="layerInfoType" class="badge bg-primary"></p>
                        </div>
                        <div class="col-md-6">
                            <h6><i class="fas fa-layer-group"></i> Workspace</h6>
                            <p id="layerInfoWorkspace" class="font-monospace"></p>
                        </div>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Затвори</button>
                </div>
            </div>
        </div>
    </div>

</body>
</html>
