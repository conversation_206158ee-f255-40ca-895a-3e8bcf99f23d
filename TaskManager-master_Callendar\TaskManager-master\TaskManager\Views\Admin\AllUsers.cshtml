﻿@model IEnumerable<UserViewModel>;

@{
    ViewData["Title"] = "Всички потребители";
}

<div class="admin-page-container">
    <div class="admin-page-header">
        <div class="page-header-content">
            <a href="@Url.Action("AdminPanel", "Admin")" class="back-btn">
                <i class="fas fa-arrow-left"></i>
                Назад към панела
            </a>
            <h1 class="page-title">
                <i class="fas fa-users"></i>
                Всички потребители
            </h1>
            <p class="page-subtitle">Управление и преглед на всички регистрирани потребители</p>
        </div>
    </div>

    <div class="admin-content">
        <div class="users-table-container">
            <div class="table-header">
                <h3>
                    <i class="fas fa-list"></i>
                    Списък с потребители (@Model.Count())
                </h3>
            </div>

            <div class="modern-table-wrapper">
                <table class="modern-table">
                    <thead>
                        <tr>
                            <th>
                                <i class="fas fa-user"></i>
                                Име
                            </th>
                            <th>
                                <i class="fas fa-envelope"></i>
                                Имейл
                            </th>
                            <th>
                                <i class="fas fa-cogs"></i>
                                Действия
                            </th>
                        </tr>
                    </thead>
                    <tbody>
                        @foreach (UserViewModel user in Model)
                        {
                            <tr class="table-row">
                                <td class="user-name">
                                    <div class="user-avatar">
                                        <i class="fas fa-user-circle"></i>
                                    </div>
                                    <span>@($"{user.FirstName} {user.LastName}")</span>
                                </td>
                                <td class="user-email">@user.Email</td>
                                <td class="user-actions">
                                    <a class="action-btn edit" asp-controller="Admin" asp-action="UsersEdit" asp-route-id="@user.Id">
                                        <i class="fas fa-edit"></i>
                                        Редактирай
                                    </a>
                                </td>
                            </tr>
                        }
                    </tbody>
                </table>
            </div>
        </div>
    </div>
</div>