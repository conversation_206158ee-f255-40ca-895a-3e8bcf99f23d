{"runtimeTarget": {"name": ".NETCoreApp,Version=v6.0", "signature": ""}, "compilationOptions": {}, "targets": {".NETCoreApp,Version=v6.0": {"TaskManager/1.0.0": {"dependencies": {"Microsoft.AspNetCore.Diagnostics.EntityFrameworkCore": "6.0.10", "Microsoft.AspNetCore.Identity.EntityFrameworkCore": "6.0.10", "Microsoft.AspNetCore.Identity.UI": "6.0.10", "Microsoft.EntityFrameworkCore.Design": "6.0.10", "Microsoft.EntityFrameworkCore.SqlServer": "6.0.10", "Microsoft.EntityFrameworkCore.Tools": "6.0.10", "Microsoft.VisualStudio.Web.CodeGeneration.Design": "6.0.16", "TaskManager.Common": "1.0.0", "TaskManager.Data": "1.0.0", "TaskManager.Data.Models": "1.0.0", "TaskManager.Services.Data": "1.0.0", "TaskManager.Web.Infrastructure": "1.0.0", "TaskManager.Web.ViewModels": "1.0.0"}, "runtime": {"TaskManager.dll": {}}}, "Humanizer/2.14.1": {"dependencies": {"Humanizer.Core.af": "2.14.1", "Humanizer.Core.ar": "2.14.1", "Humanizer.Core.az": "2.14.1", "Humanizer.Core.bg": "2.14.1", "Humanizer.Core.bn-BD": "2.14.1", "Humanizer.Core.cs": "2.14.1", "Humanizer.Core.da": "2.14.1", "Humanizer.Core.de": "2.14.1", "Humanizer.Core.el": "2.14.1", "Humanizer.Core.es": "2.14.1", "Humanizer.Core.fa": "2.14.1", "Humanizer.Core.fi-FI": "2.14.1", "Humanizer.Core.fr": "2.14.1", "Humanizer.Core.fr-BE": "2.14.1", "Humanizer.Core.he": "2.14.1", "Humanizer.Core.hr": "2.14.1", "Humanizer.Core.hu": "2.14.1", "Humanizer.Core.hy": "2.14.1", "Humanizer.Core.id": "2.14.1", "Humanizer.Core.is": "2.14.1", "Humanizer.Core.it": "2.14.1", "Humanizer.Core.ja": "2.14.1", "Humanizer.Core.ko-KR": "2.14.1", "Humanizer.Core.ku": "2.14.1", "Humanizer.Core.lv": "2.14.1", "Humanizer.Core.ms-MY": "2.14.1", "Humanizer.Core.mt": "2.14.1", "Humanizer.Core.nb": "2.14.1", "Humanizer.Core.nb-NO": "2.14.1", "Humanizer.Core.nl": "2.14.1", "Humanizer.Core.pl": "2.14.1", "Humanizer.Core.pt": "2.14.1", "Humanizer.Core.ro": "2.14.1", "Humanizer.Core.ru": "2.14.1", "Humanizer.Core.sk": "2.14.1", "Humanizer.Core.sl": "2.14.1", "Humanizer.Core.sr": "2.14.1", "Humanizer.Core.sr-Latn": "2.14.1", "Humanizer.Core.sv": "2.14.1", "Humanizer.Core.th-TH": "2.14.1", "Humanizer.Core.tr": "2.14.1", "Humanizer.Core.uk": "2.14.1", "Humanizer.Core.uz-Cyrl-UZ": "2.14.1", "Humanizer.Core.uz-Latn-UZ": "2.14.1", "Humanizer.Core.vi": "2.14.1", "Humanizer.Core.zh-CN": "2.14.1", "Humanizer.Core.zh-Hans": "2.14.1", "Humanizer.Core.zh-Hant": "2.14.1"}}, "Humanizer.Core/2.14.1": {"runtime": {"lib/net6.0/Humanizer.dll": {"assemblyVersion": "2.14.0.0", "fileVersion": "2.14.1.48190"}}}, "Humanizer.Core.af/2.14.1": {"dependencies": {"Humanizer.Core": "2.14.1"}, "resources": {"lib/net6.0/af/Humanizer.resources.dll": {"locale": "af"}}}, "Humanizer.Core.ar/2.14.1": {"dependencies": {"Humanizer.Core": "2.14.1"}, "resources": {"lib/net6.0/ar/Humanizer.resources.dll": {"locale": "ar"}}}, "Humanizer.Core.az/2.14.1": {"dependencies": {"Humanizer.Core": "2.14.1"}, "resources": {"lib/net6.0/az/Humanizer.resources.dll": {"locale": "az"}}}, "Humanizer.Core.bg/2.14.1": {"dependencies": {"Humanizer.Core": "2.14.1"}, "resources": {"lib/net6.0/bg/Humanizer.resources.dll": {"locale": "bg"}}}, "Humanizer.Core.bn-BD/2.14.1": {"dependencies": {"Humanizer.Core": "2.14.1"}, "resources": {"lib/net6.0/bn-BD/Humanizer.resources.dll": {"locale": "bn-BD"}}}, "Humanizer.Core.cs/2.14.1": {"dependencies": {"Humanizer.Core": "2.14.1"}, "resources": {"lib/net6.0/cs/Humanizer.resources.dll": {"locale": "cs"}}}, "Humanizer.Core.da/2.14.1": {"dependencies": {"Humanizer.Core": "2.14.1"}, "resources": {"lib/net6.0/da/Humanizer.resources.dll": {"locale": "da"}}}, "Humanizer.Core.de/2.14.1": {"dependencies": {"Humanizer.Core": "2.14.1"}, "resources": {"lib/net6.0/de/Humanizer.resources.dll": {"locale": "de"}}}, "Humanizer.Core.el/2.14.1": {"dependencies": {"Humanizer.Core": "2.14.1"}, "resources": {"lib/net6.0/el/Humanizer.resources.dll": {"locale": "el"}}}, "Humanizer.Core.es/2.14.1": {"dependencies": {"Humanizer.Core": "2.14.1"}, "resources": {"lib/net6.0/es/Humanizer.resources.dll": {"locale": "es"}}}, "Humanizer.Core.fa/2.14.1": {"dependencies": {"Humanizer.Core": "2.14.1"}, "resources": {"lib/net6.0/fa/Humanizer.resources.dll": {"locale": "fa"}}}, "Humanizer.Core.fi-FI/2.14.1": {"dependencies": {"Humanizer.Core": "2.14.1"}, "resources": {"lib/net6.0/fi-FI/Humanizer.resources.dll": {"locale": "fi-FI"}}}, "Humanizer.Core.fr/2.14.1": {"dependencies": {"Humanizer.Core": "2.14.1"}, "resources": {"lib/net6.0/fr/Humanizer.resources.dll": {"locale": "fr"}}}, "Humanizer.Core.fr-BE/2.14.1": {"dependencies": {"Humanizer.Core": "2.14.1"}, "resources": {"lib/net6.0/fr-BE/Humanizer.resources.dll": {"locale": "fr-BE"}}}, "Humanizer.Core.he/2.14.1": {"dependencies": {"Humanizer.Core": "2.14.1"}, "resources": {"lib/net6.0/he/Humanizer.resources.dll": {"locale": "he"}}}, "Humanizer.Core.hr/2.14.1": {"dependencies": {"Humanizer.Core": "2.14.1"}, "resources": {"lib/net6.0/hr/Humanizer.resources.dll": {"locale": "hr"}}}, "Humanizer.Core.hu/2.14.1": {"dependencies": {"Humanizer.Core": "2.14.1"}, "resources": {"lib/net6.0/hu/Humanizer.resources.dll": {"locale": "hu"}}}, "Humanizer.Core.hy/2.14.1": {"dependencies": {"Humanizer.Core": "2.14.1"}, "resources": {"lib/net6.0/hy/Humanizer.resources.dll": {"locale": "hy"}}}, "Humanizer.Core.id/2.14.1": {"dependencies": {"Humanizer.Core": "2.14.1"}, "resources": {"lib/net6.0/id/Humanizer.resources.dll": {"locale": "id"}}}, "Humanizer.Core.is/2.14.1": {"dependencies": {"Humanizer.Core": "2.14.1"}, "resources": {"lib/net6.0/is/Humanizer.resources.dll": {"locale": "is"}}}, "Humanizer.Core.it/2.14.1": {"dependencies": {"Humanizer.Core": "2.14.1"}, "resources": {"lib/net6.0/it/Humanizer.resources.dll": {"locale": "it"}}}, "Humanizer.Core.ja/2.14.1": {"dependencies": {"Humanizer.Core": "2.14.1"}, "resources": {"lib/net6.0/ja/Humanizer.resources.dll": {"locale": "ja"}}}, "Humanizer.Core.ko-KR/2.14.1": {"dependencies": {"Humanizer.Core": "2.14.1"}, "resources": {"lib/netstandard2.0/ko-KR/Humanizer.resources.dll": {"locale": "ko-KR"}}}, "Humanizer.Core.ku/2.14.1": {"dependencies": {"Humanizer.Core": "2.14.1"}, "resources": {"lib/net6.0/ku/Humanizer.resources.dll": {"locale": "ku"}}}, "Humanizer.Core.lv/2.14.1": {"dependencies": {"Humanizer.Core": "2.14.1"}, "resources": {"lib/net6.0/lv/Humanizer.resources.dll": {"locale": "lv"}}}, "Humanizer.Core.ms-MY/2.14.1": {"dependencies": {"Humanizer.Core": "2.14.1"}, "resources": {"lib/netstandard2.0/ms-MY/Humanizer.resources.dll": {"locale": "ms-MY"}}}, "Humanizer.Core.mt/2.14.1": {"dependencies": {"Humanizer.Core": "2.14.1"}, "resources": {"lib/netstandard2.0/mt/Humanizer.resources.dll": {"locale": "mt"}}}, "Humanizer.Core.nb/2.14.1": {"dependencies": {"Humanizer.Core": "2.14.1"}, "resources": {"lib/net6.0/nb/Humanizer.resources.dll": {"locale": "nb"}}}, "Humanizer.Core.nb-NO/2.14.1": {"dependencies": {"Humanizer.Core": "2.14.1"}, "resources": {"lib/net6.0/nb-NO/Humanizer.resources.dll": {"locale": "nb-NO"}}}, "Humanizer.Core.nl/2.14.1": {"dependencies": {"Humanizer.Core": "2.14.1"}, "resources": {"lib/net6.0/nl/Humanizer.resources.dll": {"locale": "nl"}}}, "Humanizer.Core.pl/2.14.1": {"dependencies": {"Humanizer.Core": "2.14.1"}, "resources": {"lib/net6.0/pl/Humanizer.resources.dll": {"locale": "pl"}}}, "Humanizer.Core.pt/2.14.1": {"dependencies": {"Humanizer.Core": "2.14.1"}, "resources": {"lib/net6.0/pt/Humanizer.resources.dll": {"locale": "pt"}}}, "Humanizer.Core.ro/2.14.1": {"dependencies": {"Humanizer.Core": "2.14.1"}, "resources": {"lib/net6.0/ro/Humanizer.resources.dll": {"locale": "ro"}}}, "Humanizer.Core.ru/2.14.1": {"dependencies": {"Humanizer.Core": "2.14.1"}, "resources": {"lib/net6.0/ru/Humanizer.resources.dll": {"locale": "ru"}}}, "Humanizer.Core.sk/2.14.1": {"dependencies": {"Humanizer.Core": "2.14.1"}, "resources": {"lib/net6.0/sk/Humanizer.resources.dll": {"locale": "sk"}}}, "Humanizer.Core.sl/2.14.1": {"dependencies": {"Humanizer.Core": "2.14.1"}, "resources": {"lib/net6.0/sl/Humanizer.resources.dll": {"locale": "sl"}}}, "Humanizer.Core.sr/2.14.1": {"dependencies": {"Humanizer.Core": "2.14.1"}, "resources": {"lib/net6.0/sr/Humanizer.resources.dll": {"locale": "sr"}}}, "Humanizer.Core.sr-Latn/2.14.1": {"dependencies": {"Humanizer.Core": "2.14.1"}, "resources": {"lib/net6.0/sr-Latn/Humanizer.resources.dll": {"locale": "sr-Latn"}}}, "Humanizer.Core.sv/2.14.1": {"dependencies": {"Humanizer.Core": "2.14.1"}, "resources": {"lib/net6.0/sv/Humanizer.resources.dll": {"locale": "sv"}}}, "Humanizer.Core.th-TH/2.14.1": {"dependencies": {"Humanizer.Core": "2.14.1"}, "resources": {"lib/netstandard2.0/th-TH/Humanizer.resources.dll": {"locale": "th-TH"}}}, "Humanizer.Core.tr/2.14.1": {"dependencies": {"Humanizer.Core": "2.14.1"}, "resources": {"lib/net6.0/tr/Humanizer.resources.dll": {"locale": "tr"}}}, "Humanizer.Core.uk/2.14.1": {"dependencies": {"Humanizer.Core": "2.14.1"}, "resources": {"lib/net6.0/uk/Humanizer.resources.dll": {"locale": "uk"}}}, "Humanizer.Core.uz-Cyrl-UZ/2.14.1": {"dependencies": {"Humanizer.Core": "2.14.1"}, "resources": {"lib/net6.0/uz-Cyrl-UZ/Humanizer.resources.dll": {"locale": "uz-Cyrl-UZ"}}}, "Humanizer.Core.uz-Latn-UZ/2.14.1": {"dependencies": {"Humanizer.Core": "2.14.1"}, "resources": {"lib/net6.0/uz-Latn-UZ/Humanizer.resources.dll": {"locale": "uz-Latn-UZ"}}}, "Humanizer.Core.vi/2.14.1": {"dependencies": {"Humanizer.Core": "2.14.1"}, "resources": {"lib/net6.0/vi/Humanizer.resources.dll": {"locale": "vi"}}}, "Humanizer.Core.zh-CN/2.14.1": {"dependencies": {"Humanizer.Core": "2.14.1"}, "resources": {"lib/net6.0/zh-CN/Humanizer.resources.dll": {"locale": "zh-CN"}}}, "Humanizer.Core.zh-Hans/2.14.1": {"dependencies": {"Humanizer.Core": "2.14.1"}, "resources": {"lib/net6.0/zh-Hans/Humanizer.resources.dll": {"locale": "zh-Hans"}}}, "Humanizer.Core.zh-Hant/2.14.1": {"dependencies": {"Humanizer.Core": "2.14.1"}, "resources": {"lib/net6.0/zh-Hant/Humanizer.resources.dll": {"locale": "zh-Han<PERSON>"}}}, "MessagePack/2.1.152": {"dependencies": {"MessagePack.Annotations": "2.1.152", "Microsoft.Bcl.AsyncInterfaces": "5.0.0", "System.Memory": "4.5.5", "System.Reflection.Emit": "4.6.0", "System.Reflection.Emit.Lightweight": "4.6.0", "System.Runtime.CompilerServices.Unsafe": "6.0.0", "System.Threading.Tasks.Extensions": "4.5.4"}, "runtime": {"lib/netcoreapp2.1/MessagePack.dll": {"assemblyVersion": "*******", "fileVersion": "2.1.152.29568"}}}, "MessagePack.Annotations/2.1.152": {"runtime": {"lib/netstandard2.0/MessagePack.Annotations.dll": {"assemblyVersion": "*******", "fileVersion": "2.1.152.29568"}}}, "MessagePackAnalyzer/2.1.152": {}, "Microsoft.AspNetCore.Authentication.Abstractions/2.2.0": {"dependencies": {"Microsoft.AspNetCore.Http.Abstractions": "2.2.0", "Microsoft.Extensions.Logging.Abstractions": "6.0.0", "Microsoft.Extensions.Options": "6.0.0"}}, "Microsoft.AspNetCore.Authentication.Core/2.2.0": {"dependencies": {"Microsoft.AspNetCore.Authentication.Abstractions": "2.2.0", "Microsoft.AspNetCore.Http": "2.2.0", "Microsoft.AspNetCore.Http.Extensions": "2.2.0"}}, "Microsoft.AspNetCore.Authorization/2.2.0": {"dependencies": {"Microsoft.Extensions.Logging.Abstractions": "6.0.0", "Microsoft.Extensions.Options": "6.0.0"}}, "Microsoft.AspNetCore.Authorization.Policy/2.2.0": {"dependencies": {"Microsoft.AspNetCore.Authentication.Abstractions": "2.2.0", "Microsoft.AspNetCore.Authorization": "2.2.0"}}, "Microsoft.AspNetCore.Cryptography.Internal/6.0.10": {"runtime": {"lib/net6.0/Microsoft.AspNetCore.Cryptography.Internal.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.1022.47617"}}}, "Microsoft.AspNetCore.Cryptography.KeyDerivation/6.0.10": {"dependencies": {"Microsoft.AspNetCore.Cryptography.Internal": "6.0.10"}, "runtime": {"lib/net6.0/Microsoft.AspNetCore.Cryptography.KeyDerivation.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.1022.47617"}}}, "Microsoft.AspNetCore.Diagnostics.EntityFrameworkCore/6.0.10": {"dependencies": {"Microsoft.EntityFrameworkCore.Relational": "6.0.10"}, "runtime": {"lib/net6.0/Microsoft.AspNetCore.Diagnostics.EntityFrameworkCore.dll": {"assemblyVersion": "********", "fileVersion": "6.0.1022.47617"}}}, "Microsoft.AspNetCore.Hosting.Abstractions/2.2.0": {"dependencies": {"Microsoft.AspNetCore.Hosting.Server.Abstractions": "2.2.0", "Microsoft.AspNetCore.Http.Abstractions": "2.2.0", "Microsoft.Extensions.Hosting.Abstractions": "2.2.0"}}, "Microsoft.AspNetCore.Hosting.Server.Abstractions/2.2.0": {"dependencies": {"Microsoft.AspNetCore.Http.Features": "2.2.0", "Microsoft.Extensions.Configuration.Abstractions": "6.0.0"}}, "Microsoft.AspNetCore.Http/2.2.0": {"dependencies": {"Microsoft.AspNetCore.Http.Abstractions": "2.2.0", "Microsoft.AspNetCore.WebUtilities": "2.2.0", "Microsoft.Extensions.ObjectPool": "2.2.0", "Microsoft.Extensions.Options": "6.0.0", "Microsoft.Net.Http.Headers": "2.2.0"}}, "Microsoft.AspNetCore.Http.Abstractions/2.2.0": {"dependencies": {"Microsoft.AspNetCore.Http.Features": "2.2.0", "System.Text.Encodings.Web": "6.0.0"}}, "Microsoft.AspNetCore.Http.Extensions/2.2.0": {"dependencies": {"Microsoft.AspNetCore.Http.Abstractions": "2.2.0", "Microsoft.Extensions.FileProviders.Abstractions": "6.0.0", "Microsoft.Net.Http.Headers": "2.2.0", "System.Buffers": "4.5.0"}}, "Microsoft.AspNetCore.Http.Features/2.2.0": {"dependencies": {"Microsoft.Extensions.Primitives": "6.0.0"}}, "Microsoft.AspNetCore.Identity.EntityFrameworkCore/6.0.10": {"dependencies": {"Microsoft.EntityFrameworkCore.Relational": "6.0.10", "Microsoft.Extensions.Identity.Stores": "6.0.10"}, "runtime": {"lib/net6.0/Microsoft.AspNetCore.Identity.EntityFrameworkCore.dll": {"assemblyVersion": "********", "fileVersion": "6.0.1022.47617"}}}, "Microsoft.AspNetCore.Identity.UI/6.0.10": {"dependencies": {"Microsoft.Extensions.FileProviders.Embedded": "6.0.10", "Microsoft.Extensions.Identity.Stores": "6.0.10"}, "runtime": {"lib/net6.0/Microsoft.AspNetCore.Identity.UI.dll": {"assemblyVersion": "********", "fileVersion": "6.0.1022.47617"}}}, "Microsoft.AspNetCore.Mvc.Abstractions/2.2.0": {"dependencies": {"Microsoft.AspNetCore.Routing.Abstractions": "2.2.0", "Microsoft.Net.Http.Headers": "2.2.0"}}, "Microsoft.AspNetCore.Mvc.Core/2.2.5": {"dependencies": {"Microsoft.AspNetCore.Authentication.Core": "2.2.0", "Microsoft.AspNetCore.Authorization.Policy": "2.2.0", "Microsoft.AspNetCore.Hosting.Abstractions": "2.2.0", "Microsoft.AspNetCore.Http": "2.2.0", "Microsoft.AspNetCore.Http.Extensions": "2.2.0", "Microsoft.AspNetCore.Mvc.Abstractions": "2.2.0", "Microsoft.AspNetCore.ResponseCaching.Abstractions": "2.2.0", "Microsoft.AspNetCore.Routing": "2.2.0", "Microsoft.AspNetCore.Routing.Abstractions": "2.2.0", "Microsoft.Extensions.DependencyInjection": "6.0.1", "Microsoft.Extensions.DependencyModel": "2.1.0", "Microsoft.Extensions.FileProviders.Abstractions": "6.0.0", "Microsoft.Extensions.Logging.Abstractions": "6.0.0", "System.Diagnostics.DiagnosticSource": "6.0.0", "System.Threading.Tasks.Extensions": "4.5.4"}}, "Microsoft.AspNetCore.Razor.Language/6.0.0": {"runtime": {"lib/netstandard2.0/Microsoft.AspNetCore.Razor.Language.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.21.52608"}}}, "Microsoft.AspNetCore.ResponseCaching.Abstractions/2.2.0": {"dependencies": {"Microsoft.Extensions.Primitives": "6.0.0"}}, "Microsoft.AspNetCore.Routing/2.2.0": {"dependencies": {"Microsoft.AspNetCore.Http.Extensions": "2.2.0", "Microsoft.AspNetCore.Routing.Abstractions": "2.2.0", "Microsoft.Extensions.Logging.Abstractions": "6.0.0", "Microsoft.Extensions.ObjectPool": "2.2.0", "Microsoft.Extensions.Options": "6.0.0"}}, "Microsoft.AspNetCore.Routing.Abstractions/2.2.0": {"dependencies": {"Microsoft.AspNetCore.Http.Abstractions": "2.2.0"}}, "Microsoft.AspNetCore.WebUtilities/2.2.0": {"dependencies": {"Microsoft.Net.Http.Headers": "2.2.0", "System.Text.Encodings.Web": "6.0.0"}}, "Microsoft.Bcl.AsyncInterfaces/5.0.0": {"runtime": {"lib/netstandard2.1/Microsoft.Bcl.AsyncInterfaces.dll": {"assemblyVersion": "*******", "fileVersion": "5.0.20.51904"}}}, "Microsoft.Build/17.3.2": {"dependencies": {"Microsoft.Build.Framework": "17.3.2", "Microsoft.NET.StringTools": "17.3.2", "System.Collections.Immutable": "6.0.0", "System.Configuration.ConfigurationManager": "6.0.0", "System.Reflection.Metadata": "6.0.0", "System.Reflection.MetadataLoadContext": "6.0.0", "System.Security.Principal.Windows": "5.0.0", "System.Text.Encoding.CodePages": "6.0.0", "System.Text.Json": "6.0.0", "System.Threading.Tasks.Dataflow": "6.0.0"}, "runtime": {"lib/net6.0/Microsoft.Build.dll": {"assemblyVersion": "********", "fileVersion": "17.3.2.46306"}}}, "Microsoft.Build.Framework/17.3.2": {"dependencies": {"System.Security.Permissions": "6.0.0"}, "runtime": {"lib/net6.0/Microsoft.Build.Framework.dll": {"assemblyVersion": "********", "fileVersion": "17.3.2.46306"}}}, "Microsoft.CodeAnalysis.Analyzers/3.3.2": {}, "Microsoft.CodeAnalysis.AnalyzerUtilities/3.3.0": {"runtime": {"lib/netstandard2.0/Microsoft.CodeAnalysis.AnalyzerUtilities.dll": {"assemblyVersion": "3.3.2.30504", "fileVersion": "3.3.2.30504"}}}, "Microsoft.CodeAnalysis.Common/4.0.0": {"dependencies": {"Microsoft.CodeAnalysis.Analyzers": "3.3.2", "System.Collections.Immutable": "6.0.0", "System.Memory": "4.5.5", "System.Reflection.Metadata": "6.0.0", "System.Runtime.CompilerServices.Unsafe": "6.0.0", "System.Text.Encoding.CodePages": "6.0.0", "System.Threading.Tasks.Extensions": "4.5.4"}, "runtime": {"lib/netcoreapp3.1/Microsoft.CodeAnalysis.dll": {"assemblyVersion": "*******", "fileVersion": "4.0.21.51404"}}, "resources": {"lib/netcoreapp3.1/cs/Microsoft.CodeAnalysis.resources.dll": {"locale": "cs"}, "lib/netcoreapp3.1/de/Microsoft.CodeAnalysis.resources.dll": {"locale": "de"}, "lib/netcoreapp3.1/es/Microsoft.CodeAnalysis.resources.dll": {"locale": "es"}, "lib/netcoreapp3.1/fr/Microsoft.CodeAnalysis.resources.dll": {"locale": "fr"}, "lib/netcoreapp3.1/it/Microsoft.CodeAnalysis.resources.dll": {"locale": "it"}, "lib/netcoreapp3.1/ja/Microsoft.CodeAnalysis.resources.dll": {"locale": "ja"}, "lib/netcoreapp3.1/ko/Microsoft.CodeAnalysis.resources.dll": {"locale": "ko"}, "lib/netcoreapp3.1/pl/Microsoft.CodeAnalysis.resources.dll": {"locale": "pl"}, "lib/netcoreapp3.1/pt-BR/Microsoft.CodeAnalysis.resources.dll": {"locale": "pt-BR"}, "lib/netcoreapp3.1/ru/Microsoft.CodeAnalysis.resources.dll": {"locale": "ru"}, "lib/netcoreapp3.1/tr/Microsoft.CodeAnalysis.resources.dll": {"locale": "tr"}, "lib/netcoreapp3.1/zh-Hans/Microsoft.CodeAnalysis.resources.dll": {"locale": "zh-Hans"}, "lib/netcoreapp3.1/zh-Hant/Microsoft.CodeAnalysis.resources.dll": {"locale": "zh-Han<PERSON>"}}}, "Microsoft.CodeAnalysis.CSharp/4.0.0": {"dependencies": {"Microsoft.CodeAnalysis.Common": "4.0.0"}, "runtime": {"lib/netcoreapp3.1/Microsoft.CodeAnalysis.CSharp.dll": {"assemblyVersion": "*******", "fileVersion": "4.0.21.51404"}}, "resources": {"lib/netcoreapp3.1/cs/Microsoft.CodeAnalysis.CSharp.resources.dll": {"locale": "cs"}, "lib/netcoreapp3.1/de/Microsoft.CodeAnalysis.CSharp.resources.dll": {"locale": "de"}, "lib/netcoreapp3.1/es/Microsoft.CodeAnalysis.CSharp.resources.dll": {"locale": "es"}, "lib/netcoreapp3.1/fr/Microsoft.CodeAnalysis.CSharp.resources.dll": {"locale": "fr"}, "lib/netcoreapp3.1/it/Microsoft.CodeAnalysis.CSharp.resources.dll": {"locale": "it"}, "lib/netcoreapp3.1/ja/Microsoft.CodeAnalysis.CSharp.resources.dll": {"locale": "ja"}, "lib/netcoreapp3.1/ko/Microsoft.CodeAnalysis.CSharp.resources.dll": {"locale": "ko"}, "lib/netcoreapp3.1/pl/Microsoft.CodeAnalysis.CSharp.resources.dll": {"locale": "pl"}, "lib/netcoreapp3.1/pt-BR/Microsoft.CodeAnalysis.CSharp.resources.dll": {"locale": "pt-BR"}, "lib/netcoreapp3.1/ru/Microsoft.CodeAnalysis.CSharp.resources.dll": {"locale": "ru"}, "lib/netcoreapp3.1/tr/Microsoft.CodeAnalysis.CSharp.resources.dll": {"locale": "tr"}, "lib/netcoreapp3.1/zh-Hans/Microsoft.CodeAnalysis.CSharp.resources.dll": {"locale": "zh-Hans"}, "lib/netcoreapp3.1/zh-Hant/Microsoft.CodeAnalysis.CSharp.resources.dll": {"locale": "zh-Han<PERSON>"}}}, "Microsoft.CodeAnalysis.CSharp.Features/4.0.0": {"dependencies": {"Humanizer.Core": "2.14.1", "Microsoft.CodeAnalysis.CSharp": "4.0.0", "Microsoft.CodeAnalysis.CSharp.Workspaces": "4.0.0", "Microsoft.CodeAnalysis.Common": "4.0.0", "Microsoft.CodeAnalysis.Features": "4.0.0", "Microsoft.CodeAnalysis.Workspaces.Common": "4.0.0"}, "runtime": {"lib/netcoreapp3.1/Microsoft.CodeAnalysis.CSharp.Features.dll": {"assemblyVersion": "*******", "fileVersion": "4.0.21.51404"}}, "resources": {"lib/netcoreapp3.1/cs/Microsoft.CodeAnalysis.CSharp.Features.resources.dll": {"locale": "cs"}, "lib/netcoreapp3.1/de/Microsoft.CodeAnalysis.CSharp.Features.resources.dll": {"locale": "de"}, "lib/netcoreapp3.1/es/Microsoft.CodeAnalysis.CSharp.Features.resources.dll": {"locale": "es"}, "lib/netcoreapp3.1/fr/Microsoft.CodeAnalysis.CSharp.Features.resources.dll": {"locale": "fr"}, "lib/netcoreapp3.1/it/Microsoft.CodeAnalysis.CSharp.Features.resources.dll": {"locale": "it"}, "lib/netcoreapp3.1/ja/Microsoft.CodeAnalysis.CSharp.Features.resources.dll": {"locale": "ja"}, "lib/netcoreapp3.1/ko/Microsoft.CodeAnalysis.CSharp.Features.resources.dll": {"locale": "ko"}, "lib/netcoreapp3.1/pl/Microsoft.CodeAnalysis.CSharp.Features.resources.dll": {"locale": "pl"}, "lib/netcoreapp3.1/pt-BR/Microsoft.CodeAnalysis.CSharp.Features.resources.dll": {"locale": "pt-BR"}, "lib/netcoreapp3.1/ru/Microsoft.CodeAnalysis.CSharp.Features.resources.dll": {"locale": "ru"}, "lib/netcoreapp3.1/tr/Microsoft.CodeAnalysis.CSharp.Features.resources.dll": {"locale": "tr"}, "lib/netcoreapp3.1/zh-Hans/Microsoft.CodeAnalysis.CSharp.Features.resources.dll": {"locale": "zh-Hans"}, "lib/netcoreapp3.1/zh-Hant/Microsoft.CodeAnalysis.CSharp.Features.resources.dll": {"locale": "zh-Han<PERSON>"}}}, "Microsoft.CodeAnalysis.CSharp.Workspaces/4.0.0": {"dependencies": {"Humanizer.Core": "2.14.1", "Microsoft.CodeAnalysis.CSharp": "4.0.0", "Microsoft.CodeAnalysis.Common": "4.0.0", "Microsoft.CodeAnalysis.Workspaces.Common": "4.0.0"}, "runtime": {"lib/netcoreapp3.1/Microsoft.CodeAnalysis.CSharp.Workspaces.dll": {"assemblyVersion": "*******", "fileVersion": "4.0.21.51404"}}, "resources": {"lib/netcoreapp3.1/cs/Microsoft.CodeAnalysis.CSharp.Workspaces.resources.dll": {"locale": "cs"}, "lib/netcoreapp3.1/de/Microsoft.CodeAnalysis.CSharp.Workspaces.resources.dll": {"locale": "de"}, "lib/netcoreapp3.1/es/Microsoft.CodeAnalysis.CSharp.Workspaces.resources.dll": {"locale": "es"}, "lib/netcoreapp3.1/fr/Microsoft.CodeAnalysis.CSharp.Workspaces.resources.dll": {"locale": "fr"}, "lib/netcoreapp3.1/it/Microsoft.CodeAnalysis.CSharp.Workspaces.resources.dll": {"locale": "it"}, "lib/netcoreapp3.1/ja/Microsoft.CodeAnalysis.CSharp.Workspaces.resources.dll": {"locale": "ja"}, "lib/netcoreapp3.1/ko/Microsoft.CodeAnalysis.CSharp.Workspaces.resources.dll": {"locale": "ko"}, "lib/netcoreapp3.1/pl/Microsoft.CodeAnalysis.CSharp.Workspaces.resources.dll": {"locale": "pl"}, "lib/netcoreapp3.1/pt-BR/Microsoft.CodeAnalysis.CSharp.Workspaces.resources.dll": {"locale": "pt-BR"}, "lib/netcoreapp3.1/ru/Microsoft.CodeAnalysis.CSharp.Workspaces.resources.dll": {"locale": "ru"}, "lib/netcoreapp3.1/tr/Microsoft.CodeAnalysis.CSharp.Workspaces.resources.dll": {"locale": "tr"}, "lib/netcoreapp3.1/zh-Hans/Microsoft.CodeAnalysis.CSharp.Workspaces.resources.dll": {"locale": "zh-Hans"}, "lib/netcoreapp3.1/zh-Hant/Microsoft.CodeAnalysis.CSharp.Workspaces.resources.dll": {"locale": "zh-Han<PERSON>"}}}, "Microsoft.CodeAnalysis.Features/4.0.0": {"dependencies": {"Microsoft.CodeAnalysis.AnalyzerUtilities": "3.3.0", "Microsoft.CodeAnalysis.Common": "4.0.0", "Microsoft.CodeAnalysis.Scripting.Common": "4.0.0", "Microsoft.CodeAnalysis.Workspaces.Common": "4.0.0", "Microsoft.DiaSymReader": "1.3.0", "Microsoft.VisualStudio.Debugger.Contracts": "17.2.0", "System.Threading.Tasks.Extensions": "4.5.4"}, "runtime": {"lib/netcoreapp3.1/Microsoft.CodeAnalysis.Features.dll": {"assemblyVersion": "*******", "fileVersion": "4.0.21.51404"}}, "resources": {"lib/netcoreapp3.1/cs/Microsoft.CodeAnalysis.Features.resources.dll": {"locale": "cs"}, "lib/netcoreapp3.1/de/Microsoft.CodeAnalysis.Features.resources.dll": {"locale": "de"}, "lib/netcoreapp3.1/es/Microsoft.CodeAnalysis.Features.resources.dll": {"locale": "es"}, "lib/netcoreapp3.1/fr/Microsoft.CodeAnalysis.Features.resources.dll": {"locale": "fr"}, "lib/netcoreapp3.1/it/Microsoft.CodeAnalysis.Features.resources.dll": {"locale": "it"}, "lib/netcoreapp3.1/ja/Microsoft.CodeAnalysis.Features.resources.dll": {"locale": "ja"}, "lib/netcoreapp3.1/ko/Microsoft.CodeAnalysis.Features.resources.dll": {"locale": "ko"}, "lib/netcoreapp3.1/pl/Microsoft.CodeAnalysis.Features.resources.dll": {"locale": "pl"}, "lib/netcoreapp3.1/pt-BR/Microsoft.CodeAnalysis.Features.resources.dll": {"locale": "pt-BR"}, "lib/netcoreapp3.1/ru/Microsoft.CodeAnalysis.Features.resources.dll": {"locale": "ru"}, "lib/netcoreapp3.1/tr/Microsoft.CodeAnalysis.Features.resources.dll": {"locale": "tr"}, "lib/netcoreapp3.1/zh-Hans/Microsoft.CodeAnalysis.Features.resources.dll": {"locale": "zh-Hans"}, "lib/netcoreapp3.1/zh-Hant/Microsoft.CodeAnalysis.Features.resources.dll": {"locale": "zh-Han<PERSON>"}}}, "Microsoft.CodeAnalysis.Razor/6.0.0": {"dependencies": {"Microsoft.AspNetCore.Razor.Language": "6.0.0", "Microsoft.CodeAnalysis.CSharp": "4.0.0", "Microsoft.CodeAnalysis.Common": "4.0.0"}, "runtime": {"lib/netstandard2.0/Microsoft.CodeAnalysis.Razor.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.21.52608"}}}, "Microsoft.CodeAnalysis.Scripting.Common/4.0.0": {"dependencies": {"Microsoft.CodeAnalysis.Common": "4.0.0"}, "runtime": {"lib/netcoreapp3.1/Microsoft.CodeAnalysis.Scripting.dll": {"assemblyVersion": "*******", "fileVersion": "4.0.21.51404"}}, "resources": {"lib/netcoreapp3.1/cs/Microsoft.CodeAnalysis.Scripting.resources.dll": {"locale": "cs"}, "lib/netcoreapp3.1/de/Microsoft.CodeAnalysis.Scripting.resources.dll": {"locale": "de"}, "lib/netcoreapp3.1/es/Microsoft.CodeAnalysis.Scripting.resources.dll": {"locale": "es"}, "lib/netcoreapp3.1/fr/Microsoft.CodeAnalysis.Scripting.resources.dll": {"locale": "fr"}, "lib/netcoreapp3.1/it/Microsoft.CodeAnalysis.Scripting.resources.dll": {"locale": "it"}, "lib/netcoreapp3.1/ja/Microsoft.CodeAnalysis.Scripting.resources.dll": {"locale": "ja"}, "lib/netcoreapp3.1/ko/Microsoft.CodeAnalysis.Scripting.resources.dll": {"locale": "ko"}, "lib/netcoreapp3.1/pl/Microsoft.CodeAnalysis.Scripting.resources.dll": {"locale": "pl"}, "lib/netcoreapp3.1/pt-BR/Microsoft.CodeAnalysis.Scripting.resources.dll": {"locale": "pt-BR"}, "lib/netcoreapp3.1/ru/Microsoft.CodeAnalysis.Scripting.resources.dll": {"locale": "ru"}, "lib/netcoreapp3.1/tr/Microsoft.CodeAnalysis.Scripting.resources.dll": {"locale": "tr"}, "lib/netcoreapp3.1/zh-Hans/Microsoft.CodeAnalysis.Scripting.resources.dll": {"locale": "zh-Hans"}, "lib/netcoreapp3.1/zh-Hant/Microsoft.CodeAnalysis.Scripting.resources.dll": {"locale": "zh-Han<PERSON>"}}}, "Microsoft.CodeAnalysis.Workspaces.Common/4.0.0": {"dependencies": {"Humanizer.Core": "2.14.1", "Microsoft.Bcl.AsyncInterfaces": "5.0.0", "Microsoft.CodeAnalysis.Common": "4.0.0", "System.Composition": "1.0.31", "System.IO.Pipelines": "5.0.1"}, "runtime": {"lib/netcoreapp3.1/Microsoft.CodeAnalysis.Workspaces.dll": {"assemblyVersion": "*******", "fileVersion": "4.0.21.51404"}}, "resources": {"lib/netcoreapp3.1/cs/Microsoft.CodeAnalysis.Workspaces.resources.dll": {"locale": "cs"}, "lib/netcoreapp3.1/de/Microsoft.CodeAnalysis.Workspaces.resources.dll": {"locale": "de"}, "lib/netcoreapp3.1/es/Microsoft.CodeAnalysis.Workspaces.resources.dll": {"locale": "es"}, "lib/netcoreapp3.1/fr/Microsoft.CodeAnalysis.Workspaces.resources.dll": {"locale": "fr"}, "lib/netcoreapp3.1/it/Microsoft.CodeAnalysis.Workspaces.resources.dll": {"locale": "it"}, "lib/netcoreapp3.1/ja/Microsoft.CodeAnalysis.Workspaces.resources.dll": {"locale": "ja"}, "lib/netcoreapp3.1/ko/Microsoft.CodeAnalysis.Workspaces.resources.dll": {"locale": "ko"}, "lib/netcoreapp3.1/pl/Microsoft.CodeAnalysis.Workspaces.resources.dll": {"locale": "pl"}, "lib/netcoreapp3.1/pt-BR/Microsoft.CodeAnalysis.Workspaces.resources.dll": {"locale": "pt-BR"}, "lib/netcoreapp3.1/ru/Microsoft.CodeAnalysis.Workspaces.resources.dll": {"locale": "ru"}, "lib/netcoreapp3.1/tr/Microsoft.CodeAnalysis.Workspaces.resources.dll": {"locale": "tr"}, "lib/netcoreapp3.1/zh-Hans/Microsoft.CodeAnalysis.Workspaces.resources.dll": {"locale": "zh-Hans"}, "lib/netcoreapp3.1/zh-Hant/Microsoft.CodeAnalysis.Workspaces.resources.dll": {"locale": "zh-Han<PERSON>"}}}, "Microsoft.CSharp/4.5.0": {}, "Microsoft.Data.SqlClient/2.1.4": {"dependencies": {"Microsoft.Data.SqlClient.SNI.runtime": "2.1.1", "Microsoft.Identity.Client": "4.21.1", "Microsoft.IdentityModel.JsonWebTokens": "6.8.0", "Microsoft.IdentityModel.Protocols.OpenIdConnect": "6.8.0", "Microsoft.Win32.Registry": "4.7.0", "System.Configuration.ConfigurationManager": "6.0.0", "System.Diagnostics.DiagnosticSource": "6.0.0", "System.Runtime.Caching": "4.7.0", "System.Security.Principal.Windows": "5.0.0", "System.Text.Encoding.CodePages": "6.0.0"}, "runtime": {"lib/netcoreapp3.1/Microsoft.Data.SqlClient.dll": {"assemblyVersion": "2.0.20168.4", "fileVersion": "2.0.20168.4"}}, "runtimeTargets": {"runtimes/unix/lib/netcoreapp3.1/Microsoft.Data.SqlClient.dll": {"rid": "unix", "assetType": "runtime", "assemblyVersion": "2.0.20168.4", "fileVersion": "2.0.20168.4"}, "runtimes/win/lib/netcoreapp3.1/Microsoft.Data.SqlClient.dll": {"rid": "win", "assetType": "runtime", "assemblyVersion": "2.0.20168.4", "fileVersion": "2.0.20168.4"}}}, "Microsoft.Data.SqlClient.SNI.runtime/2.1.1": {"runtimeTargets": {"runtimes/win-arm/native/Microsoft.Data.SqlClient.SNI.dll": {"rid": "win-arm", "assetType": "native", "fileVersion": "*******"}, "runtimes/win-arm64/native/Microsoft.Data.SqlClient.SNI.dll": {"rid": "win-arm64", "assetType": "native", "fileVersion": "*******"}, "runtimes/win-x64/native/Microsoft.Data.SqlClient.SNI.dll": {"rid": "win-x64", "assetType": "native", "fileVersion": "*******"}, "runtimes/win-x86/native/Microsoft.Data.SqlClient.SNI.dll": {"rid": "win-x86", "assetType": "native", "fileVersion": "*******"}}}, "Microsoft.DiaSymReader/1.3.0": {"dependencies": {"NETStandard.Library": "1.6.1"}, "runtime": {"lib/netstandard1.1/Microsoft.DiaSymReader.dll": {"assemblyVersion": "1.3.0.0", "fileVersion": "1.3.0.63011"}}}, "Microsoft.DotNet.PlatformAbstractions/2.1.0": {"dependencies": {"System.AppContext": "4.3.0", "System.Collections": "4.3.0", "System.IO": "4.3.0", "System.IO.FileSystem": "4.3.0", "System.Reflection.TypeExtensions": "4.3.0", "System.Runtime.Extensions": "4.3.0", "System.Runtime.InteropServices": "4.3.0", "System.Runtime.InteropServices.RuntimeInformation": "4.3.0"}, "runtime": {"lib/netstandard1.3/Microsoft.DotNet.PlatformAbstractions.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "Microsoft.DotNet.Scaffolding.Shared/6.0.16": {"dependencies": {"Humanizer": "2.14.1", "Microsoft.CodeAnalysis.CSharp.Features": "4.0.0", "Newtonsoft.Json": "13.0.1", "NuGet.ProjectModel": "6.6.1"}, "runtime": {"lib/net6.0/Microsoft.DotNet.Scaffolding.Shared.dll": {"assemblyVersion": "********", "fileVersion": "6.0.1623.40301"}}}, "Microsoft.EntityFrameworkCore/6.0.10": {"dependencies": {"Microsoft.EntityFrameworkCore.Abstractions": "6.0.10", "Microsoft.EntityFrameworkCore.Analyzers": "6.0.10", "Microsoft.Extensions.Caching.Memory": "6.0.1", "Microsoft.Extensions.DependencyInjection": "6.0.1", "Microsoft.Extensions.Logging": "6.0.0", "System.Collections.Immutable": "6.0.0", "System.Diagnostics.DiagnosticSource": "6.0.0"}, "runtime": {"lib/net6.0/Microsoft.EntityFrameworkCore.dll": {"assemblyVersion": "********", "fileVersion": "6.0.1022.47607"}}}, "Microsoft.EntityFrameworkCore.Abstractions/6.0.10": {"runtime": {"lib/net6.0/Microsoft.EntityFrameworkCore.Abstractions.dll": {"assemblyVersion": "********", "fileVersion": "6.0.1022.47607"}}}, "Microsoft.EntityFrameworkCore.Analyzers/6.0.10": {}, "Microsoft.EntityFrameworkCore.Design/6.0.10": {"dependencies": {"Humanizer.Core": "2.14.1", "Microsoft.EntityFrameworkCore.Relational": "6.0.10"}, "runtime": {"lib/net6.0/Microsoft.EntityFrameworkCore.Design.dll": {"assemblyVersion": "********", "fileVersion": "6.0.1022.47607"}}}, "Microsoft.EntityFrameworkCore.Relational/6.0.10": {"dependencies": {"Microsoft.EntityFrameworkCore": "6.0.10", "Microsoft.Extensions.Configuration.Abstractions": "6.0.0"}, "runtime": {"lib/net6.0/Microsoft.EntityFrameworkCore.Relational.dll": {"assemblyVersion": "********", "fileVersion": "6.0.1022.47607"}}}, "Microsoft.EntityFrameworkCore.SqlServer/6.0.10": {"dependencies": {"Microsoft.Data.SqlClient": "2.1.4", "Microsoft.EntityFrameworkCore.Relational": "6.0.10"}, "runtime": {"lib/net6.0/Microsoft.EntityFrameworkCore.SqlServer.dll": {"assemblyVersion": "********", "fileVersion": "6.0.1022.47607"}}}, "Microsoft.EntityFrameworkCore.Tools/6.0.10": {"dependencies": {"Microsoft.EntityFrameworkCore.Design": "6.0.10"}}, "Microsoft.Extensions.Caching.Abstractions/6.0.0": {"dependencies": {"Microsoft.Extensions.Primitives": "6.0.0"}}, "Microsoft.Extensions.Caching.Memory/6.0.1": {"dependencies": {"Microsoft.Extensions.Caching.Abstractions": "6.0.0", "Microsoft.Extensions.DependencyInjection.Abstractions": "6.0.0", "Microsoft.Extensions.Logging.Abstractions": "6.0.0", "Microsoft.Extensions.Options": "6.0.0", "Microsoft.Extensions.Primitives": "6.0.0"}, "runtime": {"lib/netstandard2.0/Microsoft.Extensions.Caching.Memory.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.222.6406"}}}, "Microsoft.Extensions.Configuration.Abstractions/6.0.0": {"dependencies": {"Microsoft.Extensions.Primitives": "6.0.0"}}, "Microsoft.Extensions.DependencyInjection/6.0.1": {"dependencies": {"Microsoft.Extensions.DependencyInjection.Abstractions": "6.0.0", "System.Runtime.CompilerServices.Unsafe": "6.0.0"}, "runtime": {"lib/net6.0/Microsoft.Extensions.DependencyInjection.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.1022.47605"}}}, "Microsoft.Extensions.DependencyInjection.Abstractions/6.0.0": {}, "Microsoft.Extensions.DependencyModel/2.1.0": {"dependencies": {"Microsoft.DotNet.PlatformAbstractions": "2.1.0", "Newtonsoft.Json": "13.0.1", "System.Diagnostics.Debug": "4.3.0", "System.Dynamic.Runtime": "4.0.11", "System.Linq": "4.3.0"}, "runtime": {"lib/netstandard1.6/Microsoft.Extensions.DependencyModel.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "Microsoft.Extensions.FileProviders.Abstractions/6.0.0": {"dependencies": {"Microsoft.Extensions.Primitives": "6.0.0"}}, "Microsoft.Extensions.FileProviders.Embedded/6.0.10": {"dependencies": {"Microsoft.Extensions.FileProviders.Abstractions": "6.0.0"}, "runtime": {"lib/net6.0/Microsoft.Extensions.FileProviders.Embedded.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.1022.47617"}}}, "Microsoft.Extensions.Hosting.Abstractions/2.2.0": {"dependencies": {"Microsoft.Extensions.Configuration.Abstractions": "6.0.0", "Microsoft.Extensions.DependencyInjection.Abstractions": "6.0.0", "Microsoft.Extensions.FileProviders.Abstractions": "6.0.0", "Microsoft.Extensions.Logging.Abstractions": "6.0.0"}}, "Microsoft.Extensions.Identity.Core/6.0.10": {"dependencies": {"Microsoft.AspNetCore.Cryptography.KeyDerivation": "6.0.10", "Microsoft.Extensions.Logging": "6.0.0", "Microsoft.Extensions.Options": "6.0.0"}, "runtime": {"lib/net6.0/Microsoft.Extensions.Identity.Core.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.1022.47617"}}}, "Microsoft.Extensions.Identity.Stores/6.0.10": {"dependencies": {"Microsoft.Extensions.Caching.Abstractions": "6.0.0", "Microsoft.Extensions.Identity.Core": "6.0.10", "Microsoft.Extensions.Logging": "6.0.0"}, "runtime": {"lib/net6.0/Microsoft.Extensions.Identity.Stores.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.1022.47617"}}}, "Microsoft.Extensions.Logging/6.0.0": {"dependencies": {"Microsoft.Extensions.DependencyInjection": "6.0.1", "Microsoft.Extensions.DependencyInjection.Abstractions": "6.0.0", "Microsoft.Extensions.Logging.Abstractions": "6.0.0", "Microsoft.Extensions.Options": "6.0.0", "System.Diagnostics.DiagnosticSource": "6.0.0"}}, "Microsoft.Extensions.Logging.Abstractions/6.0.0": {}, "Microsoft.Extensions.ObjectPool/2.2.0": {}, "Microsoft.Extensions.Options/6.0.0": {"dependencies": {"Microsoft.Extensions.DependencyInjection.Abstractions": "6.0.0", "Microsoft.Extensions.Primitives": "6.0.0"}}, "Microsoft.Extensions.Primitives/6.0.0": {"dependencies": {"System.Runtime.CompilerServices.Unsafe": "6.0.0"}}, "Microsoft.Identity.Client/4.21.1": {"runtime": {"lib/netcoreapp2.1/Microsoft.Identity.Client.dll": {"assemblyVersion": "4.21.1.0", "fileVersion": "4.21.1.0"}}}, "Microsoft.IdentityModel.JsonWebTokens/6.8.0": {"dependencies": {"Microsoft.IdentityModel.Tokens": "6.8.0"}, "runtime": {"lib/netstandard2.0/Microsoft.IdentityModel.JsonWebTokens.dll": {"assemblyVersion": "6.8.0.0", "fileVersion": "6.8.0.11012"}}}, "Microsoft.IdentityModel.Logging/6.8.0": {"runtime": {"lib/netstandard2.0/Microsoft.IdentityModel.Logging.dll": {"assemblyVersion": "6.8.0.0", "fileVersion": "6.8.0.11012"}}}, "Microsoft.IdentityModel.Protocols/6.8.0": {"dependencies": {"Microsoft.IdentityModel.Logging": "6.8.0", "Microsoft.IdentityModel.Tokens": "6.8.0"}, "runtime": {"lib/netstandard2.0/Microsoft.IdentityModel.Protocols.dll": {"assemblyVersion": "6.8.0.0", "fileVersion": "6.8.0.11012"}}}, "Microsoft.IdentityModel.Protocols.OpenIdConnect/6.8.0": {"dependencies": {"Microsoft.IdentityModel.Protocols": "6.8.0", "System.IdentityModel.Tokens.Jwt": "6.8.0"}, "runtime": {"lib/netstandard2.0/Microsoft.IdentityModel.Protocols.OpenIdConnect.dll": {"assemblyVersion": "6.8.0.0", "fileVersion": "6.8.0.11012"}}}, "Microsoft.IdentityModel.Tokens/6.8.0": {"dependencies": {"Microsoft.CSharp": "4.5.0", "Microsoft.IdentityModel.Logging": "6.8.0", "System.Security.Cryptography.Cng": "5.0.0"}, "runtime": {"lib/netstandard2.0/Microsoft.IdentityModel.Tokens.dll": {"assemblyVersion": "6.8.0.0", "fileVersion": "6.8.0.11012"}}}, "Microsoft.Net.Http.Headers/2.2.0": {"dependencies": {"Microsoft.Extensions.Primitives": "6.0.0", "System.Buffers": "4.5.0"}}, "Microsoft.NET.StringTools/17.3.2": {"dependencies": {"System.Memory": "4.5.5", "System.Runtime.CompilerServices.Unsafe": "6.0.0"}, "runtime": {"lib/net6.0/Microsoft.NET.StringTools.dll": {"assemblyVersion": "1.0.0.0", "fileVersion": "17.3.2.46306"}}}, "Microsoft.NETCore.Platforms/1.1.0": {}, "Microsoft.NETCore.Targets/1.1.0": {}, "Microsoft.VisualStudio.Debugger.Contracts/17.2.0": {"dependencies": {"MessagePack": "2.1.152", "MessagePackAnalyzer": "2.1.152", "Microsoft.Bcl.AsyncInterfaces": "5.0.0", "System.Collections.Immutable": "6.0.0"}, "runtime": {"lib/netstandard2.0/Microsoft.VisualStudio.Debugger.Contracts.dll": {"assemblyVersion": "1*******", "fileVersion": "17.200.21.51202"}}}, "Microsoft.VisualStudio.Web.CodeGeneration/6.0.16": {"dependencies": {"Microsoft.Extensions.DependencyInjection": "6.0.1", "Microsoft.VisualStudio.Web.CodeGeneration.EntityFrameworkCore": "6.0.16"}, "runtime": {"lib/net6.0/Microsoft.VisualStudio.Web.CodeGeneration.dll": {"assemblyVersion": "********", "fileVersion": "6.0.1623.40301"}}}, "Microsoft.VisualStudio.Web.CodeGeneration.Core/6.0.16": {"dependencies": {"Microsoft.Extensions.DependencyInjection": "6.0.1", "Microsoft.VisualStudio.Web.CodeGeneration.Templating": "6.0.16", "Newtonsoft.Json": "13.0.1"}, "runtime": {"lib/net6.0/Microsoft.VisualStudio.Web.CodeGeneration.Core.dll": {"assemblyVersion": "********", "fileVersion": "6.0.1623.40301"}}}, "Microsoft.VisualStudio.Web.CodeGeneration.Design/6.0.16": {"dependencies": {"Microsoft.DotNet.Scaffolding.Shared": "6.0.16", "Microsoft.VisualStudio.Web.CodeGenerators.Mvc": "6.0.16"}, "runtime": {"lib/net6.0/dotnet-aspnet-codegenerator-design.dll": {"assemblyVersion": "********", "fileVersion": "6.0.1623.40301"}}}, "Microsoft.VisualStudio.Web.CodeGeneration.EntityFrameworkCore/6.0.16": {"dependencies": {"Microsoft.DotNet.Scaffolding.Shared": "6.0.16", "Microsoft.VisualStudio.Web.CodeGeneration.Core": "6.0.16"}, "runtime": {"lib/net6.0/Microsoft.VisualStudio.Web.CodeGeneration.EntityFrameworkCore.dll": {"assemblyVersion": "********", "fileVersion": "6.0.1623.40301"}}}, "Microsoft.VisualStudio.Web.CodeGeneration.Templating/6.0.16": {"dependencies": {"Microsoft.AspNetCore.Razor.Language": "6.0.0", "Microsoft.CodeAnalysis.CSharp": "4.0.0", "Microsoft.CodeAnalysis.Razor": "6.0.0", "Microsoft.VisualStudio.Web.CodeGeneration.Utils": "6.0.16"}, "runtime": {"lib/net6.0/Microsoft.VisualStudio.Web.CodeGeneration.Templating.dll": {"assemblyVersion": "********", "fileVersion": "6.0.1623.40301"}}}, "Microsoft.VisualStudio.Web.CodeGeneration.Utils/6.0.16": {"dependencies": {"Microsoft.Build": "17.3.2", "Microsoft.CodeAnalysis.CSharp.Workspaces": "4.0.0", "Microsoft.DotNet.Scaffolding.Shared": "6.0.16", "Newtonsoft.Json": "13.0.1"}, "runtime": {"lib/net6.0/Microsoft.VisualStudio.Web.CodeGeneration.Utils.dll": {"assemblyVersion": "********", "fileVersion": "6.0.1623.40301"}}}, "Microsoft.VisualStudio.Web.CodeGenerators.Mvc/6.0.16": {"dependencies": {"Microsoft.DotNet.Scaffolding.Shared": "6.0.16", "Microsoft.VisualStudio.Web.CodeGeneration": "6.0.16"}, "runtime": {"lib/net6.0/Microsoft.VisualStudio.Web.CodeGenerators.Mvc.dll": {"assemblyVersion": "********", "fileVersion": "6.0.1623.40301"}}}, "Microsoft.Win32.Primitives/4.3.0": {"dependencies": {"Microsoft.NETCore.Platforms": "1.1.0", "Microsoft.NETCore.Targets": "1.1.0", "System.Runtime": "4.3.0"}}, "Microsoft.Win32.Registry/4.7.0": {"dependencies": {"System.Security.AccessControl": "6.0.0", "System.Security.Principal.Windows": "5.0.0"}}, "Microsoft.Win32.SystemEvents/6.0.0": {"runtime": {"lib/net6.0/Microsoft.Win32.SystemEvents.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.21.52210"}}, "runtimeTargets": {"runtimes/win/lib/net6.0/Microsoft.Win32.SystemEvents.dll": {"rid": "win", "assetType": "runtime", "assemblyVersion": "*******", "fileVersion": "6.0.21.52210"}}}, "NETStandard.Library/1.6.1": {"dependencies": {"Microsoft.NETCore.Platforms": "1.1.0", "Microsoft.Win32.Primitives": "4.3.0", "System.AppContext": "4.3.0", "System.Collections": "4.3.0", "System.Collections.Concurrent": "4.3.0", "System.Console": "4.3.0", "System.Diagnostics.Debug": "4.3.0", "System.Diagnostics.Tools": "4.3.0", "System.Diagnostics.Tracing": "4.3.0", "System.Globalization": "4.3.0", "System.Globalization.Calendars": "4.3.0", "System.IO": "4.3.0", "System.IO.Compression": "4.3.0", "System.IO.Compression.ZipFile": "4.3.0", "System.IO.FileSystem": "4.3.0", "System.IO.FileSystem.Primitives": "4.3.0", "System.Linq": "4.3.0", "System.Linq.Expressions": "4.3.0", "System.Net.Http": "4.3.0", "System.Net.Primitives": "4.3.0", "System.Net.Sockets": "4.3.0", "System.ObjectModel": "4.3.0", "System.Reflection": "4.3.0", "System.Reflection.Extensions": "4.3.0", "System.Reflection.Primitives": "4.3.0", "System.Resources.ResourceManager": "4.3.0", "System.Runtime": "4.3.0", "System.Runtime.Extensions": "4.3.0", "System.Runtime.Handles": "4.3.0", "System.Runtime.InteropServices": "4.3.0", "System.Runtime.InteropServices.RuntimeInformation": "4.3.0", "System.Runtime.Numerics": "4.3.0", "System.Security.Cryptography.Algorithms": "4.3.0", "System.Security.Cryptography.Encoding": "4.3.0", "System.Security.Cryptography.Primitives": "4.3.0", "System.Security.Cryptography.X509Certificates": "4.3.0", "System.Text.Encoding": "4.3.0", "System.Text.Encoding.Extensions": "4.3.0", "System.Text.RegularExpressions": "4.3.0", "System.Threading": "4.3.0", "System.Threading.Tasks": "4.3.0", "System.Threading.Timer": "4.3.0", "System.Xml.ReaderWriter": "4.3.0", "System.Xml.XDocument": "4.3.0"}}, "Newtonsoft.Json/13.0.1": {"runtime": {"lib/netstandard2.0/Newtonsoft.Json.dll": {"assemblyVersion": "13.0.0.0", "fileVersion": "13.0.1.25517"}}}, "NuGet.Common/6.6.1": {"dependencies": {"NuGet.Frameworks": "6.6.1"}, "runtime": {"lib/netstandard2.0/NuGet.Common.dll": {"assemblyVersion": "6.6.1.2", "fileVersion": "6.6.1.2"}}}, "NuGet.Configuration/6.6.1": {"dependencies": {"NuGet.Common": "6.6.1", "System.Security.Cryptography.ProtectedData": "6.0.0"}, "runtime": {"lib/netstandard2.0/NuGet.Configuration.dll": {"assemblyVersion": "6.6.1.2", "fileVersion": "6.6.1.2"}}}, "NuGet.DependencyResolver.Core/6.6.1": {"dependencies": {"NuGet.Configuration": "6.6.1", "NuGet.LibraryModel": "6.6.1", "NuGet.Protocol": "6.6.1"}, "runtime": {"lib/net5.0/NuGet.DependencyResolver.Core.dll": {"assemblyVersion": "6.6.1.2", "fileVersion": "6.6.1.2"}}}, "NuGet.Frameworks/6.6.1": {"runtime": {"lib/netstandard2.0/NuGet.Frameworks.dll": {"assemblyVersion": "6.6.1.2", "fileVersion": "6.6.1.2"}}}, "NuGet.LibraryModel/6.6.1": {"dependencies": {"NuGet.Common": "6.6.1", "NuGet.Versioning": "6.6.1"}, "runtime": {"lib/netstandard2.0/NuGet.LibraryModel.dll": {"assemblyVersion": "6.6.1.2", "fileVersion": "6.6.1.2"}}}, "NuGet.Packaging/6.6.1": {"dependencies": {"Newtonsoft.Json": "13.0.1", "NuGet.Configuration": "6.6.1", "NuGet.Versioning": "6.6.1", "System.Security.Cryptography.Cng": "5.0.0", "System.Security.Cryptography.Pkcs": "5.0.0"}, "runtime": {"lib/net5.0/NuGet.Packaging.dll": {"assemblyVersion": "6.6.1.2", "fileVersion": "6.6.1.2"}}}, "NuGet.ProjectModel/6.6.1": {"dependencies": {"NuGet.DependencyResolver.Core": "6.6.1"}, "runtime": {"lib/net5.0/NuGet.ProjectModel.dll": {"assemblyVersion": "6.6.1.2", "fileVersion": "6.6.1.2"}}}, "NuGet.Protocol/6.6.1": {"dependencies": {"NuGet.Packaging": "6.6.1"}, "runtime": {"lib/net5.0/NuGet.Protocol.dll": {"assemblyVersion": "6.6.1.2", "fileVersion": "6.6.1.2"}}}, "NuGet.Versioning/6.6.1": {"runtime": {"lib/netstandard2.0/NuGet.Versioning.dll": {"assemblyVersion": "6.6.1.2", "fileVersion": "6.6.1.2"}}}, "runtime.debian.8-x64.runtime.native.System.Security.Cryptography.OpenSsl/4.3.0": {}, "runtime.fedora.23-x64.runtime.native.System.Security.Cryptography.OpenSsl/4.3.0": {}, "runtime.fedora.24-x64.runtime.native.System.Security.Cryptography.OpenSsl/4.3.0": {}, "runtime.native.System/4.3.0": {"dependencies": {"Microsoft.NETCore.Platforms": "1.1.0", "Microsoft.NETCore.Targets": "1.1.0"}}, "runtime.native.System.IO.Compression/4.3.0": {"dependencies": {"Microsoft.NETCore.Platforms": "1.1.0", "Microsoft.NETCore.Targets": "1.1.0"}}, "runtime.native.System.Net.Http/4.3.0": {"dependencies": {"Microsoft.NETCore.Platforms": "1.1.0", "Microsoft.NETCore.Targets": "1.1.0"}}, "runtime.native.System.Security.Cryptography.Apple/4.3.0": {"dependencies": {"runtime.osx.10.10-x64.runtime.native.System.Security.Cryptography.Apple": "4.3.0"}}, "runtime.native.System.Security.Cryptography.OpenSsl/4.3.0": {"dependencies": {"runtime.debian.8-x64.runtime.native.System.Security.Cryptography.OpenSsl": "4.3.0", "runtime.fedora.23-x64.runtime.native.System.Security.Cryptography.OpenSsl": "4.3.0", "runtime.fedora.24-x64.runtime.native.System.Security.Cryptography.OpenSsl": "4.3.0", "runtime.opensuse.13.2-x64.runtime.native.System.Security.Cryptography.OpenSsl": "4.3.0", "runtime.opensuse.42.1-x64.runtime.native.System.Security.Cryptography.OpenSsl": "4.3.0", "runtime.osx.10.10-x64.runtime.native.System.Security.Cryptography.OpenSsl": "4.3.0", "runtime.rhel.7-x64.runtime.native.System.Security.Cryptography.OpenSsl": "4.3.0", "runtime.ubuntu.14.04-x64.runtime.native.System.Security.Cryptography.OpenSsl": "4.3.0", "runtime.ubuntu.16.04-x64.runtime.native.System.Security.Cryptography.OpenSsl": "4.3.0", "runtime.ubuntu.16.10-x64.runtime.native.System.Security.Cryptography.OpenSsl": "4.3.0"}}, "runtime.opensuse.13.2-x64.runtime.native.System.Security.Cryptography.OpenSsl/4.3.0": {}, "runtime.opensuse.42.1-x64.runtime.native.System.Security.Cryptography.OpenSsl/4.3.0": {}, "runtime.osx.10.10-x64.runtime.native.System.Security.Cryptography.Apple/4.3.0": {}, "runtime.osx.10.10-x64.runtime.native.System.Security.Cryptography.OpenSsl/4.3.0": {}, "runtime.rhel.7-x64.runtime.native.System.Security.Cryptography.OpenSsl/4.3.0": {}, "runtime.ubuntu.14.04-x64.runtime.native.System.Security.Cryptography.OpenSsl/4.3.0": {}, "runtime.ubuntu.16.04-x64.runtime.native.System.Security.Cryptography.OpenSsl/4.3.0": {}, "runtime.ubuntu.16.10-x64.runtime.native.System.Security.Cryptography.OpenSsl/4.3.0": {}, "System.AppContext/4.3.0": {"dependencies": {"System.Runtime": "4.3.0"}}, "System.Buffers/4.5.0": {}, "System.Collections/4.3.0": {"dependencies": {"Microsoft.NETCore.Platforms": "1.1.0", "Microsoft.NETCore.Targets": "1.1.0", "System.Runtime": "4.3.0"}}, "System.Collections.Concurrent/4.3.0": {"dependencies": {"System.Collections": "4.3.0", "System.Diagnostics.Debug": "4.3.0", "System.Diagnostics.Tracing": "4.3.0", "System.Globalization": "4.3.0", "System.Reflection": "4.3.0", "System.Resources.ResourceManager": "4.3.0", "System.Runtime": "4.3.0", "System.Runtime.Extensions": "4.3.0", "System.Threading": "4.3.0", "System.Threading.Tasks": "4.3.0"}}, "System.Collections.Immutable/6.0.0": {"dependencies": {"System.Runtime.CompilerServices.Unsafe": "6.0.0"}}, "System.Composition/1.0.31": {"dependencies": {"System.Composition.AttributedModel": "1.0.31", "System.Composition.Convention": "1.0.31", "System.Composition.Hosting": "1.0.31", "System.Composition.Runtime": "1.0.31", "System.Composition.TypedParts": "1.0.31"}}, "System.Composition.AttributedModel/1.0.31": {"dependencies": {"System.Reflection": "4.3.0", "System.Runtime": "4.3.0"}, "runtime": {"lib/netstandard1.0/System.Composition.AttributedModel.dll": {"assemblyVersion": "********", "fileVersion": "4.6.24705.1"}}}, "System.Composition.Convention/1.0.31": {"dependencies": {"System.Collections": "4.3.0", "System.Composition.AttributedModel": "1.0.31", "System.Diagnostics.Debug": "4.3.0", "System.Diagnostics.Tools": "4.3.0", "System.Globalization": "4.3.0", "System.Linq": "4.3.0", "System.Linq.Expressions": "4.3.0", "System.Reflection": "4.3.0", "System.Reflection.Extensions": "4.3.0", "System.Resources.ResourceManager": "4.3.0", "System.Runtime": "4.3.0", "System.Threading": "4.3.0"}, "runtime": {"lib/netstandard1.0/System.Composition.Convention.dll": {"assemblyVersion": "********", "fileVersion": "4.6.24705.1"}}}, "System.Composition.Hosting/1.0.31": {"dependencies": {"System.Collections": "4.3.0", "System.Composition.Runtime": "1.0.31", "System.Diagnostics.Debug": "4.3.0", "System.Diagnostics.Tools": "4.3.0", "System.Globalization": "4.3.0", "System.Linq": "4.3.0", "System.Linq.Expressions": "4.3.0", "System.ObjectModel": "4.3.0", "System.Reflection": "4.3.0", "System.Reflection.Extensions": "4.3.0", "System.Resources.ResourceManager": "4.3.0", "System.Runtime": "4.3.0", "System.Threading": "4.3.0"}, "runtime": {"lib/netstandard1.0/System.Composition.Hosting.dll": {"assemblyVersion": "********", "fileVersion": "4.6.24705.1"}}}, "System.Composition.Runtime/1.0.31": {"dependencies": {"System.Collections": "4.3.0", "System.Diagnostics.Debug": "4.3.0", "System.Diagnostics.Tools": "4.3.0", "System.Globalization": "4.3.0", "System.Linq": "4.3.0", "System.Reflection": "4.3.0", "System.Resources.ResourceManager": "4.3.0", "System.Runtime": "4.3.0"}, "runtime": {"lib/netstandard1.0/System.Composition.Runtime.dll": {"assemblyVersion": "********", "fileVersion": "4.6.24705.1"}}}, "System.Composition.TypedParts/1.0.31": {"dependencies": {"System.Collections": "4.3.0", "System.Composition.AttributedModel": "1.0.31", "System.Composition.Hosting": "1.0.31", "System.Composition.Runtime": "1.0.31", "System.Diagnostics.Debug": "4.3.0", "System.Diagnostics.Tools": "4.3.0", "System.Globalization": "4.3.0", "System.Linq": "4.3.0", "System.Linq.Expressions": "4.3.0", "System.Reflection": "4.3.0", "System.Reflection.Extensions": "4.3.0", "System.Resources.ResourceManager": "4.3.0", "System.Runtime": "4.3.0", "System.Runtime.Extensions": "4.3.0"}, "runtime": {"lib/netstandard1.0/System.Composition.TypedParts.dll": {"assemblyVersion": "********", "fileVersion": "4.6.24705.1"}}}, "System.Configuration.ConfigurationManager/6.0.0": {"dependencies": {"System.Security.Cryptography.ProtectedData": "6.0.0", "System.Security.Permissions": "6.0.0"}, "runtime": {"lib/net6.0/System.Configuration.ConfigurationManager.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.21.52210"}}}, "System.Console/4.3.0": {"dependencies": {"Microsoft.NETCore.Platforms": "1.1.0", "Microsoft.NETCore.Targets": "1.1.0", "System.IO": "4.3.0", "System.Runtime": "4.3.0", "System.Text.Encoding": "4.3.0"}}, "System.Diagnostics.Debug/4.3.0": {"dependencies": {"Microsoft.NETCore.Platforms": "1.1.0", "Microsoft.NETCore.Targets": "1.1.0", "System.Runtime": "4.3.0"}}, "System.Diagnostics.DiagnosticSource/6.0.0": {"dependencies": {"System.Runtime.CompilerServices.Unsafe": "6.0.0"}}, "System.Diagnostics.Tools/4.3.0": {"dependencies": {"Microsoft.NETCore.Platforms": "1.1.0", "Microsoft.NETCore.Targets": "1.1.0", "System.Runtime": "4.3.0"}}, "System.Diagnostics.Tracing/4.3.0": {"dependencies": {"Microsoft.NETCore.Platforms": "1.1.0", "Microsoft.NETCore.Targets": "1.1.0", "System.Runtime": "4.3.0"}}, "System.Drawing.Common/6.0.0": {"dependencies": {"Microsoft.Win32.SystemEvents": "6.0.0"}, "runtime": {"lib/net6.0/System.Drawing.Common.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.21.52210"}}, "runtimeTargets": {"runtimes/unix/lib/net6.0/System.Drawing.Common.dll": {"rid": "unix", "assetType": "runtime", "assemblyVersion": "*******", "fileVersion": "6.0.21.52210"}, "runtimes/win/lib/net6.0/System.Drawing.Common.dll": {"rid": "win", "assetType": "runtime", "assemblyVersion": "*******", "fileVersion": "6.0.21.52210"}}}, "System.Dynamic.Runtime/4.0.11": {"dependencies": {"System.Collections": "4.3.0", "System.Diagnostics.Debug": "4.3.0", "System.Globalization": "4.3.0", "System.Linq": "4.3.0", "System.Linq.Expressions": "4.3.0", "System.ObjectModel": "4.3.0", "System.Reflection": "4.3.0", "System.Reflection.Emit": "4.6.0", "System.Reflection.Emit.ILGeneration": "4.3.0", "System.Reflection.Primitives": "4.3.0", "System.Reflection.TypeExtensions": "4.3.0", "System.Resources.ResourceManager": "4.3.0", "System.Runtime": "4.3.0", "System.Runtime.Extensions": "4.3.0", "System.Threading": "4.3.0"}}, "System.Formats.Asn1/5.0.0": {}, "System.Globalization/4.3.0": {"dependencies": {"Microsoft.NETCore.Platforms": "1.1.0", "Microsoft.NETCore.Targets": "1.1.0", "System.Runtime": "4.3.0"}}, "System.Globalization.Calendars/4.3.0": {"dependencies": {"Microsoft.NETCore.Platforms": "1.1.0", "Microsoft.NETCore.Targets": "1.1.0", "System.Globalization": "4.3.0", "System.Runtime": "4.3.0"}}, "System.Globalization.Extensions/4.3.0": {"dependencies": {"Microsoft.NETCore.Platforms": "1.1.0", "System.Globalization": "4.3.0", "System.Resources.ResourceManager": "4.3.0", "System.Runtime": "4.3.0", "System.Runtime.Extensions": "4.3.0", "System.Runtime.InteropServices": "4.3.0"}}, "System.IdentityModel.Tokens.Jwt/6.8.0": {"dependencies": {"Microsoft.IdentityModel.JsonWebTokens": "6.8.0", "Microsoft.IdentityModel.Tokens": "6.8.0"}, "runtime": {"lib/netstandard2.0/System.IdentityModel.Tokens.Jwt.dll": {"assemblyVersion": "6.8.0.0", "fileVersion": "6.8.0.11012"}}}, "System.IO/4.3.0": {"dependencies": {"Microsoft.NETCore.Platforms": "1.1.0", "Microsoft.NETCore.Targets": "1.1.0", "System.Runtime": "4.3.0", "System.Text.Encoding": "4.3.0", "System.Threading.Tasks": "4.3.0"}}, "System.IO.Compression/4.3.0": {"dependencies": {"Microsoft.NETCore.Platforms": "1.1.0", "System.Buffers": "4.5.0", "System.Collections": "4.3.0", "System.Diagnostics.Debug": "4.3.0", "System.IO": "4.3.0", "System.Resources.ResourceManager": "4.3.0", "System.Runtime": "4.3.0", "System.Runtime.Extensions": "4.3.0", "System.Runtime.Handles": "4.3.0", "System.Runtime.InteropServices": "4.3.0", "System.Text.Encoding": "4.3.0", "System.Threading": "4.3.0", "System.Threading.Tasks": "4.3.0", "runtime.native.System": "4.3.0", "runtime.native.System.IO.Compression": "4.3.0"}}, "System.IO.Compression.ZipFile/4.3.0": {"dependencies": {"System.Buffers": "4.5.0", "System.IO": "4.3.0", "System.IO.Compression": "4.3.0", "System.IO.FileSystem": "4.3.0", "System.IO.FileSystem.Primitives": "4.3.0", "System.Resources.ResourceManager": "4.3.0", "System.Runtime": "4.3.0", "System.Runtime.Extensions": "4.3.0", "System.Text.Encoding": "4.3.0"}}, "System.IO.FileSystem/4.3.0": {"dependencies": {"Microsoft.NETCore.Platforms": "1.1.0", "Microsoft.NETCore.Targets": "1.1.0", "System.IO": "4.3.0", "System.IO.FileSystem.Primitives": "4.3.0", "System.Runtime": "4.3.0", "System.Runtime.Handles": "4.3.0", "System.Text.Encoding": "4.3.0", "System.Threading.Tasks": "4.3.0"}}, "System.IO.FileSystem.Primitives/4.3.0": {"dependencies": {"System.Runtime": "4.3.0"}}, "System.IO.Pipelines/5.0.1": {}, "System.Linq/4.3.0": {"dependencies": {"System.Collections": "4.3.0", "System.Diagnostics.Debug": "4.3.0", "System.Resources.ResourceManager": "4.3.0", "System.Runtime": "4.3.0", "System.Runtime.Extensions": "4.3.0"}}, "System.Linq.Expressions/4.3.0": {"dependencies": {"System.Collections": "4.3.0", "System.Diagnostics.Debug": "4.3.0", "System.Globalization": "4.3.0", "System.IO": "4.3.0", "System.Linq": "4.3.0", "System.ObjectModel": "4.3.0", "System.Reflection": "4.3.0", "System.Reflection.Emit": "4.6.0", "System.Reflection.Emit.ILGeneration": "4.3.0", "System.Reflection.Emit.Lightweight": "4.6.0", "System.Reflection.Extensions": "4.3.0", "System.Reflection.Primitives": "4.3.0", "System.Reflection.TypeExtensions": "4.3.0", "System.Resources.ResourceManager": "4.3.0", "System.Runtime": "4.3.0", "System.Runtime.Extensions": "4.3.0", "System.Threading": "4.3.0"}}, "System.Memory/4.5.5": {}, "System.Net.Http/4.3.0": {"dependencies": {"Microsoft.NETCore.Platforms": "1.1.0", "System.Collections": "4.3.0", "System.Diagnostics.Debug": "4.3.0", "System.Diagnostics.DiagnosticSource": "6.0.0", "System.Diagnostics.Tracing": "4.3.0", "System.Globalization": "4.3.0", "System.Globalization.Extensions": "4.3.0", "System.IO": "4.3.0", "System.IO.FileSystem": "4.3.0", "System.Net.Primitives": "4.3.0", "System.Resources.ResourceManager": "4.3.0", "System.Runtime": "4.3.0", "System.Runtime.Extensions": "4.3.0", "System.Runtime.Handles": "4.3.0", "System.Runtime.InteropServices": "4.3.0", "System.Security.Cryptography.Algorithms": "4.3.0", "System.Security.Cryptography.Encoding": "4.3.0", "System.Security.Cryptography.OpenSsl": "4.3.0", "System.Security.Cryptography.Primitives": "4.3.0", "System.Security.Cryptography.X509Certificates": "4.3.0", "System.Text.Encoding": "4.3.0", "System.Threading": "4.3.0", "System.Threading.Tasks": "4.3.0", "runtime.native.System": "4.3.0", "runtime.native.System.Net.Http": "4.3.0", "runtime.native.System.Security.Cryptography.OpenSsl": "4.3.0"}}, "System.Net.Primitives/4.3.0": {"dependencies": {"Microsoft.NETCore.Platforms": "1.1.0", "Microsoft.NETCore.Targets": "1.1.0", "System.Runtime": "4.3.0", "System.Runtime.Handles": "4.3.0"}}, "System.Net.Sockets/4.3.0": {"dependencies": {"Microsoft.NETCore.Platforms": "1.1.0", "Microsoft.NETCore.Targets": "1.1.0", "System.IO": "4.3.0", "System.Net.Primitives": "4.3.0", "System.Runtime": "4.3.0", "System.Threading.Tasks": "4.3.0"}}, "System.ObjectModel/4.3.0": {"dependencies": {"System.Collections": "4.3.0", "System.Diagnostics.Debug": "4.3.0", "System.Resources.ResourceManager": "4.3.0", "System.Runtime": "4.3.0", "System.Threading": "4.3.0"}}, "System.Reflection/4.3.0": {"dependencies": {"Microsoft.NETCore.Platforms": "1.1.0", "Microsoft.NETCore.Targets": "1.1.0", "System.IO": "4.3.0", "System.Reflection.Primitives": "4.3.0", "System.Runtime": "4.3.0"}}, "System.Reflection.Emit/4.6.0": {}, "System.Reflection.Emit.ILGeneration/4.3.0": {"dependencies": {"System.Reflection": "4.3.0", "System.Reflection.Primitives": "4.3.0", "System.Runtime": "4.3.0"}}, "System.Reflection.Emit.Lightweight/4.6.0": {}, "System.Reflection.Extensions/4.3.0": {"dependencies": {"Microsoft.NETCore.Platforms": "1.1.0", "Microsoft.NETCore.Targets": "1.1.0", "System.Reflection": "4.3.0", "System.Runtime": "4.3.0"}}, "System.Reflection.Metadata/6.0.0": {"dependencies": {"System.Collections.Immutable": "6.0.0"}}, "System.Reflection.MetadataLoadContext/6.0.0": {"dependencies": {"System.Collections.Immutable": "6.0.0", "System.Reflection.Metadata": "6.0.0"}, "runtime": {"lib/net6.0/System.Reflection.MetadataLoadContext.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.21.52210"}}}, "System.Reflection.Primitives/4.3.0": {"dependencies": {"Microsoft.NETCore.Platforms": "1.1.0", "Microsoft.NETCore.Targets": "1.1.0", "System.Runtime": "4.3.0"}}, "System.Reflection.TypeExtensions/4.3.0": {"dependencies": {"System.Reflection": "4.3.0", "System.Runtime": "4.3.0"}}, "System.Resources.ResourceManager/4.3.0": {"dependencies": {"Microsoft.NETCore.Platforms": "1.1.0", "Microsoft.NETCore.Targets": "1.1.0", "System.Globalization": "4.3.0", "System.Reflection": "4.3.0", "System.Runtime": "4.3.0"}}, "System.Runtime/4.3.0": {"dependencies": {"Microsoft.NETCore.Platforms": "1.1.0", "Microsoft.NETCore.Targets": "1.1.0"}}, "System.Runtime.Caching/4.7.0": {"dependencies": {"System.Configuration.ConfigurationManager": "6.0.0"}, "runtime": {"lib/netstandard2.0/System.Runtime.Caching.dll": {"assemblyVersion": "4.0.1.0", "fileVersion": "4.700.19.56404"}}, "runtimeTargets": {"runtimes/win/lib/netstandard2.0/System.Runtime.Caching.dll": {"rid": "win", "assetType": "runtime", "assemblyVersion": "4.0.1.0", "fileVersion": "4.700.19.56404"}}}, "System.Runtime.CompilerServices.Unsafe/6.0.0": {}, "System.Runtime.Extensions/4.3.0": {"dependencies": {"Microsoft.NETCore.Platforms": "1.1.0", "Microsoft.NETCore.Targets": "1.1.0", "System.Runtime": "4.3.0"}}, "System.Runtime.Handles/4.3.0": {"dependencies": {"Microsoft.NETCore.Platforms": "1.1.0", "Microsoft.NETCore.Targets": "1.1.0", "System.Runtime": "4.3.0"}}, "System.Runtime.InteropServices/4.3.0": {"dependencies": {"Microsoft.NETCore.Platforms": "1.1.0", "Microsoft.NETCore.Targets": "1.1.0", "System.Reflection": "4.3.0", "System.Reflection.Primitives": "4.3.0", "System.Runtime": "4.3.0", "System.Runtime.Handles": "4.3.0"}}, "System.Runtime.InteropServices.RuntimeInformation/4.3.0": {"dependencies": {"System.Reflection": "4.3.0", "System.Reflection.Extensions": "4.3.0", "System.Resources.ResourceManager": "4.3.0", "System.Runtime": "4.3.0", "System.Runtime.InteropServices": "4.3.0", "System.Threading": "4.3.0", "runtime.native.System": "4.3.0"}}, "System.Runtime.Numerics/4.3.0": {"dependencies": {"System.Globalization": "4.3.0", "System.Resources.ResourceManager": "4.3.0", "System.Runtime": "4.3.0", "System.Runtime.Extensions": "4.3.0"}}, "System.Security.AccessControl/6.0.0": {}, "System.Security.Cryptography.Algorithms/4.3.0": {"dependencies": {"Microsoft.NETCore.Platforms": "1.1.0", "System.Collections": "4.3.0", "System.IO": "4.3.0", "System.Resources.ResourceManager": "4.3.0", "System.Runtime": "4.3.0", "System.Runtime.Extensions": "4.3.0", "System.Runtime.Handles": "4.3.0", "System.Runtime.InteropServices": "4.3.0", "System.Runtime.Numerics": "4.3.0", "System.Security.Cryptography.Encoding": "4.3.0", "System.Security.Cryptography.Primitives": "4.3.0", "System.Text.Encoding": "4.3.0", "runtime.native.System.Security.Cryptography.Apple": "4.3.0", "runtime.native.System.Security.Cryptography.OpenSsl": "4.3.0"}}, "System.Security.Cryptography.Cng/5.0.0": {"dependencies": {"System.Formats.Asn1": "5.0.0"}}, "System.Security.Cryptography.Csp/4.3.0": {"dependencies": {"Microsoft.NETCore.Platforms": "1.1.0", "System.IO": "4.3.0", "System.Reflection": "4.3.0", "System.Resources.ResourceManager": "4.3.0", "System.Runtime": "4.3.0", "System.Runtime.Extensions": "4.3.0", "System.Runtime.Handles": "4.3.0", "System.Runtime.InteropServices": "4.3.0", "System.Security.Cryptography.Algorithms": "4.3.0", "System.Security.Cryptography.Encoding": "4.3.0", "System.Security.Cryptography.Primitives": "4.3.0", "System.Text.Encoding": "4.3.0", "System.Threading": "4.3.0"}}, "System.Security.Cryptography.Encoding/4.3.0": {"dependencies": {"Microsoft.NETCore.Platforms": "1.1.0", "System.Collections": "4.3.0", "System.Collections.Concurrent": "4.3.0", "System.Linq": "4.3.0", "System.Resources.ResourceManager": "4.3.0", "System.Runtime": "4.3.0", "System.Runtime.Extensions": "4.3.0", "System.Runtime.Handles": "4.3.0", "System.Runtime.InteropServices": "4.3.0", "System.Security.Cryptography.Primitives": "4.3.0", "System.Text.Encoding": "4.3.0", "runtime.native.System.Security.Cryptography.OpenSsl": "4.3.0"}}, "System.Security.Cryptography.OpenSsl/4.3.0": {"dependencies": {"System.Collections": "4.3.0", "System.IO": "4.3.0", "System.Resources.ResourceManager": "4.3.0", "System.Runtime": "4.3.0", "System.Runtime.Extensions": "4.3.0", "System.Runtime.Handles": "4.3.0", "System.Runtime.InteropServices": "4.3.0", "System.Runtime.Numerics": "4.3.0", "System.Security.Cryptography.Algorithms": "4.3.0", "System.Security.Cryptography.Encoding": "4.3.0", "System.Security.Cryptography.Primitives": "4.3.0", "System.Text.Encoding": "4.3.0", "runtime.native.System.Security.Cryptography.OpenSsl": "4.3.0"}}, "System.Security.Cryptography.Pkcs/5.0.0": {"dependencies": {"System.Formats.Asn1": "5.0.0", "System.Security.Cryptography.Cng": "5.0.0"}}, "System.Security.Cryptography.Primitives/4.3.0": {"dependencies": {"System.Diagnostics.Debug": "4.3.0", "System.Globalization": "4.3.0", "System.IO": "4.3.0", "System.Resources.ResourceManager": "4.3.0", "System.Runtime": "4.3.0", "System.Threading": "4.3.0", "System.Threading.Tasks": "4.3.0"}}, "System.Security.Cryptography.ProtectedData/6.0.0": {"runtime": {"lib/net6.0/System.Security.Cryptography.ProtectedData.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.21.52210"}}, "runtimeTargets": {"runtimes/win/lib/net6.0/System.Security.Cryptography.ProtectedData.dll": {"rid": "win", "assetType": "runtime", "assemblyVersion": "*******", "fileVersion": "6.0.21.52210"}}}, "System.Security.Cryptography.X509Certificates/4.3.0": {"dependencies": {"Microsoft.NETCore.Platforms": "1.1.0", "System.Collections": "4.3.0", "System.Diagnostics.Debug": "4.3.0", "System.Globalization": "4.3.0", "System.Globalization.Calendars": "4.3.0", "System.IO": "4.3.0", "System.IO.FileSystem": "4.3.0", "System.IO.FileSystem.Primitives": "4.3.0", "System.Resources.ResourceManager": "4.3.0", "System.Runtime": "4.3.0", "System.Runtime.Extensions": "4.3.0", "System.Runtime.Handles": "4.3.0", "System.Runtime.InteropServices": "4.3.0", "System.Runtime.Numerics": "4.3.0", "System.Security.Cryptography.Algorithms": "4.3.0", "System.Security.Cryptography.Cng": "5.0.0", "System.Security.Cryptography.Csp": "4.3.0", "System.Security.Cryptography.Encoding": "4.3.0", "System.Security.Cryptography.OpenSsl": "4.3.0", "System.Security.Cryptography.Primitives": "4.3.0", "System.Text.Encoding": "4.3.0", "System.Threading": "4.3.0", "runtime.native.System": "4.3.0", "runtime.native.System.Net.Http": "4.3.0", "runtime.native.System.Security.Cryptography.OpenSsl": "4.3.0"}}, "System.Security.Permissions/6.0.0": {"dependencies": {"System.Security.AccessControl": "6.0.0", "System.Windows.Extensions": "6.0.0"}, "runtime": {"lib/net6.0/System.Security.Permissions.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.21.52210"}}}, "System.Security.Principal.Windows/5.0.0": {}, "System.Text.Encoding/4.3.0": {"dependencies": {"Microsoft.NETCore.Platforms": "1.1.0", "Microsoft.NETCore.Targets": "1.1.0", "System.Runtime": "4.3.0"}}, "System.Text.Encoding.CodePages/6.0.0": {"dependencies": {"System.Runtime.CompilerServices.Unsafe": "6.0.0"}}, "System.Text.Encoding.Extensions/4.3.0": {"dependencies": {"Microsoft.NETCore.Platforms": "1.1.0", "Microsoft.NETCore.Targets": "1.1.0", "System.Runtime": "4.3.0", "System.Text.Encoding": "4.3.0"}}, "System.Text.Encodings.Web/6.0.0": {"dependencies": {"System.Runtime.CompilerServices.Unsafe": "6.0.0"}}, "System.Text.Json/6.0.0": {"dependencies": {"System.Runtime.CompilerServices.Unsafe": "6.0.0", "System.Text.Encodings.Web": "6.0.0"}}, "System.Text.RegularExpressions/4.3.0": {"dependencies": {"System.Runtime": "4.3.0"}}, "System.Threading/4.3.0": {"dependencies": {"System.Runtime": "4.3.0", "System.Threading.Tasks": "4.3.0"}}, "System.Threading.Tasks/4.3.0": {"dependencies": {"Microsoft.NETCore.Platforms": "1.1.0", "Microsoft.NETCore.Targets": "1.1.0", "System.Runtime": "4.3.0"}}, "System.Threading.Tasks.Dataflow/6.0.0": {}, "System.Threading.Tasks.Extensions/4.5.4": {}, "System.Threading.Timer/4.3.0": {"dependencies": {"Microsoft.NETCore.Platforms": "1.1.0", "Microsoft.NETCore.Targets": "1.1.0", "System.Runtime": "4.3.0"}}, "System.Windows.Extensions/6.0.0": {"dependencies": {"System.Drawing.Common": "6.0.0"}, "runtime": {"lib/net6.0/System.Windows.Extensions.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.21.52210"}}, "runtimeTargets": {"runtimes/win/lib/net6.0/System.Windows.Extensions.dll": {"rid": "win", "assetType": "runtime", "assemblyVersion": "*******", "fileVersion": "6.0.21.52210"}}}, "System.Xml.ReaderWriter/4.3.0": {"dependencies": {"System.Collections": "4.3.0", "System.Diagnostics.Debug": "4.3.0", "System.Globalization": "4.3.0", "System.IO": "4.3.0", "System.IO.FileSystem": "4.3.0", "System.IO.FileSystem.Primitives": "4.3.0", "System.Resources.ResourceManager": "4.3.0", "System.Runtime": "4.3.0", "System.Runtime.Extensions": "4.3.0", "System.Runtime.InteropServices": "4.3.0", "System.Text.Encoding": "4.3.0", "System.Text.Encoding.Extensions": "4.3.0", "System.Text.RegularExpressions": "4.3.0", "System.Threading.Tasks": "4.3.0", "System.Threading.Tasks.Extensions": "4.5.4"}}, "System.Xml.XDocument/4.3.0": {"dependencies": {"System.Collections": "4.3.0", "System.Diagnostics.Debug": "4.3.0", "System.Diagnostics.Tools": "4.3.0", "System.Globalization": "4.3.0", "System.IO": "4.3.0", "System.Reflection": "4.3.0", "System.Resources.ResourceManager": "4.3.0", "System.Runtime": "4.3.0", "System.Runtime.Extensions": "4.3.0", "System.Text.Encoding": "4.3.0", "System.Threading": "4.3.0", "System.Xml.ReaderWriter": "4.3.0"}}, "TaskManager.Common/1.0.0": {"runtime": {"TaskManager.Common.dll": {}}}, "TaskManager.Data/1.0.0": {"dependencies": {"Microsoft.AspNetCore.Identity.EntityFrameworkCore": "6.0.10", "Microsoft.EntityFrameworkCore.SqlServer": "6.0.10", "TaskManager.Data.Models": "1.0.0"}, "runtime": {"TaskManager.Data.dll": {}}}, "TaskManager.Data.Models/1.0.0": {"dependencies": {"Microsoft.AspNetCore.Identity.EntityFrameworkCore": "6.0.10", "Microsoft.EntityFrameworkCore.SqlServer": "6.0.10", "TaskManager.Common": "1.0.0"}, "runtime": {"TaskManager.Data.Models.dll": {}}}, "TaskManager.Services.Data/1.0.0": {"dependencies": {"TaskManager.Common": "1.0.0", "TaskManager.Data": "1.0.0", "TaskManager.Data.Models": "1.0.0", "TaskManager.Web.ViewModels": "1.0.0"}, "runtime": {"TaskManager.Services.Data.dll": {}}}, "TaskManager.Web.Infrastructure/1.0.0": {"dependencies": {"Microsoft.AspNetCore.Mvc.Core": "2.2.5", "TaskManager.Services.Data": "1.0.0"}, "runtime": {"TaskManager.Web.Infrastructure.dll": {}}}, "TaskManager.Web.ViewModels/1.0.0": {"dependencies": {"Microsoft.AspNetCore.Mvc.Core": "2.2.5", "TaskManager.Common": "1.0.0"}, "runtime": {"TaskManager.Web.ViewModels.dll": {}}}}}, "libraries": {"TaskManager/1.0.0": {"type": "project", "serviceable": false, "sha512": ""}, "Humanizer/2.14.1": {"type": "package", "serviceable": true, "sha512": "sha512-/FUTD3cEceAAmJSCPN9+J+VhGwmL/C12jvwlyM1DFXShEMsBzvLzLqSrJ2rb+k/W2znKw7JyflZgZpyE+tI7lA==", "path": "humanizer/2.14.1", "hashPath": "humanizer.2.14.1.nupkg.sha512"}, "Humanizer.Core/2.14.1": {"type": "package", "serviceable": true, "sha512": "sha512-lQKvtaTDOXnoVJ20ibTuSIOf2i0uO0MPbDhd1jm238I+U/2ZnRENj0cktKZhtchBMtCUSRQ5v4xBCUbKNmyVMw==", "path": "humanizer.core/2.14.1", "hashPath": "humanizer.core.2.14.1.nupkg.sha512"}, "Humanizer.Core.af/2.14.1": {"type": "package", "serviceable": true, "sha512": "sha512-BoQHyu5le+xxKOw+/AUM7CLXneM/Bh3++0qh1u0+D95n6f9eGt9kNc8LcAHLIOwId7Sd5hiAaaav0Nimj3peNw==", "path": "humanizer.core.af/2.14.1", "hashPath": "humanizer.core.af.2.14.1.nupkg.sha512"}, "Humanizer.Core.ar/2.14.1": {"type": "package", "serviceable": true, "sha512": "sha512-3d1V10LDtmqg5bZjWkA/EkmGFeSfNBcyCH+TiHcHP+HGQQmRq3eBaLcLnOJbVQVn3Z6Ak8GOte4RX4kVCxQlFA==", "path": "humanizer.core.ar/2.14.1", "hashPath": "humanizer.core.ar.2.14.1.nupkg.sha512"}, "Humanizer.Core.az/2.14.1": {"type": "package", "serviceable": true, "sha512": "sha512-8Z/tp9PdHr/K2Stve2Qs/7uqWPWLUK9D8sOZDNzyv42e20bSoJkHFn7SFoxhmaoVLJwku2jp6P7HuwrfkrP18Q==", "path": "humanizer.core.az/2.14.1", "hashPath": "humanizer.core.az.2.14.1.nupkg.sha512"}, "Humanizer.Core.bg/2.14.1": {"type": "package", "serviceable": true, "sha512": "sha512-S+hIEHicrOcbV2TBtyoPp1AVIGsBzlarOGThhQYCnP6QzEYo/5imtok6LMmhZeTnBFoKhM8yJqRfvJ5yqVQKSQ==", "path": "humanizer.core.bg/2.14.1", "hashPath": "humanizer.core.bg.2.14.1.nupkg.sha512"}, "Humanizer.Core.bn-BD/2.14.1": {"type": "package", "serviceable": true, "sha512": "sha512-U3bfj90tnUDRKlL1ZFlzhCHoVgpTcqUlTQxjvGCaFKb+734TTu3nkHUWVZltA1E/swTvimo/aXLtkxnLFrc0EQ==", "path": "humanizer.core.bn-bd/2.14.1", "hashPath": "humanizer.core.bn-bd.2.14.1.nupkg.sha512"}, "Humanizer.Core.cs/2.14.1": {"type": "package", "serviceable": true, "sha512": "sha512-jWrQkiCTy3L2u1T86cFkgijX6k7hoB0pdcFMWYaSZnm6rvG/XJE40tfhYyKhYYgIc1x9P2GO5AC7xXvFnFdqMQ==", "path": "humanizer.core.cs/2.14.1", "hashPath": "humanizer.core.cs.2.14.1.nupkg.sha512"}, "Humanizer.Core.da/2.14.1": {"type": "package", "serviceable": true, "sha512": "sha512-5o0rJyE/2wWUUphC79rgYDnif/21MKTTx9LIzRVz9cjCIVFrJ2bDyR2gapvI9D6fjoyvD1NAfkN18SHBsO8S9g==", "path": "humanizer.core.da/2.14.1", "hashPath": "humanizer.core.da.2.14.1.nupkg.sha512"}, "Humanizer.Core.de/2.14.1": {"type": "package", "serviceable": true, "sha512": "sha512-9JD/p+rqjb8f5RdZ3aEJqbjMYkbk4VFii2QDnnOdNo6ywEfg/A5YeOQ55CaBJmy7KvV4tOK4+qHJnX/tg3Z54A==", "path": "humanizer.core.de/2.14.1", "hashPath": "humanizer.core.de.2.14.1.nupkg.sha512"}, "Humanizer.Core.el/2.14.1": {"type": "package", "serviceable": true, "sha512": "sha512-Xmv6sTL5mqjOWGGpqY7bvbfK5RngaUHSa8fYDGSLyxY9mGdNbDcasnRnMOvi0SxJS9gAqBCn21Xi90n2SHZbFA==", "path": "humanizer.core.el/2.14.1", "hashPath": "humanizer.core.el.2.14.1.nupkg.sha512"}, "Humanizer.Core.es/2.14.1": {"type": "package", "serviceable": true, "sha512": "sha512-e//OIAeMB7pjBV1HqqI4pM2Bcw3Jwgpyz9G5Fi4c+RJvhqFwztoWxW57PzTnNJE2lbhGGLQZihFZjsbTUsbczA==", "path": "humanizer.core.es/2.14.1", "hashPath": "humanizer.core.es.2.14.1.nupkg.sha512"}, "Humanizer.Core.fa/2.14.1": {"type": "package", "serviceable": true, "sha512": "sha512-nzDOj1x0NgjXMjsQxrET21t1FbdoRYujzbmZoR8u8ou5CBWY1UNca0j6n/PEJR/iUbt4IxstpszRy41wL/BrpA==", "path": "humanizer.core.fa/2.14.1", "hashPath": "humanizer.core.fa.2.14.1.nupkg.sha512"}, "Humanizer.Core.fi-FI/2.14.1": {"type": "package", "serviceable": true, "sha512": "sha512-Vnxxx4LUhp3AzowYi6lZLAA9Lh8UqkdwRh4IE2qDXiVpbo08rSbokATaEzFS+o+/jCNZBmoyyyph3vgmcSzhhQ==", "path": "humanizer.core.fi-fi/2.14.1", "hashPath": "humanizer.core.fi-fi.2.14.1.nupkg.sha512"}, "Humanizer.Core.fr/2.14.1": {"type": "package", "serviceable": true, "sha512": "sha512-2p4g0BYNzFS3u9SOIDByp2VClYKO0K1ecDV4BkB9EYdEPWfFODYnF+8CH8LpUrpxL2TuWo2fiFx/4Jcmrnkbpg==", "path": "humanizer.core.fr/2.14.1", "hashPath": "humanizer.core.fr.2.14.1.nupkg.sha512"}, "Humanizer.Core.fr-BE/2.14.1": {"type": "package", "serviceable": true, "sha512": "sha512-o6R3SerxCRn5Ij8nCihDNMGXlaJ/1AqefteAssgmU2qXYlSAGdhxmnrQAXZUDlE4YWt/XQ6VkNLtH7oMqsSPFQ==", "path": "humanizer.core.fr-be/2.14.1", "hashPath": "humanizer.core.fr-be.2.14.1.nupkg.sha512"}, "Humanizer.Core.he/2.14.1": {"type": "package", "serviceable": true, "sha512": "sha512-FPsAhy7Iw6hb+ZitLgYC26xNcgGAHXb0V823yFAzcyoL5ozM+DCJtYfDPYiOpsJhEZmKFTM9No0jUn1M89WGvg==", "path": "humanizer.core.he/2.14.1", "hashPath": "humanizer.core.he.2.14.1.nupkg.sha512"}, "Humanizer.Core.hr/2.14.1": {"type": "package", "serviceable": true, "sha512": "sha512-chnaD89yOlST142AMkAKLuzRcV5df3yyhDyRU5rypDiqrq2HN8y1UR3h1IicEAEtXLoOEQyjSAkAQ6QuXkn7aw==", "path": "humanizer.core.hr/2.14.1", "hashPath": "humanizer.core.hr.2.14.1.nupkg.sha512"}, "Humanizer.Core.hu/2.14.1": {"type": "package", "serviceable": true, "sha512": "sha512-hAfnaoF9LTGU/CmFdbnvugN4tIs8ppevVMe3e5bD24+tuKsggMc5hYta9aiydI8JH9JnuVmxvNI4DJee1tK05A==", "path": "humanizer.core.hu/2.14.1", "hashPath": "humanizer.core.hu.2.14.1.nupkg.sha512"}, "Humanizer.Core.hy/2.14.1": {"type": "package", "serviceable": true, "sha512": "sha512-sVIKxOiSBUb4gStRHo9XwwAg9w7TNvAXbjy176gyTtaTiZkcjr9aCPziUlYAF07oNz6SdwdC2mwJBGgvZ0Sl2g==", "path": "humanizer.core.hy/2.14.1", "hashPath": "humanizer.core.hy.2.14.1.nupkg.sha512"}, "Humanizer.Core.id/2.14.1": {"type": "package", "serviceable": true, "sha512": "sha512-4Zl3GTvk3a49Ia/WDNQ97eCupjjQRs2iCIZEQdmkiqyaLWttfb+cYXDMGthP42nufUL0SRsvBctN67oSpnXtsg==", "path": "humanizer.core.id/2.14.1", "hashPath": "humanizer.core.id.2.14.1.nupkg.sha512"}, "Humanizer.Core.is/2.14.1": {"type": "package", "serviceable": true, "sha512": "sha512-R67A9j/nNgcWzU7gZy1AJ07ABSLvogRbqOWvfRDn4q6hNdbg/mjGjZBp4qCTPnB2mHQQTCKo3oeCUayBCNIBCw==", "path": "humanizer.core.is/2.14.1", "hashPath": "humanizer.core.is.2.14.1.nupkg.sha512"}, "Humanizer.Core.it/2.14.1": {"type": "package", "serviceable": true, "sha512": "sha512-jYxGeN4XIKHVND02FZ+Woir3CUTyBhLsqxu9iqR/9BISArkMf1Px6i5pRZnvq4fc5Zn1qw71GKKoCaHDJBsLFw==", "path": "humanizer.core.it/2.14.1", "hashPath": "humanizer.core.it.2.14.1.nupkg.sha512"}, "Humanizer.Core.ja/2.14.1": {"type": "package", "serviceable": true, "sha512": "sha512-TM3ablFNoYx4cYJybmRgpDioHpiKSD7q0QtMrmpsqwtiiEsdW5zz/q4PolwAczFnvrKpN6nBXdjnPPKVet93ng==", "path": "humanizer.core.ja/2.14.1", "hashPath": "humanizer.core.ja.2.14.1.nupkg.sha512"}, "Humanizer.Core.ko-KR/2.14.1": {"type": "package", "serviceable": true, "sha512": "sha512-CtvwvK941k/U0r8PGdEuBEMdW6jv/rBiA9tUhakC7Zd2rA/HCnDcbr1DiNZ+/tRshnhzxy/qwmpY8h4qcAYCtQ==", "path": "humanizer.core.ko-kr/2.14.1", "hashPath": "humanizer.core.ko-kr.2.14.1.nupkg.sha512"}, "Humanizer.Core.ku/2.14.1": {"type": "package", "serviceable": true, "sha512": "sha512-vHmzXcVMe+LNrF9txpdHzpG7XJX65SiN9GQd/Zkt6gsGIIEeECHrkwCN5Jnlkddw2M/b0HS4SNxdR1GrSn7uCA==", "path": "humanizer.core.ku/2.14.1", "hashPath": "humanizer.core.ku.2.14.1.nupkg.sha512"}, "Humanizer.Core.lv/2.14.1": {"type": "package", "serviceable": true, "sha512": "sha512-E1/KUVnYBS1bdOTMNDD7LV/jdoZv/fbWTLPtvwdMtSdqLyRTllv6PGM9xVQoFDYlpvVGtEl/09glCojPHw8ffA==", "path": "humanizer.core.lv/2.14.1", "hashPath": "humanizer.core.lv.2.14.1.nupkg.sha512"}, "Humanizer.Core.ms-MY/2.14.1": {"type": "package", "serviceable": true, "sha512": "sha512-vX8oq9HnYmAF7bek4aGgGFJficHDRTLgp/EOiPv9mBZq0i4SA96qVMYSjJ2YTaxs7Eljqit7pfpE2nmBhY5Fnw==", "path": "humanizer.core.ms-my/2.14.1", "hashPath": "humanizer.core.ms-my.2.14.1.nupkg.sha512"}, "Humanizer.Core.mt/2.14.1": {"type": "package", "serviceable": true, "sha512": "sha512-pEgTBzUI9hzemF7xrIZigl44LidTUhNu4x/P6M9sAwZjkUF0mMkbpxKkaasOql7lLafKrnszs0xFfaxQyzeuZQ==", "path": "humanizer.core.mt/2.14.1", "hashPath": "humanizer.core.mt.2.14.1.nupkg.sha512"}, "Humanizer.Core.nb/2.14.1": {"type": "package", "serviceable": true, "sha512": "sha512-mbs3m6JJq53ssLqVPxNfqSdTxAcZN3njlG8yhJVx83XVedpTe1ECK9aCa8FKVOXv93Gl+yRHF82Hw9T9LWv2hw==", "path": "humanizer.core.nb/2.14.1", "hashPath": "humanizer.core.nb.2.14.1.nupkg.sha512"}, "Humanizer.Core.nb-NO/2.14.1": {"type": "package", "serviceable": true, "sha512": "sha512-AsJxrrVYmIMbKDGe8W6Z6//wKv9dhWH7RsTcEHSr4tQt/80pcNvLi0hgD3fqfTtg0tWKtgch2cLf4prorEV+5A==", "path": "humanizer.core.nb-no/2.14.1", "hashPath": "humanizer.core.nb-no.2.14.1.nupkg.sha512"}, "Humanizer.Core.nl/2.14.1": {"type": "package", "serviceable": true, "sha512": "sha512-24b0OUdzJxfoqiHPCtYnR5Y4l/s4Oh7KW7uDp+qX25NMAHLCGog2eRfA7p2kRJp8LvnynwwQxm2p534V9m55wQ==", "path": "humanizer.core.nl/2.14.1", "hashPath": "humanizer.core.nl.2.14.1.nupkg.sha512"}, "Humanizer.Core.pl/2.14.1": {"type": "package", "serviceable": true, "sha512": "sha512-17mJNYaBssENVZyQHduiq+bvdXS0nhZJGEXtPKoMhKv3GD//WO0mEfd9wjEBsWCSmWI7bjRqhCidxzN+YtJmsg==", "path": "humanizer.core.pl/2.14.1", "hashPath": "humanizer.core.pl.2.14.1.nupkg.sha512"}, "Humanizer.Core.pt/2.14.1": {"type": "package", "serviceable": true, "sha512": "sha512-8HB8qavcVp2la1GJX6t+G9nDYtylPKzyhxr9LAooIei9MnQvNsjEiIE4QvHoeDZ4weuQ9CsPg1c211XUMVEZ4A==", "path": "humanizer.core.pt/2.14.1", "hashPath": "humanizer.core.pt.2.14.1.nupkg.sha512"}, "Humanizer.Core.ro/2.14.1": {"type": "package", "serviceable": true, "sha512": "sha512-psXNOcA6R8fSHoQYhpBTtTTYiOk8OBoN3PKCEDgsJKIyeY5xuK81IBdGi77qGZMu/OwBRQjQCBMtPJb0f4O1+A==", "path": "humanizer.core.ro/2.14.1", "hashPath": "humanizer.core.ro.2.14.1.nupkg.sha512"}, "Humanizer.Core.ru/2.14.1": {"type": "package", "serviceable": true, "sha512": "sha512-zm245xUWrajSN2t9H7BTf84/2APbUkKlUJpcdgsvTdAysr1ag9fi1APu6JEok39RRBXDfNRVZHawQ/U8X0pSvQ==", "path": "humanizer.core.ru/2.14.1", "hashPath": "humanizer.core.ru.2.14.1.nupkg.sha512"}, "Humanizer.Core.sk/2.14.1": {"type": "package", "serviceable": true, "sha512": "sha512-Ncw24Vf3ioRnbU4MsMFHafkyYi8JOnTqvK741GftlQvAbULBoTz2+e7JByOaasqeSi0KfTXeegJO+5Wk1c0Mbw==", "path": "humanizer.core.sk/2.14.1", "hashPath": "humanizer.core.sk.2.14.1.nupkg.sha512"}, "Humanizer.Core.sl/2.14.1": {"type": "package", "serviceable": true, "sha512": "sha512-l8sUy4ciAIbVThWNL0atzTS2HWtv8qJrsGWNlqrEKmPwA4SdKolSqnTes9V89fyZTc2Q43jK8fgzVE2C7t009A==", "path": "humanizer.core.sl/2.14.1", "hashPath": "humanizer.core.sl.2.14.1.nupkg.sha512"}, "Humanizer.Core.sr/2.14.1": {"type": "package", "serviceable": true, "sha512": "sha512-rnNvhpkOrWEymy7R/MiFv7uef8YO5HuXDyvojZ7JpijHWA5dXuVXooCOiA/3E93fYa3pxDuG2OQe4M/olXbQ7w==", "path": "humanizer.core.sr/2.14.1", "hashPath": "humanizer.core.sr.2.14.1.nupkg.sha512"}, "Humanizer.Core.sr-Latn/2.14.1": {"type": "package", "serviceable": true, "sha512": "sha512-nuy/ykpk974F8ItoQMS00kJPr2dFNjOSjgzCwfysbu7+gjqHmbLcYs7G4kshLwdA4AsVncxp99LYeJgoh1JF5g==", "path": "humanizer.core.sr-latn/2.14.1", "hashPath": "humanizer.core.sr-latn.2.14.1.nupkg.sha512"}, "Humanizer.Core.sv/2.14.1": {"type": "package", "serviceable": true, "sha512": "sha512-E53+tpAG0RCp+cSSI7TfBPC+NnsEqUuoSV0sU+rWRXWr9MbRWx1+Zj02XMojqjGzHjjOrBFBBio6m74seFl0AA==", "path": "humanizer.core.sv/2.14.1", "hashPath": "humanizer.core.sv.2.14.1.nupkg.sha512"}, "Humanizer.Core.th-TH/2.14.1": {"type": "package", "serviceable": true, "sha512": "sha512-eSevlJtvs1r4vQarNPfZ2kKDp/xMhuD00tVVzRXkSh1IAZbBJI/x2ydxUOwfK9bEwEp+YjvL1Djx2+kw7ziu7g==", "path": "humanizer.core.th-th/2.14.1", "hashPath": "humanizer.core.th-th.2.14.1.nupkg.sha512"}, "Humanizer.Core.tr/2.14.1": {"type": "package", "serviceable": true, "sha512": "sha512-rQ8N+o7yFcFqdbtu1mmbrXFi8TQ+uy+fVH9OPI0CI3Cu1om5hUU/GOMC3hXsTCI6d79y4XX+0HbnD7FT5khegA==", "path": "humanizer.core.tr/2.14.1", "hashPath": "humanizer.core.tr.2.14.1.nupkg.sha512"}, "Humanizer.Core.uk/2.14.1": {"type": "package", "serviceable": true, "sha512": "sha512-2uEfujwXKNm6bdpukaLtEJD+04uUtQD65nSGCetA1fYNizItEaIBUboNfr3GzJxSMQotNwGVM3+nSn8jTd0VSg==", "path": "humanizer.core.uk/2.14.1", "hashPath": "humanizer.core.uk.2.14.1.nupkg.sha512"}, "Humanizer.Core.uz-Cyrl-UZ/2.14.1": {"type": "package", "serviceable": true, "sha512": "sha512-TD3ME2sprAvFqk9tkWrvSKx5XxEMlAn1sjk+cYClSWZlIMhQQ2Bp/w0VjX1Kc5oeKjxRAnR7vFcLUFLiZIDk9Q==", "path": "humanizer.core.uz-cyrl-uz/2.14.1", "hashPath": "humanizer.core.uz-cyrl-uz.2.14.1.nupkg.sha512"}, "Humanizer.Core.uz-Latn-UZ/2.14.1": {"type": "package", "serviceable": true, "sha512": "sha512-/kHAoF4g0GahnugZiEMpaHlxb+W6jCEbWIdsq9/I1k48ULOsl/J0pxZj93lXC3omGzVF1BTVIeAtv5fW06Phsg==", "path": "humanizer.core.uz-latn-uz/2.14.1", "hashPath": "humanizer.core.uz-latn-uz.2.14.1.nupkg.sha512"}, "Humanizer.Core.vi/2.14.1": {"type": "package", "serviceable": true, "sha512": "sha512-rsQNh9rmHMBtnsUUlJbShMsIMGflZtPmrMM6JNDw20nhsvqfrdcoDD8cMnLAbuSovtc3dP+swRmLQzKmXDTVPA==", "path": "humanizer.core.vi/2.14.1", "hashPath": "humanizer.core.vi.2.14.1.nupkg.sha512"}, "Humanizer.Core.zh-CN/2.14.1": {"type": "package", "serviceable": true, "sha512": "sha512-uH2dWhrgugkCjDmduLdAFO9w1Mo0q07EuvM0QiIZCVm6FMCu/lGv2fpMu4GX+4HLZ6h5T2Pg9FIdDLCPN2a67w==", "path": "humanizer.core.zh-cn/2.14.1", "hashPath": "humanizer.core.zh-cn.2.14.1.nupkg.sha512"}, "Humanizer.Core.zh-Hans/2.14.1": {"type": "package", "serviceable": true, "sha512": "sha512-WH6IhJ8V1UBG7rZXQk3dZUoP2gsi8a0WkL8xL0sN6WGiv695s8nVcmab9tWz20ySQbuzp0UkSxUQFi5jJHIpOQ==", "path": "humanizer.core.zh-hans/2.14.1", "hashPath": "humanizer.core.zh-hans.2.14.1.nupkg.sha512"}, "Humanizer.Core.zh-Hant/2.14.1": {"type": "package", "serviceable": true, "sha512": "sha512-VIXB7HCUC34OoaGnO3HJVtSv2/wljPhjV7eKH4+TFPgQdJj2lvHNKY41Dtg0Bphu7X5UaXFR4zrYYyo+GNOjbA==", "path": "humanizer.core.zh-hant/2.14.1", "hashPath": "humanizer.core.zh-hant.2.14.1.nupkg.sha512"}, "MessagePack/2.1.152": {"type": "package", "serviceable": true, "sha512": "sha512-PlJ31qf42uGuJfwc61x/Pt4hJi01xh1rrBofj1MJSLzEot/2UAIRdSgxEHN/8qou5CV8OBeDM9HXKPi1Oj8rpQ==", "path": "messagepack/2.1.152", "hashPath": "messagepack.2.1.152.nupkg.sha512"}, "MessagePack.Annotations/2.1.152": {"type": "package", "serviceable": true, "sha512": "sha512-RONktDA/HA641ds/2bfOqYSVew8o8EJMcQ1P4M1J77QGgbzWiWt3nBHvCAwlx0VfO6K9S8xq4b5OLD2CUnhtCg==", "path": "messagepack.annotations/2.1.152", "hashPath": "messagepack.annotations.2.1.152.nupkg.sha512"}, "MessagePackAnalyzer/2.1.152": {"type": "package", "serviceable": true, "sha512": "sha512-uJhZlGMkXDaFYsH8V9S6o1EyvsUqB9mpU4DVBXNr0DXZVzZMhuLP1IkLj5xK3EKlaAcvkFkZv3eSvuz360wb3Q==", "path": "messagepackanalyzer/2.1.152", "hashPath": "messagepackanalyzer.2.1.152.nupkg.sha512"}, "Microsoft.AspNetCore.Authentication.Abstractions/2.2.0": {"type": "package", "serviceable": true, "sha512": "sha512-VloMLDJMf3n/9ic5lCBOa42IBYJgyB1JhzLsL68Zqg+2bEPWfGBj/xCJy/LrKTArN0coOcZp3wyVTZlx0y9pHQ==", "path": "microsoft.aspnetcore.authentication.abstractions/2.2.0", "hashPath": "microsoft.aspnetcore.authentication.abstractions.2.2.0.nupkg.sha512"}, "Microsoft.AspNetCore.Authentication.Core/2.2.0": {"type": "package", "serviceable": true, "sha512": "sha512-XlVJzJ5wPOYW+Y0J6Q/LVTEyfS4ssLXmt60T0SPP+D8abVhBTl+cgw2gDHlyKYIkcJg7btMVh383NDkMVqD/fg==", "path": "microsoft.aspnetcore.authentication.core/2.2.0", "hashPath": "microsoft.aspnetcore.authentication.core.2.2.0.nupkg.sha512"}, "Microsoft.AspNetCore.Authorization/2.2.0": {"type": "package", "serviceable": true, "sha512": "sha512-/L0W8H3jMYWyaeA9gBJqS/tSWBegP9aaTM0mjRhxTttBY9z4RVDRYJ2CwPAmAXIuPr3r1sOw+CS8jFVRGHRezQ==", "path": "microsoft.aspnetcore.authorization/2.2.0", "hashPath": "microsoft.aspnetcore.authorization.2.2.0.nupkg.sha512"}, "Microsoft.AspNetCore.Authorization.Policy/2.2.0": {"type": "package", "serviceable": true, "sha512": "sha512-aJCo6niDRKuNg2uS2WMEmhJTooQUGARhV2ENQ2tO5443zVHUo19MSgrgGo9FIrfD+4yKPF8Q+FF33WkWfPbyKw==", "path": "microsoft.aspnetcore.authorization.policy/2.2.0", "hashPath": "microsoft.aspnetcore.authorization.policy.2.2.0.nupkg.sha512"}, "Microsoft.AspNetCore.Cryptography.Internal/6.0.10": {"type": "package", "serviceable": true, "sha512": "sha512-F4YiT2L1H2p5imDno6nGMd/h9y3s0jpCbP0DvvCHA72b2TFRVHaE5qeGqeDkcRMK9d6RauVhXGiAT0zTY6DT+w==", "path": "microsoft.aspnetcore.cryptography.internal/6.0.10", "hashPath": "microsoft.aspnetcore.cryptography.internal.6.0.10.nupkg.sha512"}, "Microsoft.AspNetCore.Cryptography.KeyDerivation/6.0.10": {"type": "package", "serviceable": true, "sha512": "sha512-ziq8vzvlVN0yKHzUp+z0qwZoSU2P/XUdNsEJxRzLRg1HbHp11Za6c2cuR6f+MgjxLHGkCupSam5b9is4sJSs5A==", "path": "microsoft.aspnetcore.cryptography.keyderivation/6.0.10", "hashPath": "microsoft.aspnetcore.cryptography.keyderivation.6.0.10.nupkg.sha512"}, "Microsoft.AspNetCore.Diagnostics.EntityFrameworkCore/6.0.10": {"type": "package", "serviceable": true, "sha512": "sha512-QfoXqYywSKAal7dyZDpKABuFRd+eWXKXSnuLHNQNME0OAgQHDDHQLQc/ek5uK68vCRqx9QFq6mlXLh5NNxOEqQ==", "path": "microsoft.aspnetcore.diagnostics.entityframeworkcore/6.0.10", "hashPath": "microsoft.aspnetcore.diagnostics.entityframeworkcore.6.0.10.nupkg.sha512"}, "Microsoft.AspNetCore.Hosting.Abstractions/2.2.0": {"type": "package", "serviceable": true, "sha512": "sha512-ubycklv+ZY7Kutdwuy1W4upWcZ6VFR8WUXU7l7B2+mvbDBBPAcfpi+E+Y5GFe+Q157YfA3C49D2GCjAZc7Mobw==", "path": "microsoft.aspnetcore.hosting.abstractions/2.2.0", "hashPath": "microsoft.aspnetcore.hosting.abstractions.2.2.0.nupkg.sha512"}, "Microsoft.AspNetCore.Hosting.Server.Abstractions/2.2.0": {"type": "package", "serviceable": true, "sha512": "sha512-1PMijw8RMtuQF60SsD/JlKtVfvh4NORAhF4wjysdABhlhTrYmtgssqyncR0Stq5vqtjplZcj6kbT4LRTglt9IQ==", "path": "microsoft.aspnetcore.hosting.server.abstractions/2.2.0", "hashPath": "microsoft.aspnetcore.hosting.server.abstractions.2.2.0.nupkg.sha512"}, "Microsoft.AspNetCore.Http/2.2.0": {"type": "package", "serviceable": true, "sha512": "sha512-YogBSMotWPAS/X5967pZ+yyWPQkThxhmzAwyCHCSSldzYBkW5W5d6oPfBaPqQOnSHYTpSOSOkpZoAce0vwb6+A==", "path": "microsoft.aspnetcore.http/2.2.0", "hashPath": "microsoft.aspnetcore.http.2.2.0.nupkg.sha512"}, "Microsoft.AspNetCore.Http.Abstractions/2.2.0": {"type": "package", "serviceable": true, "sha512": "sha512-Nxs7Z1q3f1STfLYKJSVXCs1iBl+Ya6E8o4Oy1bCxJ/rNI44E/0f6tbsrVqAWfB7jlnJfyaAtIalBVxPKUPQb4Q==", "path": "microsoft.aspnetcore.http.abstractions/2.2.0", "hashPath": "microsoft.aspnetcore.http.abstractions.2.2.0.nupkg.sha512"}, "Microsoft.AspNetCore.Http.Extensions/2.2.0": {"type": "package", "serviceable": true, "sha512": "sha512-2DgZ9rWrJtuR7RYiew01nGRzuQBDaGHGmK56Rk54vsLLsCdzuFUPqbDTJCS1qJQWTbmbIQ9wGIOjpxA1t0l7/w==", "path": "microsoft.aspnetcore.http.extensions/2.2.0", "hashPath": "microsoft.aspnetcore.http.extensions.2.2.0.nupkg.sha512"}, "Microsoft.AspNetCore.Http.Features/2.2.0": {"type": "package", "serviceable": true, "sha512": "sha512-ziFz5zH8f33En4dX81LW84I6XrYXKf9jg6aM39cM+LffN9KJahViKZ61dGMSO2gd3e+qe5yBRwsesvyqlZaSMg==", "path": "microsoft.aspnetcore.http.features/2.2.0", "hashPath": "microsoft.aspnetcore.http.features.2.2.0.nupkg.sha512"}, "Microsoft.AspNetCore.Identity.EntityFrameworkCore/6.0.10": {"type": "package", "serviceable": true, "sha512": "sha512-GLynXPCJFSNELz/umiKY4xje2NG3f84e0zXdpzGJ121vd/doHnRkcO67aVulk0Ek1vsZ24zTnju1zrzsqU+wSg==", "path": "microsoft.aspnetcore.identity.entityframeworkcore/6.0.10", "hashPath": "microsoft.aspnetcore.identity.entityframeworkcore.6.0.10.nupkg.sha512"}, "Microsoft.AspNetCore.Identity.UI/6.0.10": {"type": "package", "serviceable": true, "sha512": "sha512-oB0PdqoSa9TOhVlqJgSNG7ipjlPRl9I7cRKBx6dF/Kl2DsgrNEmzAUtwTiRzAcljchyxGqggnLPP50HX7Jm5mg==", "path": "microsoft.aspnetcore.identity.ui/6.0.10", "hashPath": "microsoft.aspnetcore.identity.ui.6.0.10.nupkg.sha512"}, "Microsoft.AspNetCore.Mvc.Abstractions/2.2.0": {"type": "package", "serviceable": true, "sha512": "sha512-ET6uZpfVbGR1NjCuLaLy197cQ3qZUjzl7EG5SL4GfJH/c9KRE89MMBrQegqWsh0w1iRUB/zQaK0anAjxa/pz4g==", "path": "microsoft.aspnetcore.mvc.abstractions/2.2.0", "hashPath": "microsoft.aspnetcore.mvc.abstractions.2.2.0.nupkg.sha512"}, "Microsoft.AspNetCore.Mvc.Core/2.2.5": {"type": "package", "serviceable": true, "sha512": "sha512-/8sr8ixIUD57UFwUntha9bOwex7/AkZfdk1f9oNJG1Ek7p/uuKVa7fuHmYZpQOf35Oxrt+2Ku4WPwMSbNxOuWg==", "path": "microsoft.aspnetcore.mvc.core/2.2.5", "hashPath": "microsoft.aspnetcore.mvc.core.2.2.5.nupkg.sha512"}, "Microsoft.AspNetCore.Razor.Language/6.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-yCtBr1GSGzJrrp1NJUb4ltwFYMKHw/tJLnIDvg9g/FnkGIEzmE19tbCQqXARIJv5kdtBgsoVIdGLL+zmjxvM/A==", "path": "microsoft.aspnetcore.razor.language/6.0.0", "hashPath": "microsoft.aspnetcore.razor.language.6.0.0.nupkg.sha512"}, "Microsoft.AspNetCore.ResponseCaching.Abstractions/2.2.0": {"type": "package", "serviceable": true, "sha512": "sha512-CIHWEKrHzZfFp7t57UXsueiSA/raku56TgRYauV/W1+KAQq6vevz60zjEKaazt3BI76zwMz3B4jGWnCwd8kwQw==", "path": "microsoft.aspnetcore.responsecaching.abstractions/2.2.0", "hashPath": "microsoft.aspnetcore.responsecaching.abstractions.2.2.0.nupkg.sha512"}, "Microsoft.AspNetCore.Routing/2.2.0": {"type": "package", "serviceable": true, "sha512": "sha512-jAhDBy0wryOnMhhZTtT9z63gJbvCzFuLm8yC6pHzuVu9ZD1dzg0ltxIwT4cfwuNkIL/TixdKsm3vpVOpG8euWQ==", "path": "microsoft.aspnetcore.routing/2.2.0", "hashPath": "microsoft.aspnetcore.routing.2.2.0.nupkg.sha512"}, "Microsoft.AspNetCore.Routing.Abstractions/2.2.0": {"type": "package", "serviceable": true, "sha512": "sha512-lRRaPN7jDlUCVCp9i0W+PB0trFaKB0bgMJD7hEJS9Uo4R9MXaMC8X2tJhPLmeVE3SGDdYI4QNKdVmhNvMJGgPQ==", "path": "microsoft.aspnetcore.routing.abstractions/2.2.0", "hashPath": "microsoft.aspnetcore.routing.abstractions.2.2.0.nupkg.sha512"}, "Microsoft.AspNetCore.WebUtilities/2.2.0": {"type": "package", "serviceable": true, "sha512": "sha512-9ErxAAKaDzxXASB/b5uLEkLgUWv1QbeVxyJYEHQwMaxXOeFFVkQxiq8RyfVcifLU7NR0QY0p3acqx4ZpYfhHDg==", "path": "microsoft.aspnetcore.webutilities/2.2.0", "hashPath": "microsoft.aspnetcore.webutilities.2.2.0.nupkg.sha512"}, "Microsoft.Bcl.AsyncInterfaces/5.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-W8<PERSON>QjkMScOMTtJbPwmPyj9c3zYSFGawDW3jwlBOOsnY+EzZFLgNQ/UMkK35JmkNOVPdCyPr2Tw7Vv9N+KA3ZQ==", "path": "microsoft.bcl.asyncinterfaces/5.0.0", "hashPath": "microsoft.bcl.asyncinterfaces.5.0.0.nupkg.sha512"}, "Microsoft.Build/17.3.2": {"type": "package", "serviceable": true, "sha512": "sha512-k5+7CfF/aM/hykfnrF93VhbUnhGfpJkGaD+ce8VlhLnOqDyts7WV+8Up3YCP6qmXMZFeeH/Cp23w2wSliP0mBw==", "path": "microsoft.build/17.3.2", "hashPath": "microsoft.build.17.3.2.nupkg.sha512"}, "Microsoft.Build.Framework/17.3.2": {"type": "package", "serviceable": true, "sha512": "sha512-iGfJt6rm/vIEowBG6qNX2Udn7UagI6MzalDwwdkDUkSwhvvrGCnDLphyRABAwrrsWHTD/LJlUAJsbW1SkC4CUQ==", "path": "microsoft.build.framework/17.3.2", "hashPath": "microsoft.build.framework.17.3.2.nupkg.sha512"}, "Microsoft.CodeAnalysis.Analyzers/3.3.2": {"type": "package", "serviceable": true, "sha512": "sha512-7xt6zTlIEizUgEsYAIgm37EbdkiMmr6fP6J9pDoKEpiGM4pi32BCPGr/IczmSJI9Zzp0a6HOzpr9OvpMP+2veA==", "path": "microsoft.codeanalysis.analyzers/3.3.2", "hashPath": "microsoft.codeanalysis.analyzers.3.3.2.nupkg.sha512"}, "Microsoft.CodeAnalysis.AnalyzerUtilities/3.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-gyQ70pJ4T7hu/s0+QnEaXtYfeG/JrttGnxHJlrhpxsQjRIUGuRhVwNBtkHHYOrUAZ/l47L98/NiJX6QmTwAyrg==", "path": "microsoft.codeanalysis.analyzerutilities/3.3.0", "hashPath": "microsoft.codeanalysis.analyzerutilities.3.3.0.nupkg.sha512"}, "Microsoft.CodeAnalysis.Common/4.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-d02ybMhUJl1r/dI6SkJPHrTiTzXBYCZeJdOLMckV+jyoMU/GGkjqFX/sRbv1K0QmlpwwKuLTiYVQvfYC+8ox2g==", "path": "microsoft.codeanalysis.common/4.0.0", "hashPath": "microsoft.codeanalysis.common.4.0.0.nupkg.sha512"}, "Microsoft.CodeAnalysis.CSharp/4.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-2UVTGtyQGgTCazvnT6t82f+7AV2L+kqJdyb61rT9GQed4yK+tVh5IkaKcsm70VqyZQhBbDqsfZFNHnY65xhrRw==", "path": "microsoft.codeanalysis.csharp/4.0.0", "hashPath": "microsoft.codeanalysis.csharp.4.0.0.nupkg.sha512"}, "Microsoft.CodeAnalysis.CSharp.Features/4.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-WOTeKJN0I4/AzylA+VuTtB7V7VvnM41GXqnyiiDRNa3QtKFAzJbQ7CwLG97pcme+oQLz708z+s4Nzd9g0irytw==", "path": "microsoft.codeanalysis.csharp.features/4.0.0", "hashPath": "microsoft.codeanalysis.csharp.features.4.0.0.nupkg.sha512"}, "Microsoft.CodeAnalysis.CSharp.Workspaces/4.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-RQMc1+2cIRdOvY8vp6ygkzfBrvlYphnbmhhluKNh9+X+PpprQDKlbPrn9fLn6v9RlCsfa87joS3zJyGBEeWTXQ==", "path": "microsoft.codeanalysis.csharp.workspaces/4.0.0", "hashPath": "microsoft.codeanalysis.csharp.workspaces.4.0.0.nupkg.sha512"}, "Microsoft.CodeAnalysis.Features/4.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-ci4pgjEgihb+fu2i1E12iOMGXU6GisYR3YrNd2aqt55ijjzLNqPV/+zt+cV3Qc0n08rJrIY2Bf6cTTMPY0bwJQ==", "path": "microsoft.codeanalysis.features/4.0.0", "hashPath": "microsoft.codeanalysis.features.4.0.0.nupkg.sha512"}, "Microsoft.CodeAnalysis.Razor/6.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-uqdzuQXxD7XrJCbIbbwpI/LOv0PBJ9VIR0gdvANTHOfK5pjTaCir+XcwvYvBZ5BIzd0KGzyiamzlEWw1cK1q0w==", "path": "microsoft.codeanalysis.razor/6.0.0", "hashPath": "microsoft.codeanalysis.razor.6.0.0.nupkg.sha512"}, "Microsoft.CodeAnalysis.Scripting.Common/4.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-jU/ncoHSFnXU/L2iju9AFlOwmsAYByIxhCCpeLPNTguXwosJp1o13mtrboJOfkDhi7ebvzQmvcZ++yBu8cF4Sw==", "path": "microsoft.codeanalysis.scripting.common/4.0.0", "hashPath": "microsoft.codeanalysis.scripting.common.4.0.0.nupkg.sha512"}, "Microsoft.CodeAnalysis.Workspaces.Common/4.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-bEfHzx8PLmEN6aogVYHWmViHCWfms8emI43w9nNX+C8MLgtynOmVL/OJ4hbuONVp+4OuDAy5BDj69n5MnXQX8g==", "path": "microsoft.codeanalysis.workspaces.common/4.0.0", "hashPath": "microsoft.codeanalysis.workspaces.common.4.0.0.nupkg.sha512"}, "Microsoft.CSharp/4.5.0": {"type": "package", "serviceable": true, "sha512": "sha512-kaj6Wb4qoMuH3HySFJhxwQfe8R/sJsNJnANrvv8WdFPMoNbKY5htfNscv+LHCu5ipz+49m2e+WQXpLXr9XYemQ==", "path": "microsoft.csharp/4.5.0", "hashPath": "microsoft.csharp.4.5.0.nupkg.sha512"}, "Microsoft.Data.SqlClient/2.1.4": {"type": "package", "serviceable": true, "sha512": "sha512-cDcKBTKILdRuAzJjbgXwGcUQXzMue+SG02kD4tZTXXfoz4ALrGLpCnA5k9khw3fnAMlMnRzLIGuvRdJurqmESA==", "path": "microsoft.data.sqlclient/2.1.4", "hashPath": "microsoft.data.sqlclient.2.1.4.nupkg.sha512"}, "Microsoft.Data.SqlClient.SNI.runtime/2.1.1": {"type": "package", "serviceable": true, "sha512": "sha512-JwGDWkyZgm7SATJmFLfT2G4teimvNbNtq3lsS9a5DzvhEZnQrZjZhevCU0vdx8MjheLHoG5vocuO03QtioFQxQ==", "path": "microsoft.data.sqlclient.sni.runtime/2.1.1", "hashPath": "microsoft.data.sqlclient.sni.runtime.2.1.1.nupkg.sha512"}, "Microsoft.DiaSymReader/1.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-/fn1Tfo7j7k/slViPlM8azJuxQmri7FZ8dQ+gTeLbI29leN/1VK0U/BFcRdJNctsRCUgyKJ2q+I0Tjq07Rc1/Q==", "path": "microsoft.diasymreader/1.3.0", "hashPath": "microsoft.diasymreader.1.3.0.nupkg.sha512"}, "Microsoft.DotNet.PlatformAbstractions/2.1.0": {"type": "package", "serviceable": true, "sha512": "sha512-9KPDwvb/hLEVXYruVHVZ8BkebC8j17DmPb56LnqRF74HqSPLjCkrlFUjOtFpQPA2DeADBRTI/e69aCfRBfrhxw==", "path": "microsoft.dotnet.platformabstractions/2.1.0", "hashPath": "microsoft.dotnet.platformabstractions.2.1.0.nupkg.sha512"}, "Microsoft.DotNet.Scaffolding.Shared/6.0.16": {"type": "package", "serviceable": true, "sha512": "sha512-c4oruz49J7HALnyG15bfyfbXM3srbzcAfjre4y4UaPxwi8ZPQuig8SrkbfS+ixxSq0+Vf86PWd8X2qg2F2YJBg==", "path": "microsoft.dotnet.scaffolding.shared/6.0.16", "hashPath": "microsoft.dotnet.scaffolding.shared.6.0.16.nupkg.sha512"}, "Microsoft.EntityFrameworkCore/6.0.10": {"type": "package", "serviceable": true, "sha512": "sha512-aMk8c7XKynkDJM8vnRuVz3VHSLiWy4tWpkvSdrQ4No1DNLdtTI6P3iT2wAPvVkuJsS22Ifs62/Jr6AyveX5b4A==", "path": "microsoft.entityframeworkcore/6.0.10", "hashPath": "microsoft.entityframeworkcore.6.0.10.nupkg.sha512"}, "Microsoft.EntityFrameworkCore.Abstractions/6.0.10": {"type": "package", "serviceable": true, "sha512": "sha512-3BMwnBB6Bgwd6bjUqx2pOYuHpGBHCJxY3vorRJYX3U2wzrz5q4jNxDZZGsMViFZeJ7PXFIwbgy6rR73J5aalsg==", "path": "microsoft.entityframeworkcore.abstractions/6.0.10", "hashPath": "microsoft.entityframeworkcore.abstractions.6.0.10.nupkg.sha512"}, "Microsoft.EntityFrameworkCore.Analyzers/6.0.10": {"type": "package", "serviceable": true, "sha512": "sha512-b4jKcjo6BLuBRjLTkZPjJCvZ7oa3a798Q1AXSMAknitpBEOEIDryyRd7XZ0cnEIVCvfSND+Trgb00Z4TiTqOvg==", "path": "microsoft.entityframeworkcore.analyzers/6.0.10", "hashPath": "microsoft.entityframeworkcore.analyzers.6.0.10.nupkg.sha512"}, "Microsoft.EntityFrameworkCore.Design/6.0.10": {"type": "package", "serviceable": true, "sha512": "sha512-SVqDfUftgBoKCMPfTaWXiKBZPHMjbiBJLE5WE7MeD28nTk7CkmUNX8eXyNIeWxpDuk4r0zZ6XG9zyG7Ef3KS4A==", "path": "microsoft.entityframeworkcore.design/6.0.10", "hashPath": "microsoft.entityframeworkcore.design.6.0.10.nupkg.sha512"}, "Microsoft.EntityFrameworkCore.Relational/6.0.10": {"type": "package", "serviceable": true, "sha512": "sha512-8rjVzKtGSTojSefghXqh3Y2wq8A7P7iWuUQQyQieXNYrYA7nw2aHZI2rjU+7ta4jHgKITddUHFaPQJ69H18dQA==", "path": "microsoft.entityframeworkcore.relational/6.0.10", "hashPath": "microsoft.entityframeworkcore.relational.6.0.10.nupkg.sha512"}, "Microsoft.EntityFrameworkCore.SqlServer/6.0.10": {"type": "package", "serviceable": true, "sha512": "sha512-A5xZ2yO3zdYdZKlX90AjMruftIT73ApG2yW11T7sougUi1HyzpTh9/RF9LaLeMxZVzIAjuKvb7WkVzxduc2/Kg==", "path": "microsoft.entityframeworkcore.sqlserver/6.0.10", "hashPath": "microsoft.entityframeworkcore.sqlserver.6.0.10.nupkg.sha512"}, "Microsoft.EntityFrameworkCore.Tools/6.0.10": {"type": "package", "serviceable": true, "sha512": "sha512-LWQvCb3lmTxkcnzO8H0ib0Ot/Pow9Kubt2iqvhhAHEbhs7SS1ygB938EpmuD9o1yyyliAg6FHln5o6jVfEkdyQ==", "path": "microsoft.entityframeworkcore.tools/6.0.10", "hashPath": "microsoft.entityframeworkcore.tools.6.0.10.nupkg.sha512"}, "Microsoft.Extensions.Caching.Abstractions/6.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-bcz5sSFJbganH0+YrfvIjJDIcKNW7TL07C4d1eTmXy/wOt52iz4LVogJb6pazs7W0+74j0YpXFErvp++Aq5Bsw==", "path": "microsoft.extensions.caching.abstractions/6.0.0", "hashPath": "microsoft.extensions.caching.abstractions.6.0.0.nupkg.sha512"}, "Microsoft.Extensions.Caching.Memory/6.0.1": {"type": "package", "serviceable": true, "sha512": "sha512-B4y+Cev05eMcjf1na0v9gza6GUtahXbtY1JCypIgx3B4Ea/KAgsWyXEmW4q6zMbmTMtKzmPVk09rvFJirvMwTg==", "path": "microsoft.extensions.caching.memory/6.0.1", "hashPath": "microsoft.extensions.caching.memory.6.0.1.nupkg.sha512"}, "Microsoft.Extensions.Configuration.Abstractions/6.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-qWzV9o+ZRWq+pGm+1dF+R7qTgTYoXvbyowRoBxQJGfqTpqDun2eteerjRQhq5PQ/14S+lqto3Ft4gYaRyl4rdQ==", "path": "microsoft.extensions.configuration.abstractions/6.0.0", "hashPath": "microsoft.extensions.configuration.abstractions.6.0.0.nupkg.sha512"}, "Microsoft.Extensions.DependencyInjection/6.0.1": {"type": "package", "serviceable": true, "sha512": "sha512-vWXPg3HJQIpZkENn1KWq8SfbqVujVD7S7vIAyFXXqK5xkf1Vho+vG0bLBCHxU36lD1cLLtmGpfYf0B3MYFi9tQ==", "path": "microsoft.extensions.dependencyinjection/6.0.1", "hashPath": "microsoft.extensions.dependencyinjection.6.0.1.nupkg.sha512"}, "Microsoft.Extensions.DependencyInjection.Abstractions/6.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-xlzi2IYREJH3/m6+lUrQlujzX8wDitm4QGnUu6kUXTQAWPuZY8i+ticFJbzfqaetLA6KR/rO6Ew/HuYD+bxifg==", "path": "microsoft.extensions.dependencyinjection.abstractions/6.0.0", "hashPath": "microsoft.extensions.dependencyinjection.abstractions.6.0.0.nupkg.sha512"}, "Microsoft.Extensions.DependencyModel/2.1.0": {"type": "package", "serviceable": true, "sha512": "sha512-nS2XKqi+1A1umnYNLX2Fbm/XnzCxs5i+zXVJ3VC6r9t2z0NZr9FLnJN4VQpKigdcWH/iFTbMuX6M6WQJcTjVIg==", "path": "microsoft.extensions.dependencymodel/2.1.0", "hashPath": "microsoft.extensions.dependencymodel.2.1.0.nupkg.sha512"}, "Microsoft.Extensions.FileProviders.Abstractions/6.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-0pd4/fho0gC12rQswaGQxbU34jOS1TPS8lZPpkFCH68ppQjHNHYle9iRuHeev1LhrJ94YPvzcRd8UmIuFk23Qw==", "path": "microsoft.extensions.fileproviders.abstractions/6.0.0", "hashPath": "microsoft.extensions.fileproviders.abstractions.6.0.0.nupkg.sha512"}, "Microsoft.Extensions.FileProviders.Embedded/6.0.10": {"type": "package", "serviceable": true, "sha512": "sha512-/BpehFkQiwqnG1kbhSXtBC1t26a7GFEeWGwOQPWtBuEe2JaBYY0ELFQmB17W33HPXXqj1KUvIrQsTmodlRQIdQ==", "path": "microsoft.extensions.fileproviders.embedded/6.0.10", "hashPath": "microsoft.extensions.fileproviders.embedded.6.0.10.nupkg.sha512"}, "Microsoft.Extensions.Hosting.Abstractions/2.2.0": {"type": "package", "serviceable": true, "sha512": "sha512-+k4AEn68HOJat5gj1TWa6X28WlirNQO9sPIIeQbia+91n03esEtMSSoekSTpMjUzjqtJWQN3McVx0GvSPFHF/Q==", "path": "microsoft.extensions.hosting.abstractions/2.2.0", "hashPath": "microsoft.extensions.hosting.abstractions.2.2.0.nupkg.sha512"}, "Microsoft.Extensions.Identity.Core/6.0.10": {"type": "package", "serviceable": true, "sha512": "sha512-/w7FSk2rqeFmbVgc5zDnCnSxJHoiYmmFpgke3P4F6XyoX6Pb2cAgrYnPLm1TzDgtK0V/nIRgz0r+mP4fq0MSxw==", "path": "microsoft.extensions.identity.core/6.0.10", "hashPath": "microsoft.extensions.identity.core.6.0.10.nupkg.sha512"}, "Microsoft.Extensions.Identity.Stores/6.0.10": {"type": "package", "serviceable": true, "sha512": "sha512-a0OGDd75e24oBnDCb8gKfV94DjqX+BDgf86IRAp96TFz//6F1v6OjHz5IBJ6yJoy9A1XlzC/HKV7e4n4uuGdiw==", "path": "microsoft.extensions.identity.stores/6.0.10", "hashPath": "microsoft.extensions.identity.stores.6.0.10.nupkg.sha512"}, "Microsoft.Extensions.Logging/6.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-eIbyj40QDg1NDz0HBW0S5f3wrLVnKWnDJ/JtZ+yJDFnDj90VoPuoPmFkeaXrtu+0cKm5GRAwoDf+dBWXK0TUdg==", "path": "microsoft.extensions.logging/6.0.0", "hashPath": "microsoft.extensions.logging.6.0.0.nupkg.sha512"}, "Microsoft.Extensions.Logging.Abstractions/6.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-/HggWBbTwy8TgebGSX5DBZ24ndhzi93sHUBDvP1IxbZD7FDokYzdAr6+vbWGjw2XAfR2EJ1sfKUotpjHnFWPxA==", "path": "microsoft.extensions.logging.abstractions/6.0.0", "hashPath": "microsoft.extensions.logging.abstractions.6.0.0.nupkg.sha512"}, "Microsoft.Extensions.ObjectPool/2.2.0": {"type": "package", "serviceable": true, "sha512": "sha512-gA8H7uQOnM5gb+L0uTNjViHYr+hRDqCdfugheGo/MxQnuHzmhhzCBTIPm19qL1z1Xe0NEMabfcOBGv9QghlZ8g==", "path": "microsoft.extensions.objectpool/2.2.0", "hashPath": "microsoft.extensions.objectpool.2.2.0.nupkg.sha512"}, "Microsoft.Extensions.Options/6.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-dzXN0+V1AyjOe2xcJ86Qbo233KHuLEY0njf/P2Kw8SfJU+d45HNS2ctJdnEnrWbM9Ye2eFgaC5Mj9otRMU6IsQ==", "path": "microsoft.extensions.options/6.0.0", "hashPath": "microsoft.extensions.options.6.0.0.nupkg.sha512"}, "Microsoft.Extensions.Primitives/6.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-9+PnzmQFfEFNR9J2aDTfJGGupShHjOuGw4VUv+JB044biSHrnmCIMD+mJHmb2H7YryrfBEXDurxQ47gJZdCKNQ==", "path": "microsoft.extensions.primitives/6.0.0", "hashPath": "microsoft.extensions.primitives.6.0.0.nupkg.sha512"}, "Microsoft.Identity.Client/4.21.1": {"type": "package", "serviceable": true, "sha512": "sha512-vycgk7S/HAbHaUaK4Tid1fsWHsXdFRRP2KavAIOHCVV27zvuQfYAjXmMvctuuF4egydSumG58CwPZob3gWeYgQ==", "path": "microsoft.identity.client/4.21.1", "hashPath": "microsoft.identity.client.4.21.1.nupkg.sha512"}, "Microsoft.IdentityModel.JsonWebTokens/6.8.0": {"type": "package", "serviceable": true, "sha512": "sha512-+7JIww64PkMt7NWFxoe4Y/joeF7TAtA/fQ0b2GFGcagzB59sKkTt/sMZWR6aSZht5YC7SdHi3W6yM1yylRGJCQ==", "path": "microsoft.identitymodel.jsonwebtokens/6.8.0", "hashPath": "microsoft.identitymodel.jsonwebtokens.6.8.0.nupkg.sha512"}, "Microsoft.IdentityModel.Logging/6.8.0": {"type": "package", "serviceable": true, "sha512": "sha512-Rfh/p4MaN4gkmhPxwbu8IjrmoDncGfHHPh1sTnc0AcM/Oc39/fzC9doKNWvUAjzFb8LqA6lgZyblTrIsX/wDXg==", "path": "microsoft.identitymodel.logging/6.8.0", "hashPath": "microsoft.identitymodel.logging.6.8.0.nupkg.sha512"}, "Microsoft.IdentityModel.Protocols/6.8.0": {"type": "package", "serviceable": true, "sha512": "sha512-OJZx5nPdiH+MEkwCkbJrTAUiO/YzLe0VSswNlDxJsJD9bhOIdXHufh650pfm59YH1DNevp3/bXzukKrG57gA1w==", "path": "microsoft.identitymodel.protocols/6.8.0", "hashPath": "microsoft.identitymodel.protocols.6.8.0.nupkg.sha512"}, "Microsoft.IdentityModel.Protocols.OpenIdConnect/6.8.0": {"type": "package", "serviceable": true, "sha512": "sha512-X/PiV5l3nYYsodtrNMrNQIVlDmHpjQQ5w48E+o/D5H4es2+4niEyQf3l03chvZGWNzBRhfSstaXr25/Ye4AeYw==", "path": "microsoft.identitymodel.protocols.openidconnect/6.8.0", "hashPath": "microsoft.identitymodel.protocols.openidconnect.6.8.0.nupkg.sha512"}, "Microsoft.IdentityModel.Tokens/6.8.0": {"type": "package", "serviceable": true, "sha512": "sha512-gTqzsGcmD13HgtNePPcuVHZ/NXWmyV+InJgalW/FhWpII1D7V1k0obIseGlWMeA4G+tZfeGMfXr0klnWbMR/mQ==", "path": "microsoft.identitymodel.tokens/6.8.0", "hashPath": "microsoft.identitymodel.tokens.6.8.0.nupkg.sha512"}, "Microsoft.Net.Http.Headers/2.2.0": {"type": "package", "serviceable": true, "sha512": "sha512-iZNkjYqlo8sIOI0bQfpsSoMTmB/kyvmV2h225ihyZT33aTp48ZpF6qYnXxzSXmHt8DpBAwBTX+1s1UFLbYfZKg==", "path": "microsoft.net.http.headers/2.2.0", "hashPath": "microsoft.net.http.headers.2.2.0.nupkg.sha512"}, "Microsoft.NET.StringTools/17.3.2": {"type": "package", "serviceable": true, "sha512": "sha512-3sIZECEDSY9kP7BqPLOSIHLsiqv0TSU5cIGAMung+NrefIooo1tBMVRt598CGz+kUF1xlbOsO8nPAYpgfokx/Q==", "path": "microsoft.net.stringtools/17.3.2", "hashPath": "microsoft.net.stringtools.17.3.2.nupkg.sha512"}, "Microsoft.NETCore.Platforms/1.1.0": {"type": "package", "serviceable": true, "sha512": "sha512-kz0PEW2lhqygehI/d6XsPCQzD7ff7gUJaVGPVETX611eadGsA3A877GdSlU0LRVMCTH/+P3o2iDTak+S08V2+A==", "path": "microsoft.netcore.platforms/1.1.0", "hashPath": "microsoft.netcore.platforms.1.1.0.nupkg.sha512"}, "Microsoft.NETCore.Targets/1.1.0": {"type": "package", "serviceable": true, "sha512": "sha512-aOZA3BWfz9RXjpzt0sRJJMjAscAUm3Hoa4UWAfceV9UTYxgwZ1lZt5nO2myFf+/jetYQo4uTP7zS8sJY67BBxg==", "path": "microsoft.netcore.targets/1.1.0", "hashPath": "microsoft.netcore.targets.1.1.0.nupkg.sha512"}, "Microsoft.VisualStudio.Debugger.Contracts/17.2.0": {"type": "package", "serviceable": true, "sha512": "sha512-br/qV/aHqLqVlqtcMKglCC8MHMMLey0yMkKSplnMl58F5gKjwnh7wjs8+g0j/vf4T6h4KK7JWrC0+oN70pbugg==", "path": "microsoft.visualstudio.debugger.contracts/17.2.0", "hashPath": "microsoft.visualstudio.debugger.contracts.17.2.0.nupkg.sha512"}, "Microsoft.VisualStudio.Web.CodeGeneration/6.0.16": {"type": "package", "serviceable": true, "sha512": "sha512-Bkk8ZEBrwrlLqgmce1sm7tlO2AKh3eTceR3JsS1YonCAJ7NKLnkHLyNaPxTgZ6TN5xgoksxzMkfyhDPiyQ6QPw==", "path": "microsoft.visualstudio.web.codegeneration/6.0.16", "hashPath": "microsoft.visualstudio.web.codegeneration.6.0.16.nupkg.sha512"}, "Microsoft.VisualStudio.Web.CodeGeneration.Core/6.0.16": {"type": "package", "serviceable": true, "sha512": "sha512-ipaMs7Lugc8llq77w/qdlTIcsCuei6O/T7l1qKbiKQ+ydnfaTgskcfHW/SxwAjvaC5ijrgKMaMrnKCZC4UocDg==", "path": "microsoft.visualstudio.web.codegeneration.core/6.0.16", "hashPath": "microsoft.visualstudio.web.codegeneration.core.6.0.16.nupkg.sha512"}, "Microsoft.VisualStudio.Web.CodeGeneration.Design/6.0.16": {"type": "package", "serviceable": true, "sha512": "sha512-92befclpF95fZIJbmOYdQcR28ynDD6Mj2dJrUkxhe6LX26k2UN4CYRFQ22Jo5rvqYq4dZrmjw77qicCC+SVLmg==", "path": "microsoft.visualstudio.web.codegeneration.design/6.0.16", "hashPath": "microsoft.visualstudio.web.codegeneration.design.6.0.16.nupkg.sha512"}, "Microsoft.VisualStudio.Web.CodeGeneration.EntityFrameworkCore/6.0.16": {"type": "package", "serviceable": true, "sha512": "sha512-xAO4veABPkd3mJtMaumMNQcLpzrCe9z76qu56m1IMeBxuXH3cCc0+JBVKyEnvM1h8qi34E+zR9SfdbDTgmXxgw==", "path": "microsoft.visualstudio.web.codegeneration.entityframeworkcore/6.0.16", "hashPath": "microsoft.visualstudio.web.codegeneration.entityframeworkcore.6.0.16.nupkg.sha512"}, "Microsoft.VisualStudio.Web.CodeGeneration.Templating/6.0.16": {"type": "package", "serviceable": true, "sha512": "sha512-fUqz8nro0Xw7MysTidVX2C0WnS4v8oXrB+qMNU34xQzG2dxOnLPcIlqIXAs3zQXR7Jmr9fZKXrzsxq5mrLhw5A==", "path": "microsoft.visualstudio.web.codegeneration.templating/6.0.16", "hashPath": "microsoft.visualstudio.web.codegeneration.templating.6.0.16.nupkg.sha512"}, "Microsoft.VisualStudio.Web.CodeGeneration.Utils/6.0.16": {"type": "package", "serviceable": true, "sha512": "sha512-hBi10iGLW7dFz+c73mNyNtkTH0JqHb0qqfxU6TxlCITbzJcGSfBDnqILFh9C6WY7OY2lAVNcBIXDSwnRlWE23A==", "path": "microsoft.visualstudio.web.codegeneration.utils/6.0.16", "hashPath": "microsoft.visualstudio.web.codegeneration.utils.6.0.16.nupkg.sha512"}, "Microsoft.VisualStudio.Web.CodeGenerators.Mvc/6.0.16": {"type": "package", "serviceable": true, "sha512": "sha512-19xEgfg9ak2VNI7e1HAMDztQA/Lk9WtyY3Dbs0uPyqauproMpdPtjGLaOqrkqZWnnmpA0mOrG0Dgtwf9Qg+YMg==", "path": "microsoft.visualstudio.web.codegenerators.mvc/6.0.16", "hashPath": "microsoft.visualstudio.web.codegenerators.mvc.6.0.16.nupkg.sha512"}, "Microsoft.Win32.Primitives/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-9ZQKCWxH7Ijp9BfahvL2Zyf1cJIk8XYLF6Yjzr2yi0b2cOut/HQ31qf1ThHAgCc3WiZMdnWcfJCgN82/0UunxA==", "path": "microsoft.win32.primitives/4.3.0", "hashPath": "microsoft.win32.primitives.4.3.0.nupkg.sha512"}, "Microsoft.Win32.Registry/4.7.0": {"type": "package", "serviceable": true, "sha512": "sha512-KSrRMb5vNi0CWSGG1++id2ZOs/1QhRqROt+qgbEAdQuGjGrFcl4AOl4/exGPUYz2wUnU42nvJqon1T3U0kPXLA==", "path": "microsoft.win32.registry/4.7.0", "hashPath": "microsoft.win32.registry.4.7.0.nupkg.sha512"}, "Microsoft.Win32.SystemEvents/6.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-hqTM5628jSsQiv+HGpiq3WKBl2c8v1KZfby2J6Pr7pEPlK9waPdgEO6b8A/+/xn/yZ9ulv8HuqK71ONy2tg67A==", "path": "microsoft.win32.systemevents/6.0.0", "hashPath": "microsoft.win32.systemevents.6.0.0.nupkg.sha512"}, "NETStandard.Library/1.6.1": {"type": "package", "serviceable": true, "sha512": "sha512-WcSp3+vP+yHNgS8EV5J7pZ9IRpeDuARBPN28by8zqff1wJQXm26PVU8L3/fYLBJVU7BtDyqNVWq2KlCVvSSR4A==", "path": "netstandard.library/1.6.1", "hashPath": "netstandard.library.1.6.1.nupkg.sha512"}, "Newtonsoft.Json/13.0.1": {"type": "package", "serviceable": true, "sha512": "sha512-ppPFpBcvxdsfUonNcvITKqLl3bqxWbDCZIzDWHzjpdAHRFfZe0Dw9HmA0+za13IdyrgJwpkDTDA9fHaxOrt20A==", "path": "newtonsoft.json/13.0.1", "hashPath": "newtonsoft.json.13.0.1.nupkg.sha512"}, "NuGet.Common/6.6.1": {"type": "package", "serviceable": true, "sha512": "sha512-hW5NtShErO3qbdkyv7doCRsFNK9Rlcc7mVjYM+hg1sOAWheTh/oo95DzNbsZthiqyHZfaioopfWtzmoxNw9h4g==", "path": "nuget.common/6.6.1", "hashPath": "nuget.common.6.6.1.nupkg.sha512"}, "NuGet.Configuration/6.6.1": {"type": "package", "serviceable": true, "sha512": "sha512-9WbK8wgwPfRpSwuG+ZhMshE48qUYvPIw7VNLCncrq/in4vE6SGsuawPSxPJkkLBtcKTGbPMez5JDvUf6vEBgKg==", "path": "nuget.configuration/6.6.1", "hashPath": "nuget.configuration.6.6.1.nupkg.sha512"}, "NuGet.DependencyResolver.Core/6.6.1": {"type": "package", "serviceable": true, "sha512": "sha512-K+EXXLU37PBnwLGx6WnVGxlfWYkdedvUUOeDMERXelNgjg20irtKf3hk+wGB7NmxAdNY9/gGcOgSDOV+M0w3Jg==", "path": "nuget.dependencyresolver.core/6.6.1", "hashPath": "nuget.dependencyresolver.core.6.6.1.nupkg.sha512"}, "NuGet.Frameworks/6.6.1": {"type": "package", "serviceable": true, "sha512": "sha512-iRtDhL0zPqVw037fHEK9bQljTKPuOHhfIkz86/IH2P8eetr910HTTe5G8lJTuzZHh592Ze/sYhh173HIFjPSfg==", "path": "nuget.frameworks/6.6.1", "hashPath": "nuget.frameworks.6.6.1.nupkg.sha512"}, "NuGet.LibraryModel/6.6.1": {"type": "package", "serviceable": true, "sha512": "sha512-B0KH19sNNUq56YfEozIsIpk8EOyolG0LRT+hqG1/mhuXlQFiP9BgT6pZgwLQVLUl9YBDx3+KWQQbl6pz8Yh/Sw==", "path": "nuget.librarymodel/6.6.1", "hashPath": "nuget.librarymodel.6.6.1.nupkg.sha512"}, "NuGet.Packaging/6.6.1": {"type": "package", "serviceable": true, "sha512": "sha512-GwhFi2Ep4YzAGQFYz1OsMVNfiJ1M46nyCgHQ7xjJSMvxDYFgodR1RqVugWFMbIUUq6I8iYASwp5lpHXvITeuIQ==", "path": "nuget.packaging/6.6.1", "hashPath": "nuget.packaging.6.6.1.nupkg.sha512"}, "NuGet.ProjectModel/6.6.1": {"type": "package", "serviceable": true, "sha512": "sha512-CEyRXXvgIoEQBWn3WZupjkIVC6rPcGUAO5p4Gz+fnF8kcefWQOXHfnOE+UKZ0WwAJG5iMWRvXBKAGOuFiFhNpQ==", "path": "nuget.projectmodel/6.6.1", "hashPath": "nuget.projectmodel.6.6.1.nupkg.sha512"}, "NuGet.Protocol/6.6.1": {"type": "package", "serviceable": true, "sha512": "sha512-HhKLsK6Q0NNp6qb0T26GLR5gCTRZu+gzqDVK4xqXHZmsolaDVIdIYpn44b2etaVYLzNJCvgRkw+I422u2bIvMw==", "path": "nuget.protocol/6.6.1", "hashPath": "nuget.protocol.6.6.1.nupkg.sha512"}, "NuGet.Versioning/6.6.1": {"type": "package", "serviceable": true, "sha512": "sha512-Wm/AOFICTIrCgbVxv9dNWusraTzcggbo5W4ao7hD8NNVq911an9TGwW+uNuYc8I5PkpTeMuSXneV2u6hbi1P4w==", "path": "nuget.versioning/6.6.1", "hashPath": "nuget.versioning.6.6.1.nupkg.sha512"}, "runtime.debian.8-x64.runtime.native.System.Security.Cryptography.OpenSsl/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-HdSSp5MnJSsg08KMfZThpuLPJpPwE5hBXvHwoKWosyHHfe8Mh5WKT0ylEOf6yNzX6Ngjxe4Whkafh5q7Ymac4Q==", "path": "runtime.debian.8-x64.runtime.native.system.security.cryptography.openssl/4.3.0", "hashPath": "runtime.debian.8-x64.runtime.native.system.security.cryptography.openssl.4.3.0.nupkg.sha512"}, "runtime.fedora.23-x64.runtime.native.System.Security.Cryptography.OpenSsl/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-+yH1a49wJMy8Zt4yx5RhJrxO/DBDByAiCzNwiETI+1S4mPdCu0OY4djdciC7Vssk0l22wQaDLrXxXkp+3+7bVA==", "path": "runtime.fedora.23-x64.runtime.native.system.security.cryptography.openssl/4.3.0", "hashPath": "runtime.fedora.23-x64.runtime.native.system.security.cryptography.openssl.4.3.0.nupkg.sha512"}, "runtime.fedora.24-x64.runtime.native.System.Security.Cryptography.OpenSsl/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-c3YNH1GQJbfIPJeCnr4avseugSqPrxwIqzthYyZDN6EuOyNOzq+y2KSUfRcXauya1sF4foESTgwM5e1A8arAKw==", "path": "runtime.fedora.24-x64.runtime.native.system.security.cryptography.openssl/4.3.0", "hashPath": "runtime.fedora.24-x64.runtime.native.system.security.cryptography.openssl.4.3.0.nupkg.sha512"}, "runtime.native.System/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-c/qWt2LieNZIj1jGnVNsE2Kl23Ya2aSTBuXMD6V7k9KWr6l16Tqdwq+hJScEpWER9753NWC8h96PaVNY5Ld7Jw==", "path": "runtime.native.system/4.3.0", "hashPath": "runtime.native.system.4.3.0.nupkg.sha512"}, "runtime.native.System.IO.Compression/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-INBPonS5QPEgn7naufQFXJEp3zX6L4bwHgJ/ZH78aBTpeNfQMtf7C6VrAFhlq2xxWBveIOWyFzQjJ8XzHMhdOQ==", "path": "runtime.native.system.io.compression/4.3.0", "hashPath": "runtime.native.system.io.compression.4.3.0.nupkg.sha512"}, "runtime.native.System.Net.Http/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-ZVuZJqnnegJhd2k/PtAbbIcZ3aZeITq3sj06oKfMBSfphW3HDmk/t4ObvbOk/JA/swGR0LNqMksAh/f7gpTROg==", "path": "runtime.native.system.net.http/4.3.0", "hashPath": "runtime.native.system.net.http.4.3.0.nupkg.sha512"}, "runtime.native.System.Security.Cryptography.Apple/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-DloMk88juo0OuOWr56QG7MNchmafTLYWvABy36izkrLI5VledI0rq28KGs1i9wbpeT9NPQrx/wTf8U2vazqQ3Q==", "path": "runtime.native.system.security.cryptography.apple/4.3.0", "hashPath": "runtime.native.system.security.cryptography.apple.4.3.0.nupkg.sha512"}, "runtime.native.System.Security.Cryptography.OpenSsl/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-NS1U+700m4KFRHR5o4vo9DSlTmlCKu/u7dtE5sUHVIPB+xpXxYQvgBgA6wEIeCz6Yfn0Z52/72WYsToCEPJnrw==", "path": "runtime.native.system.security.cryptography.openssl/4.3.0", "hashPath": "runtime.native.system.security.cryptography.openssl.4.3.0.nupkg.sha512"}, "runtime.opensuse.13.2-x64.runtime.native.System.Security.Cryptography.OpenSsl/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-b3pthNgxxFcD+Pc0WSEoC0+md3MyhRS6aCEeenvNE3Fdw1HyJ18ZhRFVJJzIeR/O/jpxPboB805Ho0T3Ul7w8A==", "path": "runtime.opensuse.13.2-x64.runtime.native.system.security.cryptography.openssl/4.3.0", "hashPath": "runtime.opensuse.13.2-x64.runtime.native.system.security.cryptography.openssl.4.3.0.nupkg.sha512"}, "runtime.opensuse.42.1-x64.runtime.native.System.Security.Cryptography.OpenSsl/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-KeLz4HClKf+nFS7p/6Fi/CqyLXh81FpiGzcmuS8DGi9lUqSnZ6Es23/gv2O+1XVGfrbNmviF7CckBpavkBoIFQ==", "path": "runtime.opensuse.42.1-x64.runtime.native.system.security.cryptography.openssl/4.3.0", "hashPath": "runtime.opensuse.42.1-x64.runtime.native.system.security.cryptography.openssl.4.3.0.nupkg.sha512"}, "runtime.osx.10.10-x64.runtime.native.System.Security.Cryptography.Apple/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-kVXCuMTrTlxq4XOOMAysuNwsXWpYeboGddNGpIgNSZmv1b6r/s/DPk0fYMB7Q5Qo4bY68o48jt4T4y5BVecbCQ==", "path": "runtime.osx.10.10-x64.runtime.native.system.security.cryptography.apple/4.3.0", "hashPath": "runtime.osx.10.10-x64.runtime.native.system.security.cryptography.apple.4.3.0.nupkg.sha512"}, "runtime.osx.10.10-x64.runtime.native.System.Security.Cryptography.OpenSsl/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-X7IdhILzr4ROXd8mI1BUCQMSHSQwelUlBjF1JyTKCjXaOGn2fB4EKBxQbCK2VjO3WaWIdlXZL3W6TiIVnrhX4g==", "path": "runtime.osx.10.10-x64.runtime.native.system.security.cryptography.openssl/4.3.0", "hashPath": "runtime.osx.10.10-x64.runtime.native.system.security.cryptography.openssl.4.3.0.nupkg.sha512"}, "runtime.rhel.7-x64.runtime.native.System.Security.Cryptography.OpenSsl/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-nyFNiCk/r+VOiIqreLix8yN+q3Wga9+SE8BCgkf+2BwEKiNx6DyvFjCgkfV743/grxv8jHJ8gUK4XEQw7yzRYg==", "path": "runtime.rhel.7-x64.runtime.native.system.security.cryptography.openssl/4.3.0", "hashPath": "runtime.rhel.7-x64.runtime.native.system.security.cryptography.openssl.4.3.0.nupkg.sha512"}, "runtime.ubuntu.14.04-x64.runtime.native.System.Security.Cryptography.OpenSsl/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-ytoewC6wGorL7KoCAvRfsgoJPJbNq+64k2SqW6JcOAebWsFUvCCYgfzQMrnpvPiEl4OrblUlhF2ji+Q1+SVLrQ==", "path": "runtime.ubuntu.14.04-x64.runtime.native.system.security.cryptography.openssl/4.3.0", "hashPath": "runtime.ubuntu.14.04-x64.runtime.native.system.security.cryptography.openssl.4.3.0.nupkg.sha512"}, "runtime.ubuntu.16.04-x64.runtime.native.System.Security.Cryptography.OpenSsl/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-I8bKw2I8k58Wx7fMKQJn2R8lamboCAiHfHeV/pS65ScKWMMI0+wJkLYlEKvgW1D/XvSl/221clBoR2q9QNNM7A==", "path": "runtime.ubuntu.16.04-x64.runtime.native.system.security.cryptography.openssl/4.3.0", "hashPath": "runtime.ubuntu.16.04-x64.runtime.native.system.security.cryptography.openssl.4.3.0.nupkg.sha512"}, "runtime.ubuntu.16.10-x64.runtime.native.System.Security.Cryptography.OpenSsl/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-VB5cn/7OzUfzdnC8tqAIMQciVLiq2epm2NrAm1E9OjNRyG4lVhfR61SMcLizejzQP8R8Uf/0l5qOIbUEi+RdEg==", "path": "runtime.ubuntu.16.10-x64.runtime.native.system.security.cryptography.openssl/4.3.0", "hashPath": "runtime.ubuntu.16.10-x64.runtime.native.system.security.cryptography.openssl.4.3.0.nupkg.sha512"}, "System.AppContext/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-fKC+rmaLfeIzUhagxY17Q9siv/sPrjjKcfNg1Ic8IlQkZLipo8ljcaZQu4VtI4Jqbzjc2VTjzGLF6WmsRXAEgA==", "path": "system.appcontext/4.3.0", "hashPath": "system.appcontext.4.3.0.nupkg.sha512"}, "System.Buffers/4.5.0": {"type": "package", "serviceable": true, "sha512": "sha512-pL2ChpaRRWI/p4LXyy4RgeWlYF2sgfj/pnVMvBqwNFr5cXg7CXNnWZWxrOONLg8VGdFB8oB+EG2Qw4MLgTOe+A==", "path": "system.buffers/4.5.0", "hashPath": "system.buffers.4.5.0.nupkg.sha512"}, "System.Collections/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-3Dcj85/TBdVpL5Zr+gEEBUuFe2icOnLalmEh9hfck1PTYbbyWuZgh4fmm2ysCLTrqLQw6t3TgTyJ+VLp+Qb+Lw==", "path": "system.collections/4.3.0", "hashPath": "system.collections.4.3.0.nupkg.sha512"}, "System.Collections.Concurrent/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-ztl69Xp0Y/UXCL+3v3tEU+lIy+bvjKNUmopn1wep/a291pVPK7dxBd6T7WnlQqRog+d1a/hSsgRsmFnIBKTPLQ==", "path": "system.collections.concurrent/4.3.0", "hashPath": "system.collections.concurrent.4.3.0.nupkg.sha512"}, "System.Collections.Immutable/6.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-l4zZJ1WU2hqpQQHXz1rvC3etVZN+2DLmQMO79FhOTZHMn8tDRr+WU287sbomD0BETlmKDn0ygUgVy9k5xkkJdA==", "path": "system.collections.immutable/6.0.0", "hashPath": "system.collections.immutable.6.0.0.nupkg.sha512"}, "System.Composition/1.0.31": {"type": "package", "serviceable": true, "sha512": "sha512-I+D26qpYdoklyAVUdqwUBrEIckMNjAYnuPJy/h9dsQItpQwVREkDFs4b4tkBza0kT2Yk48Lcfsv2QQ9hWsh9Iw==", "path": "system.composition/1.0.31", "hashPath": "system.composition.1.0.31.nupkg.sha512"}, "System.Composition.AttributedModel/1.0.31": {"type": "package", "serviceable": true, "sha512": "sha512-NHWhkM3ZkspmA0XJEsKdtTt1ViDYuojgSND3yHhTzwxepiwqZf+BCWuvCbjUt4fe0NxxQhUDGJ5km6sLjo9qnQ==", "path": "system.composition.attributedmodel/1.0.31", "hashPath": "system.composition.attributedmodel.1.0.31.nupkg.sha512"}, "System.Composition.Convention/1.0.31": {"type": "package", "serviceable": true, "sha512": "sha512-GLjh2Ju71k6C0qxMMtl4efHa68NmWeIUYh4fkUI8xbjQrEBvFmRwMDFcylT8/PR9SQbeeL48IkFxU/+gd0nYEQ==", "path": "system.composition.convention/1.0.31", "hashPath": "system.composition.convention.1.0.31.nupkg.sha512"}, "System.Composition.Hosting/1.0.31": {"type": "package", "serviceable": true, "sha512": "sha512-fN1bT4RX4vUqjbgoyuJFVUizAl2mYF5VAb+bVIxIYZSSc0BdnX+yGAxcavxJuDDCQ1K+/mdpgyEFc8e9ikjvrg==", "path": "system.composition.hosting/1.0.31", "hashPath": "system.composition.hosting.1.0.31.nupkg.sha512"}, "System.Composition.Runtime/1.0.31": {"type": "package", "serviceable": true, "sha512": "sha512-0LEJN+2NVM89CE4SekDrrk5tHV5LeATltkp+9WNYrR+Huiyt0vaCqHbbHtVAjPyeLWIc8dOz/3kthRBj32wGQg==", "path": "system.composition.runtime/1.0.31", "hashPath": "system.composition.runtime.1.0.31.nupkg.sha512"}, "System.Composition.TypedParts/1.0.31": {"type": "package", "serviceable": true, "sha512": "sha512-0Zae/FtzeFgDBBuILeIbC/T9HMYbW4olAmi8XqqAGosSOWvXfiQLfARZEhiGd0LVXaYgXr0NhxiU1LldRP1fpQ==", "path": "system.composition.typedparts/1.0.31", "hashPath": "system.composition.typedparts.1.0.31.nupkg.sha512"}, "System.Configuration.ConfigurationManager/6.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-7T+m0kDSlIPTHIkPMIu6m6tV6qsMqJpvQWW2jIc2qi7sn40qxFo0q+7mEQAhMPXZHMKnWrnv47ntGlM/ejvw3g==", "path": "system.configuration.configurationmanager/6.0.0", "hashPath": "system.configuration.configurationmanager.6.0.0.nupkg.sha512"}, "System.Console/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-DHDrIxiqk1h03m6khKWV2X8p/uvN79rgSqpilL6uzpmSfxfU5ng8VcPtW4qsDsQDHiTv6IPV9TmD5M/vElPNLg==", "path": "system.console/4.3.0", "hashPath": "system.console.4.3.0.nupkg.sha512"}, "System.Diagnostics.Debug/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-ZUhUOdqmaG5Jk3Xdb8xi5kIyQYAA4PnTNlHx1mu9ZY3qv4ELIdKbnL/akbGaKi2RnNUWaZsAs31rvzFdewTj2g==", "path": "system.diagnostics.debug/4.3.0", "hashPath": "system.diagnostics.debug.4.3.0.nupkg.sha512"}, "System.Diagnostics.DiagnosticSource/6.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-frQDfv0rl209cKm1lnwTgFPzNigy2EKk1BS3uAvHvlBVKe5cymGyHO+Sj+NLv5VF/AhHsqPIUUwya5oV4CHMUw==", "path": "system.diagnostics.diagnosticsource/6.0.0", "hashPath": "system.diagnostics.diagnosticsource.6.0.0.nupkg.sha512"}, "System.Diagnostics.Tools/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-UUvkJfSYJMM6x527dJg2VyWPSRqIVB0Z7dbjHst1zmwTXz5CcXSYJFWRpuigfbO1Lf7yfZiIaEUesfnl/g5EyA==", "path": "system.diagnostics.tools/4.3.0", "hashPath": "system.diagnostics.tools.4.3.0.nupkg.sha512"}, "System.Diagnostics.Tracing/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-rswfv0f/Cqkh78rA5S8eN8Neocz234+emGCtTF3lxPY96F+mmmUen6tbn0glN6PMvlKQb9bPAY5e9u7fgPTkKw==", "path": "system.diagnostics.tracing/4.3.0", "hashPath": "system.diagnostics.tracing.4.3.0.nupkg.sha512"}, "System.Drawing.Common/6.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-NfuoKUiP2nUWwKZN6twGqXioIe1zVD0RIj2t976A+czLHr2nY454RwwXs6JU9Htc6mwqL6Dn/nEL3dpVf2jOhg==", "path": "system.drawing.common/6.0.0", "hashPath": "system.drawing.common.6.0.0.nupkg.sha512"}, "System.Dynamic.Runtime/4.0.11": {"type": "package", "serviceable": true, "sha512": "sha512-db34f6LHYM0U0JpE+sOmjar27BnqTVkbLJhgfwMpTdgTigG/Hna3m2MYVwnFzGGKnEJk2UXFuoVTr8WUbU91/A==", "path": "system.dynamic.runtime/4.0.11", "hashPath": "system.dynamic.runtime.4.0.11.nupkg.sha512"}, "System.Formats.Asn1/5.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-MTvUIktmemNB+El0Fgw9egyqT9AYSIk6DTJeoDSpc3GIHxHCMo8COqkWT1mptX5tZ1SlQ6HJZ0OsSvMth1c12w==", "path": "system.formats.asn1/5.0.0", "hashPath": "system.formats.asn1.5.0.0.nupkg.sha512"}, "System.Globalization/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-kYdVd2f2PAdFGblzFswE4hkNANJBKRmsfa2X5LG2AcWE1c7/4t0pYae1L8vfZ5xvE2nK/R9JprtToA61OSHWIg==", "path": "system.globalization/4.3.0", "hashPath": "system.globalization.4.3.0.nupkg.sha512"}, "System.Globalization.Calendars/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-GUlBtdOWT4LTV3I+9/PJW+56AnnChTaOqqTLFtdmype/L500M2LIyXgmtd9X2P2VOkmJd5c67H5SaC2QcL1bFA==", "path": "system.globalization.calendars/4.3.0", "hashPath": "system.globalization.calendars.4.3.0.nupkg.sha512"}, "System.Globalization.Extensions/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-FhKmdR6MPG+pxow6wGtNAWdZh7noIOpdD5TwQ3CprzgIE1bBBoim0vbR1+AWsWjQmU7zXHgQo4TWSP6lCeiWcQ==", "path": "system.globalization.extensions/4.3.0", "hashPath": "system.globalization.extensions.4.3.0.nupkg.sha512"}, "System.IdentityModel.Tokens.Jwt/6.8.0": {"type": "package", "serviceable": true, "sha512": "sha512-5tBCjAub2Bhd5qmcd0WhR5s354e4oLYa//kOWrkX+6/7ZbDDJjMTfwLSOiZ/MMpWdE4DWPLOfTLOq/juj9CKzA==", "path": "system.identitymodel.tokens.jwt/6.8.0", "hashPath": "system.identitymodel.tokens.jwt.6.8.0.nupkg.sha512"}, "System.IO/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-3qjaHvxQPDpSOYICjUoTsmoq5u6QJAFRUITgeT/4gqkF1bajbSmb1kwSxEA8AHlofqgcKJcM8udgieRNhaJ5Cg==", "path": "system.io/4.3.0", "hashPath": "system.io.4.3.0.nupkg.sha512"}, "System.IO.Compression/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-YHndyoiV90iu4iKG115ibkhrG+S3jBm8Ap9OwoUAzO5oPDAWcr0SFwQFm0HjM8WkEZWo0zvLTyLmbvTkW1bXgg==", "path": "system.io.compression/4.3.0", "hashPath": "system.io.compression.4.3.0.nupkg.sha512"}, "System.IO.Compression.ZipFile/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-G4HwjEsgIwy3JFBduZ9quBkAu+eUwjIdJleuNSgmUojbH6O3mlvEIme+GHx/cLlTAPcrnnL7GqvB9pTlWRfhOg==", "path": "system.io.compression.zipfile/4.3.0", "hashPath": "system.io.compression.zipfile.4.3.0.nupkg.sha512"}, "System.IO.FileSystem/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-3wEMARTnuio+ulnvi+hkRNROYwa1kylvYahhcLk4HSoVdl+xxTFVeVlYOfLwrDPImGls0mDqbMhrza8qnWPTdA==", "path": "system.io.filesystem/4.3.0", "hashPath": "system.io.filesystem.4.3.0.nupkg.sha512"}, "System.IO.FileSystem.Primitives/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-6QOb2XFLch7bEc4lIcJH49nJN2HV+OC3fHDgsLVsBVBk3Y4hFAnOBGzJ2lUu7CyDDFo9IBWkSsnbkT6IBwwiMw==", "path": "system.io.filesystem.primitives/4.3.0", "hashPath": "system.io.filesystem.primitives.4.3.0.nupkg.sha512"}, "System.IO.Pipelines/5.0.1": {"type": "package", "serviceable": true, "sha512": "sha512-qEePWsaq9LoEEIqhbGe6D5J8c9IqQOUuTzzV6wn1POlfdLkJliZY3OlB0j0f17uMWlqZYjH7txj+2YbyrIA8Yg==", "path": "system.io.pipelines/5.0.1", "hashPath": "system.io.pipelines.5.0.1.nupkg.sha512"}, "System.Linq/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-5DbqIUpsDp0dFftytzuMmc0oeMdQwjcP/EWxsksIz/w1TcFRkZ3yKKz0PqiYFMmEwPSWw+qNVqD7PJ889JzHbw==", "path": "system.linq/4.3.0", "hashPath": "system.linq.4.3.0.nupkg.sha512"}, "System.Linq.Expressions/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-PGKkrd2khG4CnlyJwxwwaWWiSiWFNBGlgXvJpeO0xCXrZ89ODrQ6tjEWS/kOqZ8GwEOUATtKtzp1eRgmYNfclg==", "path": "system.linq.expressions/4.3.0", "hashPath": "system.linq.expressions.4.3.0.nupkg.sha512"}, "System.Memory/4.5.5": {"type": "package", "serviceable": true, "sha512": "sha512-XIWiDvKPXaTveaB7HVganDlOCRoj03l+jrwNvcge/t8vhGYKvqV+dMv6G4SAX2NoNmN0wZfVPTAlFwZcZvVOUw==", "path": "system.memory/4.5.5", "hashPath": "system.memory.4.5.5.nupkg.sha512"}, "System.Net.Http/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-sYg+FtILtRQuYWSIAuNOELwVuVsxVyJGWQyOnlAzhV4xvhyFnON1bAzYYC+jjRW8JREM45R0R5Dgi8MTC5sEwA==", "path": "system.net.http/4.3.0", "hashPath": "system.net.http.4.3.0.nupkg.sha512"}, "System.Net.Primitives/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-qOu+hDwFwoZPbzPvwut2qATe3ygjeQBDQj91xlsaqGFQUI5i4ZnZb8yyQuLGpDGivEPIt8EJkd1BVzVoP31FXA==", "path": "system.net.primitives/4.3.0", "hashPath": "system.net.primitives.4.3.0.nupkg.sha512"}, "System.Net.Sockets/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-m6icV6TqQOAdgt5N/9I5KNpjom/5NFtkmGseEH+AK/hny8XrytLH3+b5M8zL/Ycg3fhIocFpUMyl/wpFnVRvdw==", "path": "system.net.sockets/4.3.0", "hashPath": "system.net.sockets.4.3.0.nupkg.sha512"}, "System.ObjectModel/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-bdX+80eKv9bN6K4N+d77OankKHGn6CH711a6fcOpMQu2Fckp/Ft4L/kW9WznHpyR0NRAvJutzOMHNNlBGvxQzQ==", "path": "system.objectmodel/4.3.0", "hashPath": "system.objectmodel.4.3.0.nupkg.sha512"}, "System.Reflection/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-KMiAFoW7MfJGa9nDFNcfu+FpEdiHpWgTcS2HdMpDvt9saK3y/G4GwprPyzqjFH9NTaGPQeWNHU+iDlDILj96aQ==", "path": "system.reflection/4.3.0", "hashPath": "system.reflection.4.3.0.nupkg.sha512"}, "System.Reflection.Emit/4.6.0": {"type": "package", "serviceable": true, "sha512": "sha512-qAo4jyXtC9i71iElngX7P2r+zLaiHzxKwf66sc3X91tL5Ks6fnQ1vxL04o7ZSm3sYfLExySL7GN8aTpNYpU1qw==", "path": "system.reflection.emit/4.6.0", "hashPath": "system.reflection.emit.4.6.0.nupkg.sha512"}, "System.Reflection.Emit.ILGeneration/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-59tBslAk9733NXLrUJrwNZEzbMAcu8k344OYo+wfSVygcgZ9lgBdGIzH/nrg3LYhXceynyvTc8t5/GD4Ri0/ng==", "path": "system.reflection.emit.ilgeneration/4.3.0", "hashPath": "system.reflection.emit.ilgeneration.4.3.0.nupkg.sha512"}, "System.Reflection.Emit.Lightweight/4.6.0": {"type": "package", "serviceable": true, "sha512": "sha512-j/V5HVvxvBQ7uubYD0PptQW2KGsi1Pc2kZ9yfwLixv3ADdjL/4M78KyC5e+ymW612DY8ZE4PFoZmWpoNmN2mqg==", "path": "system.reflection.emit.lightweight/4.6.0", "hashPath": "system.reflection.emit.lightweight.4.6.0.nupkg.sha512"}, "System.Reflection.Extensions/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-rJkrJD3kBI5B712aRu4DpSIiHRtr6QlfZSQsb0hYHrDCZORXCFjQfoipo2LaMUHoT9i1B7j7MnfaEKWDFmFQNQ==", "path": "system.reflection.extensions/4.3.0", "hashPath": "system.reflection.extensions.4.3.0.nupkg.sha512"}, "System.Reflection.Metadata/6.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-sffDOcex1C3HO5kDolOYcWXTwRpZY/LvJujM6SMjn63fWMJWchYAAmkoAJXlbpZ5yf4d+KMgxd+LeETa4gD9sQ==", "path": "system.reflection.metadata/6.0.0", "hashPath": "system.reflection.metadata.6.0.0.nupkg.sha512"}, "System.Reflection.MetadataLoadContext/6.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-SuK8qTHbmG3PToLo1TEq8YSfY31FiKhASBmjozUTAleDgiX4H2X4jm0VPFb+K2soSSmYPyHTpHp35TctfNtDzQ==", "path": "system.reflection.metadataloadcontext/6.0.0", "hashPath": "system.reflection.metadataloadcontext.6.0.0.nupkg.sha512"}, "System.Reflection.Primitives/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-5RXItQz5As4xN2/YUDxdpsEkMhvw3e6aNveFXUn4Hl/udNTCNhnKp8lT9fnc3MhvGKh1baak5CovpuQUXHAlIA==", "path": "system.reflection.primitives/4.3.0", "hashPath": "system.reflection.primitives.4.3.0.nupkg.sha512"}, "System.Reflection.TypeExtensions/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-7u6ulLcZbyxB5Gq0nMkQttcdBTx57ibzw+4IOXEfR+sXYQoHvjW5LTLyNr8O22UIMrqYbchJQJnos4eooYzYJA==", "path": "system.reflection.typeextensions/4.3.0", "hashPath": "system.reflection.typeextensions.4.3.0.nupkg.sha512"}, "System.Resources.ResourceManager/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-/zrcPkkWdZmI4F92gL/TPumP98AVDu/Wxr3CSJGQQ+XN6wbRZcyfSKVoPo17ilb3iOr0cCRqJInGwNMolqhS8A==", "path": "system.resources.resourcemanager/4.3.0", "hashPath": "system.resources.resourcemanager.4.3.0.nupkg.sha512"}, "System.Runtime/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-JufQi0vPQ0xGnAczR13AUFglDyVYt4Kqnz1AZaiKZ5+GICq0/1MH/mO/eAJHt/mHW1zjKBJd7kV26SrxddAhiw==", "path": "system.runtime/4.3.0", "hashPath": "system.runtime.4.3.0.nupkg.sha512"}, "System.Runtime.Caching/4.7.0": {"type": "package", "serviceable": true, "sha512": "sha512-NdvNRjTPxYvIEhXQszT9L9vJhdQoX6AQ0AlhjTU+5NqFQVuacJTfhPVAvtGWNA2OJCqRiR/okBcZgMwI6MqcZg==", "path": "system.runtime.caching/4.7.0", "hashPath": "system.runtime.caching.4.7.0.nupkg.sha512"}, "System.Runtime.CompilerServices.Unsafe/6.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-/iUeP3tq1S0XdNNoMz5C9twLSrM/TH+qElHkXWaPvuNOt+99G75NrV0OS2EqHx5wMN7popYjpc8oTjC1y16DLg==", "path": "system.runtime.compilerservices.unsafe/6.0.0", "hashPath": "system.runtime.compilerservices.unsafe.6.0.0.nupkg.sha512"}, "System.Runtime.Extensions/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-guW0uK0fn5fcJJ1tJVXYd7/1h5F+pea1r7FLSOz/f8vPEqbR2ZAknuRDvTQ8PzAilDveOxNjSfr0CHfIQfFk8g==", "path": "system.runtime.extensions/4.3.0", "hashPath": "system.runtime.extensions.4.3.0.nupkg.sha512"}, "System.Runtime.Handles/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-OKiSUN7DmTWeYb3l51A7EYaeNMnvxwE249YtZz7yooT4gOZhmTjIn48KgSsw2k2lYdLgTKNJw/ZIfSElwDRVgg==", "path": "system.runtime.handles/4.3.0", "hashPath": "system.runtime.handles.4.3.0.nupkg.sha512"}, "System.Runtime.InteropServices/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-uv1ynXqiMK8mp1GM3jDqPCFN66eJ5w5XNomaK2XD+TuCroNTLFGeZ+WCmBMcBDyTFKou3P6cR6J/QsaqDp7fGQ==", "path": "system.runtime.interopservices/4.3.0", "hashPath": "system.runtime.interopservices.4.3.0.nupkg.sha512"}, "System.Runtime.InteropServices.RuntimeInformation/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-cbz4YJMqRDR7oLeMRbdYv7mYzc++17lNhScCX0goO2XpGWdvAt60CGN+FHdePUEHCe/Jy9jUlvNAiNdM+7jsOw==", "path": "system.runtime.interopservices.runtimeinformation/4.3.0", "hashPath": "system.runtime.interopservices.runtimeinformation.4.3.0.nupkg.sha512"}, "System.Runtime.Numerics/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-yMH+MfdzHjy17l2KESnPiF2dwq7T+xLnSJar7slyimAkUh/gTrS9/UQOtv7xarskJ2/XDSNvfLGOBQPjL7PaHQ==", "path": "system.runtime.numerics/4.3.0", "hashPath": "system.runtime.numerics.4.3.0.nupkg.sha512"}, "System.Security.AccessControl/6.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-AUADIc0LIEQe7MzC+I0cl0rAT8RrTAKFHl53yHjEUzNVIaUlhFY11vc2ebiVJzVBuOzun6F7FBA+8KAbGTTedQ==", "path": "system.security.accesscontrol/6.0.0", "hashPath": "system.security.accesscontrol.6.0.0.nupkg.sha512"}, "System.Security.Cryptography.Algorithms/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-W1kd2Y8mYSCgc3ULTAZ0hOP2dSdG5YauTb1089T0/kRcN2MpSAW1izOFROrJgxSlMn3ArsgHXagigyi+ibhevg==", "path": "system.security.cryptography.algorithms/4.3.0", "hashPath": "system.security.cryptography.algorithms.4.3.0.nupkg.sha512"}, "System.Security.Cryptography.Cng/5.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-jIMXsKn94T9JY7PvPq/tMfqa6GAaHpElRDpmG+SuL+D3+sTw2M8VhnibKnN8Tq+4JqbPJ/f+BwtLeDMEnzAvRg==", "path": "system.security.cryptography.cng/5.0.0", "hashPath": "system.security.cryptography.cng.5.0.0.nupkg.sha512"}, "System.Security.Cryptography.Csp/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-X4s/FCkEUnRGnwR3aSfVIkldBmtURMhmexALNTwpjklzxWU7yjMk7GHLKOZTNkgnWnE0q7+BCf9N2LVRWxewaA==", "path": "system.security.cryptography.csp/4.3.0", "hashPath": "system.security.cryptography.csp.4.3.0.nupkg.sha512"}, "System.Security.Cryptography.Encoding/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-1DEWjZZly9ae9C79vFwqaO5kaOlI5q+3/55ohmq/7dpDyDfc8lYe7YVxJUZ5MF/NtbkRjwFRo14yM4OEo9EmDw==", "path": "system.security.cryptography.encoding/4.3.0", "hashPath": "system.security.cryptography.encoding.4.3.0.nupkg.sha512"}, "System.Security.Cryptography.OpenSsl/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-h4CEgOgv5PKVF/HwaHzJRiVboL2THYCou97zpmhjghx5frc7fIvlkY1jL+lnIQyChrJDMNEXS6r7byGif8Cy4w==", "path": "system.security.cryptography.openssl/4.3.0", "hashPath": "system.security.cryptography.openssl.4.3.0.nupkg.sha512"}, "System.Security.Cryptography.Pkcs/5.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-9TPLGjBCGKmNvG8pjwPeuYy0SMVmGZRwlTZvyPHDbYv/DRkoeumJdfumaaDNQzVGMEmbWtg07zUpSW9q70IlDQ==", "path": "system.security.cryptography.pkcs/5.0.0", "hashPath": "system.security.cryptography.pkcs.5.0.0.nupkg.sha512"}, "System.Security.Cryptography.Primitives/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-7bDIyVFNL/xKeFHjhobUAQqSpJq9YTOpbEs6mR233Et01STBMXNAc/V+BM6dwYGc95gVh/Zf+iVXWzj3mE8DWg==", "path": "system.security.cryptography.primitives/4.3.0", "hashPath": "system.security.cryptography.primitives.4.3.0.nupkg.sha512"}, "System.Security.Cryptography.ProtectedData/6.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-rp1gMNEZpvx9vP0JW0oHLxlf8oSiQgtno77Y4PLUBjSiDYoD77Y8uXHr1Ea5XG4/pIKhqAdxZ8v8OTUtqo9PeQ==", "path": "system.security.cryptography.protecteddata/6.0.0", "hashPath": "system.security.cryptography.protecteddata.6.0.0.nupkg.sha512"}, "System.Security.Cryptography.X509Certificates/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-t2Tmu6Y2NtJ2um0RtcuhP7ZdNNxXEgUm2JeoA/0NvlMjAhKCnM1NX07TDl3244mVp3QU6LPEhT3HTtH1uF7IYw==", "path": "system.security.cryptography.x509certificates/4.3.0", "hashPath": "system.security.cryptography.x509certificates.4.3.0.nupkg.sha512"}, "System.Security.Permissions/6.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-T/uuc7AklkDoxmcJ7LGkyX1CcSviZuLCa4jg3PekfJ7SU0niF0IVTXwUiNVP9DSpzou2PpxJ+eNY2IfDM90ZCg==", "path": "system.security.permissions/6.0.0", "hashPath": "system.security.permissions.6.0.0.nupkg.sha512"}, "System.Security.Principal.Windows/5.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-t0MGLukB5WAVU9bO3MGzvlGnyJPgUlcwerXn1kzBRjwLKixT96XV0Uza41W49gVd8zEMFu9vQEFlv0IOrytICA==", "path": "system.security.principal.windows/5.0.0", "hashPath": "system.security.principal.windows.5.0.0.nupkg.sha512"}, "System.Text.Encoding/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-BiIg+KWaSDOITze6jGQynxg64naAPtqGHBwDrLaCtixsa5bKiR8dpPOHA7ge3C0JJQizJE+sfkz1wV+BAKAYZw==", "path": "system.text.encoding/4.3.0", "hashPath": "system.text.encoding.4.3.0.nupkg.sha512"}, "System.Text.Encoding.CodePages/6.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-ZFCILZuOvtKPauZ/j/swhvw68ZRi9ATCfvGbk1QfydmcXBkIWecWKn/250UH7rahZ5OoDBaiAudJtPvLwzw85A==", "path": "system.text.encoding.codepages/6.0.0", "hashPath": "system.text.encoding.codepages.6.0.0.nupkg.sha512"}, "System.Text.Encoding.Extensions/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-YVMK0Bt/A43RmwizJoZ22ei2nmrhobgeiYwFzC4YAN+nue8RF6djXDMog0UCn+brerQoYVyaS+ghy9P/MUVcmw==", "path": "system.text.encoding.extensions/4.3.0", "hashPath": "system.text.encoding.extensions.4.3.0.nupkg.sha512"}, "System.Text.Encodings.Web/6.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-Vg8eB5Tawm1IFqj4TVK1czJX89rhFxJo9ELqc/Eiq0eXy13RK00eubyU6TJE6y+GQXjyV5gSfiewDUZjQgSE0w==", "path": "system.text.encodings.web/6.0.0", "hashPath": "system.text.encodings.web.6.0.0.nupkg.sha512"}, "System.Text.Json/6.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-zaJsHfESQvJ11vbXnNlkrR46IaMULk/gHxYsJphzSF+07kTjPHv+Oc14w6QEOfo3Q4hqLJgStUaYB9DBl0TmWg==", "path": "system.text.json/6.0.0", "hashPath": "system.text.json.6.0.0.nupkg.sha512"}, "System.Text.RegularExpressions/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-RpT2DA+L660cBt1FssIE9CAGpLFdFPuheB7pLpKpn6ZXNby7jDERe8Ua/Ne2xGiwLVG2JOqziiaVCGDon5sKFA==", "path": "system.text.regularexpressions/4.3.0", "hashPath": "system.text.regularexpressions.4.3.0.nupkg.sha512"}, "System.Threading/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-VkUS0kOBcUf3Wwm0TSbrevDDZ6BlM+b/HRiapRFWjM5O0NS0LviG0glKmFK+hhPDd1XFeSdU1GmlLhb2CoVpIw==", "path": "system.threading/4.3.0", "hashPath": "system.threading.4.3.0.nupkg.sha512"}, "System.Threading.Tasks/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-LbSxKEdOUhVe8BezB/9uOGGppt+nZf6e1VFyw6v3DN6lqitm0OSn2uXMOdtP0M3W4iMcqcivm2J6UgqiwwnXiA==", "path": "system.threading.tasks/4.3.0", "hashPath": "system.threading.tasks.4.3.0.nupkg.sha512"}, "System.Threading.Tasks.Dataflow/6.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-+tyDCU3/B1lDdOOAJywHQoFwyXIUghIaP2BxG79uvhfTnO+D9qIgjVlL/JV2NTliYbMHpd6eKDmHp2VHpij7MA==", "path": "system.threading.tasks.dataflow/6.0.0", "hashPath": "system.threading.tasks.dataflow.6.0.0.nupkg.sha512"}, "System.Threading.Tasks.Extensions/4.5.4": {"type": "package", "serviceable": true, "sha512": "sha512-zteT+G8xuGu6mS+mzDzYXbzS7rd3K6Fjb9RiZlYlJPam2/hU7JCBZBVEcywNuR+oZ1ncTvc/cq0faRr3P01OVg==", "path": "system.threading.tasks.extensions/4.5.4", "hashPath": "system.threading.tasks.extensions.4.5.4.nupkg.sha512"}, "System.Threading.Timer/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-Z6YfyYTCg7lOZjJzBjONJTFKGN9/NIYKSxhU5GRd+DTwHSZyvWp1xuI5aR+dLg+ayyC5Xv57KiY4oJ0tMO89fQ==", "path": "system.threading.timer/4.3.0", "hashPath": "system.threading.timer.4.3.0.nupkg.sha512"}, "System.Windows.Extensions/6.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-IXoJOXIqc39AIe+CIR7koBtRGMiCt/LPM3lI+PELtDIy9XdyeSrwXFdWV9dzJ2Awl0paLWUaknLxFQ5HpHZUog==", "path": "system.windows.extensions/6.0.0", "hashPath": "system.windows.extensions.6.0.0.nupkg.sha512"}, "System.Xml.ReaderWriter/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-GrprA+Z0RUXaR4N7/eW71j1rgMnEnEVlgii49GZyAjTH7uliMnrOU3HNFBr6fEDBCJCIdlVNq9hHbaDR621XBA==", "path": "system.xml.readerwriter/4.3.0", "hashPath": "system.xml.readerwriter.4.3.0.nupkg.sha512"}, "System.Xml.XDocument/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-5zJ0XDxAIg8iy+t4aMnQAu0MqVbqyvfoUVl1yDV61xdo3Vth45oA2FoY4pPkxYAH5f8ixpmTqXeEIya95x0aCQ==", "path": "system.xml.xdocument/4.3.0", "hashPath": "system.xml.xdocument.4.3.0.nupkg.sha512"}, "TaskManager.Common/1.0.0": {"type": "project", "serviceable": false, "sha512": ""}, "TaskManager.Data/1.0.0": {"type": "project", "serviceable": false, "sha512": ""}, "TaskManager.Data.Models/1.0.0": {"type": "project", "serviceable": false, "sha512": ""}, "TaskManager.Services.Data/1.0.0": {"type": "project", "serviceable": false, "sha512": ""}, "TaskManager.Web.Infrastructure/1.0.0": {"type": "project", "serviceable": false, "sha512": ""}, "TaskManager.Web.ViewModels/1.0.0": {"type": "project", "serviceable": false, "sha512": ""}}}