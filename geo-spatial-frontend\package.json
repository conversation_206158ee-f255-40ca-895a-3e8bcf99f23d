{"name": "geo-spatial-frontend", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "build": "vite build", "lint": "eslint . --ext js,jsx --report-unused-disable-directives --max-warnings 0", "preview": "vite preview", "test": "vitest"}, "dependencies": {"react": "^18.2.0", "react-dom": "^18.2.0", "leaflet": "^1.9.4", "react-leaflet": "^4.2.1", "axios": "^1.6.0", "react-router-dom": "^6.8.0", "react-hook-form": "^7.48.0", "@hookform/resolvers": "^3.3.0", "yup": "^1.3.0", "react-toastify": "^9.1.0", "lucide-react": "^0.294.0", "clsx": "^2.0.0"}, "devDependencies": {"@types/react": "^18.2.43", "@types/react-dom": "^18.2.17", "@types/leaflet": "^1.9.8", "@vitejs/plugin-react": "^4.2.1", "eslint": "^8.55.0", "eslint-plugin-react": "^7.33.2", "eslint-plugin-react-hooks": "^4.6.0", "eslint-plugin-react-refresh": "^0.4.5", "vite": "^5.0.8", "vitest": "^1.0.0"}}