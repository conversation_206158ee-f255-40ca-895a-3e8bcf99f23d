import React, { useState, useEffect, useRef } from 'react'
import { <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, WMSTileLayer, useMap } from 'react-leaflet'
import { useAuth } from '../contexts/AuthContext'
import LayerControl from './LayerControl'
import { toast } from 'react-toastify'
import { Layers, X } from 'lucide-react'
import 'leaflet/dist/leaflet.css'

// Fix for default markers in react-leaflet
import L from 'leaflet'
delete L.Icon.Default.prototype._getIconUrl
L.Icon.Default.mergeOptions({
  iconRetinaUrl: 'https://cdnjs.cloudflare.com/ajax/libs/leaflet/1.7.1/images/marker-icon-2x.png',
  iconUrl: 'https://cdnjs.cloudflare.com/ajax/libs/leaflet/1.7.1/images/marker-icon.png',
  shadowUrl: 'https://cdnjs.cloudflare.com/ajax/libs/leaflet/1.7.1/images/marker-shadow.png',
})

const MapView = () => {
  const { user, logout } = useAuth()
  const [layers, setLayers] = useState([])
  const [baseLayers, setBaseLayers] = useState([])
  const [loading, setLoading] = useState(true)
  const [showLayerControl, setShowLayerControl] = useState(true)
  const [userPreferences, setUserPreferences] = useState({})
  const mapRef = useRef()

  // Map configuration
  const mapConfig = {
    center: [42.7339, 25.4858], // Bulgaria center
    zoom: 7,
    minZoom: 1,
    maxZoom: 18
  }

  useEffect(() => {
    loadLayers()
    loadUserPreferences()
  }, [])

  const loadLayers = async () => {
    try {
      const response = await fetch('/api/geolayer/visible', {
        credentials: 'include'
      })
      
      if (response.ok) {
        const layersData = await response.json()
        
        // Separate base layers from regular layers
        const baseLayersData = layersData.filter(layer => layer.isBaseLayer)
        const regularLayersData = layersData.filter(layer => !layer.isBaseLayer)
        
        setBaseLayers(baseLayersData)
        setLayers(regularLayersData)
      } else {
        toast.error('Грешка при зареждане на слоевете')
      }
    } catch (error) {
      console.error('Error loading layers:', error)
      toast.error('Грешка при свързване със сървъра')
    } finally {
      setLoading(false)
    }
  }

  const loadUserPreferences = async () => {
    try {
      const response = await fetch('/api/geolayer/user-preferences', {
        credentials: 'include'
      })
      
      if (response.ok) {
        const preferences = await response.json()
        const prefsMap = {}
        preferences.forEach(pref => {
          prefsMap[pref.geoLayerId] = pref
        })
        setUserPreferences(prefsMap)
      }
    } catch (error) {
      console.error('Error loading user preferences:', error)
    }
  }

  const toggleLayerVisibility = async (layerId) => {
    try {
      const response = await fetch(`/api/geolayer/toggle-visibility/${layerId}`, {
        method: 'POST',
        credentials: 'include'
      })
      
      if (response.ok) {
        // Update local state
        setLayers(prevLayers => 
          prevLayers.map(layer => 
            layer.id === layerId 
              ? { ...layer, isVisible: !layer.isVisible }
              : layer
          )
        )
        
        // Reload user preferences to get updated state
        loadUserPreferences()
      } else {
        toast.error('Грешка при промяна на видимостта')
      }
    } catch (error) {
      console.error('Error toggling layer visibility:', error)
      toast.error('Грешка при свързване със сървъра')
    }
  }

  const updateLayerOpacity = async (layerId, opacity) => {
    try {
      const response = await fetch(`/api/geolayer/update-opacity/${layerId}`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        credentials: 'include',
        body: JSON.stringify(opacity)
      })
      
      if (response.ok) {
        // Update local state
        setLayers(prevLayers => 
          prevLayers.map(layer => 
            layer.id === layerId 
              ? { ...layer, opacity: opacity }
              : layer
          )
        )
        
        // Update user preferences
        setUserPreferences(prev => ({
          ...prev,
          [layerId]: {
            ...prev[layerId],
            opacity: opacity
          }
        }))
      } else {
        toast.error('Грешка при промяна на прозрачността')
      }
    } catch (error) {
      console.error('Error updating layer opacity:', error)
      toast.error('Грешка при свързване със сървъра')
    }
  }

  const handleLogout = async () => {
    await logout()
  }

  if (loading) {
    return (
      <div className="loading">
        <div>Зареждане на картата...</div>
      </div>
    )
  }

  return (
    <div className="map-container">
      {/* Header */}
      <div className="map-header">
        <h1>Геопространствени данни КККР</h1>
        <div className="user-info">
          <span>Добре дошли, {user?.firstName} {user?.lastName}</span>
          <button onClick={handleLogout} className="logout-btn">
            Изход
          </button>
        </div>
      </div>

      {/* Toggle Layer Control Button */}
      <button 
        className="toggle-button"
        onClick={() => setShowLayerControl(!showLayerControl)}
        title={showLayerControl ? 'Скрий контрола за слоеве' : 'Покажи контрола за слоеве'}
      >
        {showLayerControl ? <X size={20} /> : <Layers size={20} />}
      </button>

      {/* Map Content */}
      <div className="map-content">
        <MapContainer
          center={mapConfig.center}
          zoom={mapConfig.zoom}
          minZoom={mapConfig.minZoom}
          maxZoom={mapConfig.maxZoom}
          style={{ height: '100%', width: '100%' }}
          ref={mapRef}
        >
          {/* Base Layer - OpenStreetMap as default */}
          <TileLayer
            url="https://{s}.tile.openstreetmap.org/{z}/{x}/{y}.png"
            attribution='&copy; <a href="https://www.openstreetmap.org/copyright">OpenStreetMap</a> contributors'
          />

          {/* WMS Layers */}
          {layers
            .filter(layer => {
              const userPref = userPreferences[layer.id]
              return userPref ? userPref.isVisible : layer.isVisible
            })
            .map(layer => {
              const userPref = userPreferences[layer.id]
              const opacity = userPref?.opacity ?? layer.opacity ?? 1.0
              
              if (layer.layerType === 'WMS') {
                return (
                  <WMSTileLayer
                    key={layer.id}
                    url={layer.wmsUrl}
                    layers={`${layer.workspace}:${layer.layerName}`}
                    format="image/png"
                    transparent={true}
                    opacity={opacity}
                    attribution={`Layer: ${layer.name}`}
                  />
                )
              }
              return null
            })}
        </MapContainer>

        {/* Layer Control Panel */}
        {showLayerControl && (
          <LayerControl
            layers={layers}
            baseLayers={baseLayers}
            userPreferences={userPreferences}
            onToggleVisibility={toggleLayerVisibility}
            onUpdateOpacity={updateLayerOpacity}
          />
        )}
      </div>
    </div>
  )
}

export default MapView
