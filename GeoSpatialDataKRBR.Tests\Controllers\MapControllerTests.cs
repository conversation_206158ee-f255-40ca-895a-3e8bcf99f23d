using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Http;
using Moq;
using Xunit;
using FluentAssertions;
using System.Security.Claims;
using GeoSpatialDataKRBR.Controllers;
using GeoSpatialDataKRBR.Services.Data.Interfaces;
using GeoSpatialDataKRBR.Web.ViewModels.Map;
using GeoSpatialDataKRBR.Web.ViewModels.GeoLayer;
using GeoSpatialDataKRBR.Data.Models;

namespace GeoSpatialDataKRBR.Tests.Controllers
{
    public class MapControllerTests
    {
        private readonly Mock<IGeoLayerService> _mockGeoLayerService;
        private readonly Mock<IUserLayerPreferenceService> _mockUserLayerPreferenceService;
        private readonly MapController _controller;
        private readonly Guid _testUserId = Guid.NewGuid();

        public MapControllerTests()
        {
            _mockGeoLayerService = new Mock<IGeoLayerService>();
            _mockUserLayerPreferenceService = new Mock<IUserLayerPreferenceService>();
            _controller = new MapController(_mockGeoLayerService.Object, _mockUserLayerPreferenceService.Object);

            // Setup user context
            var claims = new List<Claim>
            {
                new Claim(ClaimTypes.NameIdentifier, _testUserId.ToString())
            };
            var identity = new ClaimsIdentity(claims, "TestAuthType");
            var claimsPrincipal = new ClaimsPrincipal(identity);

            _controller.ControllerContext = new ControllerContext
            {
                HttpContext = new DefaultHttpContext
                {
                    User = claimsPrincipal
                }
            };
        }

        [Fact]
        public async Task Index_ShouldReturnViewWithCorrectModel()
        {
            // Arrange
            var baseLayers = new List<GeoLayerListViewModel>
            {
                new GeoLayerListViewModel
                {
                    Id = Guid.NewGuid(),
                    Name = "Basemap",
                    Description = "Basic map layer",
                    LayerType = "Tile",
                    IsBaseLayer = true,
                    IsVisible = true,
                    Workspace = "base",
                    LayerName = "basemap"
                },
                new GeoLayerListViewModel
                {
                    Id = Guid.NewGuid(),
                    Name = "Транспортна карта",
                    Description = "Transport focused map",
                    LayerType = "Tile",
                    IsBaseLayer = true,
                    IsVisible = false,
                    Workspace = "transport",
                    LayerName = "transport_map"
                }
            };

            var generalLayers = new List<GeoLayerListViewModel>
            {
                new GeoLayerListViewModel
                {
                    Id = Guid.NewGuid(),
                    Name = "Велоалеи",
                    Description = "Bicycle lanes in Bulgaria",
                    LayerType = "WMS",
                    IsBaseLayer = false,
                    IsVisible = false,
                    Workspace = "transport",
                    LayerName = "bike_lanes"
                },
                new GeoLayerListViewModel
                {
                    Id = Guid.NewGuid(),
                    Name = "Железопътни линии",
                    Description = "Railway network in Bulgaria",
                    LayerType = "WMS",
                    IsBaseLayer = false,
                    IsVisible = false,
                    Workspace = "transport",
                    LayerName = "railways"
                }
            };

            var userPreferences = new List<UserLayerPreference>();

            _mockGeoLayerService
                .Setup(s => s.GetAllLayersAsync())
                .ReturnsAsync(baseLayers.Concat(generalLayers).ToList());

            _mockUserLayerPreferenceService
                .Setup(s => s.GetUserPreferencesAsync(_testUserId))
                .ReturnsAsync(userPreferences);

            // Act
            var result = await _controller.Index();

            // Assert
            result.Should().BeOfType<ViewResult>();
            var viewResult = result as ViewResult;
            viewResult!.Model.Should().BeOfType<MapViewModel>();

            var model = viewResult.Model as MapViewModel;
            model!.BaseLayers.Should().HaveCount(2);
            model.Layers.Should().HaveCount(2);
            model.BaseLayers.Should().Contain(l => l.Name == "Basemap");
            model.BaseLayers.Should().Contain(l => l.Name == "Транспортна карта");
            model.Layers.Should().Contain(l => l.Name == "Велоалеи");
            model.Layers.Should().Contain(l => l.Name == "Железопътни линии");
        }

        [Fact]
        public async Task Index_ShouldApplyUserPreferences()
        {
            // Arrange
            var layerId = Guid.NewGuid();
            var layers = new List<GeoLayerListViewModel>
            {
                new GeoLayerListViewModel
                {
                    Id = layerId,
                    Name = "Test Layer",
                    IsBaseLayer = false,
                    IsVisible = false,
                    Opacity = 1.0
                }
            };

            var userPreferences = new List<UserLayerPreference>
            {
                new UserLayerPreference
                {
                    GeoLayerId = layerId,
                    UserId = _testUserId,
                    IsVisible = true,
                    Opacity = 0.7
                }
            };

            _mockGeoLayerService
                .Setup(s => s.GetAllLayersAsync())
                .ReturnsAsync(layers);

            _mockUserLayerPreferenceService
                .Setup(s => s.GetUserPreferencesAsync(_testUserId))
                .ReturnsAsync(userPreferences);

            // Act
            var result = await _controller.Index();

            // Assert
            var viewResult = result as ViewResult;
            var model = viewResult!.Model as MapViewModel;
            
            var layer = model!.Layers.First(l => l.Id == layerId);
            layer.IsVisible.Should().BeTrue();
            layer.Opacity.Should().Be(0.7);
        }

        [Fact]
        public async Task Index_ShouldSetSelectedBaseLayer()
        {
            // Arrange
            var baseLayerId = Guid.NewGuid();
            var baseLayers = new List<GeoLayerListViewModel>
            {
                new GeoLayerListViewModel
                {
                    Id = baseLayerId,
                    Name = "Basemap",
                    IsBaseLayer = true,
                    IsVisible = true
                }
            };

            _mockGeoLayerService
                .Setup(s => s.GetAllLayersAsync())
                .ReturnsAsync(baseLayers);

            _mockUserLayerPreferenceService
                .Setup(s => s.GetUserPreferencesAsync(_testUserId))
                .ReturnsAsync(new List<UserLayerPreference>());

            // Act
            var result = await _controller.Index();

            // Assert
            var viewResult = result as ViewResult;
            var model = viewResult!.Model as MapViewModel;
            
            model!.SelectedBaseLayerId.Should().Be(baseLayerId.ToString());
        }

        [Fact]
        public async Task Index_WithNoLayers_ShouldReturnEmptyModel()
        {
            // Arrange
            _mockGeoLayerService
                .Setup(s => s.GetAllLayersAsync())
                .ReturnsAsync(new List<GeoLayerListViewModel>());

            _mockUserLayerPreferenceService
                .Setup(s => s.GetUserPreferencesAsync(_testUserId))
                .ReturnsAsync(new List<UserLayerPreference>());

            // Act
            var result = await _controller.Index();

            // Assert
            var viewResult = result as ViewResult;
            var model = viewResult!.Model as MapViewModel;
            
            model!.BaseLayers.Should().BeEmpty();
            model.Layers.Should().BeEmpty();
            model.SelectedBaseLayerId.Should().BeNull();
        }

        [Fact]
        public async Task Index_WithException_ShouldThrow()
        {
            // Arrange
            _mockGeoLayerService
                .Setup(s => s.GetAllLayersAsync())
                .ThrowsAsync(new Exception("Database error"));

            // Act & Assert
            await Assert.ThrowsAsync<Exception>(() => _controller.Index());
        }
    }
}
