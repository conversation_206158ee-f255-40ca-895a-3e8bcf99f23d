namespace TaskManager.Data.Configurations
{
    using Microsoft.EntityFrameworkCore;
    using Microsoft.EntityFrameworkCore.Metadata.Builders;
    using TaskManager.Data.Models;

    public class KanbanColumnEntityConfiguration : IEntityTypeConfiguration<KanbanColumn>
    {
        public void Configure(EntityTypeBuilder<KanbanColumn> builder)
        {
            builder.HasKey(kc => kc.Id);

            builder.Property(kc => kc.Name)
                .IsRequired()
                .HasMaxLength(50);

            builder.Property(kc => kc.Description)
                .HasMaxLength(200);

            builder.Property(kc => kc.Position)
                .IsRequired();

            builder.Property(kc => kc.Color)
                .HasMaxLength(7);

            builder.Property(kc => kc.CreatedOn)
                .IsRequired();

            builder.Property(kc => kc.IsActive)
                .IsRequired()
                .HasDefaultValue(true);

            builder.Property(kc => kc.BoardId)
                .IsRequired();

            // Configure relationships
            builder.HasOne(kc => kc.Board)
                .WithMany(kb => kb.Columns)
                .HasForeignKey(kc => kc.BoardId)
                .OnDelete(DeleteBehavior.Cascade);

            builder.HasMany(kc => kc.Cards)
                .WithOne(card => card.Column)
                .HasForeignKey(card => card.ColumnId)
                .OnDelete(DeleteBehavior.Cascade);
        }
    }
}
