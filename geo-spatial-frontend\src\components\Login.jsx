import React, { useState, useEffect } from 'react'
import { Navigate } from 'react-router-dom'
import { useAuth } from '../contexts/AuthContext'
import { toast } from 'react-toastify'

const Login = () => {
  const [email, setEmail] = useState('')
  const [password, setPassword] = useState('')
  const [loading, setLoading] = useState(false)
  const [errors, setErrors] = useState({})
  
  const { login, isAuthenticated } = useAuth()

  useEffect(() => {
    // Clear any existing errors when component mounts
    setErrors({})
  }, [])

  if (isAuthenticated) {
    return <Navigate to="/" replace />
  }

  const handleSubmit = async (e) => {
    e.preventDefault()
    setLoading(true)
    setErrors({})

    // Basic validation
    const newErrors = {}
    if (!email) {
      newErrors.email = 'Email е задължителен'
    } else if (!/\S+@\S+\.\S+/.test(email)) {
      newErrors.email = 'Невалиден email адрес'
    }
    
    if (!password) {
      newErrors.password = 'Паролата е задължителна'
    } else if (password.length < 6) {
      newErrors.password = 'Паролата трябва да бъде поне 6 символа'
    }

    if (Object.keys(newErrors).length > 0) {
      setErrors(newErrors)
      setLoading(false)
      return
    }

    try {
      const result = await login(email, password)
      
      if (!result.success) {
        if (result.error.includes('email')) {
          setErrors({ email: result.error })
        } else if (result.error.includes('password')) {
          setErrors({ password: result.error })
        } else {
          toast.error(result.error)
        }
      }
    } catch (error) {
      toast.error('Възникна неочаквана грешка')
    } finally {
      setLoading(false)
    }
  }

  return (
    <div className="login-container">
      <form className="login-form" onSubmit={handleSubmit}>
        <h2>Вход в системата</h2>
        
        <div className="form-group">
          <label htmlFor="email">Email адрес</label>
          <input
            type="email"
            id="email"
            value={email}
            onChange={(e) => setEmail(e.target.value)}
            placeholder="Въведете вашия email"
            disabled={loading}
          />
          {errors.email && <div className="error">{errors.email}</div>}
        </div>

        <div className="form-group">
          <label htmlFor="password">Парола</label>
          <input
            type="password"
            id="password"
            value={password}
            onChange={(e) => setPassword(e.target.value)}
            placeholder="Въведете вашата парола"
            disabled={loading}
          />
          {errors.password && <div className="error">{errors.password}</div>}
        </div>

        <button 
          type="submit" 
          className="login-button"
          disabled={loading}
        >
          {loading ? 'Влизане...' : 'Вход'}
        </button>

        <div style={{ marginTop: '20px', textAlign: 'center', fontSize: '14px', color: '#666' }}>
          <p>Тестови данни:</p>
          <p>Email: <EMAIL></p>
          <p>Парола: Admin123!</p>
        </div>
      </form>
    </div>
  )
}

export default Login
