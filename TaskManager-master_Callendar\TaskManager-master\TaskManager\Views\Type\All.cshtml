﻿@model IEnumerable<TypeViewModel>;

@{
    ViewData["Title"] = "Типове задачи";
}

<div class="modern-container">
    <div class="modern-page-header">
        <h1 class="modern-page-title">
            <i class="fas fa-tags"></i>
            Типове задачи
        </h1>
        <p class="modern-page-subtitle">
            Управление на типовете задачи в системата
        </p>
    </div>

    @if (!Model.Any())
    {
        <!-- Empty State -->
        <div class="modern-card">
            <div class="modern-card-body">
                <div class="empty-types-state">
                    <i class="fas fa-tag"></i>
                    <h3>Няма създадени типове</h3>
                    <p>Все още няма създадени типове задачи в системата.</p>
                    <a class="modern-btn modern-btn-primary" asp-controller="Type" asp-action="Add">
                        <i class="fas fa-plus"></i>
                        Създай първия тип
                    </a>
                </div>
            </div>
        </div>
    }
    else
    {
        <!-- Types Grid -->
        <div class="types-container">
            <div class="types-header">
                <h3>
                    <i class="fas fa-list"></i>
                    Налични типове (@Model.Count())
                </h3>
                <a class="add-type-btn" asp-controller="Type" asp-action="Add">
                    <i class="fas fa-plus"></i>
                    Нов тип
                </a>
            </div>

            <div class="types-grid">
                @foreach (TypeViewModel type in Model)
                {
                    <div class="type-card">
                        <div class="type-card-header">
                            <div class="type-id">
                                <span class="id-badge">
                                    <i class="fas fa-hashtag"></i>
                                    @type.Id
                                </span>
                            </div>
                            <div class="type-actions">
                                <a asp-controller="Type" asp-action="Edit" asp-route-id="@type.Id" class="type-action-btn edit">
                                    <i class="fas fa-edit"></i>
                                </a>
                            </div>
                        </div>

                        <div class="type-card-body">
                            <div class="type-icon">
                                <i class="fas fa-tag"></i>
                            </div>
                            <h4 class="type-name">@type.Name</h4>
                            <div class="type-description">
                                Тип задача за геодезически услуги
                            </div>
                        </div>

                        <div class="type-card-footer">
                            <a asp-controller="Type" asp-action="Edit" asp-route-id="@type.Id" class="type-action-btn details">
                                <i class="fas fa-edit"></i>
                                Редактирай
                            </a>
                            <button class="type-action-btn info" onclick="showTypeInfo('@type.Id', '@type.Name')">
                                <i class="fas fa-info-circle"></i>
                                Информация
                            </button>
                        </div>
                    </div>
                }
            </div>
        </div>
    }
</div>

<!-- Type Info Modal -->
<div class="modal fade" id="typeInfoModal" tabindex="-1" aria-labelledby="typeInfoModalLabel" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content modern-modal">
            <div class="modal-header">
                <h5 class="modal-title" id="typeInfoModalLabel">
                    <i class="fas fa-info-circle"></i>
                    Информация за типа
                </h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <div class="type-info-content">
                    <div class="info-item">
                        <strong>ID:</strong> <span id="modalTypeId"></span>
                    </div>
                    <div class="info-item">
                        <strong>Име:</strong> <span id="modalTypeName"></span>
                    </div>
                    <div class="info-item">
                        <strong>Статус:</strong> <span class="status-badge active">Активен</span>
                    </div>
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="modern-btn modern-btn-secondary" data-bs-dismiss="modal">Затвори</button>
            </div>
        </div>
    </div>
</div>

<script>
function showTypeInfo(typeId, typeName) {
    $('#modalTypeId').text(typeId);
    $('#modalTypeName').text(typeName);
    $('#typeInfoModal').modal('show');
}
</script>