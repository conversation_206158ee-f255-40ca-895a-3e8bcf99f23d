using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using TaskManager.Services.Data.Interfaces;
using TaskManager.Web.ViewModels.Calendar;

namespace TaskManager.Controllers.Api
{
    [ApiController]
    [Route("api/[controller]")]
    [Authorize(Roles = "Worker,Administrator")]
    public class WorkersApiController : ControllerBase
    {
        private readonly ICalendarService calendarService;

        public WorkersApiController(ICalendarService calendarService)
        {
            this.calendarService = calendarService;
        }

        [HttpPut("{workerId}/color")]
        public async Task<IActionResult> UpdateWorkerColor(string workerId, [FromBody] UpdateWorkerColorRequest request)
        {
            try
            {
                Console.WriteLine($"🎨 API UpdateWorkerColor called: workerId={workerId}, color={request?.Color}");
                
                if (string.IsNullOrEmpty(workerId))
                {
                    return BadRequest(new { error = "Worker ID is required" });
                }
                
                if (request == null || string.IsNullOrEmpty(request.Color))
                {
                    return BadRequest(new { error = "Color is required" });
                }
                
                await calendarService.UpdateWorkerColorAsync(workerId, request.Color);
                Console.WriteLine($"✅ API Color updated successfully for worker {workerId}");
                return Ok(new { message = "Color updated successfully" });
            }
            catch (Exception ex)
            {
                Console.WriteLine($"❌ API Error updating color: {ex.Message}");
                return BadRequest(new { error = ex.Message });
            }
        }
    }
}
