@model TaskManager.Web.ViewModels.Kanban.UpdateKanbanCardViewModel

<div class="modal-header">
    <h5 class="modal-title">
        <i class="fas fa-edit"></i>
        Редактиране на карта
    </h5>
    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
</div>

<form id="editCardForm">
    <div class="modal-body">
        @Html.AntiForgeryToken()
        
        <input type="hidden" asp-for="Id" />
        <input type="hidden" asp-for="ColumnId" />

        <div class="mb-3">
            <label asp-for="Title" class="form-label">Заглавие</label>
            <input asp-for="Title" class="form-control" placeholder="Въведете заглавие на картата" />
            <span asp-validation-for="Title" class="text-danger"></span>
        </div>

        <div class="mb-3">
            <label asp-for="Description" class="form-label">Описание</label>
            <textarea asp-for="Description" class="form-control" rows="3" placeholder="Въведете описание (по избор)"></textarea>
            <span asp-validation-for="Description" class="text-danger"></span>
        </div>

        <!-- GeoTask Times (Read-only if connected to GeoTask) -->
        <div class="row mb-3">
            <div class="col-md-6">
                <label for="editStartTime" class="form-label">Начало на работа</label>
                <div class="input-group">
                    <input type="datetime-local" class="form-control" id="editStartTime" name="StartTime">
                    <button type="button" class="btn btn-outline-primary" id="setCurrentTime">
                        <i class="fas fa-clock"></i> Сега
                    </button>
                </div>
                <small class="text-muted" id="startTimeNote" style="display: none;">
                    <i class="fas fa-info-circle"></i> Времето се взема от свързания проект
                </small>
            </div>
            <div class="col-md-6">
                <label for="editEndTime" class="form-label">Край на работа</label>
                <input type="datetime-local" class="form-control" id="editEndTime" name="EndTime" readonly>
                <small class="text-muted" id="endTimeNote" style="display: none;">
                    <i class="fas fa-info-circle"></i> Времето се взема от свързания проект
                </small>
            </div>
        </div>

        <div class="row">
            <div class="col-md-6">
                <div class="mb-3">
                    <label asp-for="DueDate" class="form-label">Краен срок</label>
                    <input asp-for="DueDate" type="datetime-local" class="form-control" />
                </div>
            </div>
            <div class="col-md-6">
                <div class="mb-3">
                    <label asp-for="Color" class="form-label">Цвят</label>
                    <input asp-for="Color" type="color" class="form-control form-control-color" value="#6c757d" />
                </div>
            </div>
        </div>

        <!-- Worker Assignment Section -->
        <div class="card mb-3">
            <div class="card-header">
                <h6 class="mb-0">
                    <i class="fas fa-user-cog"></i>
                    Преназначаване на работник
                </h6>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-6">
                        <label for="editAssignedTo" class="form-label">Работник</label>
                        <select asp-for="AssignedToId" class="form-select" id="editAssignedTo">
                            <option value="">-- Без назначение --</option>
                            @foreach (var worker in ViewBag.Workers as IEnumerable<TaskManager.Web.ViewModels.Admin.AllWorkersViewModel>)
                            {
                                <option value="@worker.UserId">@worker.FirstName @worker.LastName</option>
                            }
                        </select>
                    </div>
                    <div class="col-md-6">
                        <label for="editTargetColumn" class="form-label">Целева колона</label>
                        <select class="form-select" id="editTargetColumn" name="TargetColumnId" disabled>
                            <option value="">-- Изберете работник първо --</option>
                        </select>
                    </div>
                </div>
                <div class="mt-2">
                    <small class="text-muted">
                        <i class="fas fa-info-circle"></i>
                        При промяна на работника, картата ще бъде преместена в неговата лична дъска
                    </small>
                </div>
            </div>
        </div>

        <div class="row">
            <div class="col-md-6">
                <div class="mb-3">
                    <label asp-for="Color" class="form-label">Цвят</label>
                    <select asp-for="Color" class="form-select">
                        <option value="">-- По подразбиране --</option>
                        <option value="#3b82f6" style="background-color: #3b82f6; color: white;" selected="@(Model.Color == "#3b82f6")">Син</option>
                        <option value="#10b981" style="background-color: #10b981; color: white;" selected="@(Model.Color == "#10b981")">Зелен</option>
                        <option value="#f59e0b" style="background-color: #f59e0b; color: white;" selected="@(Model.Color == "#f59e0b")">Оранжев</option>
                        <option value="#ef4444" style="background-color: #ef4444; color: white;" selected="@(Model.Color == "#ef4444")">Червен</option>
                        <option value="#8b5cf6" style="background-color: #8b5cf6; color: white;" selected="@(Model.Color == "#8b5cf6")">Лилав</option>
                        <option value="#6b7280" style="background-color: #6b7280; color: white;" selected="@(Model.Color == "#6b7280")">Сив</option>
                    </select>
                </div>
            </div>
            <div class="col-md-6">
                <div class="mb-3">
                    <label asp-for="Labels" class="form-label">Етикети</label>
                    <input asp-for="Labels" class="form-control" placeholder="Етикет1, Етикет2, ..." />
                    <small class="form-text text-muted">Разделете етикетите със запетая</small>
                </div>
            </div>
        </div>

        <!-- Project Connection Info -->
        <div id="projectInfo" class="card mb-3" style="display: none;">
            <div class="card-header">
                <h6 class="mb-0">
                    <i class="fas fa-project-diagram"></i>
                    Свързан проект
                </h6>
            </div>
            <div class="card-body">
                <div id="projectDetails"></div>
            </div>
        </div>

        <!-- Comments Section -->
        <div id="commentsSection" class="card mb-3">
            <div class="card-header">
                <h6 class="mb-0">
                    <i class="fas fa-comments"></i>
                    Коментари
                </h6>
            </div>
            <div class="card-body">
                <!-- Existing Comments -->
                <div id="existingComments" class="mb-3">
                    <!-- Comments will be loaded here -->
                </div>

                <!-- Add New Comment -->
                <div class="border-top pt-3">
                    <label for="newComment" class="form-label">Добави коментар</label>
                    <div class="input-group">
                        <textarea class="form-control" id="newComment" rows="2" placeholder="Напишете коментар..."></textarea>
                        <button type="button" class="btn btn-primary" id="addCommentBtn">
                            <i class="fas fa-plus"></i>
                            Добави
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="modal-footer">
        <button type="button" class="btn btn-danger" id="deleteCardBtn">
            <i class="fas fa-trash"></i>
            Изтрий
        </button>
        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">
            <i class="fas fa-times"></i>
            Отказ
        </button>
        <button type="submit" class="btn btn-primary">
            <i class="fas fa-save"></i>
            Запази промените
        </button>
    </div>
</form>

<script>
$(document).ready(function() {
    // Set current time button
    $('#setCurrentTime').on('click', function() {
        const now = new Date();
        const localDateTime = new Date(now.getTime() - now.getTimezoneOffset() * 60000).toISOString().slice(0, 16);
        $('#editStartTime').val(localDateTime);
    });

    // Worker selection change - load their columns
    $('#editAssignedTo').on('change', function() {
        const selectedUserId = $(this).val();
        const targetColumnSelect = $('#editTargetColumn');

        if (selectedUserId) {
            // Load worker's columns
            $.get('@Url.Action("GetWorkerColumns", "Kanban")', { userId: selectedUserId })
                .done(function(response) {
                    if (response.success && response.columns) {
                        targetColumnSelect.empty();
                        targetColumnSelect.append('<option value="">-- Изберете колона --</option>');

                        response.columns.forEach(function(column) {
                            const columnId = column.Id || column.id;
                            const columnName = column.Name || column.name;
                            targetColumnSelect.append(`<option value="${columnId}">${columnName}</option>`);
                        });

                        targetColumnSelect.prop('disabled', false);
                    } else {
                        toastr.error('Грешка при зареждането на колоните');
                    }
                })
                .fail(function() {
                    toastr.error('Грешка при зареждането на колоните');
                });
        } else {
            targetColumnSelect.empty();
            targetColumnSelect.append('<option value="">-- Изберете работник първо --</option>');
            targetColumnSelect.prop('disabled', true);
        }
    });

    // Add comment functionality
    $('#addCommentBtn').on('click', function() {
        const comment = $('#newComment').val().trim();
        const cardId = $('#Id').val();

        if (!comment) {
            toastr.warning('Моля, въведете коментар');
            return;
        }

        // Add comment via AJAX
        $.post('@Url.Action("AddCardComment", "Kanban")', {
            cardId: cardId,
            comment: comment,
            __RequestVerificationToken: $('input[name="__RequestVerificationToken"]').val()
        })
        .done(function(response) {
            if (response.success) {
                $('#newComment').val('');
                loadComments(cardId); // Reload comments
                toastr.success('Коментарът беше добавен');
            } else {
                toastr.error(response.message || 'Грешка при добавянето на коментар');
            }
        })
        .fail(function() {
            toastr.error('Грешка при добавянето на коментар');
        });
    });

    // Delete card functionality
    $('#deleteCardBtn').on('click', function() {
        const cardId = $('#Id').val();

        if (confirm('Сигурни ли сте, че искате да изтриете тази карта?')) {
            $.post('@Url.Action("DeleteCard", "Kanban")', {
                cardId: cardId,
                __RequestVerificationToken: $('input[name="__RequestVerificationToken"]').val()
            })
            .done(function(response) {
                if (response.success) {
                    $('#editCardModal').modal('hide');
                    location.reload(); // Refresh the board
                    toastr.success('Картата беше изтрита');
                } else {
                    toastr.error(response.message || 'Грешка при изтриването на картата');
                }
            })
            .fail(function() {
                toastr.error('Грешка при изтриването на картата');
            });
        }
    });

    // Form submission
    $('#editCardForm').on('submit', function(e) {
        e.preventDefault();

        var formData = {
            Id: $('#Id').val(),
            Title: $('#Title').val(),
            Description: $('#Description').val(),
            ColumnId: $('#ColumnId').val(),
            AssignedToId: $('#editAssignedTo').val(),
            TargetColumnId: $('#editTargetColumn').val(),
            DueDate: $('#DueDate').val(),
            Color: $('#Color').val(),
            Labels: $('#Labels').val(),
            __RequestVerificationToken: $('input[name="__RequestVerificationToken"]').val()
        };

        // Only include StartTime if card is not connected to GeoTask
        if (!$('#editStartTime').prop('readonly')) {
            formData.StartTime = $('#editStartTime').val();
        }

        $.post('@Url.Action("UpdateCard", "Kanban")', formData)
            .done(function(response) {
                if (response.success) {
                    $('#editCardModal').modal('hide');
                    location.reload(); // Refresh the board
                    toastr.success('Картата беше обновена успешно');
                } else {
                    toastr.error(response.message || 'Грешка при обновяването на картата');
                }
            })
            .fail(function() {
                toastr.error('Грешка при обновяването на картата');
            });
    });
});

// Function to load comments for a card
function loadComments(cardId) {
    $.get('@Url.Action("GetCardComments", "Kanban")', { cardId: cardId })
        .done(function(response) {
            if (response.success) {
                displayComments(response.comments);
            }
        });
}

// Function to display comments
function displayComments(comments) {
    const container = $('#existingComments');
    container.empty();

    if (!comments || comments.length === 0) {
        container.append('<p class="text-muted">Няма коментари</p>');
        return;
    }

    comments.forEach(function(comment) {
        const commentHtml = `
            <div class="border-bottom pb-2 mb-2">
                <div class="d-flex justify-content-between">
                    <strong>${comment.authorName}</strong>
                    <small class="text-muted">${comment.createdOn}</small>
                </div>
                <p class="mb-0">${comment.content}</p>
            </div>
        `;
        container.append(commentHtml);
    });
}


</script>
