﻿@using TaskManager.Services.Data.Interfaces;
@using TaskManager.Web.Infrastructure.Extentions;
@inject IUserService UserService;

@{
    bool isUserWorker = await this.UserService.IsUserWorkerByIdAsync(User.GetId());
    bool isUserAdmin = this.User.isAdmin();
}

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="utf-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>@ViewData["Title"] - TaskManager</title>
    <link rel="stylesheet" href="~/lib/bootstrap/dist/css/bootstrap.min.css" />
    <link rel="stylesheet" href="~/TaskManager.styles.css" asp-append-version="true" />
    <script src="~/lib/jquery/dist/jquery.min.js"></script>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.2/css/all.min.css">
    <script src="https://cdnjs.cloudflare.com/ajax/libs/toastr.js/2.1.4/toastr.min.js" integrity="sha512-lbwH47l/tPXJYG9AcFNoJaTMhGvYWhVM9YI43CT+uteTRRaiLCui8snIgyAN8XWgNjNhCqlAUdzZptso6OCoFQ==" crossorigin="anonymous" referrerpolicy="no-referrer"></script>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/toastr.js/2.1.4/toastr.min.css" integrity="sha512-6S2HWzVFxruDlZxI3sXOZZ4/eJ8AcxkQH1+JjSe/ONCEqR9L4Ysq5JdT5ipqtzU7WHalNwzwBv+iE51gNHJNqQ==" crossorigin="anonymous" referrerpolicy="no-referrer" />
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/select2@4.0.13/dist/css/select2.min.css"/>
    <script type="text/javascript" src="https://cdn.jsdelivr.net/npm/select2@4.0.13/dist/js/select2.min.js"></script>
    <link rel="stylesheet" href="~/css/site.css" asp-append-version="true" />
    @await RenderSectionAsync("Styles", required: false)


</head>
<body>
    <partial name="_NotificationPartial" />
    <header>
        <nav class="modern-navbar navbar navbar-expand-lg">
            <div class="container-fluid">
                <a class="navbar-brand modern-nav-brand" asp-area="" asp-controller="Home" asp-action="Index">
                    <i class="fas fa-mountain"></i>
                    <span class="brand-text">Geodesy</span>
                </a>
                <button class="navbar-toggler modern-toggler" type="button" data-bs-toggle="collapse" data-bs-target=".navbar-collapse" aria-controls="navbarSupportedContent"
                        aria-expanded="false" aria-label="Toggle navigation">
                    <i class="fas fa-bars"></i>
                </button>
                <div class="navbar-collapse collapse d-lg-inline-flex justify-content-between">
                    <ul class="navbar-nav flex-grow-1">
                        @if (!isUserWorker)
                        {
                            <li class="nav-item">
                                <a class="modern-nav-link" asp-area="" asp-controller="Request" asp-action="CreateRequest">
                                    <i class="fas fa-paper-plane"></i>
                                    Изпрати заявка
                                </a>
                            </li>
                        }
                        @if (User.Identity?.IsAuthenticated ?? false)
                        {
                            @if (isUserWorker)
                            {
                                <li class="nav-item">
                                    <a class="modern-nav-link" asp-area="" asp-controller="GeoTask" asp-action="MyTasks">
                                        <i class="fas fa-home"></i>
                                        Моите задачи
                                    </a>
                                </li>
                                <li class="nav-item">
                                    <a class="modern-nav-link kanban-link" asp-area="" asp-controller="Kanban" asp-action="TeamBoard">
                                        <i class="fas fa-columns"></i>
                                        Екипна дъска
                                    </a>
                                </li>
                                <li class="nav-item">
                                    <a class="modern-nav-link" asp-area="" asp-controller="GeoTask" asp-action="AllTasks">
                                        <i class="fas fa-tasks"></i>
                                        Всички задачи
                                    </a>
                                </li>
                                <li class="nav-item">
                                    <a class="modern-nav-link" asp-area="" asp-controller="GeoTask" asp-action="Checked">
                                        <i class="fas fa-check-circle"></i>
                                        За проверка
                                    </a>
                                </li>
                                <li class="nav-item">
                                    <a class="modern-nav-link" asp-area="" asp-controller="Client" asp-action="Clients">
                                        <i class="fas fa-users"></i>
                                        Клиенти
                                    </a>
                                </li>
                                <li class="nav-item">
                                    <a class="modern-nav-link" asp-area="" asp-controller="Calendar" asp-action="Calendar">
                                        <i class="fas fa-calendar-alt"></i>
                                        Календар
                                    </a>
                                </li>
                            }
                        }
                    </ul>
                    <ul class="navbar-nav">
                        @if (User.Identity?.IsAuthenticated ?? false)
                        {
                            @if (isUserWorker)
                            {
                                <li class="nav-item">
                                    <a class="modern-nav-link" asp-area="" asp-controller="Request" asp-action="All">
                                        <i class="fas fa-inbox"></i>
                                        Поръчки
                                    </a>
                                </li>
                                @if (isUserAdmin)
                                {
                                    <li class="nav-item">
                                        <a class="modern-nav-link admin-link" asp-area="" asp-controller="Admin" asp-action="AdminPanel">
                                            <i class="fas fa-cogs"></i>
                                            Админ
                                        </a>
                                    </li>
                                }
                            }
                        }
                    </ul>
                    <partial name="_LoginPartial" />
                </div>
            </div>
        </nav>
    </header>
    <div class="modern-main-container">
        <main role="main" class="modern-main-content">
            @RenderBody()
        </main>
    </div>

    <footer class="border-top footer text-muted" >
        <div class="container text-lg" style="color:forestgreen">
            &copy; @DateTime.UtcNow.Year - Geodesy Manager
        </div>
    </footer>
    <script src="~/lib/bootstrap/dist/js/bootstrap.bundle.min.js"></script>
    <script src="~/js/site.js" asp-append-version="true"></script>
    @await RenderSectionAsync("Scripts", required: false)
</body>
</html>


            