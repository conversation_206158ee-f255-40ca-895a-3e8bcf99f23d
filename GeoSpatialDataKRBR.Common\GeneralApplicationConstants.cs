namespace GeoSpatialDataKRBR.Common
{
    public static class GeneralApplicationConstants
    {
        public const int ReleaseYear = 2024;
        public const string AdminAreaName = "Admin";
        public const string AdminRoleName = "Administrator";
        public const string UserRoleName = "User";
        
        public const string OnlineUsersCookieName = "IsOnline";
        public const int LastActivityBeforeOfflineMinutes = 10;

        // GeoServer related constants
        public const string DefaultGeoServerWorkspace = "geoserver";
        public const string DefaultSRS = "EPSG:4326";
        public const string DefaultImageFormat = "image/png";
        public const string DefaultInfoFormat = "application/json";
        
        // Map related constants - centered on Sofia
        public const double DefaultMapCenterLat = 42.6977;
        public const double DefaultMapCenterLng = 23.3219;
        public const int DefaultMapZoom = 12;
        public const int MinMapZoom = 1;
        public const int MaxMapZoom = 18;

        // Layer types
        public const string WmsLayerType = "WMS";
        public const string WfsLayerType = "WFS";
        public const string TileLayerType = "TILE";
        public const string PointLayerType = "POINT";

        // Cache settings
        public const int LayerCacheExpirationMinutes = 30;
        public const int GeoServerResponseCacheMinutes = 15;
    }
}
