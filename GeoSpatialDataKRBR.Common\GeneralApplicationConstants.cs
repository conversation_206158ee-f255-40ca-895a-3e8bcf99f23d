namespace GeoSpatialDataKRBR.Common
{
    public static class GeneralApplicationConstants
    {
        public const int ReleaseYear = 2024;
        public const string AdminAreaName = "Admin";
        public const string AdminRoleName = "Administrator";
        public const string UserRoleName = "User";
        
        public const string OnlineUsersCookieName = "IsOnline";
        public const int LastActivityBeforeOfflineMinutes = 10;

        // GeoServer related constants
        public const string DefaultGeoServerWorkspace = "geoserver";
        public const string DefaultSRS = "EPSG:4326";
        public const string DefaultImageFormat = "image/png";
        public const string DefaultInfoFormat = "application/json";
        
        // Map related constants
        public const double DefaultMapCenterLat = 42.7339;
        public const double DefaultMapCenterLng = 25.4858;
        public const int DefaultMapZoom = 7;
        public const int MinMapZoom = 1;
        public const int MaxMapZoom = 18;

        // Layer types
        public const string WmsLayerType = "WMS";
        public const string WfsLayerType = "WFS";
        public const string TileLayerType = "TILE";

        // Cache settings
        public const int LayerCacheExpirationMinutes = 30;
        public const int GeoServerResponseCacheMinutes = 15;
    }
}
