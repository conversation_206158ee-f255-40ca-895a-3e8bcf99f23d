namespace TaskManager.Data.Configurations
{
    using Microsoft.EntityFrameworkCore;
    using Microsoft.EntityFrameworkCore.Metadata.Builders;
    using TaskManager.Data.Models;

    public class KanbanBoardEntityConfiguration : IEntityTypeConfiguration<KanbanBoard>
    {
        public void Configure(EntityTypeBuilder<KanbanBoard> builder)
        {
            builder.HasKey(kb => kb.Id);

            builder.Property(kb => kb.Name)
                .IsRequired()
                .HasMaxLength(100);

            builder.Property(kb => kb.Description)
                .HasMaxLength(500);

            builder.Property(kb => kb.CreatedOn)
                .IsRequired();

            builder.Property(kb => kb.IsActive)
                .IsRequired()
                .HasDefaultValue(true);

            // Configure relationships
            builder.HasMany(kb => kb.Columns)
                .WithOne(kc => kc.Board)
                .HasForeignKey(kc => kc.BoardId)
                .OnDelete(DeleteBehavior.Cascade);
        }
    }
}
