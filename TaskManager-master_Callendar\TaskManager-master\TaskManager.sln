﻿
Microsoft Visual Studio Solution File, Format Version 12.00
# Visual Studio Version 17
VisualStudioVersion = 17.4.33110.190
MinimumVisualStudioVersion = 10.0.40219.1
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "TaskManager", "TaskManager\TaskManager.csproj", "{ECFB082A-62FD-4640-BEB6-AD55755C811A}"
EndProject
Project("{2150E333-8FDC-42A3-9474-1A3956D46DE8}") = "Web", "Web", "{FAC802D9-6640-44C4-B9D0-BFF7B21E4FC5}"
EndProject
Project("{2150E333-8FDC-42A3-9474-1A3956D46DE8}") = "Services", "Services", "{DA6ED3CE-B2B0-462E-92C9-B08965152CDB}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "TaskManager.Common", "TaskManager.Common\TaskManager.Common.csproj", "{3BDA8209-E283-41F1-9FB3-88A4AE44608D}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "TaskManager.Web.ViewModels", "TaskManager.Web.ViewModels\TaskManager.Web.ViewModels.csproj", "{E3146A0F-D734-4F2A-BFEE-A88A2D041481}"
EndProject
Project("{2150E333-8FDC-42A3-9474-1A3956D46DE8}") = "Data", "Data", "{645CDC6E-B4DD-4B05-A2F8-5E7BB3816761}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "TaskManager.Data", "TaskManager.Data\TaskManager.Data.csproj", "{F968F8FE-5692-41D5-BE6F-57D04A1BD23F}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "TaskManager.Data.Models", "TaskManager.Data.Models\TaskManager.Data.Models.csproj", "{1B27B3CB-BF45-4A1E-8CC4-FE1B22C912BF}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "TaskManager.Services.Data", "TaskManager.Services.Data\TaskManager.Services.Data.csproj", "{E3CD1DF3-594A-458C-A7AA-C64DB0D00814}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "TaskManager.Web.Infrastructure", "TaskManager.Web.Infrastructure\TaskManager.Web.Infrastructure.csproj", "{28AE4078-7A52-473B-8497-C35142517C1D}"
EndProject
Project("{2150E333-8FDC-42A3-9474-1A3956D46DE8}") = "Tests", "Tests", "{8B055F65-F389-4732-A324-16BE63F929B4}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "TaskManager.Services.Tests", "TaskManager.Services.Tests\TaskManager.Services.Tests.csproj", "{9FB5F0BA-671C-4BC6-A893-E224E8BC5A41}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "TaskManager.FronEndServices.Tests", "TaskManager.FronEndServices.Tests\TaskManager.FronEndServices.Tests.csproj", "{EFD19E55-20E3-4567-8958-ABAF313760AD}"
EndProject
Global
	GlobalSection(SolutionConfigurationPlatforms) = preSolution
		Debug|Any CPU = Debug|Any CPU
		Release|Any CPU = Release|Any CPU
	EndGlobalSection
	GlobalSection(ProjectConfigurationPlatforms) = postSolution
		{ECFB082A-62FD-4640-BEB6-AD55755C811A}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{ECFB082A-62FD-4640-BEB6-AD55755C811A}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{ECFB082A-62FD-4640-BEB6-AD55755C811A}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{ECFB082A-62FD-4640-BEB6-AD55755C811A}.Release|Any CPU.Build.0 = Release|Any CPU
		{3BDA8209-E283-41F1-9FB3-88A4AE44608D}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{3BDA8209-E283-41F1-9FB3-88A4AE44608D}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{3BDA8209-E283-41F1-9FB3-88A4AE44608D}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{3BDA8209-E283-41F1-9FB3-88A4AE44608D}.Release|Any CPU.Build.0 = Release|Any CPU
		{E3146A0F-D734-4F2A-BFEE-A88A2D041481}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{E3146A0F-D734-4F2A-BFEE-A88A2D041481}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{E3146A0F-D734-4F2A-BFEE-A88A2D041481}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{E3146A0F-D734-4F2A-BFEE-A88A2D041481}.Release|Any CPU.Build.0 = Release|Any CPU
		{F968F8FE-5692-41D5-BE6F-57D04A1BD23F}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{F968F8FE-5692-41D5-BE6F-57D04A1BD23F}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{F968F8FE-5692-41D5-BE6F-57D04A1BD23F}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{F968F8FE-5692-41D5-BE6F-57D04A1BD23F}.Release|Any CPU.Build.0 = Release|Any CPU
		{1B27B3CB-BF45-4A1E-8CC4-FE1B22C912BF}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{1B27B3CB-BF45-4A1E-8CC4-FE1B22C912BF}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{1B27B3CB-BF45-4A1E-8CC4-FE1B22C912BF}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{1B27B3CB-BF45-4A1E-8CC4-FE1B22C912BF}.Release|Any CPU.Build.0 = Release|Any CPU
		{E3CD1DF3-594A-458C-A7AA-C64DB0D00814}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{E3CD1DF3-594A-458C-A7AA-C64DB0D00814}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{E3CD1DF3-594A-458C-A7AA-C64DB0D00814}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{E3CD1DF3-594A-458C-A7AA-C64DB0D00814}.Release|Any CPU.Build.0 = Release|Any CPU
		{28AE4078-7A52-473B-8497-C35142517C1D}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{28AE4078-7A52-473B-8497-C35142517C1D}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{28AE4078-7A52-473B-8497-C35142517C1D}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{28AE4078-7A52-473B-8497-C35142517C1D}.Release|Any CPU.Build.0 = Release|Any CPU
		{9FB5F0BA-671C-4BC6-A893-E224E8BC5A41}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{9FB5F0BA-671C-4BC6-A893-E224E8BC5A41}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{9FB5F0BA-671C-4BC6-A893-E224E8BC5A41}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{9FB5F0BA-671C-4BC6-A893-E224E8BC5A41}.Release|Any CPU.Build.0 = Release|Any CPU
		{EFD19E55-20E3-4567-8958-ABAF313760AD}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{EFD19E55-20E3-4567-8958-ABAF313760AD}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{EFD19E55-20E3-4567-8958-ABAF313760AD}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{EFD19E55-20E3-4567-8958-ABAF313760AD}.Release|Any CPU.Build.0 = Release|Any CPU
	EndGlobalSection
	GlobalSection(SolutionProperties) = preSolution
		HideSolutionNode = FALSE
	EndGlobalSection
	GlobalSection(NestedProjects) = preSolution
		{ECFB082A-62FD-4640-BEB6-AD55755C811A} = {FAC802D9-6640-44C4-B9D0-BFF7B21E4FC5}
		{E3146A0F-D734-4F2A-BFEE-A88A2D041481} = {FAC802D9-6640-44C4-B9D0-BFF7B21E4FC5}
		{F968F8FE-5692-41D5-BE6F-57D04A1BD23F} = {645CDC6E-B4DD-4B05-A2F8-5E7BB3816761}
		{1B27B3CB-BF45-4A1E-8CC4-FE1B22C912BF} = {645CDC6E-B4DD-4B05-A2F8-5E7BB3816761}
		{E3CD1DF3-594A-458C-A7AA-C64DB0D00814} = {DA6ED3CE-B2B0-462E-92C9-B08965152CDB}
		{28AE4078-7A52-473B-8497-C35142517C1D} = {FAC802D9-6640-44C4-B9D0-BFF7B21E4FC5}
		{9FB5F0BA-671C-4BC6-A893-E224E8BC5A41} = {8B055F65-F389-4732-A324-16BE63F929B4}
		{EFD19E55-20E3-4567-8958-ABAF313760AD} = {8B055F65-F389-4732-A324-16BE63F929B4}
	EndGlobalSection
	GlobalSection(ExtensibilityGlobals) = postSolution
		SolutionGuid = {DAD5B25C-104F-4FCD-9A08-8412D59F258F}
	EndGlobalSection
EndGlobal
