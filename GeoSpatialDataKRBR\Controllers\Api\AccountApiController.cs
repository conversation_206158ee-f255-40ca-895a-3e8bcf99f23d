namespace GeoSpatialDataKRBR.Controllers.Api
{
    using Microsoft.AspNetCore.Authorization;
    using Microsoft.AspNetCore.Identity;
    using Microsoft.AspNetCore.Mvc;
    using GeoSpatialDataKRBR.Data.Models;
    using System.Security.Claims;

    [ApiController]
    [Route("api/[controller]")]
    public class AccountApiController : ControllerBase
    {
        private readonly UserManager<ApplicationUser> userManager;
        private readonly SignInManager<ApplicationUser> signInManager;

        public AccountApiController(
            UserManager<ApplicationUser> userManager,
            SignInManager<ApplicationUser> signInManager)
        {
            this.userManager = userManager;
            this.signInManager = signInManager;
        }

        [HttpPost("login")]
        public async Task<IActionResult> Login([FromBody] LoginRequest request)
        {
            if (!ModelState.IsValid)
            {
                return BadRequest(new { message = "Невалидни данни" });
            }

            try
            {
                var user = await this.userManager.FindByEmailAsync(request.Email);
                if (user == null || !user.IsActive)
                {
                    return BadRequest(new { message = "Невалиден email или парола" });
                }

                var result = await this.signInManager.PasswordSignInAsync(
                    user, request.Password, isPersistent: true, lockoutOnFailure: false);

                if (result.Succeeded)
                {
                    // Update last login date
                    user.LastLoginDate = DateTime.UtcNow;
                    await this.userManager.UpdateAsync(user);

                    var userInfo = new
                    {
                        id = user.Id,
                        email = user.Email,
                        firstName = user.FirstName,
                        lastName = user.LastName,
                        isAuthenticated = true
                    };

                    return Ok(userInfo);
                }
                else if (result.IsLockedOut)
                {
                    return BadRequest(new { message = "Акаунтът е заключен" });
                }
                else
                {
                    return BadRequest(new { message = "Невалиден email или парола" });
                }
            }
            catch (Exception ex)
            {
                return StatusCode(500, new { message = "Грешка при влизане в системата", error = ex.Message });
            }
        }

        [HttpPost("logout")]
        [Authorize]
        public async Task<IActionResult> Logout()
        {
            try
            {
                await this.signInManager.SignOutAsync();
                return Ok(new { message = "Успешно излизане от системата" });
            }
            catch (Exception ex)
            {
                return StatusCode(500, new { message = "Грешка при излизане от системата", error = ex.Message });
            }
        }

        [HttpGet("user-info")]
        [Authorize]
        public async Task<IActionResult> GetUserInfo()
        {
            try
            {
                var userId = User.FindFirst(ClaimTypes.NameIdentifier)?.Value;
                if (string.IsNullOrEmpty(userId) || !Guid.TryParse(userId, out var userGuid))
                {
                    return Unauthorized(new { message = "Невалиден потребител" });
                }

                var user = await this.userManager.FindByIdAsync(userId);
                if (user == null || !user.IsActive)
                {
                    return Unauthorized(new { message = "Потребителят не беше намерен" });
                }

                var userInfo = new
                {
                    id = user.Id,
                    email = user.Email,
                    firstName = user.FirstName,
                    lastName = user.LastName,
                    isAuthenticated = true,
                    lastLoginDate = user.LastLoginDate
                };

                return Ok(userInfo);
            }
            catch (Exception ex)
            {
                return StatusCode(500, new { message = "Грешка при зареждане на потребителската информация", error = ex.Message });
            }
        }

        [HttpPost("register")]
        public async Task<IActionResult> Register([FromBody] RegisterRequest request)
        {
            if (!ModelState.IsValid)
            {
                return BadRequest(new { message = "Невалидни данни" });
            }

            try
            {
                var existingUser = await this.userManager.FindByEmailAsync(request.Email);
                if (existingUser != null)
                {
                    return BadRequest(new { message = "Потребител с този email вече съществува" });
                }

                var user = new ApplicationUser
                {
                    UserName = request.Email,
                    Email = request.Email,
                    FirstName = request.FirstName,
                    LastName = request.LastName,
                    EmailConfirmed = true,
                    IsActive = true,
                    CreatedOn = DateTime.UtcNow
                };

                var result = await this.userManager.CreateAsync(user, request.Password);
                if (result.Succeeded)
                {
                    // Add user to default role
                    await this.userManager.AddToRoleAsync(user, "User");

                    var userInfo = new
                    {
                        id = user.Id,
                        email = user.Email,
                        firstName = user.FirstName,
                        lastName = user.LastName,
                        message = "Регистрацията беше успешна"
                    };

                    return Ok(userInfo);
                }
                else
                {
                    var errors = string.Join(", ", result.Errors.Select(e => e.Description));
                    return BadRequest(new { message = "Грешка при регистрация: " + errors });
                }
            }
            catch (Exception ex)
            {
                return StatusCode(500, new { message = "Грешка при регистрация", error = ex.Message });
            }
        }

        [HttpGet("check-auth")]
        public IActionResult CheckAuth()
        {
            if (User.Identity?.IsAuthenticated == true)
            {
                return Ok(new { isAuthenticated = true });
            }
            else
            {
                return Ok(new { isAuthenticated = false });
            }
        }
    }

    public class LoginRequest
    {
        public string Email { get; set; } = null!;
        public string Password { get; set; } = null!;
    }

    public class RegisterRequest
    {
        public string Email { get; set; } = null!;
        public string Password { get; set; } = null!;
        public string FirstName { get; set; } = null!;
        public string LastName { get; set; } = null!;
    }
}
