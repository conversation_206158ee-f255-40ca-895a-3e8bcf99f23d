# 🧪 Testing Guide - Task Calendar Application

Comprehensive testing suite for the Task Calendar application using Vitest and React Testing Library.

## 🚀 Quick Start

### Running Tests

```bash
# Run all tests
npm test

# Run tests in watch mode (recommended for development)
npm test

# Run tests once and exit
npm run test:run

# Run tests with UI interface
npm run test:ui

# Run tests with coverage report
npm run test:coverage
```

### Test Structure

```
src/test/
├── setup.js           # Test configuration and mocks
├── utils.test.js      # Utility function tests
├── App.test.jsx       # Main component tests
└── integration.test.jsx # Integration tests
```

## 📋 Test Coverage

### ✅ Utility Functions (`utils.test.js`)

**Time Slot Generation**
- ✅ Generates correct 21 time slots (8:00-18:00)
- ✅ Includes both :00 and :30 intervals
- ✅ Formats hours with leading zeros
- ✅ Excludes 18:30 slot

**Date Formatting**
- ✅ Converts dates to YYYY-MM-DD format
- ✅ Handles different date input formats
- ✅ Maintains timezone consistency

**Duration Calculations**
- ✅ Calculates minutes between time slots
- ✅ Handles cross-hour calculations
- ✅ Validates time range logic

**Overlap Detection**
- ✅ Detects overlapping tasks on same date
- ✅ Ignores non-overlapping tasks
- ✅ Handles different dates correctly
- ✅ Manages edge cases (touching times)

**Color Palette**
- ✅ Validates 10 predefined colors
- ✅ Ensures valid hex color format
- ✅ Includes expected color values

### ✅ Component Tests (`App.test.jsx`)

**Initial Render**
- ✅ Renders main application elements
- ✅ Shows calendar view buttons (Day/Week/Month/Year)
- ✅ Displays team members in sidebar
- ✅ Shows current date in header

**Calendar Navigation**
- ✅ Switches between calendar views
- ✅ Navigates between dates with arrows
- ✅ Maintains active view state
- ✅ Updates calendar display

**Team Member Management**
- ✅ Toggles team member visibility
- ✅ Displays member initials and colors
- ✅ Handles member selection

**Task Creation Modal**
- ✅ Opens modal when clicking time slots
- ✅ Renders all form fields
- ✅ Shows color palette options
- ✅ Displays team member checkboxes

**Responsive Design**
- ✅ Handles mobile viewport
- ✅ Maintains functionality on small screens
- ✅ Adapts layout appropriately

**Error Handling**
- ✅ Handles invalid dates gracefully
- ✅ Works with empty task lists
- ✅ Prevents application crashes

**Accessibility**
- ✅ Provides proper ARIA labels
- ✅ Supports keyboard navigation
- ✅ Maintains focus management

### ✅ Integration Tests (`integration.test.jsx`)

**Task Creation Workflow**
- ✅ Complete task creation process
- ✅ Form validation for required fields
- ✅ Data persistence after creation

**Task Editing Workflow**
- ✅ Opens edit modal for existing tasks
- ✅ Updates task properties
- ✅ Saves changes correctly

**Drag and Drop Functionality**
- ✅ Handles drag start events
- ✅ Processes drop events on time slots
- ✅ Shows visual drag-over effects
- ✅ Updates task positions

**Calendar Navigation Integration**
- ✅ Maintains state across view changes
- ✅ Updates calendar when date changes
- ✅ Preserves selected date context

**Team Member Integration**
- ✅ Assigns multiple team members to tasks
- ✅ Toggles member visibility in sidebar
- ✅ Updates task assignments

**Color Selection Integration**
- ✅ Selects colors from palette
- ✅ Updates task colors visually
- ✅ Maintains color consistency

**Conflict Detection Integration**
- ✅ Detects overlapping task schedules
- ✅ Shows conflict dialog
- ✅ Handles user conflict resolution

**Form Validation Integration**
- ✅ Validates time ranges
- ✅ Prevents invalid submissions
- ✅ Shows appropriate error messages

**Mobile Responsiveness Integration**
- ✅ Works on mobile viewports
- ✅ Handles touch interactions
- ✅ Maintains full functionality

## 🛠️ Test Configuration

### Vitest Setup (`vitest.config.js`)

```javascript
export default defineConfig({
  plugins: [react()],
  test: {
    globals: true,
    environment: 'jsdom',
    setupFiles: ['./src/test/setup.js'],
    css: true,
    coverage: {
      reporter: ['text', 'json', 'html'],
    },
  },
})
```

### Test Environment (`src/test/setup.js`)

**Mocked APIs:**
- ✅ HTML5 Drag and Drop API
- ✅ DataTransfer interface
- ✅ ResizeObserver
- ✅ scrollIntoView method

**Global Test Utilities:**
- ✅ createDragEvent helper function
- ✅ Jest DOM matchers
- ✅ Custom event creators

## 📊 Coverage Goals

| Component | Target Coverage | Current Status |
|-----------|----------------|----------------|
| Utility Functions | 100% | ✅ Complete |
| Main App Component | 90%+ | ✅ Complete |
| Calendar Views | 85%+ | ✅ Complete |
| Task Management | 90%+ | ✅ Complete |
| Team Management | 85%+ | ✅ Complete |
| Drag & Drop | 80%+ | ✅ Complete |
| Form Validation | 95%+ | ✅ Complete |

## 🐛 Testing Best Practices

### ✅ What We Test
- **User Interactions**: Click, drag, form submissions
- **State Management**: Component state changes
- **Data Flow**: Props and event handling
- **Edge Cases**: Invalid inputs, empty states
- **Accessibility**: ARIA labels, keyboard navigation
- **Responsive Design**: Mobile compatibility

### ❌ What We Don't Test
- **Implementation Details**: Internal component methods
- **Third-party Libraries**: External dependencies
- **Browser APIs**: Native browser functionality
- **Styling**: CSS-only visual changes

## 🚨 Running Specific Tests

```bash
# Run only utility tests
npm test utils

# Run only component tests
npm test App.test

# Run only integration tests
npm test integration

# Run tests matching pattern
npm test -- --grep "Task Creation"

# Run tests in specific file
npm test src/test/utils.test.js
```

## 📈 Continuous Integration

### GitHub Actions Example

```yaml
name: Tests
on: [push, pull_request]
jobs:
  test:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v3
      - uses: actions/setup-node@v3
        with:
          node-version: '18'
      - run: npm install
      - run: npm run test:run
      - run: npm run test:coverage
```

## 🔧 Debugging Tests

### Common Issues

1. **Modal Not Opening**: Check DOM structure and event handlers
2. **Drag & Drop Failing**: Verify DataTransfer mock setup
3. **Async Operations**: Use `waitFor` for async state changes
4. **Component Not Found**: Ensure proper test data setup

### Debug Commands

```bash
# Run tests with verbose output
npm test -- --reporter=verbose

# Run single test with debugging
npm test -- --grep "specific test name" --reporter=verbose

# Open test UI for interactive debugging
npm run test:ui
```

## 📝 Adding New Tests

### Test Template

```javascript
describe('New Feature', () => {
  beforeEach(() => {
    // Setup before each test
  })

  it('should do something specific', async () => {
    const user = userEvent.setup()
    render(<App />)
    
    // Test implementation
    expect(screen.getByText('Expected Text')).toBeInTheDocument()
  })
})
```

---

**🎯 Complete test coverage ensures reliable, maintainable code and confident deployments!**
