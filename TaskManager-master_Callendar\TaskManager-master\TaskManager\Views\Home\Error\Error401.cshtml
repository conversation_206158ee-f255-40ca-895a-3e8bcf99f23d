﻿@{
    ViewData["Title"] = "Error";
}
<div class="row">
    <div class="col-3"></div>
    <div class="col-6">
        <br>
        <div class="row">
            <div class="col-10">
                <img src="https://media.istockphoto.com/photos/rubber-stamp-pictureid545992942?k=6&m=545992942&s=612x612&w=0&h=XD1kddQeDrbG9DEQQxGNf6_3snw4gdFZv8GqSFta
                    6Is=" alt="Alternative image" class="img-thumbnail">
                <div class="text-left h4">
                    <p>Oops, you don't have access here.</p>
                </div>
                <div class="text-left h4"> Go back to <a asp-controller="Home" asp-action="Index">Home</a> Page</div>
            </div>
        </div>
    </div>
    <div class="col-3"></div>
</div>