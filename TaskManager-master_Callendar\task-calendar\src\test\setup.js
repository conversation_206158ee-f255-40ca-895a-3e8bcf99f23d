import '@testing-library/jest-dom'

// Mock HTML5 drag and drop API
Object.defineProperty(window, 'DataTransfer', {
  value: class DataTransfer {
    constructor() {
      this.data = {}
      this.effectAllowed = 'all'
      this.dropEffect = 'move'
    }
    
    setData(format, data) {
      this.data[format] = data
    }
    
    getData(format) {
      return this.data[format] || ''
    }
    
    clearData(format) {
      if (format) {
        delete this.data[format]
      } else {
        this.data = {}
      }
    }
  }
})

// Mock drag events
const createDragEvent = (type, dataTransfer = new DataTransfer()) => {
  const event = new Event(type, { bubbles: true, cancelable: true })
  event.dataTransfer = dataTransfer
  return event
}

global.createDragEvent = createDragEvent

// Mock ResizeObserver
global.ResizeObserver = class ResizeObserver {
  constructor(callback) {
    this.callback = callback
  }
  
  observe() {
    // Mock implementation
  }
  
  unobserve() {
    // Mock implementation
  }
  
  disconnect() {
    // Mock implementation
  }
}

// Mock scrollIntoView
Element.prototype.scrollIntoView = vi.fn()
