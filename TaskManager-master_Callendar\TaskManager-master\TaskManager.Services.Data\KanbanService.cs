namespace TaskManager.Services.Data
{
    using Microsoft.EntityFrameworkCore;
    using TaskManager.Data;
    using TaskManager.Data.Models;
    using TaskManager.Services.Data.Interfaces;
    using TaskManager.Web.ViewModels.Kanban;

    /// <summary>
    /// Service for managing Kanban board operations
    /// </summary>
    public class KanbanService : IKanbanService
    {
        private readonly TaskManagerDbContext dbContext;

        public KanbanService(TaskManagerDbContext dbContext)
        {
            this.dbContext = dbContext;
        }

        public async Task<KanbanBoardViewModel> GetTeamBoardAsync()
        {
            // Get or create the default team board
            var board = await GetOrCreateDefaultBoardAsync();

            var boardViewModel = new KanbanBoardViewModel
            {
                Id = board.Id.ToString(),
                Name = board.Name,
                Description = board.Description,
                CreatedOn = board.CreatedOn,
                Columns = await GetColumnsWithCardsAsync(board.Id)
            };

            return boardViewModel;
        }

        public async Task<KanbanBoardViewModel> GetMemberBoardAsync(string memberId)
        {
            // Get or create member-specific board
            var board = await GetOrCreateMemberBoardAsync(memberId);

            var boardViewModel = new KanbanBoardViewModel
            {
                Id = board.Id.ToString(),
                Name = board.Name,
                Description = board.Description,
                CreatedOn = board.CreatedOn,
                Columns = await GetColumnsWithCardsAsync(board.Id)
            };

            return boardViewModel;
        }

        public async Task<KanbanBoardViewModel> GetMemberBoardByUserIdAsync(string userId)
        {
            // Get the worker by UserId
            var worker = await dbContext.Workers
                .FirstOrDefaultAsync(w => w.UserId == Guid.Parse(userId));

            if (worker == null)
            {
                throw new ArgumentException("Worker not found for the specified user");
            }

            // Get or create member-specific board using WorkerId
            var board = await GetOrCreateMemberBoardAsync(worker.Id.ToString());

            var boardViewModel = new KanbanBoardViewModel
            {
                Id = board.Id.ToString(),
                Name = board.Name,
                Description = board.Description,
                CreatedOn = board.CreatedOn,
                Columns = await GetColumnsWithCardsAsync(board.Id)
            };

            return boardViewModel;
        }

        public async Task<IEnumerable<TeamMemberBoardViewModel>> GetAllMemberBoardsAsync()
        {
            var members = await dbContext.Workers
                .Include(w => w.User)
                .Where(w => w.User != null)
                .ToListAsync();

            var memberBoards = new List<TeamMemberBoardViewModel>();

            foreach (var member in members)
            {
                var board = await GetOrCreateMemberBoardAsync(member.Id.ToString());
                var totalCards = await dbContext.KanbanCards
                    .Where(c => c.Column.BoardId == board.Id && c.IsActive)
                    .CountAsync();

                var inProgressCards = await dbContext.KanbanCards
                    .Where(c => c.Column.BoardId == board.Id && c.IsActive &&
                               (c.Column.Name == "В процес на работа" || c.Column.Name == "Текущи"))
                    .CountAsync();

                var completedCards = await dbContext.KanbanCards
                    .Where(c => c.Column.BoardId == board.Id && c.IsActive && c.Column.Name == "Приключени")
                    .CountAsync();

                var lastActivity = await dbContext.KanbanCards
                    .Where(c => c.Column.BoardId == board.Id && c.IsActive)
                    .MaxAsync(c => (DateTime?)c.UpdatedOn) ?? board.CreatedOn;

                memberBoards.Add(new TeamMemberBoardViewModel
                {
                    MemberId = member.Id.ToString(),
                    MemberName = $"{member.User.FirstName} {member.User.LastName}",
                    MemberPosition = member.Position,
                    BoardId = board.Id.ToString(),
                    BoardName = board.Name,
                    TotalCards = totalCards,
                    InProgressCards = inProgressCards,
                    CompletedCards = completedCards,
                    LastActivity = lastActivity
                });
            }

            return memberBoards;
        }

        public async Task<string> CreateCardAsync(CreateKanbanCardViewModel model)
        {
            // If a worker is assigned, create the card on their personal board
            if (!string.IsNullOrEmpty(model.AssignedToId))
            {
                return await CreateAssignedCardAsync(model);
            }

            // Otherwise, create on the specified column (team board)
            var column = await dbContext.KanbanColumns.FindAsync(Guid.Parse(model.ColumnId));
            if (column == null)
            {
                throw new ArgumentException("Invalid column ID");
            }

            // Get the next position in the column
            var maxPosition = await dbContext.KanbanCards
                .Where(c => c.ColumnId == column.Id && c.IsActive)
                .MaxAsync(c => (int?)c.Position) ?? 0;

            var card = new KanbanCard
            {
                Title = model.Title,
                Description = model.Description,
                ColumnId = column.Id,
                Position = maxPosition + 1,
                Color = model.Color,
                Labels = model.Labels,
                DueDate = model.DueDate,
                AssignedToId = null, // No assignment for team board cards
                GeoTaskId = string.IsNullOrEmpty(model.GeoTaskId) ? null : Guid.Parse(model.GeoTaskId),
                CreatedById = null // TODO: Get from current user context
            };

            dbContext.KanbanCards.Add(card);
            await dbContext.SaveChangesAsync();

            return card.Id.ToString();
        }

        private async Task<string> CreateAssignedCardAsync(CreateKanbanCardViewModel model)
        {
            // Get the worker by UserId to find their WorkerId
            if (string.IsNullOrEmpty(model.AssignedToId))
            {
                throw new ArgumentException("AssignedToId cannot be null or empty");
            }

            if (!Guid.TryParse(model.AssignedToId, out var userIdGuid))
            {
                throw new ArgumentException($"Invalid AssignedToId format: {model.AssignedToId}");
            }

            var worker = await dbContext.Workers
                .FirstOrDefaultAsync(w => w.UserId == userIdGuid);

            if (worker == null)
            {
                throw new ArgumentException($"Worker not found for the assigned user: {model.AssignedToId}");
            }

            // Get or create the worker's personal board
            var memberBoard = await GetOrCreateMemberBoardAsync(worker.Id.ToString());

            // Use the specified column if it exists on their board, otherwise use default "Текущи"
            var targetColumn = await dbContext.KanbanColumns
                .FirstOrDefaultAsync(c => c.BoardId == memberBoard.Id &&
                                         c.Id == Guid.Parse(model.ColumnId) &&
                                         c.IsActive);

            if (targetColumn == null)
            {
                // Default to "Текущи" column on their personal board
                targetColumn = await dbContext.KanbanColumns
                    .FirstAsync(c => c.BoardId == memberBoard.Id && c.Name == "Текущи" && c.IsActive);
            }

            // Get the next position in the column
            var maxPosition = await dbContext.KanbanCards
                .Where(c => c.ColumnId == targetColumn.Id && c.IsActive)
                .MaxAsync(c => (int?)c.Position) ?? 0;

            var card = new KanbanCard
            {
                Title = model.Title,
                Description = model.Description,
                ColumnId = targetColumn.Id,
                Position = maxPosition + 1,
                Color = model.Color,
                Labels = model.Labels,
                DueDate = model.DueDate,
                AssignedToId = Guid.Parse(model.AssignedToId),
                GeoTaskId = string.IsNullOrEmpty(model.GeoTaskId) ? null : Guid.Parse(model.GeoTaskId),
                CreatedById = null // TODO: Get from current user context
            };

            dbContext.KanbanCards.Add(card);
            await dbContext.SaveChangesAsync();

            return card.Id.ToString();
        }

        public async Task UpdateCardAsync(UpdateKanbanCardViewModel model)
        {
            var card = await dbContext.KanbanCards.FindAsync(Guid.Parse(model.Id));
            if (card == null)
            {
                throw new ArgumentException("Card not found");
            }

            // Store original values for comparison
            var originalAssignedToId = card.AssignedToId;

            // Update basic fields
            card.Title = model.Title;
            card.Description = model.Description;
            card.Color = model.Color;
            card.Labels = model.Labels;
            card.DueDate = model.DueDate;
            card.UpdatedOn = DateTime.UtcNow;

            // Note: Start/End times come from connected GeoTask, not stored on card

            // Handle worker reassignment
            var newAssignedToId = string.IsNullOrEmpty(model.AssignedToId) ? (Guid?)null : Guid.Parse(model.AssignedToId);

            // If worker is being changed and target column is specified
            if (originalAssignedToId != newAssignedToId && !string.IsNullOrEmpty(model.TargetColumnId))
            {
                // Move card to the target worker's board and column
                var targetColumn = await dbContext.KanbanColumns.FindAsync(Guid.Parse(model.TargetColumnId));
                if (targetColumn != null)
                {
                    card.ColumnId = targetColumn.Id;
                    card.AssignedToId = newAssignedToId;

                    // Set position to end of target column
                    var maxPosition = await dbContext.KanbanCards
                        .Where(c => c.ColumnId == targetColumn.Id && c.IsActive)
                        .MaxAsync(c => (int?)c.Position) ?? 0;
                    card.Position = maxPosition + 1;
                }
            }
            else
            {
                // Just update the assigned worker without moving columns
                card.AssignedToId = newAssignedToId;
            }

            await dbContext.SaveChangesAsync();
        }

        public async Task MoveCardAsync(string cardId, string newColumnId, int newPosition)
        {
            var card = await dbContext.KanbanCards.FindAsync(Guid.Parse(cardId));
            if (card == null)
            {
                throw new ArgumentException("Card not found");
            }

            var newColumn = await dbContext.KanbanColumns.FindAsync(Guid.Parse(newColumnId));
            if (newColumn == null)
            {
                throw new ArgumentException("Column not found");
            }

            var oldColumnId = card.ColumnId;
            var oldPosition = card.Position;

            // Update card position and column
            card.ColumnId = newColumn.Id;
            card.Position = newPosition;
            card.UpdatedOn = DateTime.UtcNow;

            // Reorder cards in old column
            if (oldColumnId != newColumn.Id)
            {
                var cardsInOldColumn = await dbContext.KanbanCards
                    .Where(c => c.ColumnId == oldColumnId && c.Position > oldPosition && c.IsActive)
                    .ToListAsync();

                foreach (var c in cardsInOldColumn)
                {
                    c.Position--;
                }
            }

            // Reorder cards in new column
            var cardsInNewColumn = await dbContext.KanbanCards
                .Where(c => c.ColumnId == newColumn.Id && c.Position >= newPosition && c.Id != card.Id && c.IsActive)
                .ToListAsync();

            foreach (var c in cardsInNewColumn)
            {
                c.Position++;
            }

            await dbContext.SaveChangesAsync();
        }

        public async Task DeleteCardAsync(string cardId)
        {
            var card = await dbContext.KanbanCards.FindAsync(Guid.Parse(cardId));
            if (card == null)
            {
                throw new ArgumentException("Card not found");
            }

            card.IsActive = false;
            card.UpdatedOn = DateTime.UtcNow;

            // Reorder remaining cards in the column
            var cardsToReorder = await dbContext.KanbanCards
                .Where(c => c.ColumnId == card.ColumnId && c.Position > card.Position && c.IsActive)
                .ToListAsync();

            foreach (var c in cardsToReorder)
            {
                c.Position--;
            }

            await dbContext.SaveChangesAsync();
        }

        public async Task CreateCardForGeoTaskAsync(string geoTaskId, string title, string? assignedToId = null)
        {
            // Check if card already exists for this GeoTask
            var existingCard = await dbContext.KanbanCards
                .FirstOrDefaultAsync(c => c.GeoTaskId == Guid.Parse(geoTaskId) && c.IsActive);

            if (existingCard != null)
            {
                return; // Card already exists
            }

            // Get the GeoTask to find the assigned worker
            var geoTask = await dbContext.GeoTasks
                .Include(gt => gt.Worker)
                .FirstOrDefaultAsync(gt => gt.Id.ToString() == geoTaskId);

            if (geoTask == null)
            {
                return; // GeoTask not found
            }

            // Use the assigned worker from GeoTask if not provided
            var workerId = geoTask.WorkerId.ToString(); // Always use the WorkerId from GeoTask
            var userIdForAssignment = assignedToId ?? geoTask.Worker.UserId.ToString();

            // Get the member's board and default column
            var memberBoard = await GetOrCreateMemberBoardAsync(workerId);
            var defaultColumn = await dbContext.KanbanColumns
                .FirstAsync(c => c.BoardId == memberBoard.Id && c.Name == "Текущи" && c.IsActive);

            var createModel = new CreateKanbanCardViewModel
            {
                Title = title,
                ColumnId = defaultColumn.Id.ToString(),
                AssignedToId = userIdForAssignment,
                GeoTaskId = geoTaskId
            };

            await CreateCardAsync(createModel);
        }

        public async Task UpdateCardForGeoTaskAsync(string geoTaskId, string title, string? assignedToId = null)
        {
            var card = await dbContext.KanbanCards
                .FirstOrDefaultAsync(c => c.GeoTaskId == Guid.Parse(geoTaskId) && c.IsActive);

            if (card == null)
            {
                // Create card if it doesn't exist
                await CreateCardForGeoTaskAsync(geoTaskId, title, assignedToId);
                return;
            }

            var updateModel = new UpdateKanbanCardViewModel
            {
                Id = card.Id.ToString(),
                Title = title,
                Description = card.Description,
                Color = card.Color,
                Labels = card.Labels,
                DueDate = card.DueDate,
                AssignedToId = assignedToId,
                ColumnId = card.ColumnId.ToString()
            };

            await UpdateCardAsync(updateModel);
        }

        public async Task<KanbanCardViewModel?> GetCardByIdAsync(string cardId)
        {
            var card = await dbContext.KanbanCards
                .Include(c => c.AssignedTo)
                .Include(c => c.CreatedBy)
                .Include(c => c.GeoTask)
                .FirstOrDefaultAsync(c => c.Id == Guid.Parse(cardId) && c.IsActive);

            if (card == null)
            {
                return null;
            }

            return MapToCardViewModel(card);
        }

        public async Task InitializeDefaultBoardAsync()
        {
            if (await DefaultBoardExistsAsync())
            {
                return;
            }

            await CreateDefaultBoardAsync();
        }

        public async Task<bool> DefaultBoardExistsAsync()
        {
            return await dbContext.KanbanBoards
                .AnyAsync(b => b.Name == "Екипна дъска" && b.IsActive);
        }

        public async Task<List<GeoTask>> GetGeoTasksWithoutCardsAsync()
        {
            return await dbContext.GeoTasks
                .Where(gt => !dbContext.KanbanCards.Any(kc => kc.GeoTaskId == gt.Id && kc.IsActive))
                .ToListAsync();
        }

        public async Task<List<KanbanColumnViewModel>> GetWorkerBoardColumnsAsync(string userId)
        {
            // Get the worker by UserId
            var worker = await dbContext.Workers
                .FirstOrDefaultAsync(w => w.UserId == Guid.Parse(userId));

            if (worker == null)
            {
                throw new ArgumentException("Worker not found for the specified user");
            }

            // Get or create the worker's personal board
            var memberBoard = await GetOrCreateMemberBoardAsync(worker.Id.ToString());

            // Get all columns for this board
            var columns = await dbContext.KanbanColumns
                .Where(c => c.BoardId == memberBoard.Id && c.IsActive)
                .OrderBy(c => c.Position)
                .ToListAsync();

            // Convert to view models with fallback names
            var columnViewModels = columns.Select(c => new KanbanColumnViewModel
            {
                Id = c.Id.ToString(),
                Name = !string.IsNullOrEmpty(c.Name) ? c.Name : GetDefaultColumnName(c.Position),
                Description = c.Description,
                Position = c.Position,
                Color = c.Color
            }).ToList();

            return columnViewModels;
        }

        private string GetDefaultColumnName(int position)
        {
            return position switch
            {
                1 => "Текущи",
                2 => "Чакащи",
                3 => "В процес на работа",
                4 => "За корекция",
                5 => "За проверка",
                6 => "Приключени",
                _ => $"Колона {position}"
            };
        }

        public async Task<object> GetCardDetailsAsync(string cardId)
        {
            var card = await dbContext.KanbanCards
                .Include(c => c.AssignedTo)
                .Include(c => c.GeoTask)
                    .ThenInclude(gt => gt.Client)
                .FirstOrDefaultAsync(c => c.Id == Guid.Parse(cardId));

            if (card == null)
            {
                throw new ArgumentException("Card not found");
            }

            // Use GeoTask times if available, otherwise use card DueDate as fallback
            var startTime = card.GeoTask?.CreateDate;
            var endTime = card.GeoTask?.EndDate;

            return new
            {
                Id = card.Id.ToString(),
                Title = card.Title,
                Description = card.Description,
                ColumnId = card.ColumnId.ToString(),
                AssignedToId = card.AssignedToId?.ToString(),
                StartTime = startTime?.ToString("yyyy-MM-ddTHH:mm"),
                EndTime = endTime?.ToString("yyyy-MM-ddTHH:mm"),
                DueDate = card.DueDate?.ToString("yyyy-MM-ddTHH:mm"),
                Color = card.Color,
                Labels = card.Labels,
                GeoTaskId = card.GeoTaskId?.ToString(),
                GeoTaskTitle = card.GeoTask != null ? $"Проект #{card.GeoTask.ProjectNumber}" : null,
                ClientName = card.GeoTask?.Client?.Name,
                Address = card.GeoTask?.Adrress, // Note: property name has typo in entity
                HasGeoTask = card.GeoTaskId.HasValue
            };
        }

        public async Task<List<object>> GetCardCommentsAsync(string cardId)
        {
            var card = await dbContext.KanbanCards
                .Include(c => c.GeoTask)
                .FirstOrDefaultAsync(c => c.Id == Guid.Parse(cardId));

            if (card == null)
            {
                throw new ArgumentException("Card not found");
            }

            // If card is connected to a GeoTask, get comments from GeoTask
            if (card.GeoTaskId.HasValue)
            {
                var geoTaskComments = await dbContext.Comentars
                    .Include(c => c.Worker)
                        .ThenInclude(w => w.User)
                    .Where(c => c.TaskId == card.GeoTaskId.Value)
                    .OrderBy(c => c.CreateDate)
                    .Select(c => new
                    {
                        Id = c.Id,
                        Content = c.Description,
                        AuthorName = $"{c.Worker.User.FirstName} {c.Worker.User.LastName}",
                        CreatedOn = c.CreateDate.ToString("dd.MM.yyyy HH:mm")
                    })
                    .ToListAsync();

                return geoTaskComments.Cast<object>().ToList();
            }

            // For now, return empty list for cards without GeoTask
            // TODO: Implement card-specific comments if needed
            return new List<object>();
        }

        public async Task AddCardCommentAsync(string cardId, string comment, string userEmail)
        {
            var card = await dbContext.KanbanCards
                .Include(c => c.GeoTask)
                .FirstOrDefaultAsync(c => c.Id == Guid.Parse(cardId));

            if (card == null)
            {
                throw new ArgumentException("Card not found");
            }

            // If card is connected to a GeoTask, add comment to GeoTask
            if (card.GeoTaskId.HasValue)
            {
                // Find the worker by email
                var worker = await dbContext.Workers
                    .Include(w => w.User)
                    .FirstOrDefaultAsync(w => w.User.Email == userEmail);

                if (worker == null)
                {
                    throw new ArgumentException("Worker not found for the specified email");
                }

                var newComment = new Comentar
                {
                    Description = comment,
                    WorkerId = worker.Id,
                    TaskId = card.GeoTaskId.Value,
                    CreateDate = DateTime.Now
                };

                dbContext.Comentars.Add(newComment);
                await dbContext.SaveChangesAsync();
            }
            else
            {
                // TODO: Implement card-specific comments if needed
                throw new InvalidOperationException("Cannot add comments to cards not connected to GeoTask");
            }
        }

        private async Task<KanbanBoard> GetOrCreateDefaultBoardAsync()
        {
            var board = await dbContext.KanbanBoards
                .Include(b => b.Columns)
                .FirstOrDefaultAsync(b => b.Name == "Екипна дъска" && b.IsActive);

            if (board == null)
            {
                board = await CreateDefaultBoardAsync();
            }

            return board;
        }

        private async Task<KanbanBoard> GetOrCreateMemberBoardAsync(string memberId)
        {
            var member = await dbContext.Workers
                .Include(w => w.User)
                .FirstOrDefaultAsync(w => w.Id.ToString() == memberId);

            if (member == null)
            {
                throw new ArgumentException("Member not found");
            }

            var boardName = $"{member.User.FirstName} {member.User.LastName} - Лична дъска";

            var board = await dbContext.KanbanBoards
                .Include(b => b.Columns)
                .FirstOrDefaultAsync(b => b.Name == boardName && b.IsActive);

            if (board == null)
            {
                board = await CreateMemberBoardAsync(boardName, memberId);
            }

            return board;
        }

        private async Task<KanbanBoard> CreateDefaultBoardAsync()
        {
            var board = new KanbanBoard
            {
                Name = "Екипна дъска",
                Description = "Основна дъска за управление на задачи на екипа"
            };

            dbContext.KanbanBoards.Add(board);
            await dbContext.SaveChangesAsync();

            // Create default columns
            var defaultColumns = new[]
            {
                new { Name = "Текущи", Position = 1, Color = "#3b82f6" },
                new { Name = "Чакащи", Position = 2, Color = "#f59e0b" },
                new { Name = "В процес на работа", Position = 3, Color = "#10b981" },
                new { Name = "За корекция", Position = 4, Color = "#ef4444" },
                new { Name = "За проверка", Position = 5, Color = "#8b5cf6" },
                new { Name = "Приключени", Position = 6, Color = "#6b7280" }
            };

            foreach (var col in defaultColumns)
            {
                var column = new KanbanColumn
                {
                    Name = col.Name,
                    Position = col.Position,
                    Color = col.Color,
                    BoardId = board.Id
                };

                dbContext.KanbanColumns.Add(column);
            }

            await dbContext.SaveChangesAsync();
            return board;
        }

        private async Task<KanbanBoard> CreateMemberBoardAsync(string boardName, string memberId)
        {
            var board = new KanbanBoard
            {
                Name = boardName,
                Description = $"Лична дъска за управление на задачи"
            };

            dbContext.KanbanBoards.Add(board);
            await dbContext.SaveChangesAsync();

            // Create default columns for member board
            var defaultColumns = new[]
            {
                new { Name = "Текущи", Position = 1, Color = "#3b82f6" },
                new { Name = "Чакащи", Position = 2, Color = "#f59e0b" },
                new { Name = "В процес на работа", Position = 3, Color = "#10b981" },
                new { Name = "За корекция", Position = 4, Color = "#ef4444" },
                new { Name = "За проверка", Position = 5, Color = "#8b5cf6" },
                new { Name = "Приключени", Position = 6, Color = "#6b7280" }
            };

            foreach (var col in defaultColumns)
            {
                var column = new KanbanColumn
                {
                    Name = col.Name,
                    Position = col.Position,
                    Color = col.Color,
                    BoardId = board.Id
                };

                dbContext.KanbanColumns.Add(column);
            }

            await dbContext.SaveChangesAsync();
            return board;
        }

        private async Task<KanbanColumn> GetDefaultColumnAsync()
        {
            var board = await GetOrCreateDefaultBoardAsync();
            return await dbContext.KanbanColumns
                .FirstAsync(c => c.BoardId == board.Id && c.Name == "Текущи" && c.IsActive);
        }

        private async Task<List<KanbanColumnViewModel>> GetColumnsWithCardsAsync(Guid boardId)
        {
            var columns = await dbContext.KanbanColumns
                .Where(c => c.BoardId == boardId && c.IsActive)
                .OrderBy(c => c.Position)
                .Include(c => c.Cards.Where(card => card.IsActive))
                    .ThenInclude(card => card.AssignedTo)
                .Include(c => c.Cards.Where(card => card.IsActive))
                    .ThenInclude(card => card.GeoTask)
                .ToListAsync();

            return columns.Select(c => new KanbanColumnViewModel
            {
                Id = c.Id.ToString(),
                Name = c.Name,
                Description = c.Description,
                Position = c.Position,
                Color = c.Color,
                Cards = c.Cards
                    .Where(card => card.IsActive)
                    .OrderBy(card => card.Position)
                    .Select(MapToCardViewModel)
                    .ToList()
            }).ToList();
        }

        private static KanbanCardViewModel MapToCardViewModel(KanbanCard card)
        {
            return new KanbanCardViewModel
            {
                Id = card.Id.ToString(),
                Title = card.Title,
                Description = card.Description,
                Position = card.Position,
                Color = card.Color,
                Labels = card.Labels,
                DueDate = card.DueDate,
                CreatedOn = card.CreatedOn,
                AssignedToId = card.AssignedToId?.ToString(),
                AssignedToName = card.AssignedTo != null ? $"{card.AssignedTo.FirstName} {card.AssignedTo.LastName}" : null,
                CreatedById = card.CreatedById?.ToString(),
                CreatedByName = card.CreatedBy != null ? $"{card.CreatedBy.FirstName} {card.CreatedBy.LastName}" : null,
                GeoTaskId = card.GeoTaskId?.ToString(),
                GeoTaskProjectNumber = card.GeoTask?.ProjectNumber,
                ColumnId = card.ColumnId.ToString()
            };
        }
    }
}
