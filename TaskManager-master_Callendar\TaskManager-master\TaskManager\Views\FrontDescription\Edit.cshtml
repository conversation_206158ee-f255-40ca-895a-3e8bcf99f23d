﻿@using static TaskManager.Common.NotificationMessages;
@model FrontDescriptionTypeViewModel

 <div class="row">
   <div class="col-sm-12 offset-lg-4 col-lg-8 offset-xl-3 col-xl-6">
      <form method="post">
         <div class="form-group">
            <label asp-for="Title"></label>
            <input asp-for="Title" class="form-control" placeholder="Име...">
            <span asp-validation-for="Title" class="small text-danger"></span>
         </div>
         <div class="form-group">
            <label asp-for="Description"></label>
                <textarea asp-for="Description" rows="4" class="form-control" placeholder="Описание на задачата..."></textarea>
                <span asp-validation-for="Description" class="text-danger"></span>
         </div>
         <div class="form-group">
            <label asp-for="ImageUrl"></label>
            <input asp-for="ImageUrl" class="form-control"
               placeholder="www.freepik.com...">
            <span asp-validation-for="ImageUrl" class="small text-danger"></span>
         </div>
         <div class="form-group">
            <label asp-for="TermDays"></label>
            <input asp-for="TermDays" class="form-control"
               placeholder="3 работни дни...">
            <span asp-validation-for="TermDays" class="small text-danger"></span>
         </div>
         <div class="form-group">
            <label asp-for="Price"></label>
            <input asp-for="Price" class="form-control"
               placeholder="500 лв.">
            <span asp-validation-for="Price" class="small text-danger"></span>
         </div>
         <div class="text-center">
            <input class="btn btn-primary mt-3" type="submit" value="Запиши" />
         </div>
      </form>
   </div>
</div>

@section Scripts
    {
                    <partial name ="_ValidationScriptsPartial">
}
