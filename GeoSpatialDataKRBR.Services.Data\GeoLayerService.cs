namespace GeoSpatialDataKRBR.Services.Data
{
    using Microsoft.EntityFrameworkCore;
    using GeoSpatialDataKRBR.Data;
    using GeoSpatialDataKRBR.Data.Models;
    using GeoSpatialDataKRBR.Services.Data.Interfaces;

    public class GeoLayerService : IGeoLayerService
    {
        private readonly GeoSpatialDbContext dbContext;

        public GeoLayerService(GeoSpatialDbContext dbContext)
        {
            this.dbContext = dbContext;
        }

        public async Task<IEnumerable<GeoLayer>> GetAllLayersAsync()
        {
            return await this.dbContext.GeoLayers
                .Include(gl => gl.GeoServerConfiguration)
                .OrderBy(gl => gl.DisplayOrder)
                .ThenBy(gl => gl.Name)
                .ToListAsync();
        }

        public async Task<IEnumerable<GeoLayer>> GetVisibleLayersAsync()
        {
            return await this.dbContext.GeoLayers
                .Include(gl => gl.GeoServerConfiguration)
                .Where(gl => gl.IsVisible)
                .OrderBy(gl => gl.DisplayOrder)
                .ThenBy(gl => gl.Name)
                .ToListAsync();
        }

        public async Task<GeoLayer?> GetLayerByIdAsync(Guid id)
        {
            return await this.dbContext.GeoLayers
                .Include(gl => gl.GeoServerConfiguration)
                .FirstOrDefaultAsync(gl => gl.Id == id);
        }

        public async Task<GeoLayer?> GetLayerByNameAsync(string layerName, string workspace)
        {
            return await this.dbContext.GeoLayers
                .Include(gl => gl.GeoServerConfiguration)
                .FirstOrDefaultAsync(gl => gl.LayerName == layerName && gl.Workspace == workspace);
        }

        public async Task<bool> CreateLayerAsync(GeoLayer layer)
        {
            try
            {
                await this.dbContext.GeoLayers.AddAsync(layer);
                int result = await this.dbContext.SaveChangesAsync();
                return result > 0;
            }
            catch
            {
                return false;
            }
        }

        public async Task<bool> UpdateLayerAsync(GeoLayer layer)
        {
            try
            {
                layer.ModifiedOn = DateTime.UtcNow;
                this.dbContext.GeoLayers.Update(layer);
                int result = await this.dbContext.SaveChangesAsync();
                return result > 0;
            }
            catch
            {
                return false;
            }
        }

        public async Task<bool> DeleteLayerAsync(Guid id)
        {
            try
            {
                var layer = await this.dbContext.GeoLayers.FindAsync(id);
                if (layer == null)
                {
                    return false;
                }

                this.dbContext.GeoLayers.Remove(layer);
                int result = await this.dbContext.SaveChangesAsync();
                return result > 0;
            }
            catch
            {
                return false;
            }
        }

        public async Task<bool> ToggleLayerVisibilityAsync(Guid id)
        {
            try
            {
                var layer = await this.dbContext.GeoLayers.FindAsync(id);
                if (layer == null)
                {
                    return false;
                }

                layer.IsVisible = !layer.IsVisible;
                layer.ModifiedOn = DateTime.UtcNow;
                
                int result = await this.dbContext.SaveChangesAsync();
                return result > 0;
            }
            catch
            {
                return false;
            }
        }

        public async Task<IEnumerable<GeoLayer>> GetLayersByUserPreferencesAsync(Guid userId)
        {
            return await this.dbContext.GeoLayers
                .Include(gl => gl.GeoServerConfiguration)
                .Include(gl => gl.UserLayerPreferences.Where(ulp => ulp.UserId == userId))
                .Where(gl => gl.IsVisible)
                .OrderBy(gl => gl.UserLayerPreferences.FirstOrDefault(ulp => ulp.UserId == userId) != null 
                    ? gl.UserLayerPreferences.FirstOrDefault(ulp => ulp.UserId == userId)!.DisplayOrder 
                    : gl.DisplayOrder)
                .ThenBy(gl => gl.Name)
                .ToListAsync();
        }

        public async Task<bool> UpdateLayerOrderAsync(Guid id, int newOrder)
        {
            try
            {
                var layer = await this.dbContext.GeoLayers.FindAsync(id);
                if (layer == null)
                {
                    return false;
                }

                layer.DisplayOrder = newOrder;
                layer.ModifiedOn = DateTime.UtcNow;
                
                int result = await this.dbContext.SaveChangesAsync();
                return result > 0;
            }
            catch
            {
                return false;
            }
        }

        public async Task<bool> LayerExistsAsync(string layerName, string workspace)
        {
            return await this.dbContext.GeoLayers
                .AnyAsync(gl => gl.LayerName == layerName && gl.Workspace == workspace);
        }
    }
}
