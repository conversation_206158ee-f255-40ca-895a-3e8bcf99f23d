﻿@{
    ViewData["Title"] = "Календар за Задачи";
}

<link rel="stylesheet" href="~/css/Calendar.css" />

<div class="modern-container">
    <div class="modern-page-header">
        <h1 class="modern-page-title">
            <i class="fas fa-calendar-alt"></i>
            Календар за Задачи
        </h1>
        <p class="modern-page-subtitle">
            Управление и планиране на задачи в календарен изглед
        </p>
    </div>

    <div class="modern-card">
        <div class="modern-card-header">
            <h3>
                <i class="fas fa-calendar-check"></i>
                Календар с Задачи
            </h3>
        </div>
        <div class="modern-card-body">
            @Html.AntiForgeryToken()
            <div id="react-calendar-root" class="calendar-container"></div>
        </div>
    </div>
</div>



<!-- React and Babel for development -->
<script crossorigin src="https://unpkg.com/react@18/umd/react.development.js"></script>
<script crossorigin src="https://unpkg.com/react-dom@18/umd/react-dom.development.js"></script>
<script src="https://unpkg.com/@@babel/standalone/babel.min.js"></script>



<!-- Calendar Component -->
<script>


  // Load and transform the calendar app script
  fetch('/js/calendar-app.js')
    .then(response => response.text())
    .then(code => {
      const transformedCode = Babel.transform(code, { presets: ['react'] }).code;
      const script = document.createElement('script');
      script.textContent = transformedCode;
      document.head.appendChild(script);
    })
    .catch(error => {
      console.error('Error loading calendar app:', error);
      // Fallback: show a simple message
      document.getElementById('react-calendar-root').innerHTML =
        '<div style="padding: 2rem; text-align: center; color: #666;">Календарът се зарежда...</div>';
    });
</script>
