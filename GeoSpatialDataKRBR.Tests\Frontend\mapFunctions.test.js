// Frontend unit tests for map functionality

describe('Map Layer Functions', () => {
    let mockMapLayers;
    let mockMap;

    beforeEach(() => {
        // Reset mocks
        jest.clearAllMocks();
        
        // Setup mock objects
        mockMapLayers = {};
        mockMap = {
            addLayer: jest.fn(),
            removeLayer: jest.fn(),
            hasLayer: jest.fn().mockReturnValue(false)
        };

        // Setup global variables that would be available in the browser
        global.mapLayers = mockMapLayers;
        global.map = mockMap;

        // Mock fetch responses
        global.fetch.mockResolvedValue({
            ok: true,
            status: 200,
            json: () => Promise.resolve({ success: true })
        });
    });

    describe('toggleLayerVisibility', () => {
        const toggleLayerVisibility = (layerId) => {
            console.log('Toggling layer visibility for:', layerId);
            console.log('Available layers in mapLayers:', Object.keys(mapLayers));
            
            const checkbox = document.querySelector(`input[data-layer-id="${layerId}"]`);
            if (!checkbox) {
                console.error('Checkbox not found for layer:', layerId);
                return;
            }
            
            const isChecked = checkbox.checked;
            console.log('Checkbox checked:', isChecked);

            // Send request to server
            return fetch('/api/geolayerapi/toggle-visibility', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({
                    layerId: layerId,
                    isVisible: isChecked
                })
            })
            .then(response => {
                console.log('Response status:', response.status);
                if (response.ok) {
                    // Toggle layer on map immediately
                    const layer = mapLayers[layerId];
                    console.log('Found layer in mapLayers:', layer ? 'YES' : 'NO');
                    if (layer) {
                        if (isChecked) {
                            layer.addTo(map);
                            console.log('Layer added to map:', layerId);
                        } else {
                            map.removeLayer(layer);
                            console.log('Layer removed from map:', layerId);
                        }
                    } else {
                        console.error('Layer not found in mapLayers for ID:', layerId);
                        console.log('Available layer IDs:', Object.keys(mapLayers));
                    }
                } else {
                    console.error('Failed to toggle layer visibility');
                    checkbox.checked = !isChecked;
                }
            })
            .catch(error => {
                console.error('Error:', error);
                checkbox.checked = !isChecked;
            });
        };

        test('should toggle layer visibility successfully', async () => {
            // Arrange
            const layerId = 'test-layer-id';
            const mockLayer = {
                addTo: jest.fn(),
                removeLayer: jest.fn()
            };
            
            mockMapLayers[layerId] = mockLayer;
            
            // Create mock checkbox
            document.body.innerHTML = `
                <input type="checkbox" data-layer-id="${layerId}" checked />
            `;

            // Act
            await toggleLayerVisibility(layerId);

            // Assert
            expect(fetch).toHaveBeenCalledWith('/api/geolayerapi/toggle-visibility', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({
                    layerId: layerId,
                    isVisible: true
                })
            });

            expect(mockLayer.addTo).toHaveBeenCalledWith(mockMap);
            expect(console.log).toHaveBeenCalledWith('Layer added to map:', layerId);
        });

        test('should handle layer not found in mapLayers', async () => {
            // Arrange
            const layerId = 'non-existent-layer';
            
            document.body.innerHTML = `
                <input type="checkbox" data-layer-id="${layerId}" checked />
            `;

            // Act
            await toggleLayerVisibility(layerId);

            // Assert
            expect(console.error).toHaveBeenCalledWith('Layer not found in mapLayers for ID:', layerId);
            expect(console.log).toHaveBeenCalledWith('Available layer IDs:', []);
        });

        test('should handle checkbox not found', async () => {
            // Arrange
            const layerId = 'test-layer-id';

            // Act
            await toggleLayerVisibility(layerId);

            // Assert
            expect(console.error).toHaveBeenCalledWith('Checkbox not found for layer:', layerId);
            expect(fetch).not.toHaveBeenCalled();
        });

        test('should handle server error', async () => {
            // Arrange
            const layerId = 'test-layer-id';
            
            global.fetch.mockResolvedValue({
                ok: false,
                status: 500
            });
            
            document.body.innerHTML = `
                <input type="checkbox" data-layer-id="${layerId}" checked />
            `;

            // Act
            await toggleLayerVisibility(layerId);

            // Assert
            expect(console.error).toHaveBeenCalledWith('Failed to toggle layer visibility');
            
            const checkbox = document.querySelector(`input[data-layer-id="${layerId}"]`);
            expect(checkbox.checked).toBe(false); // Should be reverted
        });

        test('should handle network error', async () => {
            // Arrange
            const layerId = 'test-layer-id';
            const networkError = new Error('Network error');
            
            global.fetch.mockRejectedValue(networkError);
            
            document.body.innerHTML = `
                <input type="checkbox" data-layer-id="${layerId}" checked />
            `;

            // Act
            await toggleLayerVisibility(layerId);

            // Assert
            expect(console.error).toHaveBeenCalledWith('Error:', networkError);
            
            const checkbox = document.querySelector(`input[data-layer-id="${layerId}"]`);
            expect(checkbox.checked).toBe(false); // Should be reverted
        });
    });

    describe('toggleBaseLayer', () => {
        const toggleBaseLayer = (layerId) => {
            console.log('Selected base layer:', layerId);
            
            // Hide all base layers first
            const baseLayers = ['base-layer-1', 'base-layer-2', 'base-layer-3'];
            baseLayers.forEach(baseLayerId => {
                const baseLayer = mapLayers[baseLayerId];
                if (baseLayer && map.hasLayer(baseLayer)) {
                    map.removeLayer(baseLayer);
                }
            });
            
            // Show selected base layer
            const selectedLayer = mapLayers[layerId];
            if (selectedLayer) {
                selectedLayer.addTo(map);
                console.log('Base layer switched to:', layerId);
            } else {
                console.error('Base layer not found:', layerId);
            }
        };

        test('should switch base layer successfully', () => {
            // Arrange
            const selectedLayerId = 'base-layer-2';
            const mockBaseLayers = {
                'base-layer-1': { addTo: jest.fn() },
                'base-layer-2': { addTo: jest.fn() },
                'base-layer-3': { addTo: jest.fn() }
            };
            
            Object.assign(mockMapLayers, mockBaseLayers);
            mockMap.hasLayer.mockReturnValue(true);

            // Act
            toggleBaseLayer(selectedLayerId);

            // Assert
            expect(mockMap.removeLayer).toHaveBeenCalledTimes(3);
            expect(mockBaseLayers[selectedLayerId].addTo).toHaveBeenCalledWith(mockMap);
            expect(console.log).toHaveBeenCalledWith('Base layer switched to:', selectedLayerId);
        });

        test('should handle base layer not found', () => {
            // Arrange
            const selectedLayerId = 'non-existent-base-layer';

            // Act
            toggleBaseLayer(selectedLayerId);

            // Assert
            expect(console.error).toHaveBeenCalledWith('Base layer not found:', selectedLayerId);
        });
    });

    describe('updateOpacity', () => {
        const updateOpacity = (layerId, opacity) => {
            console.log('Updating opacity for layer:', layerId, 'to:', opacity);
            
            const layer = mapLayers[layerId];
            if (layer && layer.setOpacity) {
                layer.setOpacity(opacity);
                
                // Send to server
                return fetch('/api/geolayerapi/update-opacity', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({
                        layerId: layerId,
                        opacity: opacity
                    })
                })
                .then(response => {
                    if (response.ok) {
                        console.log('Opacity updated successfully');
                    } else {
                        console.error('Failed to update opacity');
                    }
                })
                .catch(error => {
                    console.error('Error updating opacity:', error);
                });
            } else {
                console.error('Layer not found or does not support opacity:', layerId);
            }
        };

        test('should update layer opacity successfully', async () => {
            // Arrange
            const layerId = 'test-layer-id';
            const opacity = 0.7;
            const mockLayer = {
                setOpacity: jest.fn()
            };
            
            mockMapLayers[layerId] = mockLayer;

            // Act
            await updateOpacity(layerId, opacity);

            // Assert
            expect(mockLayer.setOpacity).toHaveBeenCalledWith(opacity);
            expect(fetch).toHaveBeenCalledWith('/api/geolayerapi/update-opacity', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({
                    layerId: layerId,
                    opacity: opacity
                })
            });
            expect(console.log).toHaveBeenCalledWith('Opacity updated successfully');
        });

        test('should handle layer not found', async () => {
            // Arrange
            const layerId = 'non-existent-layer';
            const opacity = 0.5;

            // Act
            await updateOpacity(layerId, opacity);

            // Assert
            expect(console.error).toHaveBeenCalledWith('Layer not found or does not support opacity:', layerId);
            expect(fetch).not.toHaveBeenCalled();
        });

        test('should handle server error', async () => {
            // Arrange
            const layerId = 'test-layer-id';
            const opacity = 0.3;
            const mockLayer = {
                setOpacity: jest.fn()
            };
            
            mockMapLayers[layerId] = mockLayer;
            global.fetch.mockResolvedValue({
                ok: false,
                status: 500
            });

            // Act
            await updateOpacity(layerId, opacity);

            // Assert
            expect(mockLayer.setOpacity).toHaveBeenCalledWith(opacity);
            expect(console.error).toHaveBeenCalledWith('Failed to update opacity');
        });
    });

    describe('Tab and Checkbox Dropdown Functions', () => {
        describe('toggleAllBaseLayers', () => {
            const toggleAllBaseLayers = (show) => {
                const content = document.getElementById('baseLayersContent');
                const radioButtons = content ? content.querySelectorAll('input[type="radio"]') : [];

                if (show) {
                    if (content) content.classList.remove('hidden');
                    radioButtons.forEach(radio => radio.disabled = false);
                } else {
                    if (content) content.classList.add('hidden');
                    radioButtons.forEach(radio => {
                        radio.disabled = true;
                        if (radio.checked) {
                            radio.checked = false;
                            // Simulate hiding the base layer
                            const layerId = radio.value;
                            const layer = mapLayers[layerId];
                            if (layer && map.hasLayer(layer)) {
                                map.removeLayer(layer);
                            }
                        }
                    });
                }
            };

            test('should show base layers when checkbox is checked', () => {
                // Arrange
                document.body.innerHTML = `
                    <div id="baseLayersContent" class="hidden">
                        <input type="radio" name="baseLayer" value="base-1" />
                        <input type="radio" name="baseLayer" value="base-2" checked />
                    </div>
                `;

                // Act
                toggleAllBaseLayers(true);

                // Assert
                const content = document.getElementById('baseLayersContent');
                expect(content.classList.contains('hidden')).toBe(false);

                const radioButtons = content.querySelectorAll('input[type="radio"]');
                radioButtons.forEach(radio => {
                    expect(radio.disabled).toBe(false);
                });
            });

            test('should hide base layers when checkbox is unchecked', () => {
                // Arrange
                const mockLayer = { addTo: jest.fn() };
                mockMapLayers['base-2'] = mockLayer;
                mockMap.hasLayer.mockReturnValue(true);

                document.body.innerHTML = `
                    <div id="baseLayersContent">
                        <input type="radio" name="baseLayer" value="base-1" />
                        <input type="radio" name="baseLayer" value="base-2" checked />
                    </div>
                `;

                // Act
                toggleAllBaseLayers(false);

                // Assert
                const content = document.getElementById('baseLayersContent');
                expect(content.classList.contains('hidden')).toBe(true);

                const radioButtons = content.querySelectorAll('input[type="radio"]');
                radioButtons.forEach(radio => {
                    expect(radio.disabled).toBe(true);
                });

                const checkedRadio = document.querySelector('input[value="base-2"]');
                expect(checkedRadio.checked).toBe(false);
                expect(mockMap.removeLayer).toHaveBeenCalledWith(mockLayer);
            });
        });

        describe('toggleAllGeneralLayers', () => {
            const toggleAllGeneralLayers = (show) => {
                const content = document.getElementById('generalLayersContent');
                const checkboxes = content ? content.querySelectorAll('input[type="checkbox"]') : [];

                if (show) {
                    if (content) content.classList.remove('hidden');
                    checkboxes.forEach(checkbox => checkbox.disabled = false);
                } else {
                    if (content) content.classList.add('hidden');
                    checkboxes.forEach(checkbox => {
                        checkbox.disabled = true;
                        if (checkbox.checked) {
                            checkbox.checked = false;
                            // Simulate hiding the layer
                            const layerItem = checkbox.closest('.layer-item');
                            if (layerItem) {
                                const layerId = layerItem.getAttribute('data-layer-id');
                                const layer = mapLayers[layerId];
                                if (layer && map.hasLayer(layer)) {
                                    map.removeLayer(layer);
                                }
                            }
                        }
                    });
                }
            };

            test('should show general layers when checkbox is checked', () => {
                // Arrange
                document.body.innerHTML = `
                    <div id="generalLayersContent" class="hidden">
                        <div class="layer-item" data-layer-id="layer-1">
                            <input type="checkbox" />
                        </div>
                        <div class="layer-item" data-layer-id="layer-2">
                            <input type="checkbox" checked />
                        </div>
                    </div>
                `;

                // Act
                toggleAllGeneralLayers(true);

                // Assert
                const content = document.getElementById('generalLayersContent');
                expect(content.classList.contains('hidden')).toBe(false);

                const checkboxes = content.querySelectorAll('input[type="checkbox"]');
                checkboxes.forEach(checkbox => {
                    expect(checkbox.disabled).toBe(false);
                });
            });

            test('should hide general layers when checkbox is unchecked', () => {
                // Arrange
                const mockLayer = { addTo: jest.fn() };
                mockMapLayers['layer-2'] = mockLayer;
                mockMap.hasLayer.mockReturnValue(true);

                document.body.innerHTML = `
                    <div id="generalLayersContent">
                        <div class="layer-item" data-layer-id="layer-1">
                            <input type="checkbox" />
                        </div>
                        <div class="layer-item" data-layer-id="layer-2">
                            <input type="checkbox" checked />
                        </div>
                    </div>
                `;

                // Act
                toggleAllGeneralLayers(false);

                // Assert
                const content = document.getElementById('generalLayersContent');
                expect(content.classList.contains('hidden')).toBe(true);

                const checkboxes = content.querySelectorAll('input[type="checkbox"]');
                checkboxes.forEach(checkbox => {
                    expect(checkbox.disabled).toBe(true);
                });

                const checkedCheckbox = document.querySelector('.layer-item[data-layer-id="layer-2"] input[type="checkbox"]');
                expect(checkedCheckbox.checked).toBe(false);
                expect(mockMap.removeLayer).toHaveBeenCalledWith(mockLayer);
            });
        });

        describe('showLayerInfo', () => {
            const showLayerInfo = (layerId, layerName, description, layerType, workspace) => {
                // Populate modal
                const modalLabel = document.getElementById('layerInfoModalLabel');
                const modalDescription = document.getElementById('layerInfoDescription');
                const modalType = document.getElementById('layerInfoType');
                const modalWorkspace = document.getElementById('layerInfoWorkspace');

                if (modalLabel) modalLabel.textContent = layerName;
                if (modalDescription) modalDescription.textContent = description;
                if (modalType) modalType.textContent = layerType;
                if (modalWorkspace) modalWorkspace.textContent = workspace;

                // Mock showing modal
                console.log('Modal shown for layer:', layerName);
            };

            test('should populate modal with layer information', () => {
                // Arrange
                document.body.innerHTML = `
                    <div id="layerInfoModal">
                        <h5 id="layerInfoModalLabel"></h5>
                        <p id="layerInfoDescription"></p>
                        <p id="layerInfoType"></p>
                        <p id="layerInfoWorkspace"></p>
                    </div>
                `;

                const layerId = 'test-layer';
                const layerName = 'Test Layer';
                const description = 'Test layer description';
                const layerType = 'WMS';
                const workspace = 'test:workspace';

                // Act
                showLayerInfo(layerId, layerName, description, layerType, workspace);

                // Assert
                expect(document.getElementById('layerInfoModalLabel').textContent).toBe(layerName);
                expect(document.getElementById('layerInfoDescription').textContent).toBe(description);
                expect(document.getElementById('layerInfoType').textContent).toBe(layerType);
                expect(document.getElementById('layerInfoWorkspace').textContent).toBe(workspace);
                expect(console.log).toHaveBeenCalledWith('Modal shown for layer:', layerName);
            });

            test('should handle missing modal elements gracefully', () => {
                // Arrange
                document.body.innerHTML = '<div></div>'; // No modal elements

                // Act & Assert - should not throw error
                expect(() => {
                    showLayerInfo('test', 'Test', 'Description', 'WMS', 'workspace');
                }).not.toThrow();
            });
        });
    });
});
