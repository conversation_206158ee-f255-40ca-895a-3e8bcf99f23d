{"format": 1, "restore": {"D:\\C#Web\\IKRB_real\\GeoSpatialDataKRBR.Common\\GeoSpatialDataKRBR.Common.csproj": {}}, "projects": {"D:\\C#Web\\IKRB_real\\GeoSpatialDataKRBR.Common\\GeoSpatialDataKRBR.Common.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "D:\\C#Web\\IKRB_real\\GeoSpatialDataKRBR.Common\\GeoSpatialDataKRBR.Common.csproj", "projectName": "GeoSpatialDataKRBR.Common", "projectPath": "D:\\C#Web\\IKRB_real\\GeoSpatialDataKRBR.Common\\GeoSpatialDataKRBR.Common.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "D:\\C#Web\\IKRB_real\\GeoSpatialDataKRBR.Common\\obj\\", "projectStyle": "PackageReference", "fallbackFolders": ["C:\\Program Files (x86)\\Microsoft Visual Studio\\Shared\\NuGetPackages"], "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.FallbackLocation.config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config"], "originalTargetFrameworks": ["net7.0"], "sources": {"C:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\": {}, "https://api.nuget.org/v3/index.json": {}}, "frameworks": {"net7.0": {"targetAlias": "net7.0", "projectReferences": {}}}, "warningProperties": {"warnAsError": ["NU1605"]}}, "frameworks": {"net7.0": {"targetAlias": "net7.0", "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "frameworkReferences": {"Microsoft.NETCore.App": {"privateAssets": "all"}}, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\7.0.100\\RuntimeIdentifierGraph.json"}}}}}