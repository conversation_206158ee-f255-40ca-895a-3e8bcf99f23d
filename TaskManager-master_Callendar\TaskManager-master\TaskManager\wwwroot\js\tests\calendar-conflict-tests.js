// Calendar Conflict Detection Unit Tests
// These tests verify the conflict detection logic in calendar-app.js

describe('Calendar Conflict Detection Tests', function() {
    
    // Mock data for testing
    const mockTeamMembers = [
        { id: '1', firstName: 'Петър', lastName: 'Петров', visible: true },
        { id: '2', firstName: 'Георги', lastName: 'Георгиев', visible: true },
        { id: '3', firstName: 'Мария', lastName: 'Иванова', visible: true }
    ];

    const mockTasks = [
        {
            id: 1,
            title: 'Existing Task 1',
            date: '2024-01-15',
            startTime: '09:00',
            endTime: '11:00',
            assignedMemberIds: ['1']
        },
        {
            id: 2,
            title: 'Existing Task 2',
            date: '2024-01-15',
            startTime: '14:00',
            endTime: '16:00',
            assignedMemberIds: ['2']
        },
        {
            id: 3,
            title: 'Existing Task 3',
            date: '2024-01-16',
            startTime: '10:00',
            endTime: '12:00',
            assignedMemberIds: ['1']
        }
    ];

    // Test helper function to simulate the conflict detection
    function checkWorkerTimeConflicts(tasks, teamMembers, newTask = null) {
        const conflicts = [];
        const allTasks = newTask ? [...tasks, newTask] : tasks;

        // Skip if newTask has no date
        if (newTask && (!newTask.date || newTask.date.trim() === '')) {
            return conflicts;
        }

        // Group tasks by worker and date
        const workerTasks = {};

        allTasks.forEach(task => {
            // Skip tasks without dates
            if (!task.date || task.date.trim() === '') {
                return;
            }

            const memberIds = task.assignedMemberIds || [];
            if (memberIds.length === 0) return;

            memberIds.forEach(workerId => {
                const key = `${workerId}-${task.date}`;
                if (!workerTasks[key]) {
                    workerTasks[key] = [];
                }
                workerTasks[key].push(task);
            });
        });

        // Check for overlaps within each worker's tasks
        Object.entries(workerTasks).forEach(([key, workerDayTasks]) => {
            const [workerId, date] = key.split('-');
            const worker = teamMembers.find(m => m.id === workerId);

            for (let i = 0; i < workerDayTasks.length; i++) {
                for (let j = i + 1; j < workerDayTasks.length; j++) {
                    const task1 = workerDayTasks[i];
                    const task2 = workerDayTasks[j];

                    const start1 = new Date(`2000-01-01T${task1.startTime}:00`);
                    const end1 = new Date(`2000-01-01T${task1.endTime}:00`);
                    const start2 = new Date(`2000-01-01T${task2.startTime}:00`);
                    const end2 = new Date(`2000-01-01T${task2.endTime}:00`);

                    // Check for overlap
                    if (start1 < end2 && start2 < end1) {
                        conflicts.push({
                            worker: worker,
                            task1: task1,
                            task2: task2,
                            date: date,
                            overlapStart: new Date(Math.max(start1.getTime(), start2.getTime())),
                            overlapEnd: new Date(Math.min(end1.getTime(), end2.getTime()))
                        });
                    }
                }
            }
        });

        return conflicts;
    }

    describe('Empty Date Conflict Tests', function() {
        
        it('should not detect conflicts when updating task with null date', function() {
            const newTask = {
                id: 999,
                title: 'Task with null date',
                date: null,
                startTime: '10:00',
                endTime: '11:00',
                assignedMemberIds: ['1']
            };

            const conflicts = checkWorkerTimeConflicts(mockTasks, mockTeamMembers, newTask);
            expect(conflicts).toHaveLength(0);
        });

        it('should not detect conflicts when updating task with empty string date', function() {
            const newTask = {
                id: 999,
                title: 'Task with empty date',
                date: '',
                startTime: '10:00',
                endTime: '11:00',
                assignedMemberIds: ['1']
            };

            const conflicts = checkWorkerTimeConflicts(mockTasks, mockTeamMembers, newTask);
            expect(conflicts).toHaveLength(0);
        });

        it('should not detect conflicts when updating task with whitespace-only date', function() {
            const newTask = {
                id: 999,
                title: 'Task with whitespace date',
                date: '   ',
                startTime: '10:00',
                endTime: '11:00',
                assignedMemberIds: ['1']
            };

            const conflicts = checkWorkerTimeConflicts(mockTasks, mockTeamMembers, newTask);
            expect(conflicts).toHaveLength(0);
        });

    });

    describe('Valid Time Conflict Tests', function() {
        
        it('should detect conflicts when tasks overlap in time for same worker', function() {
            const newTask = {
                id: 999,
                title: 'Conflicting Task',
                date: '2024-01-15',
                startTime: '10:00', // Overlaps with existing task (09:00-11:00)
                endTime: '12:00',
                assignedMemberIds: ['1']
            };

            const conflicts = checkWorkerTimeConflicts(mockTasks, mockTeamMembers, newTask);
            expect(conflicts).toHaveLength(1);
            expect(conflicts[0].worker.id).toBe('1');
        });

        it('should not detect conflicts when tasks are for different workers', function() {
            const newTask = {
                id: 999,
                title: 'Non-conflicting Task',
                date: '2024-01-15',
                startTime: '10:00',
                endTime: '12:00',
                assignedMemberIds: ['3'] // Different worker
            };

            const conflicts = checkWorkerTimeConflicts(mockTasks, mockTeamMembers, newTask);
            expect(conflicts).toHaveLength(0);
        });

        it('should not detect conflicts when tasks are on different dates', function() {
            const newTask = {
                id: 999,
                title: 'Different Date Task',
                date: '2024-01-17', // Different date
                startTime: '09:00',
                endTime: '11:00',
                assignedMemberIds: ['1']
            };

            const conflicts = checkWorkerTimeConflicts(mockTasks, mockTeamMembers, newTask);
            expect(conflicts).toHaveLength(0);
        });

        it('should detect conflicts when updating existing task to conflict', function() {
            // Simulate updating task 3 to conflict with task 1
            const updatedTasks = mockTasks.map(task => 
                task.id === 3 ? { ...task, date: '2024-01-15', startTime: '10:00', endTime: '12:00' } : task
            );

            const conflicts = checkWorkerTimeConflicts(updatedTasks, mockTeamMembers);
            expect(conflicts).toHaveLength(1);
        });

    });

    describe('Edge Case Tests', function() {
        
        it('should handle tasks with no assigned members', function() {
            const newTask = {
                id: 999,
                title: 'Task with no workers',
                date: '2024-01-15',
                startTime: '10:00',
                endTime: '12:00',
                assignedMemberIds: []
            };

            const conflicts = checkWorkerTimeConflicts(mockTasks, mockTeamMembers, newTask);
            expect(conflicts).toHaveLength(0);
        });

        it('should handle tasks with undefined assignedMemberIds', function() {
            const newTask = {
                id: 999,
                title: 'Task with undefined workers',
                date: '2024-01-15',
                startTime: '10:00',
                endTime: '12:00'
                // assignedMemberIds is undefined
            };

            const conflicts = checkWorkerTimeConflicts(mockTasks, mockTeamMembers, newTask);
            expect(conflicts).toHaveLength(0);
        });

        it('should handle exact time boundaries (no overlap)', function() {
            const newTask = {
                id: 999,
                title: 'Adjacent Task',
                date: '2024-01-15',
                startTime: '11:00', // Starts exactly when existing task ends
                endTime: '13:00',
                assignedMemberIds: ['1']
            };

            const conflicts = checkWorkerTimeConflicts(mockTasks, mockTeamMembers, newTask);
            expect(conflicts).toHaveLength(0);
        });

        it('should detect minimal overlap (1 minute)', function() {
            const newTask = {
                id: 999,
                title: 'Minimal Overlap Task',
                date: '2024-01-15',
                startTime: '10:59', // 1 minute overlap with existing task (09:00-11:00)
                endTime: '13:00',
                assignedMemberIds: ['1']
            };

            const conflicts = checkWorkerTimeConflicts(mockTasks, mockTeamMembers, newTask);
            expect(conflicts).toHaveLength(1);
        });

    });

    describe('Multiple Worker Conflict Tests', function() {
        
        it('should detect conflicts for multiple workers assigned to same task', function() {
            const newTask = {
                id: 999,
                title: 'Multi-worker Task',
                date: '2024-01-15',
                startTime: '10:00',
                endTime: '12:00',
                assignedMemberIds: ['1', '2'] // Both workers have conflicts
            };

            const conflicts = checkWorkerTimeConflicts(mockTasks, mockTeamMembers, newTask);
            expect(conflicts).toHaveLength(2); // One conflict per worker
        });

        it('should detect partial conflicts when only some workers conflict', function() {
            const newTask = {
                id: 999,
                title: 'Partial Conflict Task',
                date: '2024-01-15',
                startTime: '10:00',
                endTime: '12:00',
                assignedMemberIds: ['1', '3'] // Only worker 1 has conflict
            };

            const conflicts = checkWorkerTimeConflicts(mockTasks, mockTeamMembers, newTask);
            expect(conflicts).toHaveLength(1);
            expect(conflicts[0].worker.id).toBe('1');
        });

    });

});

// Test runner setup for browser environment
if (typeof window !== 'undefined') {
    // Browser environment - can be run with a test runner like Jasmine or Mocha
    console.log('Calendar conflict tests loaded. Run with your preferred test framework.');
}

// Export for Node.js environment
if (typeof module !== 'undefined' && module.exports) {
    module.exports = {
        checkWorkerTimeConflicts,
        mockTeamMembers,
        mockTasks
    };
}
