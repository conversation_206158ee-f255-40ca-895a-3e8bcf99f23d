using System.ComponentModel.DataAnnotations;

namespace TaskManager.Web.ViewModels.Calendar
{
    public class UpdateCalendarTaskViewModel
    {
        [Required]
        [StringLength(200, MinimumLength = 3)]
        public string Title { get; set; } = null!;
        
        [StringLength(1000)]
        public string Description { get; set; } = "";
        
        [Required]
        public List<string> AssignedMemberIds { get; set; } = new List<string>();
        
        [Required]
        public DateTime Date { get; set; }
        
        [Required]
        public string StartTime { get; set; } = null!;
        
        [Required]
        public string EndTime { get; set; } = null!;
        
        public string Color { get; set; } = "#3b82f6";
        
        // Optional fields for updating actual GeoTasks
        public string? Address { get; set; }
        
        public decimal? Price { get; set; }
        
        public int? Quantity { get; set; }
        
        public string? Note { get; set; }
    }
}
