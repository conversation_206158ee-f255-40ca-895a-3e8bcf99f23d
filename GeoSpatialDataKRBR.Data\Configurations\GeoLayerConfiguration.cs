namespace GeoSpatialDataKRBR.Data.Configurations
{
    using Microsoft.EntityFrameworkCore;
    using Microsoft.EntityFrameworkCore.Metadata.Builders;
    using GeoSpatialDataKRBR.Data.Models;

    public class GeoLayerConfiguration : IEntityTypeConfiguration<GeoLayer>
    {
        public void Configure(EntityTypeBuilder<GeoLayer> builder)
        {
            builder.HasKey(gl => gl.Id);

            builder.Property(gl => gl.Name)
                .IsRequired()
                .HasMaxLength(100);

            builder.Property(gl => gl.Description)
                .HasMaxLength(500);

            builder.Property(gl => gl.LayerName)
                .IsRequired()
                .HasMaxLength(100);

            builder.Property(gl => gl.Workspace)
                .IsRequired()
                .HasMaxLength(50);

            builder.Property(gl => gl.WmsUrl)
                .IsRequired()
                .HasMaxLength(2048);

            builder.Property(gl => gl.WfsUrl)
                .HasMaxLength(2048);

            builder.Property(gl => gl.StyleName)
                .HasMaxLength(100);

            builder.Property(gl => gl.LayerType)
                .IsRequired()
                .HasMaxLength(20);

            builder.Property(gl => gl.Opacity)
                .HasColumnType("decimal(3,2)");

            builder.HasOne(gl => gl.GeoServerConfiguration)
                .WithMany(gsc => gsc.GeoLayers)
                .HasForeignKey(gl => gl.GeoServerConfigurationId)
                .OnDelete(DeleteBehavior.Restrict);

            builder.HasIndex(gl => new { gl.LayerName, gl.Workspace })
                .IsUnique();
        }
    }
}
