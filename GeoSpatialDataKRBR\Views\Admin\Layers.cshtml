@model IEnumerable<GeoSpatialDataKRBR.Web.ViewModels.GeoLayer.GeoLayerListViewModel>
@{
    ViewData["Title"] = "Управление на слоеве";
    Layout = "_Layout";
}

<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center mb-4">
                <h2>@ViewData["Title"]</h2>
                <a href="@Url.Action("CreateLayer", "Admin")" class="btn btn-primary">
                    <i class="fas fa-plus"></i> Добави нов слой
                </a>
            </div>

            @if (TempData["SuccessMessage"] != null)
            {
                <div class="alert alert-success alert-dismissible fade show" role="alert">
                    @TempData["SuccessMessage"]
                    <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                </div>
            }

            @if (TempData["ErrorMessage"] != null)
            {
                <div class="alert alert-danger alert-dismissible fade show" role="alert">
                    @TempData["ErrorMessage"]
                    <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                </div>
            }

            <div class="card">
                <div class="card-body">
                    @if (Model.Any())
                    {
                        <div class="table-responsive">
                            <table class="table table-striped table-hover">
                                <thead class="table-dark">
                                    <tr>
                                        <th>Име</th>
                                        <th>Тип</th>
                                        <th>Workspace</th>
                                        <th>Слой</th>
                                        <th>Видим</th>
                                        <th>Базов</th>
                                        <th>Ред</th>
                                        <th>Прозрачност</th>
                                        <th>Създаден</th>
                                        <th>Действия</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    @foreach (var layer in Model.OrderBy(l => l.DisplayOrder).ThenBy(l => l.Name))
                                    {
                                        <tr>
                                            <td>
                                                <strong>@layer.Name</strong>
                                                @if (!string.IsNullOrEmpty(layer.Description))
                                                {
                                                    <br><small class="text-muted">@layer.Description</small>
                                                }
                                            </td>
                                            <td>
                                                <span class="badge bg-info">@layer.LayerType</span>
                                            </td>
                                            <td>
                                                <code>@layer.Workspace</code>
                                            </td>
                                            <td>
                                                <code>@layer.LayerName</code>
                                            </td>
                                            <td>
                                                @if (layer.IsVisible)
                                                {
                                                    <span class="badge bg-success">Да</span>
                                                }
                                                else
                                                {
                                                    <span class="badge bg-secondary">Не</span>
                                                }
                                            </td>
                                            <td>
                                                @if (layer.IsBaseLayer)
                                                {
                                                    <span class="badge bg-primary">Да</span>
                                                }
                                                else
                                                {
                                                    <span class="badge bg-light text-dark">Не</span>
                                                }
                                            </td>
                                            <td>@layer.DisplayOrder</td>
                                            <td>@(Math.Round((layer.Opacity ?? 1.0) * 100))%</td>
                                            <td>
                                                <small>@layer.CreatedOn.ToString("dd.MM.yyyy HH:mm")</small>
                                            </td>
                                            <td>
                                                <div class="btn-group" role="group">
                                                    <a href="@Url.Action("EditLayer", "Admin", new { id = layer.Id })" 
                                                       class="btn btn-sm btn-outline-primary" 
                                                       title="Редактирай">
                                                        <i class="fas fa-edit"></i>
                                                    </a>
                                                    <button type="button" 
                                                            class="btn btn-sm btn-outline-danger" 
                                                            onclick="confirmDelete('@layer.Id', '@layer.Name')"
                                                            title="Изтрий">
                                                        <i class="fas fa-trash"></i>
                                                    </button>
                                                </div>
                                            </td>
                                        </tr>
                                    }
                                </tbody>
                            </table>
                        </div>
                    }
                    else
                    {
                        <div class="text-center py-5">
                            <i class="fas fa-layer-group fa-3x text-muted mb-3"></i>
                            <h5 class="text-muted">Няма налични слоеве</h5>
                            <p class="text-muted">Започнете като добавите първия слой.</p>
                            <a href="@Url.Action("CreateLayer", "Admin")" class="btn btn-primary">
                                <i class="fas fa-plus"></i> Добави слой
                            </a>
                        </div>
                    }
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Delete Confirmation Modal -->
<div class="modal fade" id="deleteModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Потвърждение за изтриване</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <p>Сигурни ли сте, че искате да изтриете слоя <strong id="layerNameToDelete"></strong>?</p>
                <p class="text-danger"><small>Това действие не може да бъде отменено.</small></p>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Отказ</button>
                <form id="deleteForm" method="post" style="display: inline;">
                    @Html.AntiForgeryToken()
                    <button type="submit" class="btn btn-danger">Изтрий</button>
                </form>
            </div>
        </div>
    </div>
</div>

@section Scripts {
    <script>
        function confirmDelete(layerId, layerName) {
            document.getElementById('layerNameToDelete').textContent = layerName;
            document.getElementById('deleteForm').action = '@Url.Action("DeleteLayer", "Admin")/' + layerId;
            
            const modal = new bootstrap.Modal(document.getElementById('deleteModal'));
            modal.show();
        }
    </script>
}
