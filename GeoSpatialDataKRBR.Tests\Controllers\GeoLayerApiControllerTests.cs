using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Http;
using Moq;
using Xunit;
using FluentAssertions;
using System.Security.Claims;
using GeoSpatialDataKRBR.Controllers.Api;
using GeoSpatialDataKRBR.Services.Data.Interfaces;
using GeoSpatialDataKRBR.Data.Models;

namespace GeoSpatialDataKRBR.Tests.Controllers
{
    public class GeoLayerApiControllerTests
    {
        private readonly Mock<IUserLayerPreferenceService> _mockUserLayerPreferenceService;
        private readonly GeoLayerApiController _controller;
        private readonly Guid _testUserId = Guid.NewGuid();

        public GeoLayerApiControllerTests()
        {
            _mockUserLayerPreferenceService = new Mock<IUserLayerPreferenceService>();
            _controller = new GeoLayerApiController(_mockUserLayerPreferenceService.Object);

            // Setup user context
            var claims = new List<Claim>
            {
                new Claim(ClaimTypes.NameIdentifier, _testUserId.ToString())
            };
            var identity = new ClaimsIdentity(claims, "TestAuthType");
            var claimsPrincipal = new ClaimsPrincipal(identity);

            _controller.ControllerContext = new ControllerContext
            {
                HttpContext = new DefaultHttpContext
                {
                    User = claimsPrincipal
                }
            };
        }

        [Fact]
        public async Task ToggleVisibility_WithValidData_ShouldReturnOk()
        {
            // Arrange
            var layerId = Guid.NewGuid();
            var isVisible = true;

            _mockUserLayerPreferenceService
                .Setup(s => s.ToggleLayerVisibilityAsync(_testUserId, layerId, isVisible))
                .ReturnsAsync(true);

            // Act
            var result = await _controller.ToggleVisibility(layerId, isVisible);

            // Assert
            result.Should().BeOfType<OkObjectResult>();
            var okResult = result as OkObjectResult;
            okResult!.Value.Should().BeEquivalentTo(new { success = true });

            _mockUserLayerPreferenceService.Verify(
                s => s.ToggleLayerVisibilityAsync(_testUserId, layerId, isVisible),
                Times.Once);
        }

        [Fact]
        public async Task ToggleVisibility_WithServiceFailure_ShouldReturnBadRequest()
        {
            // Arrange
            var layerId = Guid.NewGuid();
            var isVisible = false;

            _mockUserLayerPreferenceService
                .Setup(s => s.ToggleLayerVisibilityAsync(_testUserId, layerId, isVisible))
                .ReturnsAsync(false);

            // Act
            var result = await _controller.ToggleVisibility(layerId, isVisible);

            // Assert
            result.Should().BeOfType<BadRequestObjectResult>();
            var badRequestResult = result as BadRequestObjectResult;
            badRequestResult!.Value.Should().BeEquivalentTo(new { success = false, message = "Failed to toggle layer visibility" });
        }

        [Fact]
        public async Task ToggleVisibility_WithException_ShouldReturnInternalServerError()
        {
            // Arrange
            var layerId = Guid.NewGuid();
            var isVisible = true;

            _mockUserLayerPreferenceService
                .Setup(s => s.ToggleLayerVisibilityAsync(_testUserId, layerId, isVisible))
                .ThrowsAsync(new Exception("Database error"));

            // Act
            var result = await _controller.ToggleVisibility(layerId, isVisible);

            // Assert
            result.Should().BeOfType<ObjectResult>();
            var objectResult = result as ObjectResult;
            objectResult!.StatusCode.Should().Be(500);
        }

        [Fact]
        public async Task UpdateOpacity_WithValidData_ShouldReturnOk()
        {
            // Arrange
            var layerId = Guid.NewGuid();
            var opacity = 0.7;

            _mockUserLayerPreferenceService
                .Setup(s => s.UpdateLayerOpacityAsync(_testUserId, layerId, opacity))
                .ReturnsAsync(true);

            // Act
            var result = await _controller.UpdateOpacity(layerId, opacity);

            // Assert
            result.Should().BeOfType<OkObjectResult>();
            var okResult = result as OkObjectResult;
            okResult!.Value.Should().BeEquivalentTo(new { success = true });

            _mockUserLayerPreferenceService.Verify(
                s => s.UpdateLayerOpacityAsync(_testUserId, layerId, opacity),
                Times.Once);
        }

        [Fact]
        public async Task UpdateOpacity_WithInvalidOpacity_ShouldReturnBadRequest()
        {
            // Arrange
            var layerId = Guid.NewGuid();
            var invalidOpacity = 1.5; // Invalid opacity > 1

            // Act
            var result = await _controller.UpdateOpacity(layerId, invalidOpacity);

            // Assert
            result.Should().BeOfType<BadRequestObjectResult>();
            var badRequestResult = result as BadRequestObjectResult;
            badRequestResult!.Value.Should().BeEquivalentTo(new { success = false, message = "Opacity must be between 0 and 1" });

            _mockUserLayerPreferenceService.Verify(
                s => s.UpdateLayerOpacityAsync(It.IsAny<Guid>(), It.IsAny<Guid>(), It.IsAny<double>()),
                Times.Never);
        }

        [Fact]
        public async Task UpdateOpacity_WithNegativeOpacity_ShouldReturnBadRequest()
        {
            // Arrange
            var layerId = Guid.NewGuid();
            var invalidOpacity = -0.1; // Invalid opacity < 0

            // Act
            var result = await _controller.UpdateOpacity(layerId, invalidOpacity);

            // Assert
            result.Should().BeOfType<BadRequestObjectResult>();
            var badRequestResult = result as BadRequestObjectResult;
            badRequestResult!.Value.Should().BeEquivalentTo(new { success = false, message = "Opacity must be between 0 and 1" });
        }

        [Fact]
        public async Task UpdateOpacity_WithServiceFailure_ShouldReturnBadRequest()
        {
            // Arrange
            var layerId = Guid.NewGuid();
            var opacity = 0.5;

            _mockUserLayerPreferenceService
                .Setup(s => s.UpdateLayerOpacityAsync(_testUserId, layerId, opacity))
                .ReturnsAsync(false);

            // Act
            var result = await _controller.UpdateOpacity(layerId, opacity);

            // Assert
            result.Should().BeOfType<BadRequestObjectResult>();
            var badRequestResult = result as BadRequestObjectResult;
            badRequestResult!.Value.Should().BeEquivalentTo(new { success = false, message = "Failed to update layer opacity" });
        }

        [Fact]
        public async Task GetUserPreferences_ShouldReturnOkWithPreferences()
        {
            // Arrange
            var preferences = new List<UserLayerPreference>
            {
                new UserLayerPreference
                {
                    UserId = _testUserId,
                    GeoLayerId = Guid.NewGuid(),
                    IsVisible = true,
                    Opacity = 0.8,
                    DisplayOrder = 1
                },
                new UserLayerPreference
                {
                    UserId = _testUserId,
                    GeoLayerId = Guid.NewGuid(),
                    IsVisible = false,
                    Opacity = 0.5,
                    DisplayOrder = 2
                }
            };

            _mockUserLayerPreferenceService
                .Setup(s => s.GetUserLayerPreferencesAsync(_testUserId))
                .ReturnsAsync(preferences);

            // Act
            var result = await _controller.GetUserPreferences();

            // Assert
            result.Should().BeOfType<OkObjectResult>();
            var okResult = result as OkObjectResult;
            okResult!.Value.Should().BeEquivalentTo(preferences);

            _mockUserLayerPreferenceService.Verify(
                s => s.GetUserLayerPreferencesAsync(_testUserId),
                Times.Once);
        }

        [Fact]
        public async Task GetUserPreferences_WithException_ShouldReturnInternalServerError()
        {
            // Arrange
            _mockUserLayerPreferenceService
                .Setup(s => s.GetUserLayerPreferencesAsync(_testUserId))
                .ThrowsAsync(new Exception("Database error"));

            // Act
            var result = await _controller.GetUserPreferences();

            // Assert
            result.Should().BeOfType<ObjectResult>();
            var objectResult = result as ObjectResult;
            objectResult!.StatusCode.Should().Be(500);
        }
    }
}
