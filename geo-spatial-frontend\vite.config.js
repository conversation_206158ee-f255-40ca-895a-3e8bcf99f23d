import { defineConfig } from 'vite'
import react from '@vitejs/plugin-react'

// https://vitejs.dev/config/
export default defineConfig({
  plugins: [react()],
  server: {
    port: 3000,
    proxy: {
      '/api': {
        target: 'https://localhost:7154',
        changeOrigin: true,
        secure: false
      }
    }
  },
  build: {
    outDir: '../GeoSpatialDataKRBR/wwwroot/dist',
    emptyOutDir: true
  }
})
