namespace GeoSpatialDataKRBR.Services.Data.Interfaces
{
    using GeoSpatialDataKRBR.Data.Models;

    public interface IGeoLayerService
    {
        Task<IEnumerable<GeoLayer>> GetAllLayersAsync();
        Task<IEnumerable<GeoLayer>> GetVisibleLayersAsync();
        Task<GeoLayer?> GetLayerByIdAsync(Guid id);
        Task<GeoLayer?> GetLayerByNameAsync(string layerName, string workspace);
        Task<bool> CreateLayerAsync(GeoLayer layer);
        Task<bool> UpdateLayerAsync(GeoLayer layer);
        Task<bool> DeleteLayerAsync(Guid id);
        Task<bool> ToggleLayerVisibilityAsync(Guid id);
        Task<IEnumerable<GeoLayer>> GetLayersByUserPreferencesAsync(Guid userId);
        Task<bool> UpdateLayerOrderAsync(Guid id, int newOrder);
        Task<bool> LayerExistsAsync(string layerName, string workspace);
    }
}
