import { describe, it, expect } from 'vitest'

// Import utility functions from App.jsx
// Note: These would normally be in separate utility files
const TASK_COLORS = [
  '#3B82F6', '#10B981', '#F59E0B', '#EF4444', '#8B5CF6',
  '#F97316', '#06B6D4', '#84CC16', '#EC4899', '#6B7280'
]

// Time slots generation function (extracted from App.jsx)
const generateTimeSlots = () => {
  const slots = []
  for (let hour = 8; hour <= 18; hour++) {
    slots.push(`${hour.toString().padStart(2, '0')}:00`)
    if (hour < 18) {
      slots.push(`${hour.toString().padStart(2, '0')}:30`)
    }
  }
  return slots
}

// Date formatting function
const formatDate = (date) => {
  return new Date(date).toISOString().split('T')[0]
}

// Time calculation function
const calculateDuration = (startTime, endTime) => {
  const [startHour, startMin] = startTime.split(':').map(Number)
  const [endHour, endMin] = endTime.split(':').map(Number)
  
  const startMinutes = startHour * 60 + startMin
  const endMinutes = endHour * 60 + endMin
  
  return endMinutes - startMinutes
}

// Task overlap detection
const hasTimeOverlap = (task1, task2) => {
  if (task1.date !== task2.date) return false
  
  const task1Start = task1.startTime
  const task1End = task1.endTime
  const task2Start = task2.startTime
  const task2End = task2.endTime
  
  return task1Start < task2End && task2Start < task1End
}

describe('Utility Functions', () => {
  describe('generateTimeSlots', () => {
    it('should generate correct time slots from 8:00 to 18:00', () => {
      const slots = generateTimeSlots()
      
      expect(slots).toHaveLength(21) // 11 hours * 2 slots - 1 (no 18:30)
      expect(slots[0]).toBe('08:00')
      expect(slots[1]).toBe('08:30')
      expect(slots[slots.length - 1]).toBe('18:00')
    })
    
    it('should include both :00 and :30 slots for each hour except last', () => {
      const slots = generateTimeSlots()
      
      expect(slots).toContain('08:00')
      expect(slots).toContain('08:30')
      expect(slots).toContain('17:00')
      expect(slots).toContain('17:30')
      expect(slots).toContain('18:00')
      expect(slots).not.toContain('18:30')
    })
    
    it('should format hours with leading zeros', () => {
      const slots = generateTimeSlots()
      
      expect(slots[0]).toBe('08:00')
      expect(slots[1]).toBe('08:30')
      expect(slots[2]).toBe('09:00')
    })
  })
  
  describe('formatDate', () => {
    it('should format date to YYYY-MM-DD string', () => {
      const date = new Date('2025-06-27T10:30:00')
      const formatted = formatDate(date)
      
      expect(formatted).toBe('2025-06-27')
    })
    
    it('should handle different date formats', () => {
      const date1 = formatDate('2025-01-01')
      const date2 = formatDate(new Date(2025, 0, 1)) // Month is 0-indexed

      expect(date1).toBe('2025-01-01')
      // Handle timezone differences - the date constructor creates local time
      expect(date2).toMatch(/^2025-01-0[12]$/) // Could be 01 or 02 depending on timezone
    })
  })
  
  describe('calculateDuration', () => {
    it('should calculate duration in minutes correctly', () => {
      expect(calculateDuration('09:00', '10:00')).toBe(60)
      expect(calculateDuration('09:30', '10:00')).toBe(30)
      expect(calculateDuration('08:00', '18:00')).toBe(600)
    })
    
    it('should handle cross-hour calculations', () => {
      expect(calculateDuration('09:45', '10:15')).toBe(30)
      expect(calculateDuration('14:30', '16:45')).toBe(135)
    })
  })
  
  describe('hasTimeOverlap', () => {
    it('should detect overlapping tasks on same date', () => {
      const task1 = { date: '2025-06-27', startTime: '09:00', endTime: '10:00' }
      const task2 = { date: '2025-06-27', startTime: '09:30', endTime: '10:30' }
      
      expect(hasTimeOverlap(task1, task2)).toBe(true)
    })
    
    it('should not detect overlap for non-overlapping tasks', () => {
      const task1 = { date: '2025-06-27', startTime: '09:00', endTime: '10:00' }
      const task2 = { date: '2025-06-27', startTime: '10:00', endTime: '11:00' }
      
      expect(hasTimeOverlap(task1, task2)).toBe(false)
    })
    
    it('should not detect overlap for different dates', () => {
      const task1 = { date: '2025-06-27', startTime: '09:00', endTime: '10:00' }
      const task2 = { date: '2025-06-28', startTime: '09:30', endTime: '10:30' }
      
      expect(hasTimeOverlap(task1, task2)).toBe(false)
    })
    
    it('should handle edge cases correctly', () => {
      const task1 = { date: '2025-06-27', startTime: '09:00', endTime: '10:00' }
      const task2 = { date: '2025-06-27', startTime: '08:00', endTime: '09:00' }
      
      expect(hasTimeOverlap(task1, task2)).toBe(false)
    })
  })
  
  describe('TASK_COLORS', () => {
    it('should have 10 predefined colors', () => {
      expect(TASK_COLORS).toHaveLength(10)
    })
    
    it('should contain valid hex colors', () => {
      TASK_COLORS.forEach(color => {
        expect(color).toMatch(/^#[0-9A-F]{6}$/i)
      })
    })
    
    it('should include expected colors', () => {
      expect(TASK_COLORS).toContain('#3B82F6') // Blue
      expect(TASK_COLORS).toContain('#10B981') // Green
      expect(TASK_COLORS).toContain('#EF4444') // Red
    })
  })
})
