﻿using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using TaskManager.Services.Data.Interfaces;
using TaskManager.Web.ViewModels.Calendar;
using TaskManager.Web.Infrastructure.Extentions;
using System.Text.Json;

namespace TaskManager.Controllers
{
    [Authorize(Roles = "Worker,Administrator")]
    public class CalendarController : Controller
    {
        private readonly ICalendarService calendarService;
        private readonly IUserService userService;

        public CalendarController(ICalendarService calendarService, IUserService userService)
        {
            this.calendarService = calendarService;
            this.userService = userService;
        }

        public IActionResult Calendar()
        {
            return View();
        }

        [HttpGet]
        public async Task<IActionResult> IsAdmin()
        {
            try
            {
                string userId = User.GetId();
                bool isAdmin = await userService.IsUserAdminByIdAsync(userId);

                // Temporary test: force non-admin for testing
                // Remove this line to restore normal admin checking
                // isAdmin = false;

                Console.WriteLine($"🔑 Admin check for user {userId}: {isAdmin}");
                return Json(new { isAdmin = isAdmin });
            }
            catch (Exception ex)
            {
                Console.WriteLine($"❌ Error checking admin status: {ex.Message}");
                return Json(new { isAdmin = false });
            }
        }



        [HttpGet]
        public async Task<IActionResult> GetWorkers()
        {
            try
            {
                var workers = await calendarService.GetAllWorkersAsync();
                return Json(workers);
            }
            catch (Exception ex)
            {
                return BadRequest(new { error = ex.Message });
            }
        }

        [HttpPost]
        [IgnoreAntiforgeryToken]
        public async Task<IActionResult> UpdateWorkerColor(string workerId, [FromBody] UpdateWorkerColorRequest request)
        {
            try
            {
                Console.WriteLine($"🎨 UpdateWorkerColor called: workerId={workerId}, color={request?.Color}");
                Console.WriteLine($"🎨 Request body: {System.Text.Json.JsonSerializer.Serialize(request)}");

                // Safe access to User.Claims for logging
                var userRoles = User?.Claims?.Where(c => c.Type == System.Security.Claims.ClaimTypes.Role)?.Select(c => c.Value) ?? new string[0];
                Console.WriteLine($"🎨 User roles: {string.Join(", ", userRoles)}");

                if (string.IsNullOrEmpty(workerId))
                {
                    Console.WriteLine($"❌ Worker ID is empty");
                    return BadRequest(new { error = "Worker ID is required" });
                }

                if (request == null || string.IsNullOrEmpty(request.Color))
                {
                    Console.WriteLine($"❌ Color is empty or request is null");
                    return BadRequest(new { error = "Color is required" });
                }

                await calendarService.UpdateWorkerColorAsync(workerId, request.Color);
                Console.WriteLine($"✅ Color updated successfully for worker {workerId}");
                return Ok(new { message = "Color updated successfully" });
            }
            catch (Exception ex)
            {
                Console.WriteLine($"❌ Error updating color: {ex.Message}");
                Console.WriteLine($"❌ Stack trace: {ex.StackTrace}");
                return BadRequest(new { error = ex.Message });
            }
        }

        [HttpGet]
        public IActionResult TestWorkerColor(string workerId)
        {
            Console.WriteLine($"🧪 TestWorkerColor called: workerId={workerId}");
            return Ok(new { message = $"Test endpoint reached for worker {workerId}", workerId = workerId });
        }

        [HttpGet]
        public async Task<IActionResult> GetTasks(DateTime? startDate = null, DateTime? endDate = null)
        {
            try
            {
                System.Diagnostics.Debug.WriteLine($"Calendar Controller: GetTasks called with startDate={startDate}, endDate={endDate}");
                var tasks = await calendarService.GetTasksAsync(startDate, endDate);
                System.Diagnostics.Debug.WriteLine($"Calendar Controller: Returning {tasks.Count()} tasks");

                // Also log to console for browser debugging
                Console.WriteLine($"Calendar Controller: GetTasks called with startDate={startDate}, endDate={endDate}");
                Console.WriteLine($"Calendar Controller: Returning {tasks.Count()} tasks");

                return Json(tasks);
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Calendar Controller Error: {ex.Message}");
                Console.WriteLine($"Calendar Controller Error: {ex.Message}");
                return BadRequest(new { error = ex.Message });
            }
        }

        [HttpPost]
        public async Task<IActionResult> CreateTask([FromBody] JsonElement jsonData)
        {
            try
            {
                System.Diagnostics.Debug.WriteLine($"CreateTask called with JSON: {jsonData}");
                Console.WriteLine($"CreateTask called with JSON: {jsonData}");

                // Manually parse the JSON to avoid DateTime conversion issues
                var model = new CreateCalendarTaskViewModel();

                if (jsonData.TryGetProperty("title", out var titleProp))
                    model.Title = titleProp.GetString() ?? "";

                if (jsonData.TryGetProperty("description", out var descProp))
                    model.Description = descProp.GetString() ?? "";

                if (jsonData.TryGetProperty("startTime", out var startTimeProp))
                    model.StartTime = startTimeProp.GetString() ?? "";

                if (jsonData.TryGetProperty("endTime", out var endTimeProp))
                    model.EndTime = endTimeProp.GetString() ?? "";

                if (jsonData.TryGetProperty("color", out var colorProp))
                    model.Color = colorProp.GetString() ?? "";

                // Handle date specially to avoid timezone conversion
                if (jsonData.TryGetProperty("date", out var dateProp))
                {
                    var dateString = dateProp.GetString();
                    System.Diagnostics.Debug.WriteLine($"🎯 CREATE: Date string from JSON: '{dateString}'");
                    Console.WriteLine($"🎯 CREATE: Date string from JSON: '{dateString}'");

                    if (DateTime.TryParse(dateString, out var parsedDate))
                    {
                        // Force the date to be treated as local time without timezone conversion
                        model.Date = DateTime.SpecifyKind(new DateTime(parsedDate.Year, parsedDate.Month, parsedDate.Day), DateTimeKind.Unspecified);
                        System.Diagnostics.Debug.WriteLine($"🎯 CREATE: Parsed date as Unspecified: {model.Date}");
                        Console.WriteLine($"🎯 CREATE: Parsed date as Unspecified: {model.Date}");
                    }
                    else
                    {
                        return BadRequest($"Invalid date format: {dateString}");
                    }
                }

                // Handle assigned member IDs
                if (jsonData.TryGetProperty("assignedMemberIds", out var membersProp) && membersProp.ValueKind == JsonValueKind.Array)
                {
                    model.AssignedMemberIds = membersProp.EnumerateArray()
                        .Where(x => x.ValueKind == JsonValueKind.String)
                        .Select(x => x.GetString()!)
                        .ToList();
                }
                else
                {
                    model.AssignedMemberIds = new List<string>();
                }

                if (!ModelState.IsValid)
                {
                    var errors = ModelState.Values.SelectMany(v => v.Errors).Select(e => e.ErrorMessage);
                    System.Diagnostics.Debug.WriteLine($"ModelState errors: {string.Join(", ", errors)}");
                    Console.WriteLine($"ModelState errors: {string.Join(", ", errors)}");
                    return BadRequest(new { errors = errors });
                }

                var task = await calendarService.CreateTaskAsync(model);
                System.Diagnostics.Debug.WriteLine($"Task created successfully: {task.Id}");
                Console.WriteLine($"Task created successfully: {task.Id}");
                return Json(task);
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"CreateTask error: {ex.Message}");
                Console.WriteLine($"CreateTask error: {ex.Message}");
                return BadRequest(new { error = ex.Message });
            }
        }

        [HttpPut]
        public async Task<IActionResult> UpdateTask(string id, [FromBody] JsonElement jsonData)
        {
            try
            {
                System.Diagnostics.Debug.WriteLine($"🎯 CONTROLLER: UpdateTask called with ID: {id}");
                Console.WriteLine($"🎯 CONTROLLER: UpdateTask called with ID: {id}");
                System.Diagnostics.Debug.WriteLine($"🎯 CONTROLLER: Raw JSON received: {jsonData}");
                Console.WriteLine($"🎯 CONTROLLER: Raw JSON received: {jsonData}");

                // Manually parse the JSON to avoid DateTime conversion issues
                var model = new UpdateCalendarTaskViewModel();

                System.Diagnostics.Debug.WriteLine($"🔍 CONTROLLER: Checking for 'title' property in JSON...");
                Console.WriteLine($"🔍 CONTROLLER: Checking for 'title' property in JSON...");

                if (jsonData.TryGetProperty("title", out var titleProp))
                {
                    model.Title = titleProp.GetString() ?? "";
                    System.Diagnostics.Debug.WriteLine($"🎯 CONTROLLER: Title received from frontend: '{model.Title}'");
                    Console.WriteLine($"🎯 CONTROLLER: Title received from frontend: '{model.Title}'");
                }
                else
                {
                    System.Diagnostics.Debug.WriteLine($"❌ CONTROLLER: 'title' property NOT FOUND in JSON!");
                    Console.WriteLine($"❌ CONTROLLER: 'title' property NOT FOUND in JSON!");
                    model.Title = "";
                }

                if (jsonData.TryGetProperty("description", out var descProp))
                    model.Description = descProp.GetString() ?? "";

                if (jsonData.TryGetProperty("startTime", out var startTimeProp))
                    model.StartTime = startTimeProp.GetString() ?? "";

                if (jsonData.TryGetProperty("endTime", out var endTimeProp))
                    model.EndTime = endTimeProp.GetString() ?? "";

                if (jsonData.TryGetProperty("color", out var colorProp))
                    model.Color = colorProp.GetString() ?? "";

                // Handle date specially to avoid timezone conversion
                if (jsonData.TryGetProperty("date", out var dateProp))
                {
                    var dateString = dateProp.GetString();
                    System.Diagnostics.Debug.WriteLine($"🎯 CONTROLLER: Date string from JSON: '{dateString}'");
                    Console.WriteLine($"🎯 CONTROLLER: Date string from JSON: '{dateString}'");

                    if (DateTime.TryParse(dateString, out var parsedDate))
                    {
                        // Force the date to be treated as local time without timezone conversion
                        model.Date = DateTime.SpecifyKind(new DateTime(parsedDate.Year, parsedDate.Month, parsedDate.Day), DateTimeKind.Unspecified);
                        System.Diagnostics.Debug.WriteLine($"🎯 CONTROLLER: Parsed date as Unspecified: {model.Date}");
                        Console.WriteLine($"🎯 CONTROLLER: Parsed date as Unspecified: {model.Date}");
                    }
                    else
                    {
                        return BadRequest($"Invalid date format: {dateString}");
                    }
                }

                // Handle assigned member IDs
                if (jsonData.TryGetProperty("assignedMemberIds", out var membersProp) && membersProp.ValueKind == JsonValueKind.Array)
                {
                    model.AssignedMemberIds = membersProp.EnumerateArray()
                        .Where(x => x.ValueKind == JsonValueKind.String)
                        .Select(x => x.GetString()!)
                        .ToList();
                }
                else
                {
                    model.AssignedMemberIds = new List<string>();
                }

                System.Diagnostics.Debug.WriteLine($"🎯 CONTROLLER: Final parsed model date: {model.Date}");
                Console.WriteLine($"🎯 CONTROLLER: Final parsed model date: {model.Date}");

                if (!ModelState.IsValid)
                {
                    var errors = ModelState.Values.SelectMany(v => v.Errors).Select(e => e.ErrorMessage);
                    System.Diagnostics.Debug.WriteLine($"ModelState errors: {string.Join(", ", errors)}");
                    Console.WriteLine($"ModelState errors: {string.Join(", ", errors)}");
                    return BadRequest(new { errors = errors });
                }

                var task = await calendarService.UpdateTaskAsync(id, model);
                System.Diagnostics.Debug.WriteLine($"Task updated successfully: {task.Id}");
                Console.WriteLine($"Task updated successfully: {task.Id}");
                return Json(task);
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"UpdateTask error: {ex.Message}");
                Console.WriteLine($"UpdateTask error: {ex.Message}");
                System.Diagnostics.Debug.WriteLine($"UpdateTask stack trace: {ex.StackTrace}");
                Console.WriteLine($"UpdateTask stack trace: {ex.StackTrace}");
                return BadRequest(new { error = ex.Message, stackTrace = ex.StackTrace });
            }
        }

        [HttpDelete]
        public async Task<IActionResult> DeleteTask(string id)
        {
            try
            {
                var success = await calendarService.DeleteTaskAsync(id);
                if (success)
                {
                    return Ok(new { message = "Task deleted successfully" });
                }
                return NotFound(new { error = "Task not found" });
            }
            catch (Exception ex)
            {
                return BadRequest(new { error = ex.Message });
            }
        }

        [HttpGet]
        public async Task<IActionResult> DebugWorkerIds()
        {
            try
            {
                var workers = await calendarService.GetAllWorkersAsync();
                var tasks = await calendarService.GetTasksAsync();

                return Json(new
                {
                    workers = workers.Select(w => new { id = w.Id, name = w.Name }),
                    tasks = tasks.Select(t => new {
                        title = t.Title,
                        assignedMemberIds = t.AssignedMemberIds
                    }),
                    message = "Debug: Worker IDs vs Task Assigned Member IDs"
                });
            }
            catch (Exception ex)
            {
                return BadRequest(new { error = ex.Message });
            }
        }


    }
}
