# Геопространствени данни КККР

Уеб приложение за визуализация на геопространствени слоеве чрез GeoServer с интегрирана система за автентикация.

## 🎯 Основни функционалности

- **🔐 Сигурен достъп** - ASP.NET Core Identity с роли (Administrator/User)
- **🗺️ Интерактивна карта** - Leaflet.js карта с WMS/WFS слоеве от GeoServer
- **📊 Управление на слоеве** - Включване/изключване, прозрачност, подреждане
- **👤 Потребителски настройки** - Персонализирани настройки за всеки потребител
- **⚙️ Администраторски панел** - Управление на слоеве и GeoServer конфигурации

## 🏗️ Архитектура

Проектът следва clean architecture принципите с разделени слоеве:

```
GeoSpatialDataKRBR/                 # Основно уеб приложение
├── Controllers/                    # MVC и API контролери
├── Views/                         # Razor views
└── wwwroot/                       # Статични файлове

GeoSpatialDataKRBR.Common/         # Общи константи и помощни класове
GeoSpatialDataKRBR.Data.Models/    # Entity модели
GeoSpatialDataKRBR.Data/           # Entity Framework DbContext и конфигурации
GeoSpatialDataKRBR.Services.Data/  # Бизнес логика и сервиси
GeoSpatialDataKRBR.Web.ViewModels/ # View модели за UI
GeoSpatialDataKRBR.Web.Infrastructure/ # Помощни класове за уеб слоя

geo-spatial-frontend/              # React frontend (опционален)
```

## 🛠️ Технологии

### Backend
- **ASP.NET Core 7.0** - Уеб framework
- **Entity Framework Core** - ORM
- **PostgreSQL + PostGIS** - База данни с геопространствени разширения
- **ASP.NET Core Identity** - Автентикация и авторизация

### Frontend
- **Leaflet.js** - Интерактивни карти
- **Bootstrap 5** - UI framework
- **React** (опционален) - Модерен frontend

### GeoServer
- **GeoServer** - Сървър за геопространствени данни
- **WMS/WFS** - Стандарти за картови услуги

## 🚀 Инсталация и стартиране

### Предварителни изисквания

1. **.NET 7.0 SDK**
2. **PostgreSQL 14+** с PostGIS разширение
3. **GeoServer** (опционален за пълна функционалност)

### Стъпки за инсталация

1. **Клониране на проекта**
```bash
git clone <repository-url>
cd GeoSpatialDataKRBR
```

2. **Конфигуриране на базата данни**
```bash
# Обновете connection string в appsettings.json
"DefaultConnection": "Server=localhost;Port=5433;Database=GeoSpatialDataKKKR;User Id=postgres;Password=*****;"
```

3. **Създаване на базата данни**
```bash
dotnet ef database update --project GeoSpatialDataKRBR.Data --startup-project GeoSpatialDataKRBR
```

4. **Стартиране на приложението**
```bash
dotnet run --project GeoSpatialDataKRBR
```

Приложението ще бъде достъпно на: `http://localhost:5208`

### Тестови данни за вход

- **Email:** <EMAIL>
- **Парола:** Admin123!

## 📊 База данни

### Основни таблици

- **AspNetUsers** - Потребители (разширен с FirstName, LastName, IsActive)
- **GeoLayers** - Геопространствени слоеве
- **GeoServerConfigurations** - Конфигурации за GeoServer
- **UserLayerPreferences** - Потребителски настройки за слоеве
- **GeoLayerGroups** - Групи от слоеве

## 🔧 API Endpoints

### Автентикация
- `POST /api/account/login` - Вход в системата
- `POST /api/account/logout` - Изход от системата
- `GET /api/account/user-info` - Информация за потребителя

### Слоеве
- `GET /api/geolayer` - Всички слоеве
- `GET /api/geolayer/visible` - Видими слоеве за потребителя
- `POST /api/geolayer/toggle-visibility/{id}` - Превключване на видимостта
- `POST /api/geolayer/update-opacity/{id}` - Обновяване на прозрачността

### GeoServer
- `GET /api/geoserver/workspaces/{configId}` - Workspaces от GeoServer
- `GET /api/geoserver/layers/{configId}/{workspace}` - Слоеве от workspace
- `POST /api/geoserver/test-connection/{configId}` - Тестване на връзката

## 🎨 Потребителски интерфейс

### Карта
- Интерактивна Leaflet карта с контроли за мащаб и навигация
- Панел за управление на слоеве с възможност за скриване/показване
- Контроли за прозрачност на всеки слой
- Click събития за GetFeatureInfo заявки

### Администраторски панел
- Управление на слоеве (създаване, редактиране, изтриване)
- Конфигуриране на GeoServer връзки
- Преглед на потребители и техните настройки

## 🔒 Сигурност

- **HTTPS** - Задължително за production
- **ASP.NET Core Identity** - Хеширани пароли, роли
- **CORS** - Конфигуриран за frontend приложения
- **Авторизация** - Защитени API endpoints и страници

## 🧪 Тестване

За стартиране на тестовете:

```bash
dotnet test
```

## 📝 Допълнителни бележки

- Проектът е базиран на архитектурата на TaskManager-master_Callendar
- Поддържа както MVC views, така и API за React frontend
- Готов за разширение с допълнителни геопространствени функционалности
- Съвместим с различни GeoServer инсталации

## 🤝 Принос

За въпроси и предложения, моля свържете се с екипа за разработка.
