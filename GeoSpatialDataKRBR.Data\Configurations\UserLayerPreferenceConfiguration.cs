namespace GeoSpatialDataKRBR.Data.Configurations
{
    using Microsoft.EntityFrameworkCore;
    using Microsoft.EntityFrameworkCore.Metadata.Builders;
    using GeoSpatialDataKRBR.Data.Models;

    public class UserLayerPreferenceConfiguration : IEntityTypeConfiguration<UserLayerPreference>
    {
        public void Configure(EntityTypeBuilder<UserLayerPreference> builder)
        {
            builder.HasKey(ulp => new { ulp.UserId, ulp.GeoLayerId });

            builder.Property(ulp => ulp.CustomColor)
                .HasMaxLength(7);

            builder.Property(ulp => ulp.Opacity)
                .HasColumnType("decimal(3,2)");

            builder.HasOne(ulp => ulp.User)
                .WithMany(u => u.UserLayerPreferences)
                .HasForeignKey(ulp => ulp.UserId)
                .OnDelete(DeleteBehavior.Cascade);

            builder.HasOne(ulp => ulp.GeoLayer)
                .WithMany(gl => gl.UserLayerPreferences)
                .HasForeignKey(ulp => ulp.GeoLayerId)
                .OnDelete(DeleteBehavior.Cascade);
        }
    }
}
