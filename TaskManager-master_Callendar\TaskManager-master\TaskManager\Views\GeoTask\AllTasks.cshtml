﻿@model AllTaskQueryModel;

@{
    ViewData["Title"] = "Всички задачи";
}

<div class="modern-container">
    <div class="modern-page-header">
        <h1 class="modern-page-title">
            <i class="fas fa-tasks"></i>
            Всички задачи
        </h1>
        <p class="modern-page-subtitle">
            Преглед и управление на всички задачи в системата
        </p>
    </div>

    <!-- Filters Section -->
    <div class="modern-card">
        <div class="modern-card-header">
            <h3>
                <i class="fas fa-filter"></i>
                Филтри за търсене
            </h3>
        </div>
        <div class="modern-card-body">
            <form method="get" class="modern-filters-form">
                <div class="filters-grid">
                    <div class="filter-group">
                        <label asp-for="Type" class="modern-form-label">
                            <i class="fas fa-tag"></i>
                            Тип
                        </label>
                        <select asp-for="Type" class="modern-form-control">
                            <option value="">Всички типове</option>
                            @foreach (var category in Model.Types)
                            {
                                <option value="@category">@category</option>
                            }
                        </select>
                    </div>

                    <div class="filter-group">
                        <label asp-for="TaskPerPage" class="modern-form-label">
                            <i class="fas fa-list-ol"></i>
                            Брой на страница
                        </label>
                        <select asp-for="TaskPerPage" class="modern-form-control">
                            <option value="5">5</option>
                            <option value="10">10</option>
                            <option value="15">15</option>
                        </select>
                    </div>

                    <div class="filter-group">
                        <label asp-for="SearchString" class="modern-form-label">
                            <i class="fas fa-search"></i>
                            Търсене
                        </label>
                        <input asp-for="SearchString" class="modern-form-control" placeholder="Търси по ключова дума..."/>
                    </div>

                    <div class="filter-actions">
                        <button type="submit" class="modern-btn modern-btn-primary">
                            <i class="fas fa-search"></i>
                            Търси
                        </button>
                        <a href="@Url.Action("AllTasks")" class="modern-btn modern-btn-secondary">
                            <i class="fas fa-undo"></i>
                            Изчисти
                        </a>
                    </div>
                </div>
            </form>
        </div>
    </div>

    @if (!Model.Tasks.Any())
    {
        <!-- Empty State -->
        <div class="modern-card">
            <div class="modern-card-body">
                <div class="empty-tasks-state">
                    <i class="fas fa-clipboard-list"></i>
                    <h3>Няма намерени задачи</h3>
                    <p>Не са намерени задачи, отговарящи на зададените критерии.</p>
                    <a class="modern-btn modern-btn-primary" asp-controller="GeoTask" asp-action="Add">
                        <i class="fas fa-plus"></i>
                        Създай нова задача
                    </a>
                </div>
            </div>
        </div>
    }
    else
    {
        <!-- Tasks Table -->
        <div class="tasks-table-container">
            <div class="tasks-table-header">
                <h3>
                    <i class="fas fa-list"></i>
                    Списък със задачи (@Model.TotalTaskss)
                </h3>
                <a class="add-task-btn" asp-controller="GeoTask" asp-action="Add">
                    <i class="fas fa-plus"></i>
                    Нова задача
                </a>
            </div>

            <table class="modern-table">
                <thead>
                    <tr>
                        <th><i class="fas fa-flag"></i> Статус</th>
                        <th><i class="fas fa-hashtag"></i> Номер</th>
                        <th><i class="fas fa-user"></i> Клиент</th>
                        <th><i class="fas fa-tag"></i> Услуга</th>
                        <th><i class="fas fa-sort-numeric-up"></i> Брой</th>
                        <th><i class="fas fa-euro-sign"></i> Цена</th>
                        <th><i class="fas fa-calendar-times"></i> Краен срок</th>
                    </tr>
                </thead>
                <tbody>
                    @foreach (TaskViewModel task in Model.Tasks)
                    {
                        <tr class="task-row">
                            <td>
                                @if(task.status == "Приключена")
                                {
                                    <span class="task-status completed">@task.status</span>
                                }
                                else if(task.status == "В процес на изпълнение")
                                {
                                    <span class="task-status active">@task.status</span>
                                }
                                else
                                {
                                    <span class="task-status pending">@task.status</span>
                                }
                            </td>
                            <td>
                                <a class="task-number-link" asp-controller="GeoTask" asp-action="Edit" asp-route-id="@task.Id">
                                    <span class="task-number">Проект №@task.Number</span>
                                </a>
                            </td>
                            <td>
                                <div class="client-info">
                                    <i class="fas fa-building"></i>
                                    <span>@task.ClientName</span>
                                </div>
                            </td>
                            <td>
                                <div class="service-info">
                                    <i class="fas fa-cogs"></i>
                                    <span>@task.TaskType</span>
                                </div>
                            </td>
                            <td>
                                <span class="quantity-badge">@task.quantity бр.</span>
                            </td>
                            <td>
                                <span class="task-price">@((task.price * task.quantity).ToString("F2")) лв.</span>
                            </td>
                            <td>
                                @if (task.EndDate <= DateTime.UtcNow)
                                {
                                    <span class="task-deadline urgent">
                                        <i class="fas fa-exclamation-triangle"></i>
                                        @task.EndDate.ToString("dd/MM/yyyy")
                                    </span>
                                }
                                else if (task.EndDate.AddDays(-2) <= DateTime.UtcNow)
                                {
                                    <span class="task-deadline warning">
                                        <i class="fas fa-clock"></i>
                                        @task.EndDate.ToString("dd/MM/yyyy")
                                    </span>
                                }
                                else
                                {
                                    <span class="task-deadline normal">
                                        <i class="fas fa-calendar-check"></i>
                                        @task.EndDate.ToString("dd/MM/yyyy")
                                    </span>
                                }
                            </td>
                        </tr>
                    }
                </tbody>
            </table>
        </div>
    }
@{
    int previousPage = Model.CurrentPage - 1;
    if (previousPage < 1)
    {
        previousPage = 1;
    }
    int taskperPage = Model.TaskPerPage;
    int totaltask = Model.TotalTaskss;
    int maxPage = (int)Math.Ceiling((double)Model.TotalTaskss / Model.TaskPerPage);
}
<div class="row mb-5">
   <div class="col-md-6 d-grid gap-2 d-md-flex justify-content-md-start">
      <a class="btn btn-success @(Model.CurrentPage == 1 ? "disabled" :
      string.Empty)"
      asp-controller="GeoTask"
      asp-action="AllTasks"
      asp-route-currentPage="@previousPage"
      asp-route-TaskPerPage="@taskperPage"
      asp-route-type="@Model.Type"
      asp-route-searchTerm="@Model.SearchString">
      <<</a>
   </div>
    @{
        bool shouldNextPageBeDisabled = Model.CurrentPage == maxPage ||
        !Model.Tasks.Any();
    }
   <div class="col-md-6 d-grid gap-2 d-md-flex justify-content-md-end">
      <a class="btn btn-success
      @(shouldNextPageBeDisabled ? "disabled" : string.Empty)"
      asp-controller="GeoTask"
      asp-action="AllTasks"
      asp-route-currentPage="@(Model.CurrentPage + 1)"
      asp-route-TaskPerPage="@taskperPage"
      asp-route-type="@Model.Type"
      asp-route-searchTerm="@Model.SearchString">
        >></a>
   </div>
</div>

