namespace TaskManager.Data.Models
{
    using System.ComponentModel.DataAnnotations;
    using static TaskManager.Common.EntityValidationConstants.KanbanBoard;

    /// <summary>
    /// Represents a Kanban board for team collaboration
    /// </summary>
    public class KanbanBoard
    {
        public KanbanBoard()
        {
            this.Id = Guid.NewGuid();
            this.Columns = new HashSet<KanbanColumn>();
            this.CreatedOn = DateTime.UtcNow;
            this.IsActive = true;
        }

        [Key]
        public Guid Id { get; set; }

        [Required]
        [MaxLength(NameMaxLength)]
        public string Name { get; set; } = null!;

        [MaxLength(DescriptionMaxLength)]
        public string? Description { get; set; }

        [Required]
        public DateTime CreatedOn { get; set; }

        public DateTime? UpdatedOn { get; set; }

        [Required]
        public bool IsActive { get; set; }

        // Navigation properties
        public virtual ICollection<KanbanColumn> Columns { get; set; }
    }
}
