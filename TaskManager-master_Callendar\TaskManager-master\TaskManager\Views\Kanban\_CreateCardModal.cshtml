@model TaskManager.Web.ViewModels.Kanban.CreateKanbanCardViewModel

<div class="modal-header">
    <h5 class="modal-title">
        <i class="fas fa-plus-circle"></i>
        Нова карта
    </h5>
    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
</div>

<form id="createCardForm">
    <div class="modal-body">
        @Html.AntiForgeryToken()
        
        <input type="hidden" asp-for="ColumnId" />
        <input type="hidden" asp-for="GeoTaskId" />

        <div class="mb-3">
            <label asp-for="Title" class="form-label">Заглавие</label>
            <input asp-for="Title" class="form-control" placeholder="Въведете заглавие на картата" />
            <span asp-validation-for="Title" class="text-danger"></span>
        </div>

        <div class="mb-3">
            <label asp-for="Description" class="form-label">Описание</label>
            <textarea asp-for="Description" class="form-control" rows="3" placeholder="Въведете описание (по избор)"></textarea>
            <span asp-validation-for="Description" class="text-danger"></span>
        </div>

        <div class="row">
            <div class="col-md-6">
                <div class="mb-3">
                    <label asp-for="AssignedToId" class="form-label">Възложено на</label>
                    <select asp-for="AssignedToId" class="form-select">
                        <option value="">-- Изберете работник --</option>
                        @foreach (var worker in ViewBag.Workers as IEnumerable<TaskManager.Web.ViewModels.Admin.AllWorkersViewModel>)
                        {
                            <option value="@worker.UserId">@worker.FirstName @worker.LastName</option>
                        }
                    </select>
                </div>
            </div>
            <div class="col-md-6">
                <div class="mb-3">
                    <label asp-for="DueDate" class="form-label">Краен срок</label>
                    <input asp-for="DueDate" type="datetime-local" class="form-control" />
                </div>
            </div>
        </div>

        <div class="row">
            <div class="col-md-6">
                <div class="mb-3">
                    <label asp-for="Color" class="form-label">Цвят</label>
                    <select asp-for="Color" class="form-select">
                        <option value="">-- По подразбиране --</option>
                        <option value="#3b82f6" style="background-color: #3b82f6; color: white;">Син</option>
                        <option value="#10b981" style="background-color: #10b981; color: white;">Зелен</option>
                        <option value="#f59e0b" style="background-color: #f59e0b; color: white;">Оранжев</option>
                        <option value="#ef4444" style="background-color: #ef4444; color: white;">Червен</option>
                        <option value="#8b5cf6" style="background-color: #8b5cf6; color: white;">Лилав</option>
                        <option value="#6b7280" style="background-color: #6b7280; color: white;">Сив</option>
                    </select>
                </div>
            </div>
            <div class="col-md-6">
                <div class="mb-3">
                    <label asp-for="Labels" class="form-label">Етикети</label>
                    <input asp-for="Labels" class="form-control" placeholder="Етикет1, Етикет2, ..." />
                    <small class="form-text text-muted">Разделете етикетите със запетая</small>
                </div>
            </div>
        </div>
    </div>

    <div class="modal-footer">
        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">
            <i class="fas fa-times"></i>
            Отказ
        </button>
        <button type="submit" class="btn btn-primary">
            <i class="fas fa-save"></i>
            Създай карта
        </button>
    </div>
</form>

<script>
$(document).ready(function() {
    $('#createCardForm').on('submit', function(e) {
        e.preventDefault();

        var formData = {
            Title: $('#Title').val(),
            Description: $('#Description').val(),
            ColumnId: $('#ColumnId').val(),
            AssignedToId: $('#AssignedToId').val(),
            DueDate: $('#DueDate').val(),
            Color: $('#Color').val(),
            Labels: $('#Labels').val(),
            GeoTaskId: $('#GeoTaskId').val(),
            __RequestVerificationToken: $('input[name="__RequestVerificationToken"]').val()
        };

        // Check if a worker is assigned
        if (formData.AssignedToId) {
            // Get worker name for display
            var workerName = $('#AssignedToId option:selected').text();

            // Show column selection modal
            showColumnSelectionModal(formData.AssignedToId, workerName, formData);
        } else {
            // Create card directly on team board
            $.post('@Url.Action("CreateCard", "Kanban")', formData)
                .done(function(response) {
                    if (response.success) {
                        $('#addCardModal').modal('hide');
                        location.reload(); // Refresh the board
                        toastr.success('Картата беше създадена успешно');
                    } else {
                        toastr.error(response.message || 'Грешка при създаването на картата');
                    }
                })
                .fail(function() {
                    toastr.error('Грешка при създаването на картата');
                });
        }
    });
});
</script>
