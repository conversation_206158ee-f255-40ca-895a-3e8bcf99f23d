﻿@using TaskManager.Services.Data.Interfaces;
@using TaskManager.Web.Infrastructure.Extentions;
@using static TaskManager.Common.NotificationMessages;

@model UserViewModel
@inject IUserService UserService;
@{
    bool isUserWorker = await this.UserService.IsUserWorkerByIdAsync(Model.Id);
    bool isUserAdmin = await this.UserService.IsUserAdminByIdAsync(Model.Id);
    ViewData["Title"] = "Редактиране на потребител";
}

<div class="modern-container">
    <div class="modern-page-header">
        <h1 class="modern-page-title">
            <i class="fas fa-user-edit"></i>
            Редактиране на потребител
        </h1>
        <p class="modern-page-subtitle">
            Управление на потребителски профил и права
        </p>
    </div>

    <div class="modern-card">
        <div class="modern-card-header">
            <h3>
                <i class="fas fa-id-card"></i>
                Лични данни
            </h3>
            <div class="user-status-badges">
                @if (isUserAdmin)
                {
                    <span class="status-badge admin">
                        <i class="fas fa-crown"></i>
                        Администратор
                    </span>
                }
                @if (isUserWorker)
                {
                    <span class="status-badge worker">
                        <i class="fas fa-hard-hat"></i>
                        Работник
                    </span>
                }
                @if (!isUserWorker && !isUserAdmin)
                {
                    <span class="status-badge user">
                        <i class="fas fa-user"></i>
                        Потребител
                    </span>
                }
            </div>
        </div>
        <div class="modern-card-body">
            <form method="post" class="modern-user-form">
                <div class="user-form-grid">
                    <div class="form-group">
                        <label asp-for="FirstName" class="modern-form-label">
                            <i class="fas fa-user"></i>
                            Име <span class="required">*</span>
                        </label>
                        <input asp-for="FirstName" class="modern-form-control" id="FirstName" placeholder="Въведете име..."/>
                        <span asp-validation-for="FirstName" class="modern-validation-error"></span>
                    </div>

                    <div class="form-group">
                        <label asp-for="LastName" class="modern-form-label">
                            <i class="fas fa-user"></i>
                            Фамилия <span class="required">*</span>
                        </label>
                        <input asp-for="LastName" class="modern-form-control" id="LastName" placeholder="Въведете фамилия..."/>
                        <span asp-validation-for="LastName" class="modern-validation-error"></span>
                    </div>

                    <div class="form-group full-width">
                        <label asp-for="Email" class="modern-form-label">
                            <i class="fas fa-envelope"></i>
                            Имейл адрес <span class="required">*</span>
                        </label>
                        <input asp-for="Email" type="email" class="modern-form-control" id="Email" placeholder="<EMAIL>"/>
                        <span asp-validation-for="Email" class="modern-validation-error"></span>
                    </div>
                </div>

                @if (!isUserAdmin)
                {
                    <div class="admin-actions-section">
                        <h4>
                            <i class="fas fa-cogs"></i>
                            Административни действия
                        </h4>

                        <div class="action-buttons">
                            <button type="submit" class="modern-btn modern-btn-primary">
                                <i class="fas fa-save"></i>
                                Запиши промените
                            </button>

                            <button type="button" class="modern-btn modern-btn-info GenerateDataForUser">
                                <i class="fas fa-sync-alt"></i>
                                Генериране на нови данни
                            </button>

                            @if (!isUserWorker)
                            {
                                <a class="modern-btn modern-btn-warning" asp-controller="Admin" asp-action="MakeWorker" asp-route-id="@Model.Id">
                                    <i class="fas fa-user-plus"></i>
                                    Направи работник
                                </a>
                            }
                            else
                            {
                                <a class="modern-btn modern-btn-success" asp-controller="Admin" asp-action="PersonalFile" asp-route-id="@Model.Id">
                                    <i class="fas fa-folder-open"></i>
                                    Личен файл
                                </a>
                            }
                        </div>
                    </div>
                }

                <div class="form-actions">
                    <a asp-controller="Admin" asp-action="AllUsers" class="modern-btn modern-btn-secondary">
                        <i class="fas fa-arrow-left"></i>
                        Назад към потребителите
                    </a>
                </div>
            </form>
        </div>
    </div>
</div>

@section Scripts
{
    <partial name="_ValidationScriptsPartial">

    <script>
        $(document).ready(function() {
            $('.GenerateDataForUser').click(function(e) {
                e.preventDefault();

                if (confirm('Сигурни ли сте, че искате да генерирате нови данни за този потребител?')) {
                    // Generate random data
                    const firstNames = ['Георги', 'Иван', 'Петър', 'Мария', 'Анна', 'Елена', 'Димитър', 'Стефан'];
                    const lastNames = ['Георгиев', 'Петров', 'Иванов', 'Димитров', 'Стефанов', 'Николов', 'Христов'];

                    const randomFirstName = firstNames[Math.floor(Math.random() * firstNames.length)];
                    const randomLastName = lastNames[Math.floor(Math.random() * lastNames.length)];
                    const randomEmail = randomFirstName.toLowerCase() + '.' + randomLastName.toLowerCase() + '@@company.bg';

                    $('#FirstName').val(randomFirstName);
                    $('#LastName').val(randomLastName);
                    $('#Email').val(randomEmail);

                    // Add visual feedback
                    $('.modern-form-control').addClass('updated');
                    setTimeout(() => {
                        $('.modern-form-control').removeClass('updated');
                    }, 1000);
                }
            });
        });
    </script>
}

