using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;
using Moq;
using System.Security.Claims;
using TaskManager.Controllers;
using TaskManager.Data;
using TaskManager.Data.Models;
using TaskManager.Services.Data;
using TaskManager.Services.Data.Interfaces;
using TaskManager.Web.ViewModels.Kanban;
using NUnit.Framework;

namespace TaskManager.Services.Tests
{
    [TestFixture]
    public class KanbanControllerTests
    {
        private TaskManagerDbContext dbContext;
        private Mock<IKanbanService> mockKanbanService;
        private Mock<IUserService> mockUserService;
        private KanbanController controller;

        [SetUp]
        public void Setup()
        {
            var options = new DbContextOptionsBuilder<TaskManagerDbContext>()
                .UseInMemoryDatabase(databaseName: Guid.NewGuid().ToString())
                .Options;

            dbContext = new TaskManagerDbContext(options);
            mockKanbanService = new Mock<IKanbanService>();
            mockUserService = new Mock<IUserService>();
            
            controller = new KanbanController(mockKanbanService.Object, mockUserService.Object);
            
            // Setup user context
            SetupUserContext();
        }

        private void SetupUserContext()
        {
            var user = new ClaimsPrincipal(new ClaimsIdentity(new Claim[]
            {
                new Claim(ClaimTypes.Name, "<EMAIL>"),
                new Claim(ClaimTypes.NameIdentifier, Guid.NewGuid().ToString())
            }, "mock"));

            controller.ControllerContext = new ControllerContext()
            {
                HttpContext = new DefaultHttpContext() { User = user }
            };
        }

        [Test]
        public async Task TeamBoard_ShouldRedirectToCurrentUserBoard()
        {
            // Arrange
            var userId = Guid.NewGuid().ToString();
            var workers = new List<TaskManager.Web.ViewModels.Admin.AllWorkersViewModel>
            {
                new TaskManager.Web.ViewModels.Admin.AllWorkersViewModel
                {
                    Id = Guid.NewGuid().ToString(),
                    UserId = userId,
                    Email = "<EMAIL>",
                    FirstName = "Test",
                    LastName = "User"
                }
            };

            mockUserService.Setup(s => s.GetAllWorkersAsync())
                .ReturnsAsync(workers);

            // Act
            var result = await controller.TeamBoard();

            // Assert
            Assert.IsInstanceOf<RedirectToActionResult>(result);
            var redirectResult = (RedirectToActionResult)result;
            Assert.AreEqual("Board", redirectResult.ActionName);
            Assert.AreEqual(userId, redirectResult.RouteValues["memberId"]);
        }

        [Test]
        public async Task TeamBoard_ShouldRedirectToLogin_WhenUserNotAuthenticated()
        {
            // Arrange
            controller.ControllerContext.HttpContext.User = new ClaimsPrincipal();

            // Act
            var result = await controller.TeamBoard();

            // Assert
            Assert.IsInstanceOf<RedirectToActionResult>(result);
            var redirectResult = (RedirectToActionResult)result;
            Assert.AreEqual("Login", redirectResult.ActionName);
            Assert.AreEqual("Account", redirectResult.ControllerName);
        }

        [Test]
        public async Task Board_ShouldReturnView_WhenValidMemberIdProvided()
        {
            // Arrange
            var userId = Guid.NewGuid().ToString();
            var workerId = Guid.NewGuid().ToString();
            var boardViewModel = new TaskManager.Web.ViewModels.Kanban.KanbanBoardViewModel
            {
                Id = Guid.NewGuid().ToString(),
                Name = "Test Board",
                Description = "Test Description",
                Columns = new List<TaskManager.Web.ViewModels.Kanban.KanbanColumnViewModel>()
            };

            var workers = new List<TaskManager.Web.ViewModels.Admin.AllWorkersViewModel>
            {
                new TaskManager.Web.ViewModels.Admin.AllWorkersViewModel
                {
                    Id = workerId,
                    UserId = userId,
                    FirstName = "Test",
                    LastName = "User"
                }
            };

            mockKanbanService.Setup(s => s.InitializeDefaultBoardAsync())
                .Returns(Task.CompletedTask);

            mockKanbanService.Setup(s => s.GetMemberBoardByUserIdAsync(userId))
                .ReturnsAsync(boardViewModel);

            mockUserService.Setup(s => s.GetAllWorkersAsync())
                .ReturnsAsync(workers);

            // Act
            var result = await controller.Board(userId);

            // Assert
            Assert.IsInstanceOf<ViewResult>(result);
            var viewResult = (ViewResult)result;
            Assert.AreEqual(boardViewModel, viewResult.Model);
            Assert.AreEqual(userId, viewResult.ViewData["CurrentMemberId"]);
            Assert.AreEqual("Test User", viewResult.ViewData["CurrentMemberName"]);
        }

        [Test]
        public async Task CreateCard_ShouldReturnJson_WhenValidModelProvided()
        {
            // Arrange
            var createModel = new CreateKanbanCardViewModel
            {
                Title = "Test Card",
                ColumnId = Guid.NewGuid().ToString()
            };

            var cardId = Guid.NewGuid().ToString();
            mockKanbanService.Setup(s => s.CreateCardAsync(createModel))
                .ReturnsAsync(cardId);

            // Act
            var result = await controller.CreateCard(createModel);

            // Assert
            Assert.IsInstanceOf<JsonResult>(result);
            var jsonResult = (JsonResult)result;
            Assert.IsNotNull(jsonResult.Value);

            Assert.IsTrue(GetJsonProperty<bool>(jsonResult, "success"));
            Assert.AreEqual(cardId, GetJsonProperty<string>(jsonResult, "cardId"));
        }

        [Test]
        public async Task CreateCard_ShouldReturnError_WhenModelStateInvalid()
        {
            // Arrange
            var createModel = new CreateKanbanCardViewModel();
            controller.ModelState.AddModelError("Title", "Title is required");

            // Act
            var result = await controller.CreateCard(createModel);

            // Assert
            Assert.IsInstanceOf<JsonResult>(result);
            var jsonResult = (JsonResult)result;
            Assert.IsNotNull(jsonResult.Value);

            Assert.IsFalse(GetJsonProperty<bool>(jsonResult, "success"));
            Assert.AreEqual("Невалидни данни", GetJsonProperty<string>(jsonResult, "message"));
        }

        [Test]
        public async Task UpdateCard_ShouldReturnJson_WhenValidModelProvided()
        {
            // Arrange
            var updateModel = new UpdateKanbanCardViewModel
            {
                Id = Guid.NewGuid().ToString(),
                Title = "Updated Card"
            };

            mockKanbanService.Setup(s => s.UpdateCardAsync(updateModel))
                .Returns(Task.CompletedTask);

            // Act
            var result = await controller.UpdateCard(updateModel);

            // Assert
            Assert.IsInstanceOf<JsonResult>(result);
            var jsonResult = (JsonResult)result;
            Assert.IsNotNull(jsonResult.Value);

            Assert.IsTrue(GetJsonProperty<bool>(jsonResult, "success"));
        }

        [Test]
        public async Task UpdateCard_ShouldReturnError_WhenExceptionThrown()
        {
            // Arrange
            var updateModel = new UpdateKanbanCardViewModel
            {
                Id = Guid.NewGuid().ToString(),
                Title = "Updated Card"
            };

            mockKanbanService.Setup(s => s.UpdateCardAsync(updateModel))
                .ThrowsAsync(new InvalidOperationException("Card not found"));

            // Act
            var result = await controller.UpdateCard(updateModel);

            // Assert
            Assert.IsInstanceOf<JsonResult>(result);
            var jsonResult = (JsonResult)result;
            Assert.IsNotNull(jsonResult.Value);

            Assert.IsFalse(GetJsonProperty<bool>(jsonResult, "success"));
            Assert.AreEqual("Card not found", GetJsonProperty<string>(jsonResult, "message"));
        }

        [Test]
        public async Task DeleteCard_ShouldReturnJson_WhenValidCardIdProvided()
        {
            // Arrange
            var cardId = Guid.NewGuid().ToString();
            
            mockKanbanService.Setup(s => s.DeleteCardAsync(cardId))
                .Returns(Task.CompletedTask);

            // Act
            var result = await controller.DeleteCard(cardId);

            // Assert
            Assert.IsInstanceOf<JsonResult>(result);
            var jsonResult = (JsonResult)result;
            Assert.IsNotNull(jsonResult.Value);

            Assert.IsTrue(GetJsonProperty<bool>(jsonResult, "success"));
        }

        [Test]
        public async Task MoveCard_ShouldReturnJson_WhenValidRequestProvided()
        {
            // Arrange
            var moveRequest = new TaskManager.Controllers.MoveCardRequest
            {
                CardId = Guid.NewGuid().ToString(),
                NewColumnId = Guid.NewGuid().ToString(),
                NewPosition = 1
            };

            mockKanbanService.Setup(s => s.MoveCardAsync(moveRequest.CardId, moveRequest.NewColumnId, moveRequest.NewPosition))
                .Returns(Task.CompletedTask);

            // Act
            var result = await controller.MoveCard(moveRequest);

            // Assert
            Assert.IsInstanceOf<JsonResult>(result);
            var jsonResult = (JsonResult)result;
            Assert.IsNotNull(jsonResult.Value);

            Assert.IsTrue(GetJsonProperty<bool>(jsonResult, "success"));
        }

        [Test]
        public async Task CreateCardModal_ShouldReturnPartialView()
        {
            // Arrange
            var columnId = Guid.NewGuid().ToString();
            var workers = new List<TaskManager.Web.ViewModels.Admin.AllWorkersViewModel>();
            
            mockUserService.Setup(s => s.GetAllWorkersAsync())
                .ReturnsAsync(workers);

            // Act
            var result = await controller.CreateCardModal(columnId);

            // Assert
            Assert.IsInstanceOf<PartialViewResult>(result);
            var partialViewResult = (PartialViewResult)result;
            Assert.AreEqual("_CreateCardModal", partialViewResult.ViewName);

            Assert.IsInstanceOf<CreateKanbanCardViewModel>(partialViewResult.Model);
            var model = (CreateKanbanCardViewModel)partialViewResult.Model;
            Assert.AreEqual(columnId, model.ColumnId);
        }

        [Test]
        public async Task EditCardModal_ShouldReturnPartialView_WhenCardExists()
        {
            // Arrange
            var cardId = Guid.NewGuid();
            var updateModel = new UpdateKanbanCardViewModel
            {
                Id = cardId.ToString(),
                Title = "Test Card"
            };
            
            var workers = new List<TaskManager.Web.ViewModels.Admin.AllWorkersViewModel>();
            
            mockKanbanService.Setup(s => s.GetCardByIdAsync(cardId.ToString()))
                .ReturnsAsync(new KanbanCardViewModel { Id = cardId.ToString(), Title = "Test Card" });
                
            mockUserService.Setup(s => s.GetAllWorkersAsync())
                .ReturnsAsync(workers);

            // Act
            var result = await controller.EditCardModal(cardId.ToString());

            // Assert
            Assert.IsInstanceOf<PartialViewResult>(result);
            var partialViewResult = (PartialViewResult)result;
            Assert.AreEqual("_EditCardModal", partialViewResult.ViewName);

            Assert.IsInstanceOf<UpdateKanbanCardViewModel>(partialViewResult.Model);
            var model = (UpdateKanbanCardViewModel)partialViewResult.Model;
            Assert.AreEqual(cardId.ToString(), model.Id);
            Assert.AreEqual("Test Card", model.Title);
        }

        [TearDown]
        public void TearDown()
        {
            dbContext.Dispose();
        }

        // Helper method to extract properties from anonymous objects in JsonResult
        private T GetJsonProperty<T>(JsonResult jsonResult, string propertyName)
        {
            var resultType = jsonResult.Value.GetType();
            var property = resultType.GetProperty(propertyName);
            Assert.IsNotNull(property, $"Property '{propertyName}' not found in JSON result");
            return (T)property.GetValue(jsonResult.Value);
        }
    }
}
