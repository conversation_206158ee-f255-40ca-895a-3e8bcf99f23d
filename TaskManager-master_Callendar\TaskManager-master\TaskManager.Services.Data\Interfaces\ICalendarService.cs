﻿using TaskManager.Web.ViewModels.Calendar;

namespace TaskManager.Services.Data.Interfaces
{
    public interface ICalendarService
    {
        Task<IEnumerable<CalendarWorkerViewModel>> GetAllWorkersAsync();
        Task<IEnumerable<CalendarTaskViewModel>> GetTasksAsync(DateTime? startDate = null, DateTime? endDate = null);
        Task<CalendarTaskViewModel> CreateTaskAsync(CreateCalendarTaskViewModel model);
        Task<CalendarTaskViewModel> UpdateTaskAsync(string taskId, UpdateCalendarTaskViewModel model);
        Task<bool> DeleteTaskAsync(string taskId);
        Task<bool> TaskExistsAsync(string taskId);

        // New methods for GeoTask integration
        Task<CalendarTaskViewModel> CreateTaskFromGeoTaskAsync(string geoTaskId, CreateCalendarTaskViewModel model);
        Task<bool> IsGeoTaskLinkedToCalendarAsync(string geoTaskId);
        Task<string?> GetCalendarTaskIdByGeoTaskIdAsync(string geoTaskId);
        Task<bool> UnlinkGeoTaskFromCalendarAsync(string geoTaskId);

        // Worker color management
        Task UpdateWorkerColorAsync(string workerId, string color);
    }
}
