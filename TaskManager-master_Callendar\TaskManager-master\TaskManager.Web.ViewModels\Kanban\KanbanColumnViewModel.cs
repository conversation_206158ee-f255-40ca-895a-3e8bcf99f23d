namespace TaskManager.Web.ViewModels.Kanban
{
    /// <summary>
    /// View model for displaying a Kanban column
    /// </summary>
    public class KanbanColumnViewModel
    {
        public KanbanColumnViewModel()
        {
            this.Cards = new List<KanbanCardViewModel>();
        }

        public string Id { get; set; } = null!;

        public string Name { get; set; } = null!;

        public string? Description { get; set; }

        public int Position { get; set; }

        public string? Color { get; set; }

        public List<KanbanCardViewModel> Cards { get; set; }

        public int CardCount => Cards.Count;
    }
}
