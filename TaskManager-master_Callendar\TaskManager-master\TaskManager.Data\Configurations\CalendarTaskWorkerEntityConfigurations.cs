namespace TaskManager.Data.Configurations
{
    using Microsoft.EntityFrameworkCore;
    using Microsoft.EntityFrameworkCore.Metadata.Builders;
    using TaskManager.Data.Models;

    public class CalendarTaskWorkerEntityConfigurations : IEntityTypeConfiguration<CalendarTaskWorker>
    {
        public void Configure(EntityTypeBuilder<CalendarTaskWorker> builder)
        {
            // Configure composite primary key
            builder
                .HasKey(ctw => new { ctw.CalendarTaskId, ctw.WorkerId });

            // Configure relationship with CalendarTask
            builder
                .HasOne(ctw => ctw.CalendarTask)
                .WithMany(ct => ct.AssignedWorkers)
                .HasForeignKey(ctw => ctw.CalendarTaskId)
                .OnDelete(DeleteBehavior.Cascade);

            // Configure relationship with Worker
            builder
                .HasOne(ctw => ctw.Worker)
                .WithMany(w => w.CalendarTasks)
                .HasForeignKey(ctw => ctw.WorkerId)
                .OnDelete(DeleteBehavior.Cascade);

            // Configure properties
            builder
                .Property(ctw => ctw.AssignedDate)
                .HasDefaultValueSql("GETUTCDATE()");

            // Index for better performance
            builder
                .HasIndex(ctw => ctw.CalendarTaskId)
                .HasDatabaseName("IX_CalendarTaskWorker_CalendarTaskId");

            builder
                .HasIndex(ctw => ctw.WorkerId)
                .HasDatabaseName("IX_CalendarTaskWorker_WorkerId");
        }
    }
}
