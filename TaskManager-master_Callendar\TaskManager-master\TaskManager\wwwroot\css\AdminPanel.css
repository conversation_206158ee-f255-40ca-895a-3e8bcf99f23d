/* Admin Panel Styles */
.admin-panel-container {
    min-height: 100vh;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    padding: 2rem 1rem;
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
}

/* Header Section */
.admin-header {
    text-align: center;
    margin-bottom: 3rem;
    color: white;
}

.admin-header-content {
    max-width: 600px;
    margin: 0 auto;
}

.admin-title {
    font-size: 3rem;
    font-weight: 700;
    margin-bottom: 1rem;
    text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.3);
}

.admin-title i {
    margin-right: 1rem;
    color: #ffd700;
}

.admin-subtitle {
    font-size: 1.2rem;
    opacity: 0.9;
    margin: 0;
}

/* Quick Stats Section */
.admin-stats {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 1.5rem;
    margin-bottom: 3rem;
    max-width: 1200px;
    margin-left: auto;
    margin-right: auto;
}

.stat-card {
    background: rgba(255, 255, 255, 0.95);
    border-radius: 15px;
    padding: 1.5rem;
    display: flex;
    align-items: center;
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.2);
    transition: transform 0.3s ease, box-shadow 0.3s ease;
}

.stat-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 12px 40px rgba(0, 0, 0, 0.15);
}

.stat-icon {
    width: 60px;
    height: 60px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-right: 1rem;
    font-size: 1.5rem;
    color: white;
}

.stat-icon.users {
    background: linear-gradient(45deg, #4facfe, #00f2fe);
}

.stat-icon.workers {
    background: linear-gradient(45deg, #43e97b, #38f9d7);
}

.stat-icon.tasks {
    background: linear-gradient(45deg, #fa709a, #fee140);
}

.stat-content h3 {
    margin: 0 0 0.5rem 0;
    font-size: 1.3rem;
    color: #333;
    font-weight: 600;
}

.stat-content p {
    margin: 0;
    color: #666;
    font-size: 0.9rem;
}

/* Main Admin Grid */
.admin-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
    gap: 2rem;
    max-width: 1400px;
    margin: 0 auto;
}

/* Admin Cards */
.admin-card {
    background: rgba(255, 255, 255, 0.95);
    border-radius: 20px;
    overflow: hidden;
    box-shadow: 0 10px 40px rgba(0, 0, 0, 0.1);
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.2);
    transition: transform 0.3s ease, box-shadow 0.3s ease;
}

.admin-card:hover {
    transform: translateY(-8px);
    box-shadow: 0 20px 60px rgba(0, 0, 0, 0.15);
}

.card-header {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    padding: 2rem;
    text-align: center;
    position: relative;
    overflow: hidden;
}

.card-header::before {
    content: '';
    position: absolute;
    top: -50%;
    left: -50%;
    width: 200%;
    height: 200%;
    background: radial-gradient(circle, rgba(255, 255, 255, 0.1) 0%, transparent 70%);
    animation: shimmer 3s infinite;
}

@keyframes shimmer {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

.card-icon {
    width: 80px;
    height: 80px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0 auto 1rem auto;
    font-size: 2rem;
    color: white;
    position: relative;
    z-index: 1;
}

.users-icon {
    background: linear-gradient(45deg, #4facfe, #00f2fe);
}

.tasks-icon {
    background: linear-gradient(45deg, #fa709a, #fee140);
}

.services-icon {
    background: linear-gradient(45deg, #a8edea, #fed6e3);
}

.calendar-icon {
    background: linear-gradient(45deg, #ffecd2, #fcb69f);
}

.reports-icon {
    background: linear-gradient(45deg, #a8caba, #5d4e75);
}

.settings-icon {
    background: linear-gradient(45deg, #667eea, #764ba2);
}

.card-header h3 {
    margin: 0;
    font-size: 1.5rem;
    font-weight: 600;
    position: relative;
    z-index: 1;
}

.card-content {
    padding: 2rem;
}

.card-content p {
    color: #666;
    line-height: 1.6;
    margin-bottom: 2rem;
    font-size: 1rem;
}

.card-actions {
    display: flex;
    flex-direction: column;
    gap: 1rem;
}

/* Admin Buttons */
.admin-btn {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    padding: 1rem 1.5rem;
    border-radius: 12px;
    text-decoration: none;
    font-weight: 600;
    font-size: 1rem;
    transition: all 0.3s ease;
    border: none;
    cursor: pointer;
    position: relative;
    overflow: hidden;
}

.admin-btn i {
    margin-right: 0.5rem;
    font-size: 1.1rem;
}

.admin-btn.primary {
    background: linear-gradient(45deg, #667eea, #764ba2);
    color: white;
    box-shadow: 0 4px 15px rgba(102, 126, 234, 0.4);
}

.admin-btn.primary:hover {
    background: linear-gradient(45deg, #5a6fd8, #6a4190);
    transform: translateY(-2px);
    box-shadow: 0 6px 20px rgba(102, 126, 234, 0.6);
    color: white;
}

.admin-btn.secondary {
    background: linear-gradient(45deg, #f093fb, #f5576c);
    color: white;
    box-shadow: 0 4px 15px rgba(240, 147, 251, 0.4);
}

.admin-btn.secondary:hover {
    background: linear-gradient(45deg, #e681f0, #e94560);
    transform: translateY(-2px);
    box-shadow: 0 6px 20px rgba(240, 147, 251, 0.6);
    color: white;
}

/* Responsive Design */
@media (max-width: 768px) {
    .admin-panel-container {
        padding: 1rem 0.5rem;
    }
    
    .admin-title {
        font-size: 2rem;
    }
    
    .admin-grid {
        grid-template-columns: 1fr;
        gap: 1.5rem;
    }
    
    .admin-stats {
        grid-template-columns: 1fr;
        gap: 1rem;
    }
    
    .card-header {
        padding: 1.5rem;
    }
    
    .card-content {
        padding: 1.5rem;
    }
    
    .card-actions {
        gap: 0.75rem;
    }
    
    .admin-btn {
        padding: 0.875rem 1.25rem;
        font-size: 0.9rem;
    }
}

@media (max-width: 480px) {
    .admin-title {
        font-size: 1.75rem;
    }
    
    .admin-subtitle {
        font-size: 1rem;
    }
    
    .stat-card {
        padding: 1rem;
    }
    
    .stat-icon {
        width: 50px;
        height: 50px;
        font-size: 1.25rem;
    }
    
    .card-icon {
        width: 60px;
        height: 60px;
        font-size: 1.5rem;
    }
}

/* Animation for page load */
.admin-card {
    animation: fadeInUp 0.6s ease-out;
}

.admin-card:nth-child(1) { animation-delay: 0.1s; }
.admin-card:nth-child(2) { animation-delay: 0.2s; }
.admin-card:nth-child(3) { animation-delay: 0.3s; }
.admin-card:nth-child(4) { animation-delay: 0.4s; }
.admin-card:nth-child(5) { animation-delay: 0.5s; }
.admin-card:nth-child(6) { animation-delay: 0.6s; }

@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(30px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

/* Admin Pages Styles */
.admin-page-container {
    min-height: 100vh;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    padding: 2rem 1rem;
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
}

.admin-page-header {
    text-align: center;
    margin-bottom: 3rem;
    color: white;
}

.page-header-content {
    max-width: 800px;
    margin: 0 auto;
}

.back-btn {
    display: inline-flex;
    align-items: center;
    padding: 0.75rem 1.5rem;
    background: rgba(255, 255, 255, 0.2);
    color: white;
    text-decoration: none;
    border-radius: 25px;
    margin-bottom: 2rem;
    transition: all 0.3s ease;
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.3);
}

.back-btn:hover {
    background: rgba(255, 255, 255, 0.3);
    transform: translateY(-2px);
    color: white;
    text-decoration: none;
}

.back-btn i {
    margin-right: 0.5rem;
}

.page-title {
    font-size: 2.5rem;
    font-weight: 700;
    margin-bottom: 1rem;
    text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.3);
}

.page-title i {
    margin-right: 1rem;
    color: #ffd700;
}

.page-subtitle {
    font-size: 1.1rem;
    opacity: 0.9;
    margin: 0;
}

.admin-content {
    max-width: 1200px;
    margin: 0 auto;
}

/* Table Styles */
.users-table-container {
    background: rgba(255, 255, 255, 0.95);
    border-radius: 20px;
    overflow: hidden;
    box-shadow: 0 10px 40px rgba(0, 0, 0, 0.1);
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.2);
}

.table-header {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    padding: 2rem;
    text-align: center;
}

.table-header h3 {
    margin: 0;
    font-size: 1.5rem;
    font-weight: 600;
}

.table-header i {
    margin-right: 0.5rem;
    color: #ffd700;
}

.modern-table-wrapper {
    overflow-x: auto;
}

.modern-table {
    width: 100%;
    border-collapse: collapse;
    background: white;
}

.modern-table thead tr {
    background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
}

.modern-table th {
    padding: 1.5rem 1rem;
    text-align: left;
    font-weight: 600;
    color: #495057;
    border-bottom: 2px solid #dee2e6;
    font-size: 1rem;
}

.modern-table th i {
    margin-right: 0.5rem;
    color: #667eea;
}

.modern-table tbody tr {
    transition: all 0.3s ease;
    border-bottom: 1px solid #f1f3f4;
}

.modern-table tbody tr:hover {
    background: linear-gradient(135deg, #f8f9ff 0%, #f0f2ff 100%);
    transform: scale(1.01);
    box-shadow: 0 4px 12px rgba(102, 126, 234, 0.1);
}

.modern-table td {
    padding: 1.25rem 1rem;
    vertical-align: middle;
}

.user-name {
    display: flex;
    align-items: center;
    font-weight: 600;
    color: #333;
}

.user-avatar {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    background: linear-gradient(45deg, #667eea, #764ba2);
    display: flex;
    align-items: center;
    justify-content: center;
    margin-right: 1rem;
    color: white;
    font-size: 1.2rem;
}

.user-email {
    color: #666;
    font-size: 0.95rem;
}

.user-actions {
    text-align: right;
}

.action-btn {
    display: inline-flex;
    align-items: center;
    padding: 0.5rem 1rem;
    border-radius: 8px;
    text-decoration: none;
    font-weight: 500;
    font-size: 0.9rem;
    transition: all 0.3s ease;
    margin: 0 0.25rem;
}

.action-btn i {
    margin-right: 0.5rem;
}

.action-btn.edit {
    background: linear-gradient(45deg, #28a745, #20c997);
    color: white;
}

.action-btn.edit:hover {
    background: linear-gradient(45deg, #218838, #1ea080);
    transform: translateY(-2px);
    color: white;
    text-decoration: none;
}

.action-btn.delete {
    background: linear-gradient(45deg, #dc3545, #e74c3c);
    color: white;
}

.action-btn.delete:hover {
    background: linear-gradient(45deg, #c82333, #dc2626);
    transform: translateY(-2px);
    color: white;
    text-decoration: none;
}

/* Responsive Design for Tables */
@media (max-width: 768px) {
    .admin-page-container {
        padding: 1rem 0.5rem;
    }

    .page-title {
        font-size: 2rem;
    }

    .modern-table th,
    .modern-table td {
        padding: 1rem 0.75rem;
    }

    .user-name {
        flex-direction: column;
        align-items: flex-start;
    }

    .user-avatar {
        margin-bottom: 0.5rem;
        margin-right: 0;
    }

    .user-actions {
        text-align: left;
    }

    .action-btn {
        display: block;
        margin: 0.25rem 0;
        text-align: center;
    }
}

@media (max-width: 480px) {
    .page-title {
        font-size: 1.75rem;
    }

    .page-subtitle {
        font-size: 1rem;
    }

    .table-header {
        padding: 1.5rem;
    }

    .modern-table th,
    .modern-table td {
        padding: 0.75rem 0.5rem;
        font-size: 0.9rem;
    }
}

/* Worker-specific styles */
.worker-phone {
    color: #666;
    font-family: 'Courier New', monospace;
    font-weight: 500;
}

.worker-position {
    text-align: center;
}

.position-badge {
    display: inline-block;
    padding: 0.5rem 1rem;
    background: linear-gradient(45deg, #28a745, #20c997);
    color: white;
    border-radius: 20px;
    font-size: 0.85rem;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

/* Additional button styles */
.action-btn.view {
    background: linear-gradient(45deg, #007bff, #0056b3);
    color: white;
}

.action-btn.view:hover {
    background: linear-gradient(45deg, #0056b3, #004085);
    transform: translateY(-2px);
    color: white;
    text-decoration: none;
}

/* Loading animation */
.loading-spinner {
    display: inline-block;
    width: 20px;
    height: 20px;
    border: 3px solid rgba(255, 255, 255, 0.3);
    border-radius: 50%;
    border-top-color: #fff;
    animation: spin 1s ease-in-out infinite;
}

@keyframes spin {
    to { transform: rotate(360deg); }
}

/* Success/Error messages */
.alert-modern {
    padding: 1rem 1.5rem;
    border-radius: 12px;
    margin-bottom: 2rem;
    border: none;
    font-weight: 500;
}

.alert-modern.success {
    background: linear-gradient(45deg, #28a745, #20c997);
    color: white;
}

.alert-modern.error {
    background: linear-gradient(45deg, #dc3545, #e74c3c);
    color: white;
}

.alert-modern.warning {
    background: linear-gradient(45deg, #ffc107, #fd7e14);
    color: white;
}

/* Empty state */
.empty-state {
    text-align: center;
    padding: 4rem 2rem;
    color: #666;
}

.empty-state i {
    font-size: 4rem;
    color: #ddd;
    margin-bottom: 1rem;
}

.empty-state h3 {
    color: #999;
    margin-bottom: 1rem;
}

.empty-state p {
    color: #666;
    font-size: 1.1rem;
}
