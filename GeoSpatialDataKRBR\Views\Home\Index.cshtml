﻿@{
    ViewData["Title"] = "Геопространствени данни КККР";
    Layout = null;
}

<!DOCTYPE html>
<html lang="bg">
<head>
    <meta charset="utf-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>@ViewData["Title"]</title>
    <link rel="stylesheet" href="~/lib/bootstrap/dist/css/bootstrap.min.css" />
    <link rel="stylesheet" href="~/css/site.css" asp-append-version="true" />
    <link rel="stylesheet" href="https://unpkg.com/leaflet@1.9.4/dist/leaflet.css" />
    <style>
        body {
            margin: 0;
            padding: 0;
            height: 100vh;
            overflow: hidden;
        }

        .hero-section {
            position: relative;
            height: 100vh;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
        }

        .hero-content {
            text-align: center;
            z-index: 2;
            position: relative;
        }

        .hero-map {
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            opacity: 0.3;
            z-index: 1;
        }

        .login-overlay {
            position: absolute;
            top: 20px;
            right: 20px;
            z-index: 3;
        }

        .hero-title {
            font-size: 4rem;
            font-weight: 700;
            margin-bottom: 20px;
            text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.5);
        }

        .hero-subtitle {
            font-size: 1.5rem;
            margin-bottom: 40px;
            opacity: 0.9;
        }

        .hero-description {
            font-size: 1.1rem;
            margin-bottom: 40px;
            max-width: 600px;
            margin-left: auto;
            margin-right: auto;
            opacity: 0.8;
        }

        .btn-hero {
            padding: 15px 40px;
            font-size: 1.2rem;
            font-weight: 600;
            border-radius: 30px;
            background: rgba(255, 255, 255, 0.2);
            border: 2px solid rgba(255, 255, 255, 0.3);
            color: white;
            transition: all 0.3s ease;
            backdrop-filter: blur(10px);
        }

        .btn-hero:hover {
            background: rgba(255, 255, 255, 0.3);
            border-color: rgba(255, 255, 255, 0.5);
            transform: translateY(-3px);
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.3);
            color: white;
        }

        .features {
            position: absolute;
            bottom: 40px;
            left: 50%;
            transform: translateX(-50%);
            display: flex;
            gap: 40px;
            z-index: 2;
        }

        .feature {
            text-align: center;
            opacity: 0.8;
        }

        .feature-icon {
            font-size: 2rem;
            margin-bottom: 10px;
        }

        .feature-text {
            font-size: 0.9rem;
        }
    </style>
</head>
<body>
    <div class="hero-section">
        <!-- Login Overlay -->
        <div class="login-overlay">
            <a href="/Identity/Account/Login" class="btn btn-outline-light">
                <i class="fas fa-sign-in-alt"></i> Вход
            </a>
        </div>

        <!-- Background Map -->
        <div class="hero-map" id="background-map"></div>

        <!-- Hero Content -->
        <div class="hero-content">
            <h1 class="hero-title">Геопространствени данни</h1>
            <p class="hero-subtitle">КККР - Комисия за защита на конкуренцията</p>
            <p class="hero-description">
                Интерактивна платформа за визуализация и анализ на геопространствени данни.
                Достъп до WMS/WFS слоеве, интерактивни карти и мощни инструменти за анализ.
            </p>
            <a href="/Identity/Account/Login" class="btn btn-hero">
                <i class="fas fa-map"></i> Започнете сега
            </a>
        </div>

        <!-- Features -->
        <div class="features">
            <div class="feature">
                <div class="feature-icon">🗺️</div>
                <div class="feature-text">Интерактивни карти</div>
            </div>
            <div class="feature">
                <div class="feature-icon">📊</div>
                <div class="feature-text">Анализ на данни</div>
            </div>
            <div class="feature">
                <div class="feature-icon">🔒</div>
                <div class="feature-text">Сигурен достъп</div>
            </div>
            <div class="feature">
                <div class="feature-icon">⚡</div>
                <div class="feature-text">Бърза визуализация</div>
            </div>
        </div>
    </div>

    <script src="~/lib/jquery/dist/jquery.min.js"></script>
    <script src="~/lib/bootstrap/dist/js/bootstrap.bundle.min.js"></script>
    <script src="https://unpkg.com/leaflet@1.9.4/dist/leaflet.js"></script>
    <script src="https://kit.fontawesome.com/your-fontawesome-kit.js" crossorigin="anonymous"></script>

    <script>
        // Initialize background map
        const map = L.map('background-map', {
            zoomControl: false,
            attributionControl: false,
            dragging: false,
            touchZoom: false,
            doubleClickZoom: false,
            scrollWheelZoom: false,
            boxZoom: false,
            keyboard: false
        }).setView([42.7339, 25.4858], 7);

        // Add OpenStreetMap tiles
        L.tileLayer('https://{s}.tile.openstreetmap.org/{z}/{x}/{y}.png', {
            attribution: '',
            opacity: 0.6
        }).addTo(map);

        // Add some sample markers for Bulgaria
        const cities = [
            {name: 'София', coords: [42.6977, 23.3219]},
            {name: 'Пловдив', coords: [42.1354, 24.7453]},
            {name: 'Варна', coords: [43.2141, 27.9147]},
            {name: 'Бургас', coords: [42.5048, 27.4626]}
        ];

        cities.forEach(city => {
            L.circleMarker(city.coords, {
                radius: 8,
                fillColor: '#fff',
                color: '#667eea',
                weight: 2,
                opacity: 0.8,
                fillOpacity: 0.6
            }).addTo(map);
        });

        // Animate map view
        let currentIndex = 0;
        setInterval(() => {
            const city = cities[currentIndex];
            map.setView(city.coords, 8, {animate: true, duration: 2});
            currentIndex = (currentIndex + 1) % cities.length;
        }, 4000);
    </script>
</body>
</html>
