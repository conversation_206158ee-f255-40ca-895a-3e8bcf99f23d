import React, { useState } from 'react'
import { Eye, EyeOff, Info } from 'lucide-react'

const LayerControl = ({ 
  layers, 
  baseLayers, 
  userPreferences, 
  onToggleVisibility, 
  onUpdateOpacity 
}) => {
  const [expandedSections, setExpandedSections] = useState({
    baseLayers: true,
    overlayLayers: true
  })

  const toggleSection = (section) => {
    setExpandedSections(prev => ({
      ...prev,
      [section]: !prev[section]
    }))
  }

  const getLayerVisibility = (layer) => {
    const userPref = userPreferences[layer.id]
    return userPref ? userPref.isVisible : layer.isVisible
  }

  const getLayerOpacity = (layer) => {
    const userPref = userPreferences[layer.id]
    return userPref?.opacity ?? layer.opacity ?? 1.0
  }

  const handleOpacityChange = (layerId, opacity) => {
    onUpdateOpacity(layerId, parseFloat(opacity))
  }

  const LayerItem = ({ layer, showOpacity = true }) => {
    const isVisible = getLayerVisibility(layer)
    const opacity = getLayerOpacity(layer)

    return (
      <div className="layer-item">
        <div className="layer-header">
          <button
            className="visibility-toggle"
            onClick={() => onToggleVisibility(layer.id)}
            title={isVisible ? 'Скрий слоя' : 'Покажи слоя'}
          >
            {isVisible ? <Eye size={16} /> : <EyeOff size={16} />}
          </button>
          
          <div className="layer-info">
            <div className="layer-name">{layer.name}</div>
            {layer.description && (
              <div className="layer-description">{layer.description}</div>
            )}
            <div className="layer-details">
              <span className="layer-type">{layer.layerType}</span>
              {layer.workspace && (
                <span className="layer-workspace">• {layer.workspace}:{layer.layerName}</span>
              )}
            </div>
          </div>

          <button
            className="info-button"
            title="Информация за слоя"
            onClick={() => {
              // TODO: Show layer info modal
              console.log('Layer info:', layer)
            }}
          >
            <Info size={14} />
          </button>
        </div>

        {showOpacity && isVisible && (
          <div className="opacity-control">
            <span>Прозрачност:</span>
            <input
              type="range"
              min="0"
              max="1"
              step="0.1"
              value={opacity}
              onChange={(e) => handleOpacityChange(layer.id, e.target.value)}
              className="opacity-slider"
            />
            <span className="opacity-value">{Math.round(opacity * 100)}%</span>
          </div>
        )}
      </div>
    )
  }

  return (
    <div className="layer-control">
      <div className="layer-control-header">
        <h3>Слоеве</h3>
      </div>

      {/* Base Layers Section */}
      {baseLayers.length > 0 && (
        <div className="layer-section">
          <div 
            className="section-header"
            onClick={() => toggleSection('baseLayers')}
          >
            <h4>Базови слоеве</h4>
            <span className={`expand-icon ${expandedSections.baseLayers ? 'expanded' : ''}`}>
              ▼
            </span>
          </div>
          
          {expandedSections.baseLayers && (
            <div className="section-content">
              {baseLayers.map(layer => (
                <LayerItem 
                  key={layer.id} 
                  layer={layer} 
                  showOpacity={false}
                />
              ))}
            </div>
          )}
        </div>
      )}

      {/* Overlay Layers Section */}
      {layers.length > 0 && (
        <div className="layer-section">
          <div 
            className="section-header"
            onClick={() => toggleSection('overlayLayers')}
          >
            <h4>Тематични слоеве</h4>
            <span className={`expand-icon ${expandedSections.overlayLayers ? 'expanded' : ''}`}>
              ▼
            </span>
          </div>
          
          {expandedSections.overlayLayers && (
            <div className="section-content">
              {layers
                .sort((a, b) => {
                  // Sort by display order, then by name
                  const aOrder = userPreferences[a.id]?.displayOrder ?? a.displayOrder
                  const bOrder = userPreferences[b.id]?.displayOrder ?? b.displayOrder
                  
                  if (aOrder !== bOrder) {
                    return aOrder - bOrder
                  }
                  
                  return a.name.localeCompare(b.name, 'bg')
                })
                .map(layer => (
                  <LayerItem 
                    key={layer.id} 
                    layer={layer} 
                    showOpacity={true}
                  />
                ))}
            </div>
          )}
        </div>
      )}

      {/* Empty State */}
      {layers.length === 0 && baseLayers.length === 0 && (
        <div className="empty-state">
          <p>Няма налични слоеве</p>
        </div>
      )}

      {/* Layer Control Footer */}
      <div className="layer-control-footer">
        <div className="layer-count">
          Общо слоеве: {layers.length + baseLayers.length}
        </div>
        <div className="visible-count">
          Видими: {layers.filter(getLayerVisibility).length + baseLayers.filter(getLayerVisibility).length}
        </div>
      </div>
    </div>
  )
}

export default LayerControl
