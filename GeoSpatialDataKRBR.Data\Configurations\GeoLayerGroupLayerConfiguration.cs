namespace GeoSpatialDataKRBR.Data.Configurations
{
    using Microsoft.EntityFrameworkCore;
    using Microsoft.EntityFrameworkCore.Metadata.Builders;
    using GeoSpatialDataKRBR.Data.Models;

    public class GeoLayerGroupLayerConfiguration : IEntityTypeConfiguration<GeoLayerGroupLayer>
    {
        public void Configure(EntityTypeBuilder<GeoLayerGroupLayer> builder)
        {
            builder.HasKey(glgl => new { glgl.GeoLayerGroupId, glgl.GeoLayerId });

            // Removed foreign key constraints for easier debugging
        }
    }
}
