namespace GeoSpatialDataKRBR.Data.Configurations
{
    using Microsoft.EntityFrameworkCore;
    using Microsoft.EntityFrameworkCore.Metadata.Builders;
    using GeoSpatialDataKRBR.Data.Models;

    public class GeoLayerGroupLayerConfiguration : IEntityTypeConfiguration<GeoLayerGroupLayer>
    {
        public void Configure(EntityTypeBuilder<GeoLayerGroupLayer> builder)
        {
            builder.HasKey(glgl => new { glgl.GeoLayerGroupId, glgl.GeoLayerId });

            builder.HasOne(glgl => glgl.GeoLayerGroup)
                .WithMany(glg => glg.GeoLayerGroupLayers)
                .HasForeignKey(glgl => glgl.GeoLayerGroupId)
                .OnDelete(DeleteBehavior.Cascade);

            builder.HasOne(glgl => glgl.GeoLayer)
                .WithMany(gl => gl.GeoLayerGroupLayers)
                .HasForeignKey(glgl => glgl.GeoLayerId)
                .OnDelete(DeleteBehavior.Cascade);
        }
    }
}
