namespace GeoSpatialDataKRBR.Web.ViewModels.GeoLayer
{
    using System.ComponentModel.DataAnnotations;
    using static Common.EntityValidationConstants.GeoLayer;

    public class GeoLayerViewModel
    {
        public Guid Id { get; set; }

        [Required(ErrorMessage = "Името на слоя е задължително")]
        [StringLength(NameMaxLength, MinimumLength = NameMinLength, 
            ErrorMessage = "Името трябва да бъде между {2} и {1} символа")]
        [Display(Name = "Име на слоя")]
        public string Name { get; set; } = null!;

        [StringLength(DescriptionMaxLength, 
            ErrorMessage = "Описанието не може да бъде повече от {1} символа")]
        [Display(Name = "Описание")]
        public string? Description { get; set; }

        [Required(ErrorMessage = "Името на слоя в GeoServer е задължително")]
        [StringLength(LayerNameMaxLength, MinimumLength = LayerNameMinLength,
            ErrorMessage = "Името на слоя трябва да бъде между {2} и {1} символа")]
        [Display(Name = "Име на слоя в GeoServer")]
        public string LayerName { get; set; } = null!;

        [Required(ErrorMessage = "Workspace е задължителен")]
        [StringLength(WorkspaceMaxLength, MinimumLength = WorkspaceMinLength,
            ErrorMessage = "Workspace трябва да бъде между {2} и {1} символа")]
        [Display(Name = "Workspace")]
        public string Workspace { get; set; } = null!;

        [Required(ErrorMessage = "WMS URL е задължителен")]
        [StringLength(WmsUrlMaxLength, ErrorMessage = "WMS URL не може да бъде повече от {1} символа")]
        [Url(ErrorMessage = "Невалиден WMS URL")]
        [Display(Name = "WMS URL")]
        public string WmsUrl { get; set; } = null!;

        [StringLength(WfsUrlMaxLength, ErrorMessage = "WFS URL не може да бъде повече от {1} символа")]
        [Url(ErrorMessage = "Невалиден WFS URL")]
        [Display(Name = "WFS URL")]
        public string? WfsUrl { get; set; }

        [StringLength(StyleNameMaxLength, ErrorMessage = "Името на стила не може да бъде повече от {1} символа")]
        [Display(Name = "Име на стила")]
        public string? StyleName { get; set; }

        [Required(ErrorMessage = "Типът на слоя е задължителен")]
        [Display(Name = "Тип на слоя")]
        public string LayerType { get; set; } = null!;

        [Display(Name = "Видим")]
        public bool IsVisible { get; set; } = true;

        [Display(Name = "Базов слой")]
        public bool IsBaseLayer { get; set; } = false;

        [Range(0, int.MaxValue, ErrorMessage = "Редът за показване трябва да бъде положително число")]
        [Display(Name = "Ред за показване")]
        public int DisplayOrder { get; set; } = 0;

        [Range(0.0, 1.0, ErrorMessage = "Прозрачността трябва да бъде между 0 и 1")]
        [Display(Name = "Прозрачност")]
        public double? Opacity { get; set; } = 1.0;

        [Required(ErrorMessage = "GeoServer конфигурацията е задължителна")]
        [Display(Name = "GeoServer конфигурация")]
        public Guid GeoServerConfigurationId { get; set; }

        [Display(Name = "GeoServer конфигурация")]
        public string? GeoServerConfigurationName { get; set; }

        [Display(Name = "Дата на създаване")]
        public DateTime CreatedOn { get; set; }

        [Display(Name = "Дата на промяна")]
        public DateTime? ModifiedOn { get; set; }
    }
}
