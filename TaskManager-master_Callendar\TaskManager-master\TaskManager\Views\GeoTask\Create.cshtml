@model EditGeoTaskViewModel;
@using TaskManager.Services.Data.Interfaces;

@inject IWorkerService WorkerService;
@inject IStatusService StatusService;
@inject IClientService ClientService;
@{
	ViewData["Title"] = "Създаване на нова задача";
}

@section Styles {
	<link rel="stylesheet" href="~/css/ModernDesign.css" />
}

<div class="modern-container">
	<!-- Project Header -->
	<div class="modern-project-header">
		<div class="project-info">
			<h1 class="project-title">
				<i class="fas fa-plus-circle"></i>
				Нова задача №@Model.ProjectNumber
			</h1>
			<p class="project-date">
				<i class="fas fa-calendar-alt"></i>
				Създавана на: @DateTime.Now.ToString("dd-MM-yyyy")
			</p>
		</div>
	</div>

	<form method="post" asp-action="Create" class="modern-project-form">
		<!-- Main Data Section -->
		<div class="modern-card">
			<div class="modern-card-header">
				<h3>
					<i class="fas fa-info-circle"></i>
					Основни данни
				</h3>
			</div>
			<div class="modern-card-body">
				<div class="modern-form-grid">
					<div class="form-group">
						<label asp-for="Adrress" class="modern-form-label">
							<i class="fas fa-map-marker-alt"></i>
							Адрес <span class="required">*</span>
						</label>
						<input asp-for="Adrress" class="modern-form-control" placeholder="Въведете адрес..."/>
						<span asp-validation-for="Adrress" class="modern-validation-error"></span>
					</div>

					<div class="form-group">
						<label asp-for="Price" class="modern-form-label">
							<i class="fas fa-money-bill-wave"></i>
							Цена <span class="required">*</span>
						</label>
						<input asp-for="Price" type="number" step="0.01" class="modern-form-control" placeholder="0.00"/>
						<span asp-validation-for="Price" class="modern-validation-error"></span>
					</div>

					<div class="form-group">
						<label asp-for="quantity" class="modern-form-label">
							<i class="fas fa-hashtag"></i>
							Количество <span class="required">*</span>
						</label>
						<input asp-for="quantity" type="number" class="modern-form-control" placeholder="1"/>
						<span asp-validation-for="quantity" class="modern-validation-error"></span>
					</div>

					<div class="form-group">
						<label asp-for="IdKKKR" class="modern-form-label">
							<i class="fas fa-id-card"></i>
							Идентификатор <span class="required">*</span>
						</label>
						<input asp-for="IdKKKR" class="modern-form-control" placeholder="Въведете идентификатор..."/>
						<span asp-validation-for="IdKKKR" class="modern-validation-error"></span>
					</div>

					<div class="form-group">
						<label asp-for="CreateDate" class="modern-form-label">
							<i class="fas fa-calendar-plus"></i>
							Създадена на <span class="required">*</span>
						</label>
						<input asp-for="CreateDate" type="date" class="modern-form-control"/>
						<span asp-validation-for="CreateDate" class="modern-validation-error"></span>
					</div>

					<div class="form-group">
						<label asp-for="EndDate" class="modern-form-label">
							<i class="fas fa-calendar-check"></i>
							Крайна дата <span class="required">*</span>
						</label>
						<input asp-for="EndDate" type="date" class="modern-form-control"/>
						<span asp-validation-for="EndDate" class="modern-validation-error"></span>
					</div>
				</div>
			</div>
		</div>

		<!-- Assignment Section -->
		<div class="modern-card">
			<div class="modern-card-header">
				<h3>
					<i class="fas fa-users"></i>
					Назначения
				</h3>
			</div>
			<div class="modern-card-body">
				<div class="modern-form-grid">
					<div class="form-group">
						<label asp-for="ClientId" class="modern-form-label">
							<i class="fas fa-building"></i>
							Клиент <span class="required">*</span>
						</label>
						<select asp-for="ClientId" class="modern-form-control">
							<option value="">Изберете клиент...</option>
							@foreach (var client in Model.Clients)
							{
								<option value="@client.Id">@client.Name</option>
							}
						</select>
						<span asp-validation-for="ClientId" class="modern-validation-error"></span>
					</div>

					<div class="form-group">
						<label asp-for="WorkerId" class="modern-form-label">
							<i class="fas fa-user-hard-hat"></i>
							Работник <span class="required">*</span>
						</label>
						<select asp-for="WorkerId" class="modern-form-control">
							<option value="">Изберете работник...</option>
							@foreach (var worker in Model.Workers)
							{
								<option value="@worker.Id">@worker.FirstName @worker.LastName</option>
							}
						</select>
						<span asp-validation-for="WorkerId" class="modern-validation-error"></span>
					</div>

					<div class="form-group">
						<label asp-for="CheckerId" class="modern-form-label">
							<i class="fas fa-user-check"></i>
							Проверяващ <span class="required">*</span>
						</label>
						<select asp-for="CheckerId" class="modern-form-control">
							<option value="">Изберете проверяващ...</option>
							@foreach (var checker in Model.Checkers)
							{
								<option value="@checker.Id">@checker.FirstName @checker.LastName</option>
							}
						</select>
						<span asp-validation-for="CheckerId" class="modern-validation-error"></span>
					</div>

					<div class="form-group">
						<label asp-for="StatusId" class="modern-form-label">
							<i class="fas fa-flag"></i>
							Статус <span class="required">*</span>
						</label>
						<select asp-for="StatusId" class="modern-form-control">
							@foreach (var status in Model.Statuses)
							{
								<option value="@status.Id">@status.Name</option>
							}
						</select>
						<span asp-validation-for="StatusId" class="modern-validation-error"></span>
					</div>

					<div class="form-group">
						<label asp-for="TypeId" class="modern-form-label">
							<i class="fas fa-tags"></i>
							Тип <span class="required">*</span>
						</label>
						<select asp-for="TypeId" class="modern-form-control">
							@foreach (var type in Model.Types)
							{
								<option value="@type.Id">@type.Name</option>
							}
						</select>
						<span asp-validation-for="TypeId" class="modern-validation-error"></span>
					</div>
				</div>
			</div>
		</div>

		<!-- Notes Section -->
		<div class="modern-card">
			<div class="modern-card-header">
				<h3>
					<i class="fas fa-sticky-note"></i>
					Бележки
				</h3>
			</div>
			<div class="modern-card-body">
				<div class="form-group">
					<label asp-for="Note" class="modern-form-label">
						<i class="fas fa-comment"></i>
						Бележка
					</label>
					<textarea asp-for="Note" class="modern-form-control" rows="4" placeholder="Въведете бележка..."></textarea>
					<span asp-validation-for="Note" class="modern-validation-error"></span>
				</div>
			</div>
		</div>

		<!-- Hidden fields -->
		<input asp-for="Id" type="hidden" />
		<input asp-for="ProjectNumber" type="hidden" />

		<!-- Form Actions -->
		<div class="modern-card">
			<div class="modern-card-body">
				<div class="form-actions">
					<button type="submit" name="Save" value="Save" class="modern-btn modern-btn-primary">
						<i class="fas fa-save"></i>
						Създай задача
					</button>
					<button type="submit" name="Discard" value="Discard" class="modern-btn modern-btn-danger">
						<i class="fas fa-times"></i>
						Отказ
					</button>
					<a class="modern-btn modern-btn-secondary" asp-controller="GeoTask" asp-action="AllTasks">
						<i class="fas fa-arrow-left"></i>
						Назад към списъка
					</a>
				</div>
			</div>
		</div>
	</form>
</div>

@section Scripts {
	<partial name="_ValidationScriptsPartial" />
}
