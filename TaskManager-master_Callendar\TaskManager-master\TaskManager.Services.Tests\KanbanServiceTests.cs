using Microsoft.EntityFrameworkCore;
using TaskManager.Data;
using TaskManager.Data.Models;
using TaskManager.Services.Data;
using TaskManager.Services.Data.Interfaces;
using TaskManager.Web.ViewModels.Kanban;
using NUnit.Framework;

namespace TaskManager.Services.Tests
{
    [TestFixture]
    public class KanbanServiceTests
    {
        private TaskManagerDbContext dbContext;
        private KanbanService kanbanService;

        [SetUp]
        public void Setup()
        {
            var options = new DbContextOptionsBuilder<TaskManagerDbContext>()
                .UseInMemoryDatabase(databaseName: Guid.NewGuid().ToString())
                .Options;

            dbContext = new TaskManagerDbContext(options);
            kanbanService = new KanbanService(dbContext);

            // Seed test data
            SeedTestData();
        }

        private void SeedTestData()
        {
            // Create test users
            var user1 = new ApplicationUser
            {
                Id = Guid.NewGuid(),
                Email = "<EMAIL>",
                FirstName = "John",
                LastName = "Doe",
                UserName = "<EMAIL>"
            };

            var user2 = new ApplicationUser
            {
                Id = Guid.NewGuid(),
                Email = "<EMAIL>",
                FirstName = "Jane",
                LastName = "Smith",
                UserName = "<EMAIL>"
            };

            dbContext.Users.AddRange(user1, user2);

            // Create test workers
            var worker1 = new Worker
            {
                Id = Guid.NewGuid(),
                UserId = user1.Id,
                Position = "Developer",
                PhoneNumber = "123456789",
                User = user1
            };

            var worker2 = new Worker
            {
                Id = Guid.NewGuid(),
                UserId = user2.Id,
                Position = "Designer",
                PhoneNumber = "987654321",
                User = user2
            };

            dbContext.Workers.AddRange(worker1, worker2);

            // Create test board
            var board = new KanbanBoard
            {
                Id = Guid.NewGuid(),
                Name = "Test Board",
                Description = "Test Description",
                IsActive = true,
                CreatedOn = DateTime.UtcNow,
                UpdatedOn = DateTime.UtcNow
            };

            dbContext.KanbanBoards.Add(board);

            // Create test columns
            var columns = new List<KanbanColumn>
            {
                new KanbanColumn
                {
                    Id = Guid.NewGuid(),
                    BoardId = board.Id,
                    Name = "Текущи",
                    Position = 1,
                    Color = "#3b82f6",
                    IsActive = true,
                    CreatedOn = DateTime.UtcNow,
                    UpdatedOn = DateTime.UtcNow
                },
                new KanbanColumn
                {
                    Id = Guid.NewGuid(),
                    BoardId = board.Id,
                    Name = "В процес на работа",
                    Position = 2,
                    Color = "#f59e0b",
                    IsActive = true,
                    CreatedOn = DateTime.UtcNow,
                    UpdatedOn = DateTime.UtcNow
                },
                new KanbanColumn
                {
                    Id = Guid.NewGuid(),
                    BoardId = board.Id,
                    Name = "Приключени",
                    Position = 3,
                    Color = "#10b981",
                    IsActive = true,
                    CreatedOn = DateTime.UtcNow,
                    UpdatedOn = DateTime.UtcNow
                }
            };

            dbContext.KanbanColumns.AddRange(columns);

            // Create test GeoTask
            var geoTask = new GeoTask
            {
                Id = Guid.NewGuid(),
                ProjectNumber = 1001,
                Adrress = "Test Address",
                Note = "Test Note",
                Price = 1000.00m,
                quantity = 5,
                CreateDate = DateTime.UtcNow,
                EndDate = DateTime.UtcNow.AddDays(30),
                WorkerId = worker1.Id,
                ClientId = Guid.NewGuid(),
                StatusId = 1,
                TypeId = 1,
                CheckerId = worker2.Id,
                IdKKKR = "KKKR001"
            };

            dbContext.GeoTasks.Add(geoTask);

            dbContext.SaveChanges();
        }

        [Test]
        public async Task CreateCardAsync_ShouldCreateCard_WhenValidDataProvided()
        {
            // Arrange
            // Get a user that has a corresponding worker record
            var worker = await dbContext.Workers.FirstAsync();
            var user = worker.User;

            // Ensure the worker's personal board exists
            var memberBoard = await kanbanService.GetMemberBoardAsync(worker.Id.ToString());
            var defaultColumn = await dbContext.KanbanColumns
                .FirstAsync(c => c.BoardId == Guid.Parse(memberBoard.Id) && c.Name == "Текущи" && c.IsActive);

            var createModel = new CreateKanbanCardViewModel
            {
                Title = "Test Card",
                Description = "Test Description",
                ColumnId = defaultColumn.Id.ToString(),
                AssignedToId = user.Id.ToString(),
                DueDate = DateTime.UtcNow.AddDays(7),
                Color = "#ff0000",
                Labels = "urgent,important"
            };

            // Act
            var cardIdString = await kanbanService.CreateCardAsync(createModel);
            var cardId = Guid.Parse(cardIdString);

            // Assert
            Assert.AreNotEqual(Guid.Empty, cardId);

            var createdCard = await dbContext.KanbanCards.FindAsync(cardId);
            Assert.IsNotNull(createdCard);
            Assert.AreEqual("Test Card", createdCard.Title);
            Assert.AreEqual("Test Description", createdCard.Description);
            Assert.AreEqual(defaultColumn.Id, createdCard.ColumnId);
            Assert.AreEqual(user.Id, createdCard.AssignedToId);
            Assert.AreEqual("#ff0000", createdCard.Color);
            Assert.AreEqual("urgent,important", createdCard.Labels);
            Assert.IsTrue(createdCard.IsActive);
        }

        [Test]
        public async Task CreateCardAsync_ShouldThrowException_WhenColumnNotFound()
        {
            // Arrange
            var createModel = new CreateKanbanCardViewModel
            {
                Title = "Test Card",
                ColumnId = Guid.NewGuid().ToString() // Non-existent column
            };

            // Act & Assert
            Assert.ThrowsAsync<ArgumentException>(
                async () => await kanbanService.CreateCardAsync(createModel));
        }

        [Test]
        public async Task CreateCardAsync_ShouldSetCorrectPosition_WhenAddingToColumn()
        {
            // Arrange
            var column = await dbContext.KanbanColumns.FirstAsync();
            
            // Create first card
            var firstCard = new CreateKanbanCardViewModel
            {
                Title = "First Card",
                ColumnId = column.Id.ToString()
            };
            
            var secondCard = new CreateKanbanCardViewModel
            {
                Title = "Second Card",
                ColumnId = column.Id.ToString()
            };

            // Act
            var firstCardIdString = await kanbanService.CreateCardAsync(firstCard);
            var secondCardIdString = await kanbanService.CreateCardAsync(secondCard);
            var firstCardId = Guid.Parse(firstCardIdString);
            var secondCardId = Guid.Parse(secondCardIdString);

            // Assert
            var firstCreatedCard = await dbContext.KanbanCards.FindAsync(firstCardId);
            var secondCreatedCard = await dbContext.KanbanCards.FindAsync(secondCardId);
            
            Assert.AreEqual(1, firstCreatedCard.Position);
            Assert.AreEqual(2, secondCreatedCard.Position);
        }

        [Test]
        public async Task UpdateCardAsync_ShouldUpdateCard_WhenValidDataProvided()
        {
            // Arrange
            var column = await dbContext.KanbanColumns.FirstAsync();
            var user = await dbContext.Users.FirstAsync();

            // Create a card first
            var createModel = new CreateKanbanCardViewModel
            {
                Title = "Original Title",
                Description = "Original Description",
                ColumnId = column.Id.ToString()
            };

            var cardIdString = await kanbanService.CreateCardAsync(createModel);
            var cardId = Guid.Parse(cardIdString);

            var updateModel = new UpdateKanbanCardViewModel
            {
                Id = cardIdString,
                Title = "Updated Title",
                Description = "Updated Description",
                ColumnId = column.Id.ToString(),
                AssignedToId = user.Id.ToString(),
                DueDate = DateTime.UtcNow.AddDays(10),
                Color = "#00ff00",
                Labels = "updated,modified"
            };

            // Act
            await kanbanService.UpdateCardAsync(updateModel);

            // Assert
            var updatedCard = await dbContext.KanbanCards.FindAsync(cardId);
            Assert.NotNull(updatedCard);
            Assert.AreEqual("Updated Title", updatedCard.Title);
            Assert.AreEqual("Updated Description", updatedCard.Description);
            Assert.AreEqual(user.Id, updatedCard.AssignedToId);
            Assert.AreEqual("#00ff00", updatedCard.Color);
            Assert.AreEqual("updated,modified", updatedCard.Labels);
        }

        [Test]
        public async Task UpdateCardAsync_ShouldThrowException_WhenCardNotFound()
        {
            // Arrange
            var updateModel = new UpdateKanbanCardViewModel
            {
                Id = Guid.NewGuid().ToString(), // Non-existent card
                Title = "Updated Title"
            };

            // Act & Assert
            Assert.ThrowsAsync<ArgumentException>(
                async () => await kanbanService.UpdateCardAsync(updateModel));
        }

        [Test]
        public async Task MoveCardAsync_ShouldMoveCard_WhenValidDataProvided()
        {
            // Arrange
            var columns = await dbContext.KanbanColumns.OrderBy(c => c.Position).ToListAsync();
            var sourceColumn = columns[0];
            var targetColumn = columns[1];

            // Create a card in source column
            var createModel = new CreateKanbanCardViewModel
            {
                Title = "Card to Move",
                ColumnId = sourceColumn.Id.ToString()
            };

            var cardIdString = await kanbanService.CreateCardAsync(createModel);
            var cardId = Guid.Parse(cardIdString);

            // Act
            await kanbanService.MoveCardAsync(cardIdString, targetColumn.Id.ToString(), 1);

            // Assert
            var movedCard = await dbContext.KanbanCards.FindAsync(cardId);
            Assert.IsNotNull(movedCard);
            Assert.AreEqual(targetColumn.Id, movedCard.ColumnId);
            Assert.AreEqual(1, movedCard.Position);
        }

        [Test]
        public async Task MoveCardAsync_ShouldUpdatePositions_WhenMovingBetweenColumns()
        {
            // Arrange
            var columns = await dbContext.KanbanColumns.OrderBy(c => c.Position).ToListAsync();
            var sourceColumn = columns[0];
            var targetColumn = columns[1];

            // Create cards in target column
            var targetCard1 = new CreateKanbanCardViewModel
            {
                Title = "Target Card 1",
                ColumnId = targetColumn.Id.ToString()
            };
            var targetCard2 = new CreateKanbanCardViewModel
            {
                Title = "Target Card 2",
                ColumnId = targetColumn.Id.ToString()
            };

            await kanbanService.CreateCardAsync(targetCard1);
            await kanbanService.CreateCardAsync(targetCard2);

            // Create card to move
            var moveCard = new CreateKanbanCardViewModel
            {
                Title = "Card to Move",
                ColumnId = sourceColumn.Id.ToString()
            };

            var cardIdString = await kanbanService.CreateCardAsync(moveCard);

            // Act - Move to position 2 in target column
            await kanbanService.MoveCardAsync(cardIdString, targetColumn.Id.ToString(), 2);

            // Assert
            var cardsInTargetColumn = await dbContext.KanbanCards
                .Where(c => c.ColumnId == targetColumn.Id && c.IsActive)
                .OrderBy(c => c.Position)
                .ToListAsync();

            Assert.AreEqual(3, cardsInTargetColumn.Count);
            Assert.AreEqual("Target Card 1", cardsInTargetColumn[0].Title);
            Assert.AreEqual("Card to Move", cardsInTargetColumn[1].Title);
            Assert.AreEqual("Target Card 2", cardsInTargetColumn[2].Title);

            // Check positions
            Assert.AreEqual(1, cardsInTargetColumn[0].Position);
            Assert.AreEqual(2, cardsInTargetColumn[1].Position);
            Assert.AreEqual(3, cardsInTargetColumn[2].Position);
        }

        [Test]
        public async Task DeleteCardAsync_ShouldSoftDeleteCard_WhenValidIdProvided()
        {
            // Arrange
            var column = await dbContext.KanbanColumns.FirstAsync();
            var createModel = new CreateKanbanCardViewModel
            {
                Title = "Card to Delete",
                ColumnId = column.Id.ToString()
            };

            var cardIdString = await kanbanService.CreateCardAsync(createModel);
            var cardId = Guid.Parse(cardIdString);

            // Act
            await kanbanService.DeleteCardAsync(cardIdString);

            // Assert
            var deletedCard = await dbContext.KanbanCards.FindAsync(cardId);
            Assert.IsNotNull(deletedCard);
            Assert.IsFalse(deletedCard.IsActive); // Soft delete
        }

        [Test]
        public async Task DeleteCardAsync_ShouldThrowException_WhenCardNotFound()
        {
            // Arrange
            var nonExistentCardId = Guid.NewGuid().ToString();

            // Act & Assert
            Assert.ThrowsAsync<ArgumentException>(
                async () => await kanbanService.DeleteCardAsync(nonExistentCardId));
        }

        [Test]
        public async Task CreateCardForGeoTaskAsync_ShouldCreateCard_WhenGeoTaskExists()
        {
            // Arrange
            var geoTask = await dbContext.GeoTasks.FirstAsync();
            var worker = await dbContext.Workers.FirstAsync(w => w.Id == geoTask.WorkerId);

            // Ensure member board exists
            await kanbanService.GetMemberBoardAsync(worker.Id.ToString());

            // Act - Pass UserId instead of WorkerId
            await kanbanService.CreateCardForGeoTaskAsync(geoTask.Id.ToString(), $"Проект #{geoTask.ProjectNumber}", worker.UserId.ToString());

            // Assert
            var createdCard = await dbContext.KanbanCards
                .FirstOrDefaultAsync(c => c.GeoTaskId == geoTask.Id);

            Assert.IsNotNull(createdCard);
            Assert.AreEqual($"Проект #{geoTask.ProjectNumber}", createdCard.Title);
            Assert.IsNull(createdCard.Description); // Description is not set by CreateCardForGeoTaskAsync
            Assert.AreEqual(worker.UserId, createdCard.AssignedToId);
            Assert.IsTrue(createdCard.IsActive);
        }

        [Test]
        public async Task CreateCardForGeoTaskAsync_ShouldPlaceCardInCurrentColumn()
        {
            // Arrange
            var geoTask = await dbContext.GeoTasks.FirstAsync();
            var worker = await dbContext.Workers.FirstAsync(w => w.Id == geoTask.WorkerId);

            // Ensure member board exists
            var board = await kanbanService.GetMemberBoardAsync(worker.Id.ToString());

            // Act - Pass UserId instead of WorkerId
            await kanbanService.CreateCardForGeoTaskAsync(geoTask.Id.ToString(), $"Проект #{geoTask.ProjectNumber}", worker.UserId.ToString());

            // Assert
            var createdCard = await dbContext.KanbanCards
                .FirstOrDefaultAsync(c => c.GeoTaskId == geoTask.Id);

            Assert.IsNotNull(createdCard);

            // Should be in "Текущи" column of the worker's board
            var currentColumn = await dbContext.KanbanColumns
                .FirstAsync(c => c.BoardId == Guid.Parse(board.Id) && c.Name == "Текущи" && c.IsActive);
            Assert.AreEqual(currentColumn.Id, createdCard.ColumnId);
        }

        [Test]
        public async Task GetMemberBoardAsync_ShouldCreateBoard_WhenNotExists()
        {
            // Arrange
            var worker = await dbContext.Workers.FirstAsync();
            var expectedBoardName = $"{worker.User.FirstName} {worker.User.LastName} - Лична дъска";

            // Act
            var board = await kanbanService.GetMemberBoardAsync(worker.Id.ToString());

            // Assert
            Assert.IsNotNull(board);
            Assert.AreEqual(expectedBoardName, board.Name);
            Assert.AreEqual(6, board.Columns.Count); // Should have 6 default columns

            // Verify columns are created with correct names
            var columnNames = board.Columns.Select(c => c.Name).ToList();
            Assert.That(columnNames, Does.Contain("Текущи"));
            Assert.That(columnNames, Does.Contain("Чакащи"));
            Assert.That(columnNames, Does.Contain("В процес на работа"));
            Assert.That(columnNames, Does.Contain("За корекция"));
            Assert.That(columnNames, Does.Contain("За проверка"));
            Assert.That(columnNames, Does.Contain("Приключени"));
        }

        [Test]
        public async Task GetMemberBoardAsync_ShouldReturnExistingBoard_WhenExists()
        {
            // Arrange
            var worker = await dbContext.Workers.FirstAsync();

            // Create board first time
            var firstCall = await kanbanService.GetMemberBoardAsync(worker.Id.ToString());
            var originalBoardId = firstCall.Id;

            // Act - Get board second time
            var secondCall = await kanbanService.GetMemberBoardAsync(worker.Id.ToString());

            // Assert
            Assert.AreEqual(originalBoardId, secondCall.Id);
            Assert.AreEqual(firstCall.Name, secondCall.Name);
        }

        [Test]
        public async Task GetMemberBoardAsync_ShouldIncludeCards_WhenCardsExist()
        {
            // Arrange
            var worker = await dbContext.Workers.FirstAsync();
            var board = await kanbanService.GetMemberBoardAsync(worker.Id.ToString());

            // Create a card
            var column = board.Columns.First();
            var createModel = new CreateKanbanCardViewModel
            {
                Title = "Test Card",
                ColumnId = column.Id.ToString(),
                AssignedToId = worker.UserId.ToString()
            };

            var cardIdString = await kanbanService.CreateCardAsync(createModel);

            // Act
            var boardWithCards = await kanbanService.GetMemberBoardAsync(worker.Id.ToString());

            // Assert
            var columnWithCard = boardWithCards.Columns.First(c => c.Id.ToString() == column.Id.ToString());
            Assert.AreEqual(1, columnWithCard.Cards.Count);
            Assert.AreEqual("Test Card", columnWithCard.Cards.First().Title);
        }

        [Test]
        public async Task InitializeDefaultBoardAsync_ShouldCreateTeamBoard_WhenNotExists()
        {
            // Arrange - Remove any existing team board
            var existingBoard = await dbContext.KanbanBoards
                .FirstOrDefaultAsync(b => b.Name == "Екипна дъска" && b.IsActive);
            if (existingBoard != null)
            {
                dbContext.KanbanBoards.Remove(existingBoard);
                await dbContext.SaveChangesAsync();
            }

            // Act
            await kanbanService.InitializeDefaultBoardAsync();

            // Assert
            var teamBoard = await dbContext.KanbanBoards
                .FirstOrDefaultAsync(b => b.Name == "Екипна дъска" && b.IsActive);

            Assert.IsNotNull(teamBoard);

            var columns = await dbContext.KanbanColumns
                .Where(c => c.BoardId == teamBoard.Id && c.IsActive)
                .ToListAsync();

            Assert.AreEqual(6, columns.Count);
        }

        [Test]
        public async Task CreateCardAsync_ShouldHandleNullOptionalFields()
        {
            // Arrange
            var column = await dbContext.KanbanColumns.FirstAsync();

            var createModel = new CreateKanbanCardViewModel
            {
                Title = "Minimal Card",
                ColumnId = column.Id.ToString()
                // All other fields are null/empty
            };

            // Act
            var cardIdString = await kanbanService.CreateCardAsync(createModel);
            var cardId = Guid.Parse(cardIdString);

            // Assert
            var createdCard = await dbContext.KanbanCards.FindAsync(cardId);
            Assert.IsNotNull(createdCard);
            Assert.AreEqual("Minimal Card", createdCard.Title);
            Assert.IsNull(createdCard.Description);
            Assert.IsNull(createdCard.AssignedToId);
            Assert.IsNull(createdCard.DueDate);
            Assert.IsNull(createdCard.Color);
            Assert.IsNull(createdCard.Labels);
            Assert.IsNull(createdCard.GeoTaskId);
        }

        [TearDown]
        public void TearDown()
        {
            dbContext.Dispose();
        }
    }
}
