namespace GeoSpatialDataKRBR.Controllers.Api
{
    using Microsoft.AspNetCore.Authorization;
    using Microsoft.AspNetCore.Mvc;
    using Microsoft.EntityFrameworkCore;
    using GeoSpatialDataKRBR.Data;
    using GeoSpatialDataKRBR.Services.Data.Interfaces;

    [ApiController]
    [Route("api/[controller]")]
    [Authorize]
    public class GeoServerApiController : ControllerBase
    {
        private readonly IGeoServerService geoServerService;
        private readonly GeoSpatialDbContext dbContext;

        public GeoServerApiController(
            IGeoServerService geoServerService,
            GeoSpatialDbContext dbContext)
        {
            this.geoServerService = geoServerService;
            this.dbContext = dbContext;
        }

        [HttpGet("workspaces/{configId:guid}")]
        public async Task<IActionResult> GetWorkspaces(Guid configId)
        {
            try
            {
                var configuration = await this.dbContext.GeoServerConfigurations
                    .FirstOrDefaultAsync(c => c.Id == configId && c.IsActive);

                if (configuration == null)
                {
                    return NotFound(new { message = "GeoServer конфигурацията не беше намерена" });
                }

                var workspaces = await this.geoServerService.GetWorkspacesAsync(configuration);
                return Ok(workspaces);
            }
            catch (Exception ex)
            {
                return StatusCode(500, new { message = "Грешка при зареждане на workspace-овете", error = ex.Message });
            }
        }

        [HttpGet("layers/{configId:guid}/{workspace}")]
        public async Task<IActionResult> GetLayers(Guid configId, string workspace)
        {
            try
            {
                var configuration = await this.dbContext.GeoServerConfigurations
                    .FirstOrDefaultAsync(c => c.Id == configId && c.IsActive);

                if (configuration == null)
                {
                    return NotFound(new { message = "GeoServer конфигурацията не беше намерена" });
                }

                var layers = await this.geoServerService.GetLayersAsync(configuration, workspace);
                return Ok(layers);
            }
            catch (Exception ex)
            {
                return StatusCode(500, new { message = "Грешка при зареждане на слоевете", error = ex.Message });
            }
        }

        [HttpGet("capabilities/{configId:guid}/{layerName}")]
        public async Task<IActionResult> GetLayerCapabilities(Guid configId, string layerName)
        {
            try
            {
                var configuration = await this.dbContext.GeoServerConfigurations
                    .FirstOrDefaultAsync(c => c.Id == configId && c.IsActive);

                if (configuration == null)
                {
                    return NotFound(new { message = "GeoServer конфигурацията не беше намерена" });
                }

                var capabilities = await this.geoServerService.GetLayerCapabilitiesAsync(configuration, layerName);
                return Ok(new { capabilities });
            }
            catch (Exception ex)
            {
                return StatusCode(500, new { message = "Грешка при зареждане на capabilities", error = ex.Message });
            }
        }

        [HttpPost("feature-info/{configId:guid}/{layerName}")]
        public async Task<IActionResult> GetFeatureInfo(
            Guid configId, 
            string layerName,
            [FromBody] FeatureInfoRequest request)
        {
            try
            {
                var configuration = await this.dbContext.GeoServerConfigurations
                    .FirstOrDefaultAsync(c => c.Id == configId && c.IsActive);

                if (configuration == null)
                {
                    return NotFound(new { message = "GeoServer конфигурацията не беше намерена" });
                }

                var featureInfo = await this.geoServerService.GetFeatureInfoAsync(
                    configuration, 
                    layerName, 
                    request.X, 
                    request.Y, 
                    request.Srs, 
                    request.Width, 
                    request.Height, 
                    request.Bbox);

                return Ok(new { featureInfo });
            }
            catch (Exception ex)
            {
                return StatusCode(500, new { message = "Грешка при зареждане на feature info", error = ex.Message });
            }
        }

        [HttpPost("wfs-features/{configId:guid}/{layerName}")]
        public async Task<IActionResult> GetWfsFeatures(
            Guid configId, 
            string layerName,
            [FromBody] WfsRequest request)
        {
            try
            {
                var configuration = await this.dbContext.GeoServerConfigurations
                    .FirstOrDefaultAsync(c => c.Id == configId && c.IsActive);

                if (configuration == null)
                {
                    return NotFound(new { message = "GeoServer конфигурацията не беше намерена" });
                }

                var features = await this.geoServerService.GetWfsFeatureAsync(
                    configuration, 
                    layerName, 
                    request.Filter ?? "", 
                    request.MaxFeatures);

                return Ok(new { features });
            }
            catch (Exception ex)
            {
                return StatusCode(500, new { message = "Грешка при зареждане на WFS features", error = ex.Message });
            }
        }

        [HttpPost("test-connection/{configId:guid}")]
        public async Task<IActionResult> TestConnection(Guid configId)
        {
            try
            {
                var configuration = await this.dbContext.GeoServerConfigurations
                    .FirstOrDefaultAsync(c => c.Id == configId);

                if (configuration == null)
                {
                    return NotFound(new { message = "GeoServer конфигурацията не беше намерена" });
                }

                var isConnected = await this.geoServerService.TestConnectionAsync(configuration);
                
                if (isConnected)
                {
                    return Ok(new { message = "Връзката с GeoServer е успешна", connected = true });
                }
                else
                {
                    return BadRequest(new { message = "Връзката с GeoServer неуспешна", connected = false });
                }
            }
            catch (Exception ex)
            {
                return StatusCode(500, new { message = "Грешка при тестване на връзката", error = ex.Message });
            }
        }

        [HttpGet("map-image/{configId:guid}/{layerName}")]
        public async Task<IActionResult> GetMapImage(
            Guid configId, 
            string layerName,
            [FromQuery] string bbox,
            [FromQuery] int width = 256,
            [FromQuery] int height = 256,
            [FromQuery] string srs = "EPSG:4326",
            [FromQuery] string format = "image/png")
        {
            try
            {
                var configuration = await this.dbContext.GeoServerConfigurations
                    .FirstOrDefaultAsync(c => c.Id == configId && c.IsActive);

                if (configuration == null)
                {
                    return NotFound(new { message = "GeoServer конфигурацията не беше намерена" });
                }

                var imageBytes = await this.geoServerService.GetMapImageAsync(
                    configuration, 
                    layerName, 
                    bbox, 
                    width, 
                    height, 
                    srs, 
                    format);

                if (imageBytes.Length > 0)
                {
                    return File(imageBytes, format);
                }
                else
                {
                    return NotFound(new { message = "Изображението не беше намерено" });
                }
            }
            catch (Exception ex)
            {
                return StatusCode(500, new { message = "Грешка при зареждане на изображението", error = ex.Message });
            }
        }
    }

    public class FeatureInfoRequest
    {
        public double X { get; set; }
        public double Y { get; set; }
        public string Srs { get; set; } = "EPSG:4326";
        public int Width { get; set; } = 256;
        public int Height { get; set; } = 256;
        public string Bbox { get; set; } = null!;
    }

    public class WfsRequest
    {
        public string? Filter { get; set; }
        public int MaxFeatures { get; set; } = 100;
    }
}
