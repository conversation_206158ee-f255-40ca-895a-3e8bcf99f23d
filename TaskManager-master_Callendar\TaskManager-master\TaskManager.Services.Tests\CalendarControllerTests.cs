namespace TaskManager.Services.Tests
{
    using Microsoft.AspNetCore.Mvc;
    using Moq;
    using TaskManager.Controllers;
    using TaskManager.Services.Data.Interfaces;
    using TaskManager.Web.ViewModels.Calendar;

    public class CalendarControllerTests
    {
        private Mock<ICalendarService> mockCalendarService;
        private Mock<IUserService> mockUserService;
        private CalendarController controller;

        [SetUp]
        public void SetUp()
        {
            this.mockCalendarService = new Mock<ICalendarService>();
            this.mockUserService = new Mock<IUserService>();
            this.controller = new CalendarController(this.mockCalendarService.Object, this.mockUserService.Object);
        }

        [Test]
        public async Task UpdateWorkerColor_WithValidData_ShouldReturnOk()
        {
            // Arrange
            var workerId = Guid.NewGuid().ToString();
            var request = new UpdateWorkerColorRequest { Color = "#FF0000" };

            this.mockCalendarService
                .Setup(s => s.UpdateWorkerColorAsync(workerId, request.Color))
                .Returns(Task.CompletedTask);

            // Act
            var result = await this.controller.UpdateWorkerColor(workerId, request);

            // Assert
            Assert.That(result, Is.InstanceOf<OkObjectResult>());
            var okResult = result as OkObjectResult;
            Assert.That(okResult.Value, Is.Not.Null);

            // Verify service was called
            this.mockCalendarService.Verify(
                s => s.UpdateWorkerColorAsync(workerId, request.Color),
                Times.Once);
        }

        [Test]
        public async Task UpdateWorkerColor_WithEmptyWorkerId_ShouldReturnBadRequest()
        {
            // Arrange
            var workerId = "";
            var request = new UpdateWorkerColorRequest { Color = "#FF0000" };

            // Act
            var result = await this.controller.UpdateWorkerColor(workerId, request);

            // Assert
            Assert.That(result, Is.InstanceOf<BadRequestObjectResult>());
            var badRequestResult = result as BadRequestObjectResult;

            // Use reflection to access the anonymous object property
            var errorResponse = badRequestResult.Value;
            var errorProperty = errorResponse.GetType().GetProperty("error");
            var errorMessage = errorProperty?.GetValue(errorResponse)?.ToString();
            Assert.That(errorMessage, Is.EqualTo("Worker ID is required"));

            // Verify service was not called
            this.mockCalendarService.Verify(
                s => s.UpdateWorkerColorAsync(It.IsAny<string>(), It.IsAny<string>()),
                Times.Never);
        }

        [Test]
        public async Task UpdateWorkerColor_WithNullRequest_ShouldReturnBadRequest()
        {
            // Arrange
            var workerId = Guid.NewGuid().ToString();
            UpdateWorkerColorRequest request = null;

            // Act
            var result = await this.controller.UpdateWorkerColor(workerId, request);

            // Assert
            Assert.That(result, Is.InstanceOf<BadRequestObjectResult>());
            var badRequestResult = result as BadRequestObjectResult;

            // Use reflection to access the anonymous object property
            var errorResponse = badRequestResult.Value;
            var errorProperty = errorResponse.GetType().GetProperty("error");
            var errorMessage = errorProperty?.GetValue(errorResponse)?.ToString();
            Assert.That(errorMessage, Is.EqualTo("Color is required"));

            // Verify service was not called
            this.mockCalendarService.Verify(
                s => s.UpdateWorkerColorAsync(It.IsAny<string>(), It.IsAny<string>()),
                Times.Never);
        }

        [Test]
        public async Task UpdateWorkerColor_WithEmptyColor_ShouldReturnBadRequest()
        {
            // Arrange
            var workerId = Guid.NewGuid().ToString();
            var request = new UpdateWorkerColorRequest { Color = "" };

            // Act
            var result = await this.controller.UpdateWorkerColor(workerId, request);

            // Assert
            Assert.That(result, Is.InstanceOf<BadRequestObjectResult>());
            var badRequestResult = result as BadRequestObjectResult;

            // Use reflection to access the anonymous object property
            var errorResponse = badRequestResult.Value;
            var errorProperty = errorResponse.GetType().GetProperty("error");
            var errorMessage = errorProperty?.GetValue(errorResponse)?.ToString();
            Assert.That(errorMessage, Is.EqualTo("Color is required"));

            // Verify service was not called
            this.mockCalendarService.Verify(
                s => s.UpdateWorkerColorAsync(It.IsAny<string>(), It.IsAny<string>()),
                Times.Never);
        }

        [Test]
        public async Task UpdateWorkerColor_WhenServiceThrowsException_ShouldReturnBadRequest()
        {
            // Arrange
            var workerId = Guid.NewGuid().ToString();
            var request = new UpdateWorkerColorRequest { Color = "#FF0000" };
            var exceptionMessage = "Worker not found";

            this.mockCalendarService
                .Setup(s => s.UpdateWorkerColorAsync(workerId, request.Color))
                .ThrowsAsync(new ArgumentException(exceptionMessage));

            // Act
            var result = await this.controller.UpdateWorkerColor(workerId, request);

            // Assert
            Assert.That(result, Is.InstanceOf<BadRequestObjectResult>());
            var badRequestResult = result as BadRequestObjectResult;

            // Use reflection to access the anonymous object property
            var errorResponse = badRequestResult.Value;
            var errorProperty = errorResponse.GetType().GetProperty("error");
            var errorMessage = errorProperty?.GetValue(errorResponse)?.ToString();
            Assert.That(errorMessage, Is.EqualTo(exceptionMessage));

            // Verify service was called
            this.mockCalendarService.Verify(
                s => s.UpdateWorkerColorAsync(workerId, request.Color),
                Times.Once);
        }

        [Test]
        public async Task UpdateWorkerColor_WithValidHexColors_ShouldReturnOk()
        {
            // Arrange
            var workerId = Guid.NewGuid().ToString();
            var validColors = new[] { "#FF0000", "#00FF00", "#0000FF", "#FFFFFF", "#000000", "#123ABC" };

            foreach (var color in validColors)
            {
                var request = new UpdateWorkerColorRequest { Color = color };

                this.mockCalendarService
                    .Setup(s => s.UpdateWorkerColorAsync(workerId, color))
                    .Returns(Task.CompletedTask);

                // Act
                var result = await this.controller.UpdateWorkerColor(workerId, request);

                // Assert
                Assert.That(result, Is.InstanceOf<OkObjectResult>(), $"Failed for color: {color}");
            }

            // Verify service was called for each color
            this.mockCalendarService.Verify(
                s => s.UpdateWorkerColorAsync(workerId, It.IsAny<string>()),
                Times.Exactly(validColors.Length));
        }

        [Test]
        public async Task GetWorkers_ShouldReturnJsonResult()
        {
            // Arrange
            var workers = new List<CalendarWorkerViewModel>
            {
                new CalendarWorkerViewModel
                {
                    Id = Guid.NewGuid().ToString(),
                    Name = "Георги Петров",
                    Color = "#FF0000",
                    Position = "Геодезист"
                },
                new CalendarWorkerViewModel
                {
                    Id = Guid.NewGuid().ToString(),
                    Name = "Мария Иванова",
                    Color = "#00FF00",
                    Position = "Инженер"
                }
            };

            this.mockCalendarService
                .Setup(s => s.GetAllWorkersAsync())
                .ReturnsAsync(workers);

            // Act
            var result = await this.controller.GetWorkers();

            // Assert
            Assert.That(result, Is.InstanceOf<JsonResult>());
            var jsonResult = result as JsonResult;
            Assert.That(jsonResult.Value, Is.EqualTo(workers));

            // Verify service was called
            this.mockCalendarService.Verify(
                s => s.GetAllWorkersAsync(),
                Times.Once);
        }

        [Test]
        public async Task GetWorkers_WhenServiceThrowsException_ShouldReturnBadRequest()
        {
            // Arrange
            var exceptionMessage = "Database connection failed";

            this.mockCalendarService
                .Setup(s => s.GetAllWorkersAsync())
                .ThrowsAsync(new Exception(exceptionMessage));

            // Act
            var result = await this.controller.GetWorkers();

            // Assert
            Assert.That(result, Is.InstanceOf<BadRequestObjectResult>());
            var badRequestResult = result as BadRequestObjectResult;

            // Use reflection to access the anonymous object property
            var errorResponse = badRequestResult.Value;
            var errorProperty = errorResponse.GetType().GetProperty("error");
            var errorMessage = errorProperty?.GetValue(errorResponse)?.ToString();
            Assert.That(errorMessage, Is.EqualTo(exceptionMessage));

            // Verify service was called
            this.mockCalendarService.Verify(
                s => s.GetAllWorkersAsync(),
                Times.Once);
        }
    }
}
