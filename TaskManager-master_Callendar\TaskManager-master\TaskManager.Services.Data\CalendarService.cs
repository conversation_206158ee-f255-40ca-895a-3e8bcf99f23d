using Microsoft.EntityFrameworkCore;
using TaskManager.Data;
using TaskManager.Data.Models;
using TaskManager.Services.Data.Interfaces;
using TaskManager.Web.ViewModels.Calendar;
using TaskManager.Web.ViewModels.Comentar;

namespace TaskManager.Services.Data
{
    public class CalendarService : ICalendarService
    {
        private readonly TaskManagerDbContext dbContext;

        public CalendarService(TaskManagerDbContext dbContext)
        {
            this.dbContext = dbContext;
        }

        public async Task<IEnumerable<CalendarWorkerViewModel>> GetAllWorkersAsync()
        {
            var workers = await dbContext.Workers
                .Include(w => w.User)
                .Where(w => w.User != null) // Ensure User is not null
                .Select(w => new CalendarWorkerViewModel
                {
                    Id = w.Id.ToString(),
                    Name = $"{w.User.FirstName ?? ""} {w.User.LastName ?? ""}",
                    Initials = $"{(w.User.FirstName != null && w.User.FirstName.Length > 0 ? w.User.FirstName.Substring(0, 1) : "")}{(w.User.LastName != null && w.User.LastName.Length > 0 ? w.User.LastName.Substring(0, 1) : "")}",
                    Color = w.Color ?? "#3B82F6", // Use worker's color or default blue
                    Visible = true,
                    Position = w.Position ?? "",
                    Email = w.User.Email ?? "",
                    PhoneNumber = w.PhoneNumber ?? ""
                })
                .ToListAsync();

            return workers;
        }

        public async Task<IEnumerable<CalendarTaskViewModel>> GetTasksAsync(DateTime? startDate = null, DateTime? endDate = null)
        {
            var query = dbContext.CalendarTasks
                .Include(ct => ct.AssignedWorkers)
                    .ThenInclude(ctw => ctw.Worker)
                    .ThenInclude(w => w.User)
                .Include(ct => ct.GeoTask)
                    .ThenInclude(gt => gt.Client)
                .Include(ct => ct.GeoTask)
                    .ThenInclude(gt => gt.Status)
                .Include(ct => ct.GeoTask)
                    .ThenInclude(gt => gt.Type)
                .Include(ct => ct.GeoTask)
                    .ThenInclude(gt => gt.Comentars)
                    .ThenInclude(c => c.Worker)
                    .ThenInclude(w => w.User)
                .AsQueryable();

            if (startDate.HasValue)
            {
                query = query.Where(ct => ct.Date >= startDate.Value);
            }

            if (endDate.HasValue)
            {
                query = query.Where(ct => ct.Date <= endDate.Value);
            }

            var calendarTasks = await query.ToListAsync();

            // Debug: Log the number of tasks found
            System.Diagnostics.Debug.WriteLine($"Found {calendarTasks.Count} calendar tasks");

            var tasks = calendarTasks.Select(ct => {
                // Ensure the date is returned without timezone conversion
                var dateOnly = new DateTime(ct.Date.Year, ct.Date.Month, ct.Date.Day);
                var finalDate = DateTime.SpecifyKind(dateOnly, DateTimeKind.Unspecified);

                return new CalendarTaskViewModel
                {
                    Id = ct.Id.ToString(),
                    Title = ct.Title, // Use title exactly as stored in database - no automatic project number addition
                    Description = ct.Description,
                    AssignedMemberIds = ct.AssignedWorkers.Select(ctw => ctw.WorkerId.ToString()).ToList(),
                    Date = finalDate.ToString("yyyy-MM-dd"),
                    StartTime = ct.StartTime.ToString(@"hh\:mm"),
                    EndTime = ct.EndTime.ToString(@"hh\:mm"),
                    Color = ct.Color,
                CreatedDate = ct.CreatedDate,
                ModifiedDate = ct.ModifiedDate,
                GeoTaskId = ct.GeoTaskId?.ToString(),
                ClientName = ct.GeoTask?.Client?.Name,
                TaskType = ct.GeoTask?.Type?.Name,
                Status = ct.GeoTask?.Status?.Name,
                ProjectNumber = ct.GeoTask?.ProjectNumber,
                Address = ct.GeoTask?.Adrress,
                Price = ct.GeoTask?.Price,
                Quantity = ct.GeoTask?.quantity,
                Note = ct.GeoTask?.Note,
                Comments = ct.GeoTask?.Comentars?.Select(c => new ComentarViewModel
                {
                    Id = c.Id,
                    Description = c.Description ?? "",
                    WorkerFullName = $"{c.Worker?.User?.FirstName ?? ""} {c.Worker?.User?.LastName ?? ""}",
                    CreateDate = c.CreateDate.ToString("dd-MM-yyyy H:mm"),
                    TaskId = c.TaskId.ToString(),
                    WorkerId = c.WorkerId.ToString()
                }).ToList() ?? new List<ComentarViewModel>(),
                AssignedWorkers = ct.AssignedWorkers.Select(ctw => new CalendarWorkerViewModel
                {
                    Id = ctw.WorkerId.ToString(),
                    Name = $"{ctw.Worker.User?.FirstName ?? ""} {ctw.Worker.User?.LastName ?? ""}",
                    Initials = $"{(ctw.Worker.User?.FirstName != null && ctw.Worker.User.FirstName.Length > 0 ? ctw.Worker.User.FirstName.Substring(0, 1) : "")}{(ctw.Worker.User?.LastName != null && ctw.Worker.User.LastName.Length > 0 ? ctw.Worker.User.LastName.Substring(0, 1) : "")}",
                    Position = ctw.Worker.Position ?? "",
                    Email = ctw.Worker.User?.Email ?? "",
                    PhoneNumber = ctw.Worker.PhoneNumber ?? "",
                    Color = "#3B82F6", // Default color
                    Visible = true
                }).ToList()
                };
            }).ToList();

            return tasks;
        }



        public async Task<CalendarTaskViewModel> CreateTaskAsync(CreateCalendarTaskViewModel model)
        {
            System.Diagnostics.Debug.WriteLine($"🔧 CREATE SERVICE: Received model.Date: {model.Date} (Kind: {model.Date.Kind})");
            Console.WriteLine($"🔧 CREATE SERVICE: Received model.Date: {model.Date} (Kind: {model.Date.Kind})");

            // Use the date directly - it's already been processed in the controller to avoid timezone issues
            var dateOnly = new DateTime(model.Date.Year, model.Date.Month, model.Date.Day);
            var finalDate = DateTime.SpecifyKind(dateOnly, DateTimeKind.Unspecified);

            System.Diagnostics.Debug.WriteLine($"🔧 CREATE SERVICE: Final date: {finalDate} (Kind: {finalDate.Kind})");
            Console.WriteLine($"🔧 CREATE SERVICE: Final date: {finalDate} (Kind: {finalDate.Kind})");

            var calendarTask = new CalendarTask
            {
                Id = Guid.NewGuid(),
                Title = model.Title,
                Description = model.Description,
                Date = finalDate,
                StartTime = TimeSpan.Parse(model.StartTime),
                EndTime = TimeSpan.Parse(model.EndTime),
                Color = model.Color,
                CreatedDate = DateTime.UtcNow
            };

            dbContext.CalendarTasks.Add(calendarTask);

            // Add assigned workers
            foreach (var workerId in model.AssignedMemberIds)
            {
                var calendarTaskWorker = new CalendarTaskWorker
                {
                    CalendarTaskId = calendarTask.Id,
                    WorkerId = Guid.Parse(workerId),
                    AssignedDate = DateTime.UtcNow
                };
                dbContext.CalendarTaskWorkers.Add(calendarTaskWorker);
            }

            await dbContext.SaveChangesAsync();

            return await GetTaskByIdAsync(calendarTask.Id.ToString());
        }

        public async Task<CalendarTaskViewModel> UpdateTaskAsync(string taskId, UpdateCalendarTaskViewModel model)
        {
            System.Diagnostics.Debug.WriteLine($"UpdateTaskAsync called with taskId: {taskId}");
            Console.WriteLine($"UpdateTaskAsync called with taskId: {taskId}");

            var calendarTask = await dbContext.CalendarTasks
                .Include(ct => ct.AssignedWorkers)
                .FirstOrDefaultAsync(ct => ct.Id.ToString() == taskId);

            if (calendarTask == null)
            {
                System.Diagnostics.Debug.WriteLine($"Calendar task not found for ID: {taskId}");
                Console.WriteLine($"Calendar task not found for ID: {taskId}");
                throw new ArgumentException("Calendar task not found");
            }

            System.Diagnostics.Debug.WriteLine($"Found calendar task: {calendarTask.Title}");
            Console.WriteLine($"Found calendar task: {calendarTask.Title}");

            // Update basic fields
            // Clean the title to prevent project number duplication
            string cleanedTitle = model.Title;

            // Handle null title first
            if (model.Title == null)
            {
                cleanedTitle = "Проект";
            }
            else
            {
                // Remove ALL project number prefixes if they exist (handles corruption from frontend)
                var regex = new System.Text.RegularExpressions.Regex(@"^(#\d+\s*-\s*)+");
                cleanedTitle = regex.Replace(model.Title, "").Trim();

                // If the clean title is empty after removing prefixes, use a default
                if (string.IsNullOrWhiteSpace(cleanedTitle))
                {
                    cleanedTitle = "Проект";
                }
            }

            System.Diagnostics.Debug.WriteLine($"🧹 TITLE CLEANING: Original: '{model.Title}' -> Cleaned: '{cleanedTitle}'");
            Console.WriteLine($"🧹 TITLE CLEANING: Original: '{model.Title}' -> Cleaned: '{cleanedTitle}'");

            calendarTask.Title = cleanedTitle;
            calendarTask.Description = model.Description;

            // Fix timezone issue: Use the DateTime directly from the controller (already processed)
            System.Diagnostics.Debug.WriteLine($"🔧 SERVICE: Received model.Date: {model.Date} (Kind: {model.Date.Kind})");
            Console.WriteLine($"🔧 SERVICE: Received model.Date: {model.Date} (Kind: {model.Date.Kind})");

            // Use the date directly - it's already been processed in the controller to avoid timezone issues
            var dateOnly = new DateTime(model.Date.Year, model.Date.Month, model.Date.Day);
            calendarTask.Date = DateTime.SpecifyKind(dateOnly, DateTimeKind.Unspecified);

            System.Diagnostics.Debug.WriteLine($"🔧 SERVICE: Final date with Unspecified kind: {calendarTask.Date} (Kind: {calendarTask.Date.Kind})");
            Console.WriteLine($"🔧 SERVICE: Final date with Unspecified kind: {calendarTask.Date} (Kind: {calendarTask.Date.Kind})");

            System.Diagnostics.Debug.WriteLine($"🔧 SERVICE: About to save calendarTask.Date: {calendarTask.Date}");
            Console.WriteLine($"🔧 SERVICE: About to save calendarTask.Date: {calendarTask.Date}");

            calendarTask.StartTime = TimeSpan.Parse(model.StartTime);
            calendarTask.EndTime = TimeSpan.Parse(model.EndTime);
            calendarTask.Color = model.Color;
            calendarTask.ModifiedDate = DateTime.UtcNow;

            // Update assigned workers
            // Remove existing assignments
            System.Diagnostics.Debug.WriteLine($"Removing {calendarTask.AssignedWorkers.Count} existing worker assignments");
            Console.WriteLine($"Removing {calendarTask.AssignedWorkers.Count} existing worker assignments");
            dbContext.CalendarTaskWorkers.RemoveRange(calendarTask.AssignedWorkers);

            // Add new assignments
            System.Diagnostics.Debug.WriteLine($"Adding {model.AssignedMemberIds.Count} new worker assignments");
            Console.WriteLine($"Adding {model.AssignedMemberIds.Count} new worker assignments");
            foreach (var workerId in model.AssignedMemberIds)
            {
                var calendarTaskWorker = new CalendarTaskWorker
                {
                    CalendarTaskId = calendarTask.Id,
                    WorkerId = Guid.Parse(workerId),
                    AssignedDate = DateTime.UtcNow
                };
                dbContext.CalendarTaskWorkers.Add(calendarTaskWorker);
            }

            System.Diagnostics.Debug.WriteLine("🔧 SERVICE: Saving changes to database...");
            Console.WriteLine("🔧 SERVICE: Saving changes to database...");
            await dbContext.SaveChangesAsync();
            System.Diagnostics.Debug.WriteLine("🔧 SERVICE: Changes saved successfully");
            Console.WriteLine("🔧 SERVICE: Changes saved successfully");

            var result = await GetTaskByIdAsync(taskId);
            System.Diagnostics.Debug.WriteLine($"🔧 SERVICE: Retrieved task after save - Date: {result.Date}");
            Console.WriteLine($"🔧 SERVICE: Retrieved task after save - Date: {result.Date}");
            return result;
        }

        public async Task<bool> DeleteTaskAsync(string taskId)
        {
            var calendarTask = await dbContext.CalendarTasks
                .Include(ct => ct.AssignedWorkers)
                .FirstOrDefaultAsync(ct => ct.Id.ToString() == taskId);

            if (calendarTask == null)
                return false;

            // Remove worker assignments first
            dbContext.CalendarTaskWorkers.RemoveRange(calendarTask.AssignedWorkers);

            // Remove the calendar task
            dbContext.CalendarTasks.Remove(calendarTask);

            await dbContext.SaveChangesAsync();

            return true;
        }

        public async Task<bool> TaskExistsAsync(string taskId)
        {
            return await dbContext.CalendarTasks
                .AnyAsync(ct => ct.Id.ToString() == taskId);
        }

        private async Task<CalendarTaskViewModel> GetTaskByIdAsync(string taskId)
        {
            var calendarTask = await dbContext.CalendarTasks
                .Include(ct => ct.AssignedWorkers)
                    .ThenInclude(ctw => ctw.Worker)
                    .ThenInclude(w => w.User)
                .Include(ct => ct.GeoTask)
                    .ThenInclude(gt => gt.Client)
                .Include(ct => ct.GeoTask)
                    .ThenInclude(gt => gt.Status)
                .Include(ct => ct.GeoTask)
                    .ThenInclude(gt => gt.Type)
                .Include(ct => ct.GeoTask)
                    .ThenInclude(gt => gt.Comentars)
                    .ThenInclude(c => c.Worker)
                    .ThenInclude(w => w.User)
                .FirstAsync(ct => ct.Id.ToString() == taskId);

            System.Diagnostics.Debug.WriteLine($"🔧 GetTaskByIdAsync: Raw DB Date: {calendarTask.Date} (Kind: {calendarTask.Date.Kind})");
            Console.WriteLine($"🔧 GetTaskByIdAsync: Raw DB Date: {calendarTask.Date} (Kind: {calendarTask.Date.Kind})");

            // Ensure the date is returned without timezone conversion
            var dateOnly = new DateTime(calendarTask.Date.Year, calendarTask.Date.Month, calendarTask.Date.Day);
            var finalDate = DateTime.SpecifyKind(dateOnly, DateTimeKind.Unspecified);

            System.Diagnostics.Debug.WriteLine($"🔧 GetTaskByIdAsync: Final Date for response: {finalDate} (Kind: {finalDate.Kind})");
            Console.WriteLine($"🔧 GetTaskByIdAsync: Final Date for response: {finalDate} (Kind: {finalDate.Kind})");

            // Use the title exactly as stored in the database - no automatic modifications
            string displayTitle = calendarTask.Title;

            return new CalendarTaskViewModel
            {
                Id = calendarTask.Id.ToString(),
                Title = displayTitle,
                Description = calendarTask.Description,
                AssignedMemberIds = calendarTask.AssignedWorkers.Select(ctw => ctw.WorkerId.ToString()).ToList(),
                Date = finalDate.ToString("yyyy-MM-dd"),
                StartTime = calendarTask.StartTime.ToString(@"hh\:mm"),
                EndTime = calendarTask.EndTime.ToString(@"hh\:mm"),
                Color = calendarTask.Color,
                CreatedDate = calendarTask.CreatedDate,
                ModifiedDate = calendarTask.ModifiedDate,
                GeoTaskId = calendarTask.GeoTaskId?.ToString(),
                ClientName = calendarTask.GeoTask?.Client?.Name,
                TaskType = calendarTask.GeoTask?.Type?.Name,
                Status = calendarTask.GeoTask?.Status?.Name,
                ProjectNumber = calendarTask.GeoTask?.ProjectNumber,
                Address = calendarTask.GeoTask?.Adrress,
                Price = calendarTask.GeoTask?.Price,
                Quantity = calendarTask.GeoTask?.quantity,
                Note = calendarTask.GeoTask?.Note,
                Comments = calendarTask.GeoTask?.Comentars?.Select(c => new ComentarViewModel
                {
                    Id = c.Id,
                    Description = c.Description ?? "",
                    WorkerFullName = $"{c.Worker?.User?.FirstName ?? ""} {c.Worker?.User?.LastName ?? ""}",
                    CreateDate = c.CreateDate.ToString("dd-MM-yyyy H:mm"),
                    TaskId = c.TaskId.ToString(),
                    WorkerId = c.WorkerId.ToString()
                }).ToList() ?? new List<ComentarViewModel>(),
                AssignedWorkers = calendarTask.AssignedWorkers.Select(ctw => new CalendarWorkerViewModel
                {
                    Id = ctw.WorkerId.ToString(),
                    Name = $"{ctw.Worker.User?.FirstName ?? ""} {ctw.Worker.User?.LastName ?? ""}",
                    Initials = $"{(ctw.Worker.User?.FirstName != null && ctw.Worker.User.FirstName.Length > 0 ? ctw.Worker.User.FirstName.Substring(0, 1) : "")}{(ctw.Worker.User?.LastName != null && ctw.Worker.User.LastName.Length > 0 ? ctw.Worker.User.LastName.Substring(0, 1) : "")}",
                    Position = ctw.Worker.Position ?? "",
                    Email = ctw.Worker.User?.Email ?? "",
                    PhoneNumber = ctw.Worker.PhoneNumber ?? "",
                    Color = "#3B82F6",
                    Visible = true
                }).ToList()
            };
        }

        // New methods for GeoTask integration
        public async Task<CalendarTaskViewModel> CreateTaskFromGeoTaskAsync(string geoTaskId, CreateCalendarTaskViewModel model)
        {
            var geoTask = await dbContext.GeoTasks
                .Include(gt => gt.Worker)
                .Include(gt => gt.CheckEr)
                .FirstOrDefaultAsync(gt => gt.Id.ToString() == geoTaskId);

            if (geoTask == null)
                throw new ArgumentException("GeoTask not found");

            if (await IsGeoTaskLinkedToCalendarAsync(geoTaskId))
                throw new InvalidOperationException("GeoTask is already linked to a calendar task");

            System.Diagnostics.Debug.WriteLine($"🔧 GEOTASK SERVICE: Received model.Date: {model.Date} (Kind: {model.Date.Kind})");
            Console.WriteLine($"🔧 GEOTASK SERVICE: Received model.Date: {model.Date} (Kind: {model.Date.Kind})");

            // Use the date directly - it's already been processed in the controller to avoid timezone issues
            var dateOnly = new DateTime(model.Date.Year, model.Date.Month, model.Date.Day);
            var finalDate = DateTime.SpecifyKind(dateOnly, DateTimeKind.Unspecified);

            System.Diagnostics.Debug.WriteLine($"🔧 GEOTASK SERVICE: Final date: {finalDate} (Kind: {finalDate.Kind})");
            Console.WriteLine($"🔧 GEOTASK SERVICE: Final date: {finalDate} (Kind: {finalDate.Kind})");

            var calendarTask = new CalendarTask
            {
                Id = Guid.NewGuid(),
                Title = model.Title,
                Description = model.Description,
                Date = finalDate,
                StartTime = TimeSpan.Parse(model.StartTime),
                EndTime = TimeSpan.Parse(model.EndTime),
                Color = model.Color,
                CreatedDate = DateTime.UtcNow,
                GeoTaskId = Guid.Parse(geoTaskId)
            };

            dbContext.CalendarTasks.Add(calendarTask);

            // Add assigned workers (include GeoTask worker and checker if not already in the list)
            var workerIds = model.AssignedMemberIds.ToList();
            if (!workerIds.Contains(geoTask.WorkerId.ToString()))
                workerIds.Add(geoTask.WorkerId.ToString());
            if (!workerIds.Contains(geoTask.CheckerId.ToString()))
                workerIds.Add(geoTask.CheckerId.ToString());

            foreach (var workerId in workerIds.Distinct())
            {
                var calendarTaskWorker = new CalendarTaskWorker
                {
                    CalendarTaskId = calendarTask.Id,
                    WorkerId = Guid.Parse(workerId),
                    AssignedDate = DateTime.UtcNow
                };
                dbContext.CalendarTaskWorkers.Add(calendarTaskWorker);
            }

            await dbContext.SaveChangesAsync();

            // Debug: Verify the task was saved
            var savedTask = await dbContext.CalendarTasks
                .FirstOrDefaultAsync(ct => ct.Id == calendarTask.Id);

            if (savedTask == null)
                throw new InvalidOperationException("Failed to save calendar task to database");

            return await GetTaskByIdAsync(calendarTask.Id.ToString());
        }

        public async Task<bool> IsGeoTaskLinkedToCalendarAsync(string geoTaskId)
        {
            if (!Guid.TryParse(geoTaskId, out var geoTaskGuid))
                return false;

            return await dbContext.CalendarTasks
                .AnyAsync(ct => ct.GeoTaskId == geoTaskGuid);
        }

        public async Task<string?> GetCalendarTaskIdByGeoTaskIdAsync(string geoTaskId)
        {
            if (!Guid.TryParse(geoTaskId, out var geoTaskGuid))
                return null;

            var calendarTask = await dbContext.CalendarTasks
                .FirstOrDefaultAsync(ct => ct.GeoTaskId == geoTaskGuid);

            return calendarTask?.Id.ToString();
        }

        public async Task<bool> UnlinkGeoTaskFromCalendarAsync(string geoTaskId)
        {
            if (!Guid.TryParse(geoTaskId, out var geoTaskGuid))
                return false;

            var calendarTask = await dbContext.CalendarTasks
                .Include(ct => ct.AssignedWorkers)
                .FirstOrDefaultAsync(ct => ct.GeoTaskId == geoTaskGuid);

            if (calendarTask == null)
                return false;

            // Remove worker assignments
            dbContext.CalendarTaskWorkers.RemoveRange(calendarTask.AssignedWorkers);

            // Remove the calendar task
            dbContext.CalendarTasks.Remove(calendarTask);

            await dbContext.SaveChangesAsync();

            return true;
        }

        public async Task UpdateWorkerColorAsync(string workerId, string color)
        {
            var worker = await dbContext.Workers
                .FirstOrDefaultAsync(w => w.Id.ToString() == workerId);

            if (worker == null)
            {
                throw new ArgumentException("Worker not found");
            }

            worker.Color = color;
            await dbContext.SaveChangesAsync();
        }
    }
}
