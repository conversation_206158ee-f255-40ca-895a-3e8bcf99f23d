// Map functionality for GeoSpatial application
class MapManager {
    constructor(config) {
        this.config = config;
        this.map = null;
        this.mapLayers = {};
        this.osmLayer = null;
        this.coordinateControl = null;
        this.transportStops = [];
        this.transportMarkers = L.layerGroup();
        
        this.init();
    }

    init() {
        this.setupProjections();
        this.initializeMap();
        this.initializeLayers();
        this.initializeTransportStops();
        this.setupControls();
        this.setupEventHandlers();
    }

    setupProjections() {
        // Define Bulgarian coordinate systems
        proj4.defs("EPSG:7801", "+proj=tmerc +lat_0=0 +lon_0=25 +k=0.9999 +x_0=500000 +y_0=0 +ellps=GRS80 +towgs84=0,0,0,0,0,0,0 +units=m +no_defs"); // BGS2005 / UTM zone 35N
        proj4.defs("EPSG:4326", "+proj=longlat +datum=WGS84 +no_defs"); // WGS84
    }

    initializeMap() {
        // Initialize map
        this.map = L.map('leaflet-map').setView([this.config.centerLat, this.config.centerLng], this.config.zoomLevel);
        
        // Add default base layer (OpenStreetMap) only if no base layers are configured
        if (!this.config.hasBaseLayers) {
            this.osmLayer = L.tileLayer('https://{s}.tile.openstreetmap.org/{z}/{x}/{y}.png', {
                attribution: '&copy; <a href="https://www.openstreetmap.org/copyright">OpenStreetMap</a> contributors'
            }).addTo(this.map);
        }
    }

    initializeLayers() {
        console.log('Starting layer initialization...');
        console.log('Total layers to initialize:', this.config.layers.length);

        // Debug: Check for cadastre layers
        const cadastreLayers = this.config.layers.filter(l => l.workspace === 'cadastre');
        console.log('Found cadastre layers:', cadastreLayers);

        // Initialize regular layers
        this.config.layers.forEach(layer => {
            console.log('Initializing layer:', layer.name, 'Type:', layer.layerType, 'Workspace:', layer.workspace);
            this.initializeLayer(layer, false);
        });
        
        // Initialize base layers
        console.log('Starting base layer initialization...');
        this.config.baseLayers.forEach(layer => {
            this.initializeLayer(layer, true);
        });
    }

    initializeLayer(layer, isBaseLayer) {
        try {
            const layerPrefix = isBaseLayer ? 'base' : 'regular';
            console.log(`Initializing ${layerPrefix} ${layer.layerType} layer: ${layer.name}`);
            console.log('Layer details:', {
                id: layer.id,
                name: layer.name,
                layerType: layer.layerType,
                workspace: layer.workspace,
                wmsUrl: layer.wmsUrl,
                isVisible: layer.isVisible
            });

            let leafletLayer;
            
            if (layer.layerType === "WMS") {
                leafletLayer = L.tileLayer.wms(layer.wmsUrl, {
                    layers: `${layer.workspace}:${layer.layerName}`,
                    format: 'image/png',
                    transparent: !isBaseLayer,
                    opacity: layer.opacity || 1.0,
                    attribution: `${isBaseLayer ? 'Base ' : ''}Layer: ${layer.name}`,
                    version: '1.1.0',
                    crs: L.CRS.EPSG4326
                });
            } else if (layer.layerType === "TILE") {
                leafletLayer = L.tileLayer(layer.wmsUrl, {
                    opacity: layer.opacity || 1.0,
                    attribution: `${isBaseLayer ? 'Base ' : ''}Layer: ${layer.name}`
                });
            }

            if (leafletLayer) {
                this.mapLayers[layer.id] = leafletLayer;
                console.log(`Layer stored in mapLayers with ID: ${layer.id}`);

                // Add to map if visible
                if (layer.isVisible) {
                    leafletLayer.addTo(this.map);
                    console.log(`Layer ${layer.name} added to map (visible by default)`);
                }

                // Add error handling
                leafletLayer.on('tileerror', (error) => {
                    console.error(`${layer.layerType} layer error for ${layer.name}:`, error);
                });

                leafletLayer.on('tileload', () => {
                    console.log(`${layer.layerType} tile loaded successfully for ${layer.name}`);
                });

                if (layer.layerType === "WMS") {
                    leafletLayer.on('loading', () => {
                        console.log(`WMS layer loading: ${layer.name}`);
                    });
                }
            }
        } catch (error) {
            console.error(`Error initializing layer ${layer.name}:`, error);
        }
    }

    initializeTransportStops() {
        console.log('Initializing transport stops...');
        // Реални спирки на градски транспорт в София с точни координати
        this.transportStops = [
            {
                id: 'stop_1',
                name: 'Сердика',
                type: 'metro_bus_tram',
                lat: 42.6977,
                lng: 23.3219,
                lines: {
                    metro: ['М1', 'М2'],
                    bus: ['9', '72', '84', '94'],
                    tram: ['1', '7', '12']
                },
                description: 'Централна метростанция - пресечна точка на М1 и М2 линии'
            },
            {
                id: 'stop_2',
                name: 'Университет',
                type: 'metro_bus',
                lat: 42.6934,
                lng: 23.3265,
                lines: {
                    metro: ['М2'],
                    bus: ['9', '72', '75', '76', '84', '94', '204', '213', '214', '280', '304', '306']
                },
                description: 'Метростанция "СУ Св. Климент Охридски" до Софийския университет'
            },
            {
                id: 'stop_3',
                name: 'НДК',
                type: 'metro_bus',
                lat: 42.6847,
                lng: 23.3188,
                lines: {
                    metro: ['М2'],
                    bus: ['9', '72', '76', '94', '204']
                },
                description: 'Метростанция "НДК" - Национален дворец на културата'
            },
            {
                id: 'stop_4',
                name: 'Лъвов мост',
                type: 'metro_bus_tram',
                lat: 42.7008,
                lng: 23.3157,
                lines: {
                    metro: ['М1'],
                    bus: ['72', '76', '84'],
                    tram: ['1', '7']
                },
                description: 'Метростанция "Лъвов мост" - важен транспортен възел'
            },
            {
                id: 'stop_5',
                name: 'Опълченска',
                type: 'metro_bus',
                lat: 42.7089,
                lng: 23.3157,
                lines: {
                    metro: ['М1'],
                    bus: ['72', '76', '204', '213']
                },
                description: 'Метростанция "Опълченска" в близост до Централна гара'
            },
            {
                id: 'stop_6',
                name: 'Вардар',
                type: 'metro_bus_tram',
                lat: 42.7042,
                lng: 23.3089,
                lines: {
                    metro: ['М1'],
                    bus: ['45'],
                    tram: ['8']
                },
                description: 'Метростанция "Вардар" с връзка към трамвай 8'
            },
            {
                id: 'stop_7',
                name: 'Западен парк',
                type: 'metro_bus_tram',
                lat: 42.7025,
                lng: 23.2956,
                lines: {
                    metro: ['М1'],
                    bus: ['310'],
                    tram: ['8']
                },
                description: 'Метростанция "Западен парк" до парка'
            },
            {
                id: 'stop_8',
                name: 'Стадион Васил Левски',
                type: 'metro_bus',
                lat: 42.6869,
                lng: 23.3388,
                lines: {
                    metro: ['М2'],
                    bus: ['9', '94', '204']
                },
                description: 'Метростанция до националния стадион "Васил Левски"'
            },
            {
                id: 'stop_9',
                name: 'Европейски съюз',
                type: 'metro_bus',
                lat: 42.6742,
                lng: 23.3156,
                lines: {
                    metro: ['М2'],
                    bus: ['102', '111', '120']
                },
                description: 'Метростанция "Европейски съюз" в южната част на града'
            },
            {
                id: 'stop_10',
                name: 'Джеймс Баучер',
                type: 'metro_bus',
                lat: 42.6758,
                lng: 23.3189,
                lines: {
                    metro: ['М2'],
                    bus: ['102', '111']
                },
                description: 'Метростанция "Джеймс Баучер" в района на Лозенец'
            },
            {
                id: 'stop_11',
                name: 'Пл. Александър Невски',
                type: 'bus_tram',
                lat: 42.6957,
                lng: 23.3327,
                lines: {
                    bus: ['9', '72', '84', '94'],
                    tram: ['12', '14']
                },
                description: 'Централна спирка до катедралата "Александър Невски"'
            },
            {
                id: 'stop_12',
                name: 'Централна гара',
                type: 'bus_tram',
                lat: 42.7135,
                lng: 23.3214,
                lines: {
                    bus: ['72', '76', '84', '204', '213'],
                    tram: ['1', '7']
                },
                description: 'Главна железопътна гара на София'
            }
        ];

        // Създаване на маркери за всяка спирка
        this.transportStops.forEach(stop => {
            console.log('Creating marker for stop:', stop.name);
            const marker = this.createTransportMarker(stop);
            this.transportMarkers.addLayer(marker);
        });

        console.log('Transport stops initialized. Total markers:', this.transportMarkers.getLayers().length);

        // Добавяне на слоя към картата (първоначално скрит)
        // this.transportMarkers.addTo(this.map);
        console.log('Transport stops initialized with', this.transportStops.length, 'real stops');
    }

    createTransportMarker(stop) {
        // Определяне на икона според типа спирка
        let iconHtml = '';
        let iconClass = 'transport-marker';

        if (stop.type.includes('metro')) {
            iconHtml = '<i class="fas fa-subway"></i>';
            iconClass += ' metro-stop';
        } else if (stop.type.includes('tram')) {
            iconHtml = '<i class="fas fa-train"></i>';
            iconClass += ' tram-stop';
        } else {
            iconHtml = '<i class="fas fa-bus"></i>';
            iconClass += ' bus-stop';
        }

        // Създаване на custom икона
        const customIcon = L.divIcon({
            html: iconHtml,
            className: iconClass,
            iconSize: [30, 30],
            iconAnchor: [15, 15],
            popupAnchor: [0, -15]
        });

        // Създаване на маркер
        const marker = L.marker([stop.lat, stop.lng], { icon: customIcon });

        // Създаване на popup съдържание
        const popupContent = this.createStopPopupContent(stop);
        marker.bindPopup(popupContent, {
            maxWidth: 350,
            className: 'transport-popup'
        });

        return marker;
    }

    createStopPopupContent(stop) {
        let linesHtml = '';

        // Метро линии
        if (stop.lines.metro && stop.lines.metro.length > 0) {
            linesHtml += `
                <div class="transport-lines">
                    <div class="line-type">
                        <i class="fas fa-subway"></i> <strong>Метро:</strong>
                    </div>
                    <div class="line-numbers">
                        ${stop.lines.metro.map(line => `<span class="line-badge metro-line">${line}</span>`).join('')}
                    </div>
                </div>
            `;
        }

        // Автобусни линии
        if (stop.lines.bus && stop.lines.bus.length > 0) {
            linesHtml += `
                <div class="transport-lines">
                    <div class="line-type">
                        <i class="fas fa-bus"></i> <strong>Автобуси:</strong>
                    </div>
                    <div class="line-numbers">
                        ${stop.lines.bus.map(line => `<span class="line-badge bus-line">${line}</span>`).join('')}
                    </div>
                </div>
            `;
        }

        // Трамвайни линии
        if (stop.lines.tram && stop.lines.tram.length > 0) {
            linesHtml += `
                <div class="transport-lines">
                    <div class="line-type">
                        <i class="fas fa-train"></i> <strong>Трамваи:</strong>
                    </div>
                    <div class="line-numbers">
                        ${stop.lines.tram.map(line => `<span class="line-badge tram-line">${line}</span>`).join('')}
                    </div>
                </div>
            `;
        }

        return `
            <div class="transport-stop-info">
                <h6 class="stop-title">
                    <i class="fas fa-map-marker-alt"></i> ${stop.name}
                </h6>
                <p class="stop-description">${stop.description}</p>
                ${linesHtml}
                <div class="stop-coordinates">
                    <small><strong>Координати:</strong> ${stop.lat.toFixed(6)}, ${stop.lng.toFixed(6)}</small>
                </div>
            </div>
        `;
    }

    setupControls() {
        // Add scale control if enabled
        if (this.config.showScaleControl) {
            L.control.scale().addTo(this.map);
        }
        
        // Create coordinate display control
        this.coordinateControl = L.control({position: 'bottomleft'});
        this.coordinateControl.onAdd = (map) => {
            const div = L.DomUtil.create('div', 'coordinate-display');
            div.innerHTML = `
                <div class="coordinate-box">
                    <div class="coordinate-row">
                        <span class="coordinate-label">BGS2005:</span>
                        <span id="bgs-coords">X: -, Y: -</span>
                    </div>
                    <div class="coordinate-row">
                        <span class="coordinate-label">WGS84:</span>
                        <span id="wgs-coords">Lat: -, Lng: -</span>
                    </div>
                </div>
            `;
            return div;
        };
        this.coordinateControl.addTo(this.map);
    }

    setupEventHandlers() {
        // Mouse move handler for coordinate display
        this.map.on('mousemove', (e) => {
            // Transform coordinates to BGS2005
            const wgs84 = [e.latlng.lng, e.latlng.lat];
            const bgs2005 = proj4('EPSG:4326', 'EPSG:7801', wgs84);
            
            // Update coordinate display
            const bgsElement = document.getElementById('bgs-coords');
            const wgsElement = document.getElementById('wgs-coords');
            
            if (bgsElement && wgsElement) {
                bgsElement.textContent = `X: ${bgs2005[0].toFixed(2)}, Y: ${bgs2005[1].toFixed(2)}`;
                wgsElement.textContent = `Lat: ${e.latlng.lat.toFixed(6)}, Lng: ${e.latlng.lng.toFixed(6)}`;
            }
        });
        
        // Click handler for feature info
        this.map.on('click', (e) => {
            console.log('Map clicked at:', e.latlng);
            this.getFeatureInfo(e);
        });
    }

    // Layer control functions
    toggleLayerVisibility(layerId, forceState = null) {
        console.log('=== TOGGLE LAYER VISIBILITY DEBUG ===');
        console.log('Layer ID:', layerId);
        console.log('Force state:', forceState);
        console.log('Available layers in mapLayers:', Object.keys(this.mapLayers));
        console.log('All config layers:', this.config.layers.map(l => ({id: l.id, name: l.name, workspace: l.workspace})));

        const checkbox = document.querySelector(`input[onchange*="${layerId}"]`);
        const isChecked = forceState !== null ? forceState : (checkbox ? checkbox.checked : false);
        console.log('Checkbox checked:', isChecked);

        // Check if this is the transport stops layer
        const layerConfig = this.config.layers.find(l => l.id === layerId);
        console.log('Layer config found:', layerConfig ? layerConfig.name : 'NOT FOUND');

        if (layerConfig && layerConfig.name === 'Обществен транспорт') {
            console.log('Handling transport stops layer. Markers count:', this.transportMarkers.getLayers().length);
            // Handle transport stops separately
            if (isChecked) {
                this.transportMarkers.addTo(this.map);
                console.log('Transport stops added to map');
            } else {
                this.map.removeLayer(this.transportMarkers);
                console.log('Transport stops removed from map');
            }

            // Update server state
            fetch(`/api/geolayerapi/toggle-visibility/${layerId}`, {
                method: 'POST',
                credentials: 'include',
                headers: {
                    'Content-Type': 'application/json'
                }
            }).catch(error => console.error('Error updating server state:', error));

            return;
        }

        fetch(`/api/geolayerapi/toggle-visibility/${layerId}`, {
            method: 'POST',
            credentials: 'include',
            headers: {
                'Content-Type': 'application/json'
            }
        })
        .then(response => {
            console.log('Response status:', response.status);
            if (response.ok) {
                // Toggle layer on map immediately
                const layer = this.mapLayers[layerId];
                console.log('Found layer in mapLayers:', layer ? 'YES' : 'NO');
                if (layer) {
                    if (isChecked) {
                        // Add layer to map
                        layer.addTo(this.map);
                        console.log('Layer added to map:', layerId);
                    } else {
                        // Remove layer from map
                        this.map.removeLayer(layer);
                        console.log('Layer removed from map:', layerId);
                    }

                    // Show/hide opacity control
                    const layerItem = document.querySelector(`[data-layer-id="${layerId}"]`);
                    const opacityControl = layerItem?.querySelector('.opacity-control');
                    if (opacityControl) {
                        opacityControl.style.display = isChecked ? 'flex' : 'none';
                    }
                } else {
                    console.error('Layer not found in mapLayers for ID:', layerId);
                    console.log('Available layer IDs:', Object.keys(this.mapLayers));
                }
            } else {
                // Revert checkbox state on error
                if (checkbox) {
                    checkbox.checked = !isChecked;
                }
                response.text().then(text => {
                    console.error('Error response:', text);
                    alert('Грешка при промяна на видимостта: ' + response.status);
                });
            }
        })
        .catch(error => {
            // Revert checkbox state on error
            if (checkbox) {
                checkbox.checked = !isChecked;
            }
            console.error('Error:', error);
            alert('Грешка при свързване със сървъра: ' + error.message);
        });
    }

    updateLayerOpacity(layerId, opacity) {
        fetch(`/api/geolayerapi/update-opacity/${layerId}`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            credentials: 'include',
            body: JSON.stringify(parseFloat(opacity))
        })
        .then(response => {
            if (response.ok) {
                // Update layer opacity on map
                if (this.mapLayers[layerId]) {
                    this.mapLayers[layerId].setOpacity(parseFloat(opacity));
                }
                
                // Update opacity display
                const opacityValue = document.querySelector(`[data-layer-id="${layerId}"] .opacity-value`);
                if (opacityValue) {
                    opacityValue.textContent = Math.round(parseFloat(opacity) * 100) + '%';
                }
            } else {
                alert('Грешка при промяна на прозрачността');
            }
        })
        .catch(error => {
            console.error('Error:', error);
            alert('Грешка при свързване със сървъра');
        });
    }

    toggleBaseLayer(layerId, show = true) {
        console.log('Selected base layer:', layerId, 'show:', show);

        // Hide all base layers first
        this.config.baseLayers.forEach(baseLayer => {
            const layer = this.mapLayers[baseLayer.id];
            if (layer && this.map.hasLayer(layer)) {
                this.map.removeLayer(layer);
            }
        });

        // Show selected base layer if show is true
        if (show) {
            const selectedLayer = this.mapLayers[layerId];
            if (selectedLayer) {
                selectedLayer.addTo(this.map);
                console.log('Base layer switched to:', layerId);
            } else {
                console.error('Base layer not found:', layerId);
            }
        }
    }

    // Toggle all base layers visibility
    toggleAllBaseLayers(show) {
        const content = document.getElementById('baseLayersContent');
        const radioButtons = content.querySelectorAll('input[type="radio"]');

        if (show) {
            content.classList.remove('hidden');
            // Enable radio buttons
            radioButtons.forEach(radio => radio.disabled = false);
            // Restore the previously selected base layer
            const checkedRadio = document.querySelector('input[name="baseLayer"]:checked');
            if (checkedRadio) {
                this.toggleBaseLayer(checkedRadio.value, true);
            }
        } else {
            content.classList.add('hidden');
            // Hide all base layers first
            this.config.baseLayers.forEach(baseLayer => {
                const layer = this.mapLayers[baseLayer.id];
                if (layer && this.map.hasLayer(layer)) {
                    this.map.removeLayer(layer);
                }
            });
            // Disable radio buttons
            radioButtons.forEach(radio => {
                radio.disabled = true;
            });
        }
    }

    // Toggle all general layers visibility
    toggleAllGeneralLayers(show) {
        const content = document.getElementById('generalLayersContent');
        const checkboxes = content.querySelectorAll('input[type="checkbox"]');

        if (show) {
            content.classList.remove('hidden');
            // Enable checkboxes
            checkboxes.forEach(checkbox => checkbox.disabled = false);
        } else {
            content.classList.add('hidden');
            // Disable checkboxes and uncheck them
            checkboxes.forEach(checkbox => {
                checkbox.disabled = true;
                if (checkbox.checked) {
                    checkbox.checked = false;
                    // Hide the layer
                    const layerItem = checkbox.closest('.layer-item');
                    const layerId = layerItem.getAttribute('data-layer-id');
                    this.toggleLayerVisibility(layerId, false);
                }
            });
        }
    }

    // Toggle all cadastre layers visibility
    toggleAllCadastreLayers(show) {
        const content = document.getElementById('cadastreLayersContent');
        const checkboxes = content.querySelectorAll('input[type="checkbox"]');

        if (show) {
            content.classList.remove('hidden');
            // Enable checkboxes
            checkboxes.forEach(checkbox => checkbox.disabled = false);
        } else {
            content.classList.add('hidden');
            // Disable checkboxes and uncheck them
            checkboxes.forEach(checkbox => {
                checkbox.disabled = true;
                if (checkbox.checked) {
                    checkbox.checked = false;
                    // Hide the layer
                    const layerItem = checkbox.closest('.layer-item');
                    const layerId = layerItem.getAttribute('data-layer-id');
                    this.toggleLayerVisibility(layerId, false);
                }
            });
        }
    }

    showLayerInfo(layerId, layerName, description, layerType, workspace) {
        // Populate modal
        document.getElementById('layerInfoModalLabel').textContent = layerName;
        document.getElementById('layerInfoDescription').textContent = description;
        document.getElementById('layerInfoType').textContent = layerType;
        document.getElementById('layerInfoWorkspace').textContent = workspace;

        // Show modal
        const modal = new bootstrap.Modal(document.getElementById('layerInfoModal'));
        modal.show();
    }

    getFeatureInfo(e) {
        // Get active layers that support feature info
        const activeLayers = this.getActiveWMSLayers();

        if (activeLayers.length === 0) {
            return; // No active WMS layers to query
        }

        // For now, we'll show transport information for the public transport layer
        const transportLayer = activeLayers.find(layer =>
            layer.name === 'Обществен транспорт' ||
            layer.name === 'Железопътни линии' ||
            layer.name === 'Велоалеи'
        );

        if (transportLayer) {
            this.showTransportInfo(e.latlng, transportLayer.name);
        }
    }

    getActiveWMSLayers() {
        const activeLayers = [];

        // Check which layers are currently visible on the map
        this.config.layers.forEach(layer => {
            const leafletLayer = this.mapLayers[layer.id];
            if (leafletLayer && this.map.hasLayer(leafletLayer)) {
                activeLayers.push({
                    id: layer.id,
                    name: layer.name,
                    layerType: layer.layerType,
                    workspace: layer.workspace,
                    layerName: layer.layerName
                });
            }
        });

        return activeLayers;
    }

    showTransportInfo(latlng, layerName) {
        // Transform coordinates to BGS2005 for display
        const wgs84 = [latlng.lng, latlng.lat];
        const bgs2005 = proj4('EPSG:4326', 'EPSG:7801', wgs84);

        let content = '';
        let icon = '';

        switch (layerName) {
            case 'Обществен транспорт':
                icon = '🚌';
                content = `
                    <div class="transport-info">
                        <h6><i class="fas fa-bus"></i> Обществен транспорт</h6>
                        <div class="info-section">
                            <strong>Възможни спирки в района:</strong>
                            <ul>
                                <li>🚌 Автобусни спирки</li>
                                <li>🚊 Трамвайни спирки</li>
                                <li>🚇 Метростанции</li>
                                <li>🚂 Железопътни гари</li>
                            </ul>
                        </div>
                        <div class="info-section">
                            <strong>Информация:</strong><br>
                            Този слой показва спирки и маршрути на обществения транспорт от OpenStreetMap данни.
                        </div>
                    </div>
                `;
                break;
            case 'Железопътни линии':
                icon = '🚂';
                content = `
                    <div class="transport-info">
                        <h6><i class="fas fa-train"></i> Железопътни линии</h6>
                        <div class="info-section">
                            <strong>Железопътна инфраструктура:</strong>
                            <ul>
                                <li>🚂 Железопътни линии</li>
                                <li>🚉 Железопътни гари</li>
                                <li>🚇 Метро линии</li>
                                <li>🚊 Трамвайни линии</li>
                            </ul>
                        </div>
                        <div class="info-section">
                            <strong>Източник:</strong><br>
                            OpenRailwayMap - специализирана карта за железопътен транспорт
                        </div>
                    </div>
                `;
                break;
            case 'Велоалеи':
                icon = '🚴';
                content = `
                    <div class="transport-info">
                        <h6><i class="fas fa-bicycle"></i> Велосипедни алеи</h6>
                        <div class="info-section">
                            <strong>Велосипедна инфраструктура:</strong>
                            <ul>
                                <li>🚴 Велосипедни алеи</li>
                                <li>🛤️ Велосипедни пътеки</li>
                                <li>🚲 Места за паркиране на велосипеди</li>
                                <li>🔧 Сервизи за велосипеди</li>
                            </ul>
                        </div>
                        <div class="info-section">
                            <strong>Източник:</strong><br>
                            CyclOSM - специализирана карта за велосипедисти
                        </div>
                    </div>
                `;
                break;
        }

        // Add coordinate information
        content += `
            <div class="coordinate-info">
                <hr>
                <h6><i class="fas fa-map-marker-alt"></i> Координати</h6>
                <div class="coordinate-row">
                    <strong>BGS2005:</strong> X: ${bgs2005[0].toFixed(2)}, Y: ${bgs2005[1].toFixed(2)}
                </div>
                <div class="coordinate-row">
                    <strong>WGS84:</strong> ${latlng.lat.toFixed(6)}°, ${latlng.lng.toFixed(6)}°
                </div>
            </div>
        `;

        // Show popup
        L.popup()
            .setLatLng(latlng)
            .setContent(content)
            .openOn(this.map);
    }
}

// Global variables for backward compatibility
let mapManager = null;

// Global functions for backward compatibility
function toggleLayerVisibility(layerId, forceState = null) {
    if (mapManager) {
        mapManager.toggleLayerVisibility(layerId, forceState);
    }
}

function updateLayerOpacity(layerId, opacity) {
    if (mapManager) {
        mapManager.updateLayerOpacity(layerId, opacity);
    }
}

function toggleBaseLayer(layerId, show = true) {
    if (mapManager) {
        mapManager.toggleBaseLayer(layerId, show);
    }
}

function toggleAllBaseLayers(show) {
    if (mapManager) {
        mapManager.toggleAllBaseLayers(show);
    }
}

function toggleAllGeneralLayers(show) {
    if (mapManager) {
        mapManager.toggleAllGeneralLayers(show);
    }
}

function toggleAllCadastreLayers(show) {
    if (mapManager) {
        mapManager.toggleAllCadastreLayers(show);
    }
}

function showLayerInfo(layerId, layerName, description, layerType, workspace) {
    if (mapManager) {
        mapManager.showLayerInfo(layerId, layerName, description, layerType, workspace);
    }
}
