// Map functionality for GeoSpatial application
class MapManager {
    constructor(config) {
        this.config = config;
        this.map = null;
        this.mapLayers = {};
        this.osmLayer = null;
        this.coordinateControl = null;
        this.transportStops = [];
        this.transportMarkers = L.layerGroup();
        
        this.init();
    }

    init() {
        this.setupProjections();
        this.initializeMap();
        this.initializeLayers();
        this.initializeTransportStops();
        this.setupControls();
        this.setupEventHandlers();
    }

    setupProjections() {
        // Define Bulgarian coordinate systems
        proj4.defs("EPSG:7801", "+proj=tmerc +lat_0=0 +lon_0=25 +k=0.9999 +x_0=500000 +y_0=0 +ellps=GRS80 +towgs84=0,0,0,0,0,0,0 +units=m +no_defs"); // BGS2005 / UTM zone 35N
        proj4.defs("EPSG:4326", "+proj=longlat +datum=WGS84 +no_defs"); // WGS84
    }

    initializeMap() {
        // Initialize map
        this.map = L.map('leaflet-map').setView([this.config.centerLat, this.config.centerLng], this.config.zoomLevel);
        
        // Add default base layer (OpenStreetMap) only if no base layers are configured
        if (!this.config.hasBaseLayers) {
            this.osmLayer = L.tileLayer('https://{s}.tile.openstreetmap.org/{z}/{x}/{y}.png', {
                attribution: '&copy; <a href="https://www.openstreetmap.org/copyright">OpenStreetMap</a> contributors'
            }).addTo(this.map);
        }
    }

    initializeLayers() {
        console.log('Starting layer initialization...');
        console.log('Total layers to initialize:', this.config.layers.length);

        // Debug: Check for cadastre layers
        const cadastreLayers = this.config.layers.filter(l => l.workspace.includes('cadastre'));
        console.log('Found cadastre layers:', cadastreLayers);

        // Initialize regular layers
        this.config.layers.forEach(layer => {
            console.log('Initializing layer:', layer.name, 'Type:', layer.layerType, 'Workspace:', layer.workspace);
            this.initializeLayer(layer, false);
        });
        
        // Initialize base layers
        console.log('Starting base layer initialization...');
        this.config.baseLayers.forEach(layer => {
            this.initializeLayer(layer, true);
        });
    }

    initializeLayer(layer, isBaseLayer) {
        try {
            const layerPrefix = isBaseLayer ? 'base' : 'regular';
            console.log(`Initializing ${layerPrefix} ${layer.layerType} layer: ${layer.name}`);
            console.log('Layer details:', {
                id: layer.id,
                name: layer.name,
                layerType: layer.layerType,
                workspace: layer.workspace,
                wmsUrl: layer.wmsUrl,
                isVisible: layer.isVisible
            });

            let leafletLayer;
            
            if (layer.layerType === "WMS") {
                // Special handling for INSPIRE cadastral layers
                let layersParam;
                if (layer.workspace.includes('cadastre')) {
                    layersParam = layer.layerName; // Use layer name directly for INSPIRE
                } else {
                    layersParam = `${layer.workspace}:${layer.layerName}`;
                }

                const wmsOptions = {
                    layers: layersParam,
                    format: 'image/png',
                    transparent: !isBaseLayer,
                    opacity: layer.opacity || 1.0,
                    attribution: `${isBaseLayer ? 'Base ' : ''}Layer: ${layer.name}`,
                    version: layer.workspace.includes('cadastre') ? '1.3.0' : '1.1.0',
                    crs: layer.workspace.includes('cadastre') ? L.CRS.EPSG3857 : L.CRS.EPSG4326
                };

                // Add zoom restrictions for cadastral layers
                if (layer.workspace.includes('cadastre')) {
                    if (layer.name.includes('парцели')) {
                        wmsOptions.minZoom = 14;
                    } else if (layer.name.includes('Сгради')) {
                        wmsOptions.minZoom = 15;
                    }
                    wmsOptions.maxZoom = 18;
                }

                leafletLayer = L.tileLayer.wms(layer.wmsUrl, wmsOptions);

                // Debug info for cadastral layers
                if (layer.workspace.includes('cadastre')) {
                    console.log(`=== CADASTRAL LAYER DEBUG ===`);
                    console.log(`Layer: ${layer.name}`);
                    console.log(`WMS URL: ${layer.wmsUrl}`);
                    console.log(`Layers param: ${layersParam}`);
                    console.log(`Version: ${wmsOptions.version}`);
                    console.log(`CRS: ${wmsOptions.crs === L.CRS.EPSG3857 ? 'EPSG:3857' : 'EPSG:4326'}`);
                    console.log(`Min zoom: ${wmsOptions.minZoom}`);
                    console.log(`Max zoom: ${wmsOptions.maxZoom}`);
                    console.log(`Current map zoom: ${this.map.getZoom()}`);
                }
            } else if (layer.layerType === "TILE") {
                leafletLayer = L.tileLayer(layer.wmsUrl, {
                    opacity: layer.opacity || 1.0,
                    attribution: `${isBaseLayer ? 'Base ' : ''}Layer: ${layer.name}`
                });
            }

            if (leafletLayer) {
                this.mapLayers[layer.id] = leafletLayer;
                console.log(`Layer stored in mapLayers with ID: ${layer.id}`);

                // Add to map if visible
                if (layer.isVisible) {
                    leafletLayer.addTo(this.map);
                    console.log(`Layer ${layer.name} added to map (visible by default)`);
                }

                // Add error handling
                leafletLayer.on('tileerror', (error) => {
                    console.error(`${layer.layerType} layer error for ${layer.name}:`, error);
                    console.error('WMS URL:', layer.wmsUrl);
                    console.error('Layer name:', layer.layerName);
                    console.error('Workspace:', layer.workspace);
                    console.error('Full WMS request URL:', error.tile?.src || 'Unknown');
                });

                leafletLayer.on('tileload', () => {
                    console.log(`${layer.layerType} tile loaded successfully for ${layer.name}`);
                });

                if (layer.layerType === "WMS") {
                    leafletLayer.on('loading', () => {
                        console.log(`WMS layer loading: ${layer.name}`);
                    });
                }
            }
        } catch (error) {
            console.error(`Error initializing layer ${layer.name}:`, error);
        }
    }

    initializeTransportStops() {
        console.log('Initializing transport stops...');
        // Реални спирки на градски транспорт в София с точни координати
        this.transportStops = [
            {
                id: 'stop_1',
                name: 'Сердика',
                type: 'metro_bus_tram',
                lat: 42.6977,
                lng: 23.3219,
                lines: {
                    metro: ['М1', 'М2'],
                    bus: ['9', '72', '84', '94'],
                    tram: ['1', '7', '12']
                },
                description: 'Централна метростанция - пресечна точка на М1 и М2 линии'
            },
            {
                id: 'stop_2',
                name: 'Университет',
                type: 'metro_bus',
                lat: 42.6934,
                lng: 23.3265,
                lines: {
                    metro: ['М2'],
                    bus: ['9', '72', '75', '76', '84', '94', '204', '213', '214', '280', '304', '306']
                },
                description: 'Метростанция "СУ Св. Климент Охридски" до Софийския университет'
            },
            {
                id: 'stop_3',
                name: 'НДК',
                type: 'metro_bus',
                lat: 42.6847,
                lng: 23.3188,
                lines: {
                    metro: ['М2'],
                    bus: ['9', '72', '76', '94', '204']
                },
                description: 'Метростанция "НДК" - Национален дворец на културата'
            },
            {
                id: 'stop_4',
                name: 'Лъвов мост',
                type: 'metro_bus_tram',
                lat: 42.7008,
                lng: 23.3157,
                lines: {
                    metro: ['М1'],
                    bus: ['72', '76', '84'],
                    tram: ['1', '7']
                },
                description: 'Метростанция "Лъвов мост" - важен транспортен възел'
            },
            {
                id: 'stop_5',
                name: 'Опълченска',
                type: 'metro_bus',
                lat: 42.7089,
                lng: 23.3157,
                lines: {
                    metro: ['М1'],
                    bus: ['72', '76', '204', '213']
                },
                description: 'Метростанция "Опълченска" в близост до Централна гара'
            },
            {
                id: 'stop_6',
                name: 'Вардар',
                type: 'metro_bus_tram',
                lat: 42.7042,
                lng: 23.3089,
                lines: {
                    metro: ['М1'],
                    bus: ['45'],
                    tram: ['8']
                },
                description: 'Метростанция "Вардар" с връзка към трамвай 8'
            },
            {
                id: 'stop_7',
                name: 'Западен парк',
                type: 'metro_bus_tram',
                lat: 42.7025,
                lng: 23.2956,
                lines: {
                    metro: ['М1'],
                    bus: ['310'],
                    tram: ['8']
                },
                description: 'Метростанция "Западен парк" до парка'
            },
            {
                id: 'stop_8',
                name: 'Стадион Васил Левски',
                type: 'metro_bus',
                lat: 42.6869,
                lng: 23.3388,
                lines: {
                    metro: ['М2'],
                    bus: ['9', '94', '204']
                },
                description: 'Метростанция до националния стадион "Васил Левски"'
            },
            {
                id: 'stop_9',
                name: 'Европейски съюз',
                type: 'metro_bus',
                lat: 42.6742,
                lng: 23.3156,
                lines: {
                    metro: ['М2'],
                    bus: ['102', '111', '120']
                },
                description: 'Метростанция "Европейски съюз" в южната част на града'
            },
            {
                id: 'stop_10',
                name: 'Джеймс Баучер',
                type: 'metro_bus',
                lat: 42.6758,
                lng: 23.3189,
                lines: {
                    metro: ['М2'],
                    bus: ['102', '111']
                },
                description: 'Метростанция "Джеймс Баучер" в района на Лозенец'
            },
            {
                id: 'stop_11',
                name: 'Пл. Александър Невски',
                type: 'bus_tram',
                lat: 42.6957,
                lng: 23.3327,
                lines: {
                    bus: ['9', '72', '84', '94'],
                    tram: ['12', '14']
                },
                description: 'Централна спирка до катедралата "Александър Невски"'
            },
            {
                id: 'stop_12',
                name: 'Централна гара',
                type: 'bus_tram',
                lat: 42.7135,
                lng: 23.3214,
                lines: {
                    bus: ['72', '76', '84', '204', '213'],
                    tram: ['1', '7']
                },
                description: 'Главна железопътна гара на София'
            }
        ];

        // Създаване на маркери за всяка спирка
        this.transportStops.forEach(stop => {
            console.log('Creating marker for stop:', stop.name);
            const marker = this.createTransportMarker(stop);
            this.transportMarkers.addLayer(marker);
        });

        console.log('Transport stops initialized. Total markers:', this.transportMarkers.getLayers().length);

        // Добавяне на слоя към картата (първоначално скрит)
        // this.transportMarkers.addTo(this.map);
        console.log('Transport stops initialized with', this.transportStops.length, 'real stops');
    }

    createTransportMarker(stop) {
        // Определяне на икона според типа спирка
        let iconHtml = '';
        let iconClass = 'transport-marker';

        if (stop.type.includes('metro')) {
            iconHtml = '<i class="fas fa-subway"></i>';
            iconClass += ' metro-stop';
        } else if (stop.type.includes('tram')) {
            iconHtml = '<i class="fas fa-train"></i>';
            iconClass += ' tram-stop';
        } else {
            iconHtml = '<i class="fas fa-bus"></i>';
            iconClass += ' bus-stop';
        }

        // Създаване на custom икона
        const customIcon = L.divIcon({
            html: iconHtml,
            className: iconClass,
            iconSize: [30, 30],
            iconAnchor: [15, 15],
            popupAnchor: [0, -15]
        });

        // Създаване на маркер
        const marker = L.marker([stop.lat, stop.lng], { icon: customIcon });

        // Създаване на popup съдържание
        const popupContent = this.createStopPopupContent(stop);
        marker.bindPopup(popupContent, {
            maxWidth: 350,
            className: 'transport-popup'
        });

        return marker;
    }

    createStopPopupContent(stop) {
        let linesHtml = '';

        // Метро линии
        if (stop.lines.metro && stop.lines.metro.length > 0) {
            linesHtml += `
                <div class="transport-lines">
                    <div class="line-type">
                        <i class="fas fa-subway"></i> <strong>Метро:</strong>
                    </div>
                    <div class="line-numbers">
                        ${stop.lines.metro.map(line => `<span class="line-badge metro-line">${line}</span>`).join('')}
                    </div>
                </div>
            `;
        }

        // Автобусни линии
        if (stop.lines.bus && stop.lines.bus.length > 0) {
            linesHtml += `
                <div class="transport-lines">
                    <div class="line-type">
                        <i class="fas fa-bus"></i> <strong>Автобуси:</strong>
                    </div>
                    <div class="line-numbers">
                        ${stop.lines.bus.map(line => `<span class="line-badge bus-line">${line}</span>`).join('')}
                    </div>
                </div>
            `;
        }

        // Трамвайни линии
        if (stop.lines.tram && stop.lines.tram.length > 0) {
            linesHtml += `
                <div class="transport-lines">
                    <div class="line-type">
                        <i class="fas fa-train"></i> <strong>Трамваи:</strong>
                    </div>
                    <div class="line-numbers">
                        ${stop.lines.tram.map(line => `<span class="line-badge tram-line">${line}</span>`).join('')}
                    </div>
                </div>
            `;
        }

        return `
            <div class="transport-stop-info">
                <h6 class="stop-title">
                    <i class="fas fa-map-marker-alt"></i> ${stop.name}
                </h6>
                <p class="stop-description">${stop.description}</p>
                ${linesHtml}
                <div class="stop-coordinates">
                    <small><strong>Координати:</strong> ${stop.lat.toFixed(6)}, ${stop.lng.toFixed(6)}</small>
                </div>
            </div>
        `;
    }

    setupControls() {
        // Add scale control if enabled
        if (this.config.showScaleControl) {
            L.control.scale().addTo(this.map);
        }
        
        // Create coordinate display control
        this.coordinateControl = L.control({position: 'bottomleft'});
        this.coordinateControl.onAdd = (map) => {
            const div = L.DomUtil.create('div', 'coordinate-display');
            div.innerHTML = `
                <div class="coordinate-box">
                    <div class="coordinate-row">
                        <span class="coordinate-label">BGS2005:</span>
                        <span id="bgs-coords">X: -, Y: -</span>
                    </div>
                    <div class="coordinate-row">
                        <span class="coordinate-label">WGS84:</span>
                        <span id="wgs-coords">Lat: -, Lng: -</span>
                    </div>
                </div>
            `;
            return div;
        };
        this.coordinateControl.addTo(this.map);
    }

    setupEventHandlers() {
        // Mouse move handler for coordinate display
        this.map.on('mousemove', (e) => {
            // Transform coordinates to BGS2005
            const wgs84 = [e.latlng.lng, e.latlng.lat];
            const bgs2005 = proj4('EPSG:4326', 'EPSG:7801', wgs84);
            
            // Update coordinate display
            const bgsElement = document.getElementById('bgs-coords');
            const wgsElement = document.getElementById('wgs-coords');
            
            if (bgsElement && wgsElement) {
                bgsElement.textContent = `X: ${bgs2005[0].toFixed(2)}, Y: ${bgs2005[1].toFixed(2)}`;
                wgsElement.textContent = `Lat: ${e.latlng.lat.toFixed(6)}, Lng: ${e.latlng.lng.toFixed(6)}`;
            }
        });
        
        // Click handler for feature info
        this.map.on('click', (e) => {
            console.log('Map clicked at:', e.latlng);
            this.getFeatureInfo(e);
        });

        // Initialize movable info window
        this.initializeInfoWindow();
    }

    // Layer control functions
    toggleLayerVisibility(layerId, forceState = null) {
        console.log('=== TOGGLE LAYER VISIBILITY DEBUG ===');
        console.log('Layer ID:', layerId);
        console.log('Force state:', forceState);
        console.log('Available layers in mapLayers:', Object.keys(this.mapLayers));
        console.log('All config layers:', this.config.layers.map(l => ({id: l.id, name: l.name, workspace: l.workspace})));
        console.log('Config layers count:', this.config.layers.length);
        console.log('MapLayers count:', Object.keys(this.mapLayers).length);

        const checkbox = document.querySelector(`input[onchange*="${layerId}"]`);
        const isChecked = forceState !== null ? forceState : (checkbox ? checkbox.checked : false);
        console.log('Checkbox checked:', isChecked);

        // Check if this is the transport stops layer
        const layerConfig = this.config.layers.find(l => l.id === layerId);
        console.log('Layer config found:', layerConfig ? layerConfig.name : 'NOT FOUND');

        // Debug for cadastral layers
        if (layerConfig && layerConfig.workspace && layerConfig.workspace.includes('cadastre')) {
            console.log('=== CADASTRAL LAYER TOGGLE DEBUG ===');
            console.log('Cadastral layer:', layerConfig.name);
            console.log('Workspace:', layerConfig.workspace);
            console.log('Is checked:', isChecked);
            console.log('Layer exists in mapLayers:', !!this.mapLayers[layerId]);
        }

        if (layerConfig && layerConfig.name === 'Обществен транспорт') {
            console.log('Handling transport stops layer. Markers count:', this.transportMarkers.getLayers().length);
            // Handle transport stops separately
            if (isChecked) {
                this.transportMarkers.addTo(this.map);
                console.log('Transport stops added to map');
            } else {
                this.map.removeLayer(this.transportMarkers);
                console.log('Transport stops removed from map');
            }

            // Update server state
            fetch(`/api/geolayerapi/toggle-visibility/${layerId}`, {
                method: 'POST',
                credentials: 'include',
                headers: {
                    'Content-Type': 'application/json'
                }
            }).catch(error => console.error('Error updating server state:', error));

            return;
        }

        fetch(`/api/geolayerapi/toggle-visibility/${layerId}`, {
            method: 'POST',
            credentials: 'include',
            headers: {
                'Content-Type': 'application/json'
            }
        })
        .then(response => {
            console.log('Response status:', response.status);
            if (response.ok) {
                // Toggle layer on map immediately
                const layer = this.mapLayers[layerId];
                console.log('Found layer in mapLayers:', layer ? 'YES' : 'NO');
                if (layer) {
                    if (isChecked) {
                        // Add layer to map
                        layer.addTo(this.map);
                        console.log('Layer added to map:', layerId);
                    } else {
                        // Remove layer from map
                        this.map.removeLayer(layer);
                        console.log('Layer removed from map:', layerId);
                    }

                    // Show/hide opacity control
                    const layerItem = document.querySelector(`[data-layer-id="${layerId}"]`);
                    const opacityControl = layerItem?.querySelector('.opacity-control');
                    if (opacityControl) {
                        opacityControl.style.display = isChecked ? 'flex' : 'none';
                    }
                } else {
                    console.error('Layer not found in mapLayers for ID:', layerId);
                    console.log('Available layer IDs:', Object.keys(this.mapLayers));
                }
            } else {
                // Revert checkbox state on error
                if (checkbox) {
                    checkbox.checked = !isChecked;
                }
                response.text().then(text => {
                    console.error('Error response:', text);
                    alert('Грешка при промяна на видимостта: ' + response.status);
                });
            }
        })
        .catch(error => {
            // Revert checkbox state on error
            if (checkbox) {
                checkbox.checked = !isChecked;
            }
            console.error('Error:', error);
            alert('Грешка при свързване със сървъра: ' + error.message);
        });
    }

    updateLayerOpacity(layerId, opacity) {
        fetch(`/api/geolayerapi/update-opacity/${layerId}`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            credentials: 'include',
            body: JSON.stringify(parseFloat(opacity))
        })
        .then(response => {
            if (response.ok) {
                // Update layer opacity on map
                if (this.mapLayers[layerId]) {
                    this.mapLayers[layerId].setOpacity(parseFloat(opacity));
                }
                
                // Update opacity display
                const opacityValue = document.querySelector(`[data-layer-id="${layerId}"] .opacity-value`);
                if (opacityValue) {
                    opacityValue.textContent = Math.round(parseFloat(opacity) * 100) + '%';
                }
            } else {
                alert('Грешка при промяна на прозрачността');
            }
        })
        .catch(error => {
            console.error('Error:', error);
            alert('Грешка при свързване със сървъра');
        });
    }

    toggleBaseLayer(layerId, show = true) {
        console.log('Selected base layer:', layerId, 'show:', show);

        // Hide all base layers first
        this.config.baseLayers.forEach(baseLayer => {
            const layer = this.mapLayers[baseLayer.id];
            if (layer && this.map.hasLayer(layer)) {
                this.map.removeLayer(layer);
            }
        });

        // Show selected base layer if show is true
        if (show) {
            const selectedLayer = this.mapLayers[layerId];
            if (selectedLayer) {
                selectedLayer.addTo(this.map);
                console.log('Base layer switched to:', layerId);
            } else {
                console.error('Base layer not found:', layerId);
            }
        }
    }

    // Toggle all base layers visibility
    toggleAllBaseLayers(show) {
        const content = document.getElementById('baseLayersContent');
        const radioButtons = content.querySelectorAll('input[type="radio"]');

        if (show) {
            content.classList.remove('hidden');
            // Enable radio buttons
            radioButtons.forEach(radio => radio.disabled = false);
            // Restore the previously selected base layer
            const checkedRadio = document.querySelector('input[name="baseLayer"]:checked');
            if (checkedRadio) {
                this.toggleBaseLayer(checkedRadio.value, true);
            }
        } else {
            content.classList.add('hidden');
            // Hide all base layers first
            this.config.baseLayers.forEach(baseLayer => {
                const layer = this.mapLayers[baseLayer.id];
                if (layer && this.map.hasLayer(layer)) {
                    this.map.removeLayer(layer);
                }
            });
            // Disable radio buttons
            radioButtons.forEach(radio => {
                radio.disabled = true;
            });
        }
    }

    // Toggle all general layers visibility
    toggleAllGeneralLayers(show) {
        const content = document.getElementById('generalLayersContent');
        const checkboxes = content.querySelectorAll('input[type="checkbox"]');

        if (show) {
            content.classList.remove('hidden');
            // Enable checkboxes
            checkboxes.forEach(checkbox => checkbox.disabled = false);
        } else {
            content.classList.add('hidden');
            // Disable checkboxes and uncheck them
            checkboxes.forEach(checkbox => {
                checkbox.disabled = true;
                if (checkbox.checked) {
                    checkbox.checked = false;
                    // Hide the layer
                    const layerItem = checkbox.closest('.layer-item');
                    const layerId = layerItem.getAttribute('data-layer-id');
                    this.toggleLayerVisibility(layerId, false);
                }
            });
        }
    }

    // Toggle all cadastre layers visibility
    toggleAllCadastreLayers(show) {
        const content = document.getElementById('cadastreLayersContent');
        const checkboxes = content.querySelectorAll('input[type="checkbox"]');

        if (show) {
            content.classList.remove('hidden');
            // Enable checkboxes
            checkboxes.forEach(checkbox => checkbox.disabled = false);
        } else {
            content.classList.add('hidden');
            // Disable checkboxes and uncheck them
            checkboxes.forEach(checkbox => {
                checkbox.disabled = true;
                if (checkbox.checked) {
                    checkbox.checked = false;
                    // Hide the layer
                    const layerItem = checkbox.closest('.layer-item');
                    const layerId = layerItem.getAttribute('data-layer-id');
                    this.toggleLayerVisibility(layerId, false);
                }
            });
        }
    }

    // Check if cadastral layers are visible at current zoom
    checkCadastralLayerVisibility() {
        const currentZoom = this.map.getZoom();
        const cadastralLayers = this.config.layers.filter(l => l.workspace.includes('cadastre'));

        cadastralLayers.forEach(layer => {
            const minZoom = layer.name.includes('парцели') ? 14 : 15;
            if (currentZoom < minZoom) {
                this.showZoomWarning(layer.name, minZoom);
            }
        });
    }

    // Show zoom warning for cadastral layers
    showZoomWarning(layerName, minZoom) {
        const message = `${layerName} са видими само при zoom ≥ ${minZoom}. Текущ zoom: ${this.map.getZoom()}`;
        console.warn(message);

        // You can add a toast notification here if needed
        if (window.showToast) {
            window.showToast(message, 'warning');
        }
    }

    showLayerInfo(layerId, layerName, description, layerType, workspace) {
        // Populate modal
        document.getElementById('layerInfoModalLabel').textContent = layerName;
        document.getElementById('layerInfoDescription').textContent = description;
        document.getElementById('layerInfoType').textContent = layerType;
        document.getElementById('layerInfoWorkspace').textContent = workspace;

        // Show modal
        const modal = new bootstrap.Modal(document.getElementById('layerInfoModal'));
        modal.show();
    }

    getFeatureInfo(e) {
        // Get active layers that support feature info
        const activeLayers = this.getActiveWMSLayers();

        if (activeLayers.length === 0) {
            return; // No active WMS layers to query
        }

        console.log('Getting feature info for active layers:', activeLayers.map(l => l.name));

        // Show info window and start loading
        this.showInfoWindow(e.containerPoint);
        this.setInfoWindowContent('<div class="loading-spinner">Зареждане информация...</div>');

        // Query all active layers
        this.queryAllActiveLayers(e.latlng, activeLayers);
    }

    getActiveWMSLayers() {
        const activeLayers = [];

        // Check which layers are currently visible on the map
        this.config.layers.forEach(layer => {
            const leafletLayer = this.mapLayers[layer.id];
            if (leafletLayer && this.map.hasLayer(leafletLayer)) {
                activeLayers.push({
                    id: layer.id,
                    name: layer.name,
                    layerType: layer.layerType,
                    workspace: layer.workspace,
                    layerName: layer.layerName
                });
            }
        });

        return activeLayers;
    }

    // Initialize movable info window
    initializeInfoWindow() {
        const infoWindow = document.getElementById('infoWindow');
        const header = document.getElementById('infoWindowHeader');

        let isDragging = false;
        let currentX;
        let currentY;
        let initialX;
        let initialY;
        let xOffset = 0;
        let yOffset = 0;

        header.addEventListener('mousedown', dragStart);
        document.addEventListener('mousemove', drag);
        document.addEventListener('mouseup', dragEnd);

        function dragStart(e) {
            initialX = e.clientX - xOffset;
            initialY = e.clientY - yOffset;

            if (e.target === header || header.contains(e.target)) {
                isDragging = true;
            }
        }

        function drag(e) {
            if (isDragging) {
                e.preventDefault();
                currentX = e.clientX - initialX;
                currentY = e.clientY - initialY;

                xOffset = currentX;
                yOffset = currentY;

                infoWindow.style.transform = `translate3d(${currentX}px, ${currentY}px, 0)`;
            }
        }

        function dragEnd(e) {
            initialX = currentX;
            initialY = currentY;
            isDragging = false;
        }
    }

    // Show info window at specific position
    showInfoWindow(containerPoint) {
        const infoWindow = document.getElementById('infoWindow');
        infoWindow.style.display = 'block';
        infoWindow.style.left = (containerPoint.x + 10) + 'px';
        infoWindow.style.top = (containerPoint.y + 10) + 'px';
    }

    // Set content of info window
    setInfoWindowContent(content) {
        document.getElementById('infoWindowContent').innerHTML = content;
    }

    // Query all active layers for feature info
    async queryAllActiveLayers(latlng, activeLayers) {
        const results = [];

        for (const layer of activeLayers) {
            try {
                const info = await this.queryLayerFeatureInfo(latlng, layer);
                if (info) {
                    results.push({
                        layerName: layer.name,
                        info: info
                    });
                }
            } catch (error) {
                console.error(`Error querying layer ${layer.name}:`, error);
            }
        }

        // Display results
        this.displayFeatureInfoResults(latlng, results);
    }

    // Query specific layer for feature info
    async queryLayerFeatureInfo(latlng, layer) {
        // Handle different layer types
        if (layer.workspace && layer.workspace.includes('cadastre')) {
            return await this.queryCadastralLayer(latlng, layer);
        } else if (layer.name.includes('транспорт') || layer.name.includes('Железопътни') || layer.name.includes('Велоалеи')) {
            return this.getTransportInfo(latlng, layer.name);
        }

        return null;
    }

    // Query cadastral layers using GetFeatureInfo
    async queryCadastralLayer(latlng, layer) {
        const map = this.map;
        const point = map.latLngToContainerPoint(latlng);
        const size = map.getSize();
        const bounds = map.getBounds();

        // Convert bounds to the appropriate CRS
        const sw = bounds.getSouthWest();
        const ne = bounds.getNorthEast();

        const params = new URLSearchParams({
            service: 'WMS',
            version: '1.3.0',
            request: 'GetFeatureInfo',
            layers: layer.layerName,
            query_layers: layer.layerName,
            styles: '',
            bbox: `${sw.lng},${sw.lat},${ne.lng},${ne.lat}`,
            width: size.x,
            height: size.y,
            crs: 'EPSG:4326',
            info_format: 'application/json',
            i: Math.floor(point.x),
            j: Math.floor(point.y)
        });

        const url = layer.workspace.includes('cadastre_parcels')
            ? 'https://inspire.cadastre.bg/arcgis/services/Cadastral_Parcel/MapServer/WMSServer'
            : 'https://inspire.cadastre.bg/arcgis/services/Building/MapServer/WMSServer';

        try {
            const response = await fetch(`${url}?${params}`);
            const data = await response.json();

            if (data.features && data.features.length > 0) {
                return this.formatCadastralInfo(data.features[0], layer.name);
            }
        } catch (error) {
            console.error('Error querying cadastral layer:', error);
            // Fallback to basic coordinate info
            return this.getBasicLocationInfo(latlng, layer.name);
        }

        return null;
    }

    // Format cadastral information
    formatCadastralInfo(feature, layerName) {
        const props = feature.properties || {};

        if (layerName.includes('Кадастрални')) {
            return {
                title: 'Кадастрален парцел',
                data: {
                    'Номер на парцел': props.parcelNumber || props.PARCEL_NUMBER || 'Няма данни',
                    'Кадастрален район': props.cadastralArea || props.CADASTRAL_AREA || 'Няма данни',
                    'Площ': props.area ? `${props.area} кв.м` : 'Няма данни',
                    'Тип собственост': props.ownershipType || props.OWNERSHIP_TYPE || 'Няма данни',
                    'Статус': props.status || props.STATUS || 'Няма данни'
                }
            };
        } else if (layerName.includes('Сгради')) {
            return {
                title: 'Сграда',
                data: {
                    'Тип сграда': props.buildingType || props.BUILDING_TYPE || 'Няма данни',
                    'Брой етажи': props.floors || props.FLOORS || 'Няма данни',
                    'Площ': props.area ? `${props.area} кв.м` : 'Няма данни',
                    'Година на строеж': props.constructionYear || props.CONSTRUCTION_YEAR || 'Няма данни',
                    'Статус': props.status || props.STATUS || 'Няма данни'
                }
            };
        }

        return null;
    }

    // Get basic location info as fallback
    getBasicLocationInfo(latlng, layerName) {
        const wgs84 = [latlng.lng, latlng.lat];
        const bgs2005 = proj4('EPSG:4326', 'EPSG:7801', wgs84);

        return {
            title: layerName,
            data: {
                'Координати (WGS84)': `${latlng.lat.toFixed(6)}, ${latlng.lng.toFixed(6)}`,
                'Координати (BGS2005)': `${bgs2005[0].toFixed(2)}, ${bgs2005[1].toFixed(2)}`,
                'Статус': 'Слой е активен, но няма данни за тази точка'
            }
        };
    }

    // Get transport info (existing functionality)
    getTransportInfo(latlng, layerName) {
        const wgs84 = [latlng.lng, latlng.lat];
        const bgs2005 = proj4('EPSG:4326', 'EPSG:7801', wgs84);

        let content = '';
        let icon = '';

        if (layerName === 'Обществен транспорт') {
            icon = '🚌';
            content = 'Информация за обществен транспорт в района';
        } else if (layerName === 'Железопътни линии') {
            icon = '🚂';
            content = 'Информация за железопътни линии';
        } else if (layerName === 'Велоалеи') {
            icon = '🚴';
            content = 'Информация за велосипедни алеи';
        }

        return {
            title: `${icon} ${layerName}`,
            data: {
                'Координати (WGS84)': `${latlng.lat.toFixed(6)}, ${latlng.lng.toFixed(6)}`,
                'Координати (BGS2005)': `${bgs2005[0].toFixed(2)}, ${bgs2005[1].toFixed(2)}`,
                'Информация': content
            }
        };
    }

    // Display feature info results in the movable window
    displayFeatureInfoResults(latlng, results) {
        if (results.length === 0) {
            this.setInfoWindowContent('<div class="info-section"><p>Няма информация за тази точка от активните слоеве.</p></div>');
            return;
        }

        const wgs84 = [latlng.lng, latlng.lat];
        const bgs2005 = proj4('EPSG:4326', 'EPSG:7801', wgs84);

        let content = `
            <div class="info-section">
                <h4>📍 Координати</h4>
                <p><strong>WGS84:</strong> ${latlng.lat.toFixed(6)}, ${latlng.lng.toFixed(6)}</p>
                <p><strong>BGS2005:</strong> ${bgs2005[0].toFixed(2)}, ${bgs2005[1].toFixed(2)}</p>
            </div>
        `;

        results.forEach(result => {
            content += `
                <div class="info-section">
                    <h4>${result.info.title}</h4>
            `;

            Object.entries(result.info.data).forEach(([key, value]) => {
                content += `<p><strong>${key}:</strong> ${value}</p>`;
            });

            content += '</div>';
        });

        this.setInfoWindowContent(content);
    }
}

// Global function to close info window
function closeInfoWindow() {
    document.getElementById('infoWindow').style.display = 'none';
}

// Global variables for backward compatibility
let mapManager = null;

// Global functions for backward compatibility
function toggleLayerVisibility(layerId, forceState = null) {
    if (mapManager) {
        mapManager.toggleLayerVisibility(layerId, forceState);
    }
}

function updateLayerOpacity(layerId, opacity) {
    if (mapManager) {
        mapManager.updateLayerOpacity(layerId, opacity);
    }
}

function toggleBaseLayer(layerId, show = true) {
    if (mapManager) {
        mapManager.toggleBaseLayer(layerId, show);
    }
}

function toggleAllBaseLayers(show) {
    if (mapManager) {
        mapManager.toggleAllBaseLayers(show);
    }
}

function toggleAllGeneralLayers(show) {
    if (mapManager) {
        mapManager.toggleAllGeneralLayers(show);
    }
}

function toggleAllCadastreLayers(show) {
    if (mapManager) {
        mapManager.toggleAllCadastreLayers(show);
    }
}

function showLayerInfo(layerId, layerName, description, layerType, workspace) {
    if (mapManager) {
        mapManager.showLayerInfo(layerId, layerName, description, layerType, workspace);
    }
}
