// Map functionality for GeoSpatial application
class MapManager {
    constructor(config) {
        this.config = config;
        this.map = null;
        this.mapLayers = {};
        this.osmLayer = null;
        this.coordinateControl = null;
        
        this.init();
    }

    init() {
        this.setupProjections();
        this.initializeMap();
        this.initializeLayers();
        this.setupControls();
        this.setupEventHandlers();
    }

    setupProjections() {
        // Define Bulgarian coordinate systems
        proj4.defs("EPSG:7801", "+proj=tmerc +lat_0=0 +lon_0=25 +k=0.9999 +x_0=500000 +y_0=0 +ellps=GRS80 +towgs84=0,0,0,0,0,0,0 +units=m +no_defs"); // BGS2005 / UTM zone 35N
        proj4.defs("EPSG:4326", "+proj=longlat +datum=WGS84 +no_defs"); // WGS84
    }

    initializeMap() {
        // Initialize map
        this.map = L.map('leaflet-map').setView([this.config.centerLat, this.config.centerLng], this.config.zoomLevel);
        
        // Add default base layer (OpenStreetMap) only if no base layers are configured
        if (!this.config.hasBaseLayers) {
            this.osmLayer = L.tileLayer('https://{s}.tile.openstreetmap.org/{z}/{x}/{y}.png', {
                attribution: '&copy; <a href="https://www.openstreetmap.org/copyright">OpenStreetMap</a> contributors'
            }).addTo(this.map);
        }
    }

    initializeLayers() {
        console.log('Starting layer initialization...');
        
        // Initialize regular layers
        this.config.layers.forEach(layer => {
            this.initializeLayer(layer, false);
        });
        
        // Initialize base layers
        console.log('Starting base layer initialization...');
        this.config.baseLayers.forEach(layer => {
            this.initializeLayer(layer, true);
        });
    }

    initializeLayer(layer, isBaseLayer) {
        try {
            const layerPrefix = isBaseLayer ? 'base' : 'regular';
            console.log(`Initializing ${layerPrefix} ${layer.layerType} layer: ${layer.name}`);
            
            let leafletLayer;
            
            if (layer.layerType === "WMS") {
                leafletLayer = L.tileLayer.wms(layer.wmsUrl, {
                    layers: `${layer.workspace}:${layer.layerName}`,
                    format: 'image/png',
                    transparent: !isBaseLayer,
                    opacity: layer.opacity || 1.0,
                    attribution: `${isBaseLayer ? 'Base ' : ''}Layer: ${layer.name}`,
                    version: '1.1.0',
                    crs: L.CRS.EPSG4326
                });
            } else if (layer.layerType === "TILE") {
                leafletLayer = L.tileLayer(layer.wmsUrl, {
                    opacity: layer.opacity || 1.0,
                    attribution: `${isBaseLayer ? 'Base ' : ''}Layer: ${layer.name}`
                });
            }

            if (leafletLayer) {
                this.mapLayers[layer.id] = leafletLayer;
                console.log(`Layer stored in mapLayers with ID: ${layer.id}`);

                // Add to map if visible
                if (layer.isVisible) {
                    leafletLayer.addTo(this.map);
                    console.log(`Layer ${layer.name} added to map (visible by default)`);
                }

                // Add error handling
                leafletLayer.on('tileerror', (error) => {
                    console.error(`${layer.layerType} layer error for ${layer.name}:`, error);
                });

                leafletLayer.on('tileload', () => {
                    console.log(`${layer.layerType} tile loaded successfully for ${layer.name}`);
                });

                if (layer.layerType === "WMS") {
                    leafletLayer.on('loading', () => {
                        console.log(`WMS layer loading: ${layer.name}`);
                    });
                }
            }
        } catch (error) {
            console.error(`Error initializing layer ${layer.name}:`, error);
        }
    }

    setupControls() {
        // Add scale control if enabled
        if (this.config.showScaleControl) {
            L.control.scale().addTo(this.map);
        }
        
        // Create coordinate display control
        this.coordinateControl = L.control({position: 'bottomleft'});
        this.coordinateControl.onAdd = (map) => {
            const div = L.DomUtil.create('div', 'coordinate-display');
            div.innerHTML = `
                <div class="coordinate-box">
                    <div class="coordinate-row">
                        <span class="coordinate-label">BGS2005:</span>
                        <span id="bgs-coords">X: -, Y: -</span>
                    </div>
                    <div class="coordinate-row">
                        <span class="coordinate-label">WGS84:</span>
                        <span id="wgs-coords">Lat: -, Lng: -</span>
                    </div>
                </div>
            `;
            return div;
        };
        this.coordinateControl.addTo(this.map);
    }

    setupEventHandlers() {
        // Mouse move handler for coordinate display
        this.map.on('mousemove', (e) => {
            // Transform coordinates to BGS2005
            const wgs84 = [e.latlng.lng, e.latlng.lat];
            const bgs2005 = proj4('EPSG:4326', 'EPSG:7801', wgs84);
            
            // Update coordinate display
            const bgsElement = document.getElementById('bgs-coords');
            const wgsElement = document.getElementById('wgs-coords');
            
            if (bgsElement && wgsElement) {
                bgsElement.textContent = `X: ${bgs2005[0].toFixed(2)}, Y: ${bgs2005[1].toFixed(2)}`;
                wgsElement.textContent = `Lat: ${e.latlng.lat.toFixed(6)}, Lng: ${e.latlng.lng.toFixed(6)}`;
            }
        });
        
        // Click handler for feature info only
        this.map.on('click', (e) => {
            console.log('Map clicked at:', e.latlng);
            // TODO: Implement GetFeatureInfo request for active layers
        });
    }

    // Layer control functions
    toggleLayerVisibility(layerId, forceState = null) {
        console.log('Toggling layer visibility for:', layerId, 'forceState:', forceState);
        console.log('Available layers in mapLayers:', Object.keys(this.mapLayers));

        const checkbox = document.querySelector(`input[onchange*="${layerId}"]`);
        const isChecked = forceState !== null ? forceState : (checkbox ? checkbox.checked : false);
        console.log('Checkbox checked:', isChecked);

        fetch(`/api/geolayerapi/toggle-visibility/${layerId}`, {
            method: 'POST',
            credentials: 'include',
            headers: {
                'Content-Type': 'application/json'
            }
        })
        .then(response => {
            console.log('Response status:', response.status);
            if (response.ok) {
                // Toggle layer on map immediately
                const layer = this.mapLayers[layerId];
                console.log('Found layer in mapLayers:', layer ? 'YES' : 'NO');
                if (layer) {
                    if (isChecked) {
                        // Add layer to map
                        layer.addTo(this.map);
                        console.log('Layer added to map:', layerId);
                    } else {
                        // Remove layer from map
                        this.map.removeLayer(layer);
                        console.log('Layer removed from map:', layerId);
                    }

                    // Show/hide opacity control
                    const layerItem = document.querySelector(`[data-layer-id="${layerId}"]`);
                    const opacityControl = layerItem?.querySelector('.opacity-control');
                    if (opacityControl) {
                        opacityControl.style.display = isChecked ? 'flex' : 'none';
                    }
                } else {
                    console.error('Layer not found in mapLayers for ID:', layerId);
                    console.log('Available layer IDs:', Object.keys(this.mapLayers));
                }
            } else {
                // Revert checkbox state on error
                if (checkbox) {
                    checkbox.checked = !isChecked;
                }
                response.text().then(text => {
                    console.error('Error response:', text);
                    alert('Грешка при промяна на видимостта: ' + response.status);
                });
            }
        })
        .catch(error => {
            // Revert checkbox state on error
            if (checkbox) {
                checkbox.checked = !isChecked;
            }
            console.error('Error:', error);
            alert('Грешка при свързване със сървъра: ' + error.message);
        });
    }

    updateLayerOpacity(layerId, opacity) {
        fetch(`/api/geolayerapi/update-opacity/${layerId}`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            credentials: 'include',
            body: JSON.stringify(parseFloat(opacity))
        })
        .then(response => {
            if (response.ok) {
                // Update layer opacity on map
                if (this.mapLayers[layerId]) {
                    this.mapLayers[layerId].setOpacity(parseFloat(opacity));
                }
                
                // Update opacity display
                const opacityValue = document.querySelector(`[data-layer-id="${layerId}"] .opacity-value`);
                if (opacityValue) {
                    opacityValue.textContent = Math.round(parseFloat(opacity) * 100) + '%';
                }
            } else {
                alert('Грешка при промяна на прозрачността');
            }
        })
        .catch(error => {
            console.error('Error:', error);
            alert('Грешка при свързване със сървъра');
        });
    }

    toggleBaseLayer(layerId, show = true) {
        console.log('Selected base layer:', layerId, 'show:', show);

        // Hide all base layers first
        this.config.baseLayers.forEach(baseLayer => {
            const layer = this.mapLayers[baseLayer.id];
            if (layer && this.map.hasLayer(layer)) {
                this.map.removeLayer(layer);
            }
        });

        // Show selected base layer if show is true
        if (show) {
            const selectedLayer = this.mapLayers[layerId];
            if (selectedLayer) {
                selectedLayer.addTo(this.map);
                console.log('Base layer switched to:', layerId);
            } else {
                console.error('Base layer not found:', layerId);
            }
        }
    }

    // Toggle all base layers visibility
    toggleAllBaseLayers(show) {
        const content = document.getElementById('baseLayersContent');
        const radioButtons = content.querySelectorAll('input[type="radio"]');

        if (show) {
            content.classList.remove('hidden');
            // Enable radio buttons
            radioButtons.forEach(radio => radio.disabled = false);
            // Restore the previously selected base layer
            const checkedRadio = document.querySelector('input[name="baseLayer"]:checked');
            if (checkedRadio) {
                this.toggleBaseLayer(checkedRadio.value, true);
            }
        } else {
            content.classList.add('hidden');
            // Hide all base layers first
            this.config.baseLayers.forEach(baseLayer => {
                const layer = this.mapLayers[baseLayer.id];
                if (layer && this.map.hasLayer(layer)) {
                    this.map.removeLayer(layer);
                }
            });
            // Disable radio buttons
            radioButtons.forEach(radio => {
                radio.disabled = true;
            });
        }
    }

    // Toggle all general layers visibility
    toggleAllGeneralLayers(show) {
        const content = document.getElementById('generalLayersContent');
        const checkboxes = content.querySelectorAll('input[type="checkbox"]');

        if (show) {
            content.classList.remove('hidden');
            // Enable checkboxes
            checkboxes.forEach(checkbox => checkbox.disabled = false);
        } else {
            content.classList.add('hidden');
            // Disable checkboxes and uncheck them
            checkboxes.forEach(checkbox => {
                checkbox.disabled = true;
                if (checkbox.checked) {
                    checkbox.checked = false;
                    // Hide the layer
                    const layerItem = checkbox.closest('.layer-item');
                    const layerId = layerItem.getAttribute('data-layer-id');
                    this.toggleLayerVisibility(layerId, false);
                }
            });
        }
    }

    showLayerInfo(layerId, layerName, description, layerType, workspace) {
        // Populate modal
        document.getElementById('layerInfoModalLabel').textContent = layerName;
        document.getElementById('layerInfoDescription').textContent = description;
        document.getElementById('layerInfoType').textContent = layerType;
        document.getElementById('layerInfoWorkspace').textContent = workspace;

        // Show modal
        const modal = new bootstrap.Modal(document.getElementById('layerInfoModal'));
        modal.show();
    }
}

// Global variables for backward compatibility
let mapManager = null;

// Global functions for backward compatibility
function toggleLayerVisibility(layerId, forceState = null) {
    if (mapManager) {
        mapManager.toggleLayerVisibility(layerId, forceState);
    }
}

function updateLayerOpacity(layerId, opacity) {
    if (mapManager) {
        mapManager.updateLayerOpacity(layerId, opacity);
    }
}

function toggleBaseLayer(layerId, show = true) {
    if (mapManager) {
        mapManager.toggleBaseLayer(layerId, show);
    }
}

function toggleAllBaseLayers(show) {
    if (mapManager) {
        mapManager.toggleAllBaseLayers(show);
    }
}

function toggleAllGeneralLayers(show) {
    if (mapManager) {
        mapManager.toggleAllGeneralLayers(show);
    }
}

function showLayerInfo(layerId, layerName, description, layerType, workspace) {
    if (mapManager) {
        mapManager.showLayerInfo(layerId, layerName, description, layerType, workspace);
    }
}
