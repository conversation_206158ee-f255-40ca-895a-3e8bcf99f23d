﻿@using Microsoft.AspNetCore.Identity
@using TaskManager.Services.Data.Interfaces;

@inject SignInManager<ApplicationUser> SignInManager
@inject UserManager<ApplicationUser> UserManager
@inject IUserService UserService;
@{
    string fullName = await this.UserService.GetFullNameByEmailAsync(User.Identity?.Name!);
}

<ul class="navbar-nav modern-auth-nav">
@if (SignInManager.IsSignedIn(User))
{
    <li class="nav-item">
        <div class="modern-user-info">
            <div class="user-avatar-small">
                <i class="fas fa-user"></i>
            </div>
            <span class="user-name">@fullName</span>
        </div>
    </li>
    <li class="nav-item">
        <form class="form-inline" asp-area="Identity" asp-page="/Account/Logout" asp-route-returnUrl="@Url.Action("Index", "Home", new { area = "" })">
            <button type="submit" class="modern-logout-btn">
                <i class="fas fa-sign-out-alt"></i>
                Изход
            </button>
        </form>
    </li>
}
else
{
    <li class="nav-item">
        <a class="modern-auth-link register" asp-area="" asp-controller="User" asp-action="Register">
            <i class="fas fa-user-plus"></i>
            Регистрация
        </a>
    </li>
    <li class="nav-item">
        <a class="modern-auth-link login" asp-area="" asp-controller="User" asp-action="Login">
            <i class="fas fa-sign-in-alt"></i>
            Влизане
        </a>
    </li>
}
</ul>
