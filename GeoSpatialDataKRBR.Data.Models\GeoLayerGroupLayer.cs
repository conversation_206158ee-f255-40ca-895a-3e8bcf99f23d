namespace GeoSpatialDataKRBR.Data.Models
{
    using System.ComponentModel.DataAnnotations;
    using System.ComponentModel.DataAnnotations.Schema;

    public class GeoLayerGroupLayer
    {
        public Guid GeoLayerGroupId { get; set; }

        public virtual GeoLayerGroup? GeoLayerGroup { get; set; }

        public Guid GeoLayerId { get; set; }

        public virtual GeoLayer? GeoLayer { get; set; }

        public int DisplayOrder { get; set; } = 0;

        public DateTime CreatedOn { get; set; } = DateTime.UtcNow;
    }
}
