namespace GeoSpatialDataKRBR.Data.Models
{
    using System.ComponentModel.DataAnnotations;
    using System.ComponentModel.DataAnnotations.Schema;

    public class GeoLayerGroupLayer
    {
        [Required]
        public Guid GeoLayerGroupId { get; set; }

        [ForeignKey(nameof(GeoLayerGroupId))]
        public virtual GeoLayerGroup GeoLayerGroup { get; set; } = null!;

        [Required]
        public Guid GeoLayerId { get; set; }

        [ForeignKey(nameof(GeoLayerId))]
        public virtual GeoLayer GeoLayer { get; set; } = null!;

        public int DisplayOrder { get; set; } = 0;

        public DateTime CreatedOn { get; set; } = DateTime.UtcNow;
    }
}
