using System.Text.Json;
using System.Text.Json.Serialization;

namespace TaskManager.Infrastructure
{
    public class DateTimeConverter : JsonConverter<DateTime>
    {
        public override DateTime Read(ref Utf8JsonReader reader, Type typeToConvert, JsonSerializerOptions options)
        {
            var dateString = reader.GetString();
            if (DateTime.TryParse(dateString, out var date))
            {
                // Always treat as unspecified to prevent timezone conversion
                return DateTime.SpecifyKind(date, DateTimeKind.Unspecified);
            }
            return default;
        }

        public override void Write(Utf8JsonWriter writer, DateTime value, JsonSerializerOptions options)
        {
            // Always write as date-only string to prevent timezone conversion
            var dateOnly = new DateTime(value.Year, value.Month, value.Day);
            writer.WriteStringValue(dateOnly.ToString("yyyy-MM-dd"));
        }
    }
}
