namespace GeoSpatialDataKRBR.Data.Models
{
    using System.ComponentModel.DataAnnotations;
    using System.ComponentModel.DataAnnotations.Schema;
    using static Common.EntityValidationConstants.UserLayerPreference;

    public class UserLayerPreference
    {
        public Guid UserId { get; set; }

        public virtual ApplicationUser? User { get; set; }

        public Guid GeoLayerId { get; set; }

        public virtual GeoLayer? GeoLayer { get; set; }

        public bool IsVisible { get; set; } = true;

        public double? Opacity { get; set; } = 1.0;

        [MaxLength(ColorMaxLength)]
        public string? CustomColor { get; set; }

        public int DisplayOrder { get; set; } = 0;

        public DateTime CreatedOn { get; set; } = DateTime.UtcNow;

        public DateTime? ModifiedOn { get; set; }
    }
}
