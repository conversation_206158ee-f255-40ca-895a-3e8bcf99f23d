namespace GeoSpatialDataKRBR.Data.Models
{
    using System.ComponentModel.DataAnnotations;
    using System.ComponentModel.DataAnnotations.Schema;
    using static Common.EntityValidationConstants.UserLayerPreference;

    public class UserLayerPreference
    {
        [Required]
        public Guid UserId { get; set; }

        [ForeignKey(nameof(UserId))]
        public virtual ApplicationUser User { get; set; } = null!;

        [Required]
        public Guid GeoLayerId { get; set; }

        [ForeignKey(nameof(GeoLayerId))]
        public virtual GeoLayer GeoLayer { get; set; } = null!;

        public bool IsVisible { get; set; } = true;

        public double? Opacity { get; set; } = 1.0;

        [MaxLength(ColorMaxLength)]
        public string? CustomColor { get; set; }

        public int DisplayOrder { get; set; } = 0;

        public DateTime CreatedOn { get; set; } = DateTime.UtcNow;

        public DateTime? ModifiedOn { get; set; }
    }
}
