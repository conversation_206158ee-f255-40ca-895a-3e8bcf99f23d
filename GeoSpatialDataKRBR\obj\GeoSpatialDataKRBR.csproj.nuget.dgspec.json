{"format": 1, "restore": {"D:\\C#Web\\IKRB_real\\GeoSpatialDataKRBR\\GeoSpatialDataKRBR.csproj": {}}, "projects": {"D:\\C#Web\\IKRB_real\\GeoSpatialDataKRBR.Common\\GeoSpatialDataKRBR.Common.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "D:\\C#Web\\IKRB_real\\GeoSpatialDataKRBR.Common\\GeoSpatialDataKRBR.Common.csproj", "projectName": "GeoSpatialDataKRBR.Common", "projectPath": "D:\\C#Web\\IKRB_real\\GeoSpatialDataKRBR.Common\\GeoSpatialDataKRBR.Common.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "D:\\C#Web\\IKRB_real\\GeoSpatialDataKRBR.Common\\obj\\", "projectStyle": "PackageReference", "fallbackFolders": ["C:\\Program Files (x86)\\Microsoft Visual Studio\\Shared\\NuGetPackages"], "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.FallbackLocation.config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config"], "originalTargetFrameworks": ["net7.0"], "sources": {"C:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\": {}, "https://api.nuget.org/v3/index.json": {}}, "frameworks": {"net7.0": {"targetAlias": "net7.0", "projectReferences": {}}}, "warningProperties": {"warnAsError": ["NU1605"]}}, "frameworks": {"net7.0": {"targetAlias": "net7.0", "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "frameworkReferences": {"Microsoft.NETCore.App": {"privateAssets": "all"}}, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\7.0.100\\RuntimeIdentifierGraph.json"}}}, "D:\\C#Web\\IKRB_real\\GeoSpatialDataKRBR.Data.Models\\GeoSpatialDataKRBR.Data.Models.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "D:\\C#Web\\IKRB_real\\GeoSpatialDataKRBR.Data.Models\\GeoSpatialDataKRBR.Data.Models.csproj", "projectName": "GeoSpatialDataKRBR.Data.Models", "projectPath": "D:\\C#Web\\IKRB_real\\GeoSpatialDataKRBR.Data.Models\\GeoSpatialDataKRBR.Data.Models.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "D:\\C#Web\\IKRB_real\\GeoSpatialDataKRBR.Data.Models\\obj\\", "projectStyle": "PackageReference", "fallbackFolders": ["C:\\Program Files (x86)\\Microsoft Visual Studio\\Shared\\NuGetPackages"], "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.FallbackLocation.config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config"], "originalTargetFrameworks": ["net7.0"], "sources": {"C:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\": {}, "https://api.nuget.org/v3/index.json": {}}, "frameworks": {"net7.0": {"targetAlias": "net7.0", "projectReferences": {"D:\\C#Web\\IKRB_real\\GeoSpatialDataKRBR.Common\\GeoSpatialDataKRBR.Common.csproj": {"projectPath": "D:\\C#Web\\IKRB_real\\GeoSpatialDataKRBR.Common\\GeoSpatialDataKRBR.Common.csproj"}}}}, "warningProperties": {"warnAsError": ["NU1605"]}}, "frameworks": {"net7.0": {"targetAlias": "net7.0", "dependencies": {"Microsoft.AspNetCore.Identity.EntityFrameworkCore": {"target": "Package", "version": "[7.0.0, )"}, "Microsoft.EntityFrameworkCore": {"target": "Package", "version": "[7.0.0, )"}, "System.ComponentModel.Annotations": {"target": "Package", "version": "[5.0.0, )"}}, "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "frameworkReferences": {"Microsoft.NETCore.App": {"privateAssets": "all"}}, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\7.0.100\\RuntimeIdentifierGraph.json"}}}, "D:\\C#Web\\IKRB_real\\GeoSpatialDataKRBR.Data\\GeoSpatialDataKRBR.Data.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "D:\\C#Web\\IKRB_real\\GeoSpatialDataKRBR.Data\\GeoSpatialDataKRBR.Data.csproj", "projectName": "GeoSpatialDataKRBR.Data", "projectPath": "D:\\C#Web\\IKRB_real\\GeoSpatialDataKRBR.Data\\GeoSpatialDataKRBR.Data.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "D:\\C#Web\\IKRB_real\\GeoSpatialDataKRBR.Data\\obj\\", "projectStyle": "PackageReference", "fallbackFolders": ["C:\\Program Files (x86)\\Microsoft Visual Studio\\Shared\\NuGetPackages"], "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.FallbackLocation.config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config"], "originalTargetFrameworks": ["net7.0"], "sources": {"C:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\": {}, "https://api.nuget.org/v3/index.json": {}}, "frameworks": {"net7.0": {"targetAlias": "net7.0", "projectReferences": {"D:\\C#Web\\IKRB_real\\GeoSpatialDataKRBR.Common\\GeoSpatialDataKRBR.Common.csproj": {"projectPath": "D:\\C#Web\\IKRB_real\\GeoSpatialDataKRBR.Common\\GeoSpatialDataKRBR.Common.csproj"}, "D:\\C#Web\\IKRB_real\\GeoSpatialDataKRBR.Data.Models\\GeoSpatialDataKRBR.Data.Models.csproj": {"projectPath": "D:\\C#Web\\IKRB_real\\GeoSpatialDataKRBR.Data.Models\\GeoSpatialDataKRBR.Data.Models.csproj"}}}}, "warningProperties": {"warnAsError": ["NU1605"]}}, "frameworks": {"net7.0": {"targetAlias": "net7.0", "dependencies": {"Microsoft.AspNetCore.Identity.EntityFrameworkCore": {"target": "Package", "version": "[7.0.0, )"}, "Microsoft.EntityFrameworkCore": {"target": "Package", "version": "[7.0.0, )"}, "Microsoft.EntityFrameworkCore.Design": {"target": "Package", "version": "[7.0.0, )"}, "Microsoft.EntityFrameworkCore.SqlServer": {"target": "Package", "version": "[7.0.0, )"}, "Microsoft.EntityFrameworkCore.Tools": {"target": "Package", "version": "[7.0.0, )"}}, "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "frameworkReferences": {"Microsoft.NETCore.App": {"privateAssets": "all"}}, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\7.0.100\\RuntimeIdentifierGraph.json"}}}, "D:\\C#Web\\IKRB_real\\GeoSpatialDataKRBR.Services.Data\\GeoSpatialDataKRBR.Services.Data.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "D:\\C#Web\\IKRB_real\\GeoSpatialDataKRBR.Services.Data\\GeoSpatialDataKRBR.Services.Data.csproj", "projectName": "GeoSpatialDataKRBR.Services.Data", "projectPath": "D:\\C#Web\\IKRB_real\\GeoSpatialDataKRBR.Services.Data\\GeoSpatialDataKRBR.Services.Data.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "D:\\C#Web\\IKRB_real\\GeoSpatialDataKRBR.Services.Data\\obj\\", "projectStyle": "PackageReference", "fallbackFolders": ["C:\\Program Files (x86)\\Microsoft Visual Studio\\Shared\\NuGetPackages"], "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.FallbackLocation.config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config"], "originalTargetFrameworks": ["net7.0"], "sources": {"C:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\": {}, "https://api.nuget.org/v3/index.json": {}}, "frameworks": {"net7.0": {"targetAlias": "net7.0", "projectReferences": {"D:\\C#Web\\IKRB_real\\GeoSpatialDataKRBR.Common\\GeoSpatialDataKRBR.Common.csproj": {"projectPath": "D:\\C#Web\\IKRB_real\\GeoSpatialDataKRBR.Common\\GeoSpatialDataKRBR.Common.csproj"}, "D:\\C#Web\\IKRB_real\\GeoSpatialDataKRBR.Data.Models\\GeoSpatialDataKRBR.Data.Models.csproj": {"projectPath": "D:\\C#Web\\IKRB_real\\GeoSpatialDataKRBR.Data.Models\\GeoSpatialDataKRBR.Data.Models.csproj"}, "D:\\C#Web\\IKRB_real\\GeoSpatialDataKRBR.Data\\GeoSpatialDataKRBR.Data.csproj": {"projectPath": "D:\\C#Web\\IKRB_real\\GeoSpatialDataKRBR.Data\\GeoSpatialDataKRBR.Data.csproj"}}}}, "warningProperties": {"warnAsError": ["NU1605"]}}, "frameworks": {"net7.0": {"targetAlias": "net7.0", "dependencies": {"Microsoft.EntityFrameworkCore": {"target": "Package", "version": "[7.0.0, )"}, "Microsoft.Extensions.DependencyInjection.Abstractions": {"target": "Package", "version": "[7.0.0, )"}, "Microsoft.Extensions.Http": {"target": "Package", "version": "[7.0.0, )"}, "System.Text.Json": {"target": "Package", "version": "[7.0.0, )"}}, "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "frameworkReferences": {"Microsoft.NETCore.App": {"privateAssets": "all"}}, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\7.0.100\\RuntimeIdentifierGraph.json"}}}, "D:\\C#Web\\IKRB_real\\GeoSpatialDataKRBR.Web.Infrastructure\\GeoSpatialDataKRBR.Web.Infrastructure.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "D:\\C#Web\\IKRB_real\\GeoSpatialDataKRBR.Web.Infrastructure\\GeoSpatialDataKRBR.Web.Infrastructure.csproj", "projectName": "GeoSpatialDataKRBR.Web.Infrastructure", "projectPath": "D:\\C#Web\\IKRB_real\\GeoSpatialDataKRBR.Web.Infrastructure\\GeoSpatialDataKRBR.Web.Infrastructure.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "D:\\C#Web\\IKRB_real\\GeoSpatialDataKRBR.Web.Infrastructure\\obj\\", "projectStyle": "PackageReference", "fallbackFolders": ["C:\\Program Files (x86)\\Microsoft Visual Studio\\Shared\\NuGetPackages"], "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.FallbackLocation.config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config"], "originalTargetFrameworks": ["net7.0"], "sources": {"C:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\": {}, "https://api.nuget.org/v3/index.json": {}}, "frameworks": {"net7.0": {"targetAlias": "net7.0", "projectReferences": {"D:\\C#Web\\IKRB_real\\GeoSpatialDataKRBR.Data\\GeoSpatialDataKRBR.Data.csproj": {"projectPath": "D:\\C#Web\\IKRB_real\\GeoSpatialDataKRBR.Data\\GeoSpatialDataKRBR.Data.csproj"}, "D:\\C#Web\\IKRB_real\\GeoSpatialDataKRBR.Services.Data\\GeoSpatialDataKRBR.Services.Data.csproj": {"projectPath": "D:\\C#Web\\IKRB_real\\GeoSpatialDataKRBR.Services.Data\\GeoSpatialDataKRBR.Services.Data.csproj"}}}}, "warningProperties": {"warnAsError": ["NU1605"]}}, "frameworks": {"net7.0": {"targetAlias": "net7.0", "dependencies": {"Microsoft.AspNetCore.Mvc.Core": {"target": "Package", "version": "[2.2.5, )"}, "Microsoft.Extensions.DependencyInjection.Abstractions": {"target": "Package", "version": "[7.0.0, )"}}, "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "frameworkReferences": {"Microsoft.NETCore.App": {"privateAssets": "all"}}, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\7.0.100\\RuntimeIdentifierGraph.json"}}}, "D:\\C#Web\\IKRB_real\\GeoSpatialDataKRBR.Web.ViewModels\\GeoSpatialDataKRBR.Web.ViewModels.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "D:\\C#Web\\IKRB_real\\GeoSpatialDataKRBR.Web.ViewModels\\GeoSpatialDataKRBR.Web.ViewModels.csproj", "projectName": "GeoSpatialDataKRBR.Web.ViewModels", "projectPath": "D:\\C#Web\\IKRB_real\\GeoSpatialDataKRBR.Web.ViewModels\\GeoSpatialDataKRBR.Web.ViewModels.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "D:\\C#Web\\IKRB_real\\GeoSpatialDataKRBR.Web.ViewModels\\obj\\", "projectStyle": "PackageReference", "fallbackFolders": ["C:\\Program Files (x86)\\Microsoft Visual Studio\\Shared\\NuGetPackages"], "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.FallbackLocation.config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config"], "originalTargetFrameworks": ["net7.0"], "sources": {"C:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\": {}, "https://api.nuget.org/v3/index.json": {}}, "frameworks": {"net7.0": {"targetAlias": "net7.0", "projectReferences": {"D:\\C#Web\\IKRB_real\\GeoSpatialDataKRBR.Common\\GeoSpatialDataKRBR.Common.csproj": {"projectPath": "D:\\C#Web\\IKRB_real\\GeoSpatialDataKRBR.Common\\GeoSpatialDataKRBR.Common.csproj"}}}}, "warningProperties": {"warnAsError": ["NU1605"]}}, "frameworks": {"net7.0": {"targetAlias": "net7.0", "dependencies": {"System.ComponentModel.Annotations": {"target": "Package", "version": "[5.0.0, )"}}, "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "frameworkReferences": {"Microsoft.NETCore.App": {"privateAssets": "all"}}, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\7.0.100\\RuntimeIdentifierGraph.json"}}}, "D:\\C#Web\\IKRB_real\\GeoSpatialDataKRBR\\GeoSpatialDataKRBR.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "D:\\C#Web\\IKRB_real\\GeoSpatialDataKRBR\\GeoSpatialDataKRBR.csproj", "projectName": "GeoSpatialDataKRBR", "projectPath": "D:\\C#Web\\IKRB_real\\GeoSpatialDataKRBR\\GeoSpatialDataKRBR.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "D:\\C#Web\\IKRB_real\\GeoSpatialDataKRBR\\obj\\", "projectStyle": "PackageReference", "fallbackFolders": ["C:\\Program Files (x86)\\Microsoft Visual Studio\\Shared\\NuGetPackages"], "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.FallbackLocation.config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config"], "originalTargetFrameworks": ["net7.0"], "sources": {"C:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\": {}, "https://api.nuget.org/v3/index.json": {}}, "frameworks": {"net7.0": {"targetAlias": "net7.0", "projectReferences": {"D:\\C#Web\\IKRB_real\\GeoSpatialDataKRBR.Common\\GeoSpatialDataKRBR.Common.csproj": {"projectPath": "D:\\C#Web\\IKRB_real\\GeoSpatialDataKRBR.Common\\GeoSpatialDataKRBR.Common.csproj"}, "D:\\C#Web\\IKRB_real\\GeoSpatialDataKRBR.Data.Models\\GeoSpatialDataKRBR.Data.Models.csproj": {"projectPath": "D:\\C#Web\\IKRB_real\\GeoSpatialDataKRBR.Data.Models\\GeoSpatialDataKRBR.Data.Models.csproj"}, "D:\\C#Web\\IKRB_real\\GeoSpatialDataKRBR.Data\\GeoSpatialDataKRBR.Data.csproj": {"projectPath": "D:\\C#Web\\IKRB_real\\GeoSpatialDataKRBR.Data\\GeoSpatialDataKRBR.Data.csproj"}, "D:\\C#Web\\IKRB_real\\GeoSpatialDataKRBR.Services.Data\\GeoSpatialDataKRBR.Services.Data.csproj": {"projectPath": "D:\\C#Web\\IKRB_real\\GeoSpatialDataKRBR.Services.Data\\GeoSpatialDataKRBR.Services.Data.csproj"}, "D:\\C#Web\\IKRB_real\\GeoSpatialDataKRBR.Web.Infrastructure\\GeoSpatialDataKRBR.Web.Infrastructure.csproj": {"projectPath": "D:\\C#Web\\IKRB_real\\GeoSpatialDataKRBR.Web.Infrastructure\\GeoSpatialDataKRBR.Web.Infrastructure.csproj"}, "D:\\C#Web\\IKRB_real\\GeoSpatialDataKRBR.Web.ViewModels\\GeoSpatialDataKRBR.Web.ViewModels.csproj": {"projectPath": "D:\\C#Web\\IKRB_real\\GeoSpatialDataKRBR.Web.ViewModels\\GeoSpatialDataKRBR.Web.ViewModels.csproj"}}}}, "warningProperties": {"warnAsError": ["NU1605"]}}, "frameworks": {"net7.0": {"targetAlias": "net7.0", "dependencies": {"Microsoft.AspNetCore.Diagnostics.EntityFrameworkCore": {"target": "Package", "version": "[7.0.0, )"}, "Microsoft.AspNetCore.Identity.EntityFrameworkCore": {"target": "Package", "version": "[7.0.0, )"}, "Microsoft.AspNetCore.Identity.UI": {"target": "Package", "version": "[7.0.0, )"}, "Microsoft.EntityFrameworkCore.SqlServer": {"target": "Package", "version": "[7.0.0, )"}, "Microsoft.EntityFrameworkCore.Tools": {"target": "Package", "version": "[7.0.0, )"}}, "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "frameworkReferences": {"Microsoft.AspNetCore.App": {"privateAssets": "none"}, "Microsoft.NETCore.App": {"privateAssets": "all"}}, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\7.0.100\\RuntimeIdentifierGraph.json"}}}}}