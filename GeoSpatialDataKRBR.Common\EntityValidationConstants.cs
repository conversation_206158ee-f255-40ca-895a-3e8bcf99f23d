namespace GeoSpatialDataKRBR.Common
{
    public static class EntityValidationConstants
    {
        public static class User
        {
            public const int FirstNameMaxLength = 50;
            public const int FirstNameMinLength = 2;

            public const int LastNameMaxLength = 50;
            public const int LastNameMinLength = 2;
        }

        public static class GeoLayer
        {
            public const int NameMaxLength = 100;
            public const int NameMinLength = 3;

            public const int DescriptionMaxLength = 500;
            public const int DescriptionMinLength = 0;

            public const int WmsUrlMaxLength = 2048;
            public const int WfsUrlMaxLength = 2048;
            
            public const int LayerNameMaxLength = 100;
            public const int LayerNameMinLength = 1;

            public const int WorkspaceMaxLength = 50;
            public const int WorkspaceMinLength = 1;

            public const int StyleNameMaxLength = 100;
            public const int StyleNameMinLength = 0;
        }

        public static class GeoLayerGroup
        {
            public const int NameMaxLength = 100;
            public const int NameMinLength = 3;

            public const int DescriptionMaxLength = 500;
            public const int DescriptionMinLength = 0;
        }

        public static class GeoServerConfiguration
        {
            public const int NameMaxLength = 100;
            public const int NameMinLength = 3;

            public const int BaseUrlMaxLength = 2048;
            public const int BaseUrlMinLength = 10;

            public const int UsernameMaxLength = 50;
            public const int UsernameMinLength = 3;

            public const int PasswordMaxLength = 100;
            public const int PasswordMinLength = 6;

            public const int WorkspaceMaxLength = 50;
            public const int WorkspaceMinLength = 1;
        }

        public static class UserLayerPreference
        {
            public const int ColorMaxLength = 7; // For hex color codes like #3b82f6
            public const int ColorMinLength = 4; // For hex color codes like #fff
        }

        public static class LayerStyle
        {
            public const int NameMaxLength = 100;
            public const int NameMinLength = 3;

            public const int SldContentMaxLength = 10000;
            public const int SldContentMinLength = 10;
        }
    }
}
