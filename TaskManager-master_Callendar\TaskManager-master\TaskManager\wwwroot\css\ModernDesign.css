/* Modern Design System for TaskManager */

/* CSS Variables for consistent theming */
:root {
    --primary-gradient: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    --secondary-gradient: linear-gradient(45deg, #f093fb, #f5576c);
    --success-gradient: linear-gradient(45deg, #28a745, #20c997);
    --warning-gradient: linear-gradient(45deg, #ffc107, #fd7e14);
    --danger-gradient: linear-gradient(45deg, #dc3545, #e74c3c);
    --info-gradient: linear-gradient(45deg, #007bff, #0056b3);
    
    --card-shadow: 0 10px 40px rgba(0, 0, 0, 0.1);
    --card-shadow-hover: 0 20px 60px rgba(0, 0, 0, 0.15);
    --border-radius: 20px;
    --border-radius-small: 12px;
    --border-radius-large: 25px;
    
    --text-primary: #333;
    --text-secondary: #666;
    --text-muted: #999;
    --background-overlay: rgba(255, 255, 255, 0.95);
    --background-glass: rgba(255, 255, 255, 0.2);
}

/* Global Styles */
body {
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    background: var(--primary-gradient);
    min-height: 100vh;
    margin: 0;
    padding: 0;
}

/* Modern Container */
.modern-container {
    min-height: 100vh;
    background: var(--primary-gradient);
    padding: 2rem 1rem;
}

/* Modern Page Header */
.modern-page-header {
    text-align: center;
    margin-bottom: 3rem;
    color: white;
}

.modern-page-title {
    font-size: 2.5rem;
    font-weight: 700;
    margin-bottom: 1rem;
    text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.3);
}

.modern-page-title i {
    margin-right: 1rem;
    color: #ffd700;
}

.modern-page-subtitle {
    font-size: 1.1rem;
    opacity: 0.9;
    margin: 0;
}

/* Modern Cards */
.modern-card {
    background: var(--background-overlay);
    border-radius: var(--border-radius);
    overflow: hidden;
    box-shadow: var(--card-shadow);
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.2);
    transition: all 0.3s ease;
    margin-bottom: 2rem;
}

.modern-card:hover {
    transform: translateY(-5px);
    box-shadow: var(--card-shadow-hover);
}

.modern-card-header {
    background: var(--primary-gradient);
    color: white;
    padding: 2rem;
    text-align: center;
    position: relative;
    overflow: hidden;
}

.modern-card-header::before {
    content: '';
    position: absolute;
    top: -50%;
    left: -50%;
    width: 200%;
    height: 200%;
    background: radial-gradient(circle, rgba(255, 255, 255, 0.1) 0%, transparent 70%);
    animation: shimmer 3s infinite;
}

@keyframes shimmer {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

.modern-card-header h1,
.modern-card-header h2,
.modern-card-header h3 {
    margin: 0;
    position: relative;
    z-index: 1;
}

.modern-card-body {
    padding: 2rem;
}

/* Modern Buttons */
.modern-btn {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    padding: 1rem 2rem;
    border-radius: var(--border-radius-small);
    text-decoration: none;
    font-weight: 600;
    font-size: 1rem;
    transition: all 0.3s ease;
    border: none;
    cursor: pointer;
    position: relative;
    overflow: hidden;
    margin: 0.5rem;
}

.modern-btn i {
    margin-right: 0.5rem;
    font-size: 1.1rem;
}

.modern-btn-primary {
    background: var(--primary-gradient);
    color: white;
    box-shadow: 0 4px 15px rgba(102, 126, 234, 0.4);
}

.modern-btn-primary:hover {
    background: linear-gradient(45deg, #5a6fd8, #6a4190);
    transform: translateY(-2px);
    box-shadow: 0 6px 20px rgba(102, 126, 234, 0.6);
    color: white;
    text-decoration: none;
}

.modern-btn-secondary {
    background: var(--secondary-gradient);
    color: white;
    box-shadow: 0 4px 15px rgba(240, 147, 251, 0.4);
}

.modern-btn-secondary:hover {
    background: linear-gradient(45deg, #e681f0, #e94560);
    transform: translateY(-2px);
    box-shadow: 0 6px 20px rgba(240, 147, 251, 0.6);
    color: white;
    text-decoration: none;
}

.modern-btn-success {
    background: var(--success-gradient);
    color: white;
    box-shadow: 0 4px 15px rgba(40, 167, 69, 0.4);
}

.modern-btn-success:hover {
    background: linear-gradient(45deg, #218838, #1ea080);
    transform: translateY(-2px);
    box-shadow: 0 6px 20px rgba(40, 167, 69, 0.6);
    color: white;
    text-decoration: none;
}

.modern-btn-warning {
    background: var(--warning-gradient);
    color: white;
    box-shadow: 0 4px 15px rgba(255, 193, 7, 0.4);
}

.modern-btn-warning:hover {
    background: linear-gradient(45deg, #e0a800, #e8590c);
    transform: translateY(-2px);
    box-shadow: 0 6px 20px rgba(255, 193, 7, 0.6);
    color: white;
    text-decoration: none;
}

.modern-btn-danger {
    background: var(--danger-gradient);
    color: white;
    box-shadow: 0 4px 15px rgba(220, 53, 69, 0.4);
}

.modern-btn-danger:hover {
    background: linear-gradient(45deg, #c82333, #dc2626);
    transform: translateY(-2px);
    box-shadow: 0 6px 20px rgba(220, 53, 69, 0.6);
    color: white;
    text-decoration: none;
}

/* Modern Forms */
.modern-form {
    background: var(--background-overlay);
    border-radius: var(--border-radius);
    padding: 2rem;
    box-shadow: var(--card-shadow);
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.2);
}

.modern-form-group {
    margin-bottom: 1.5rem;
}

.modern-form-label {
    display: block;
    margin-bottom: 0.5rem;
    font-weight: 600;
    color: var(--text-primary);
}

.modern-form-control {
    width: 100%;
    padding: 1rem;
    border: 2px solid rgba(102, 126, 234, 0.2);
    border-radius: var(--border-radius-small);
    font-size: 1rem;
    transition: all 0.3s ease;
    background: rgba(255, 255, 255, 0.9);
    box-sizing: border-box;
}

.modern-form-control:focus {
    outline: none;
    border-color: #667eea;
    box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
    background: white;
}

.modern-form-control::placeholder {
    color: var(--text-muted);
}

/* Modern Tables */
.modern-table-container {
    background: var(--background-overlay);
    border-radius: var(--border-radius);
    overflow: hidden;
    box-shadow: var(--card-shadow);
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.2);
    margin-bottom: 2rem;
}

.modern-table-header {
    background: var(--primary-gradient);
    color: white;
    padding: 1.5rem;
    text-align: center;
}

.modern-table-header h3 {
    margin: 0;
    font-size: 1.3rem;
    font-weight: 600;
}

.modern-table {
    width: 100%;
    border-collapse: collapse;
    background: white;
}

.modern-table thead tr {
    background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
}

.modern-table th {
    padding: 1.25rem 1rem;
    text-align: left;
    font-weight: 600;
    color: var(--text-primary);
    border-bottom: 2px solid #dee2e6;
}

.modern-table th i {
    margin-right: 0.5rem;
    color: #667eea;
}

.modern-table tbody tr {
    transition: all 0.3s ease;
    border-bottom: 1px solid #f1f3f4;
}

.modern-table tbody tr:hover {
    background: linear-gradient(135deg, #f8f9ff 0%, #f0f2ff 100%);
    transform: scale(1.01);
    box-shadow: 0 4px 12px rgba(102, 126, 234, 0.1);
}

.modern-table td {
    padding: 1rem;
    vertical-align: middle;
}

/* Modern Navigation */
.modern-navbar {
    background: var(--background-overlay);
    backdrop-filter: blur(10px);
    border-bottom: 1px solid rgba(255, 255, 255, 0.2);
    box-shadow: 0 2px 20px rgba(0, 0, 0, 0.1);
    padding: 1rem 0;
}

.modern-nav-brand {
    display: flex;
    align-items: center;
    font-size: 1.5rem;
    font-weight: 700;
    color: var(--text-primary);
    text-decoration: none;
    transition: all 0.3s ease;
}

.modern-nav-brand:hover {
    color: #667eea;
    text-decoration: none;
    transform: scale(1.05);
}

.modern-nav-brand i {
    margin-right: 0.75rem;
    font-size: 1.75rem;
    background: var(--primary-gradient);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

.brand-text {
    background: var(--primary-gradient);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

.modern-toggler {
    border: none;
    background: var(--primary-gradient);
    color: white;
    padding: 0.5rem 1rem;
    border-radius: var(--border-radius-small);
    transition: all 0.3s ease;
}

.modern-toggler:hover {
    transform: scale(1.1);
}

.modern-toggler:focus {
    box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.3);
}

.modern-nav-link {
    color: var(--text-primary);
    text-decoration: none;
    padding: 0.75rem 1.25rem;
    border-radius: var(--border-radius-small);
    transition: all 0.3s ease;
    display: inline-flex;
    align-items: center;
    margin: 0.25rem;
    font-weight: 500;
    position: relative;
    overflow: hidden;
}

.modern-nav-link::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: var(--primary-gradient);
    transition: left 0.3s ease;
    z-index: -1;
}

.modern-nav-link:hover::before {
    left: 0;
}

.modern-nav-link:hover {
    color: white;
    transform: translateY(-2px);
    text-decoration: none;
    box-shadow: 0 4px 15px rgba(102, 126, 234, 0.3);
}

.modern-nav-link.admin-link {
    background: var(--warning-gradient);
    color: white;
    font-weight: 600;
}

.modern-nav-link.admin-link:hover {
    background: linear-gradient(45deg, #e0a800, #e8590c);
    transform: translateY(-2px) scale(1.05);
}

/* Kanban Link Special Styling */
.modern-nav-link.kanban-link {
    background: linear-gradient(135deg, #10b981 0%, #059669 100%);
    color: white;
    font-weight: 600;
    position: relative;
    overflow: hidden;
}

.modern-nav-link.kanban-link::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(135deg, #047857 0%, #065f46 100%);
    transition: left 0.3s ease;
    z-index: -1;
}

.modern-nav-link.kanban-link:hover::before {
    left: 0;
}

.modern-nav-link.kanban-link:hover {
    background: linear-gradient(135deg, #047857 0%, #065f46 100%);
    transform: translateY(-2px) scale(1.05);
    box-shadow: 0 8px 25px rgba(16, 185, 129, 0.4);
}

.modern-nav-link.kanban-link i {
    margin-right: 0.5rem;
    font-size: 1.1rem;
}

.modern-nav-link i {
    margin-right: 0.5rem;
    font-size: 1rem;
}

/* Main Container */
.modern-main-container {
    min-height: calc(100vh - 120px);
    background: var(--primary-gradient);
    padding: 0;
}

.modern-main-content {
    padding: 2rem 1rem;
    max-width: 1700px;
    margin: 0 auto;
}

/* Responsive Design */
@media (max-width: 768px) {
    .modern-container {
        padding: 1rem 0.5rem;
    }
    
    .modern-page-title {
        font-size: 2rem;
    }
    
    .modern-card-body {
        padding: 1.5rem;
    }
    
    .modern-btn {
        padding: 0.875rem 1.5rem;
        font-size: 0.9rem;
    }
}

@media (max-width: 480px) {
    .modern-page-title {
        font-size: 1.75rem;
    }
    
    .modern-card-header,
    .modern-card-body {
        padding: 1rem;
    }
    
    .modern-btn {
        width: 100%;
        margin: 0.25rem 0;
    }
}

/* Home Page Styles */
.hero-section {
    text-align: center;
    padding: 4rem 2rem;
    color: white;
    margin-bottom: 4rem;
}

.hero-content {
    max-width: 800px;
    margin: 0 auto;
}

.hero-title {
    font-size: 3.5rem;
    font-weight: 700;
    margin-bottom: 1.5rem;
    text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.3);
    animation: fadeInUp 1s ease-out;
}

.hero-title i {
    margin-right: 1rem;
    color: #ffd700;
    animation: bounce 2s infinite;
}

.hero-subtitle {
    font-size: 1.3rem;
    opacity: 0.9;
    margin-bottom: 3rem;
    line-height: 1.6;
    animation: fadeInUp 1s ease-out 0.2s both;
}

.hero-stats {
    display: flex;
    justify-content: center;
    gap: 3rem;
    flex-wrap: wrap;
    animation: fadeInUp 1s ease-out 0.4s both;
}

.hero-stat {
    display: flex;
    align-items: center;
    background: var(--background-glass);
    padding: 1rem 2rem;
    border-radius: var(--border-radius-large);
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.3);
    transition: all 0.3s ease;
}

.hero-stat:hover {
    transform: translateY(-5px);
    background: rgba(255, 255, 255, 0.3);
}

.hero-stat i {
    margin-right: 0.75rem;
    font-size: 1.5rem;
    color: #ffd700;
}

.hero-stat span {
    font-weight: 600;
    font-size: 1.1rem;
}

/* Services Section */
.services-section {
    margin-bottom: 4rem;
}

.section-header {
    text-align: center;
    margin-bottom: 3rem;
    color: white;
}

.section-title {
    font-size: 2.5rem;
    font-weight: 700;
    margin-bottom: 1rem;
    text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.3);
}

.section-title i {
    margin-right: 1rem;
    color: #ffd700;
}

.section-subtitle {
    font-size: 1.1rem;
    opacity: 0.9;
    max-width: 600px;
    margin: 0 auto;
    line-height: 1.6;
}

.services-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
    gap: 2rem;
    max-width: 1200px;
    margin: 0 auto;
}

.service-card {
    background: var(--background-overlay);
    border-radius: var(--border-radius);
    overflow: hidden;
    box-shadow: var(--card-shadow);
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.2);
    transition: all 0.3s ease;
    animation: fadeInUp 0.6s ease-out;
}

.service-card:hover {
    transform: translateY(-10px);
    box-shadow: var(--card-shadow-hover);
}

.service-card:nth-child(1) { animation-delay: 0.1s; }
.service-card:nth-child(2) { animation-delay: 0.2s; }
.service-card:nth-child(3) { animation-delay: 0.3s; }
.service-card:nth-child(4) { animation-delay: 0.4s; }

.service-card-header {
    background: var(--primary-gradient);
    color: white;
    padding: 2rem;
    text-align: center;
    position: relative;
    overflow: hidden;
}

.service-card-header::before {
    content: '';
    position: absolute;
    top: -50%;
    left: -50%;
    width: 200%;
    height: 200%;
    background: radial-gradient(circle, rgba(255, 255, 255, 0.1) 0%, transparent 70%);
    animation: shimmer 3s infinite;
}

.service-icon {
    width: 80px;
    height: 80px;
    border-radius: 50%;
    background: rgba(255, 255, 255, 0.2);
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0 auto 1rem auto;
    font-size: 2rem;
    color: #ffd700;
    position: relative;
    z-index: 1;
}

.service-title {
    margin: 0;
    font-size: 1.4rem;
    font-weight: 600;
    position: relative;
    z-index: 1;
}

.service-card-body {
    padding: 2rem;
}

.service-details {
    margin-bottom: 2rem;
}

.service-detail {
    display: flex;
    align-items: center;
    margin-bottom: 1rem;
    padding: 0.75rem;
    background: rgba(102, 126, 234, 0.05);
    border-radius: var(--border-radius-small);
    border-left: 4px solid #667eea;
}

.service-detail i {
    margin-right: 0.75rem;
    color: #667eea;
    font-size: 1.1rem;
    width: 20px;
}

.detail-label {
    font-weight: 600;
    color: var(--text-primary);
    margin-right: 0.5rem;
}

.detail-value {
    color: var(--text-secondary);
    font-weight: 500;
}

.detail-value.price {
    color: #28a745;
    font-weight: 700;
    font-size: 1.1rem;
}

.service-actions {
    text-align: center;
}

/* Call to Action Section */
.cta-section {
    background: var(--background-overlay);
    border-radius: var(--border-radius);
    padding: 4rem 2rem;
    text-align: center;
    box-shadow: var(--card-shadow);
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.2);
    margin: 2rem auto;
    max-width: 800px;
}

.cta-content {
    max-width: 600px;
    margin: 0 auto;
}

.cta-title {
    font-size: 2.5rem;
    font-weight: 700;
    margin-bottom: 1rem;
    color: var(--text-primary);
}

.cta-title i {
    margin-right: 1rem;
    color: #667eea;
}

.cta-subtitle {
    font-size: 1.1rem;
    color: var(--text-secondary);
    margin-bottom: 2rem;
    line-height: 1.6;
}

.cta-actions {
    display: flex;
    justify-content: center;
    gap: 1rem;
    flex-wrap: wrap;
}

/* Animations */
@keyframes bounce {
    0%, 20%, 50%, 80%, 100% {
        transform: translateY(0);
    }
    40% {
        transform: translateY(-10px);
    }
    60% {
        transform: translateY(-5px);
    }
}

/* Responsive Design for Home Page */
@media (max-width: 768px) {
    .hero-title {
        font-size: 2.5rem;
    }

    .hero-stats {
        gap: 1rem;
    }

    .hero-stat {
        padding: 0.75rem 1.5rem;
    }

    .services-grid {
        grid-template-columns: 1fr;
        gap: 1.5rem;
    }

    .cta-actions {
        flex-direction: column;
        align-items: center;
    }

    .cta-actions .modern-btn {
        width: 100%;
        max-width: 300px;
    }
}

@media (max-width: 480px) {
    .hero-title {
        font-size: 2rem;
    }

    .hero-subtitle {
        font-size: 1.1rem;
    }

    .section-title {
        font-size: 2rem;
    }

    .cta-title {
        font-size: 2rem;
    }

    .service-card-header,
    .service-card-body {
        padding: 1.5rem;
    }

    .cta-section {
        padding: 2rem 1rem;
    }
}

/* Authentication Styles */
.modern-auth-nav {
    display: flex;
    align-items: center;
    gap: 1rem;
}

.modern-user-info {
    display: flex;
    align-items: center;
    background: var(--background-glass);
    padding: 0.5rem 1rem;
    border-radius: var(--border-radius-large);
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.3);
    color: var(--text-primary);
    font-weight: 500;
}

.user-avatar-small {
    width: 35px;
    height: 35px;
    border-radius: 50%;
    background: var(--primary-gradient);
    display: flex;
    align-items: center;
    justify-content: center;
    margin-right: 0.75rem;
    color: white;
    font-size: 0.9rem;
}

.user-name {
    font-weight: 600;
    color: var(--text-primary);
}

.modern-logout-btn {
    display: flex;
    align-items: center;
    padding: 0.5rem 1rem;
    background: var(--danger-gradient);
    color: white;
    border: none;
    border-radius: var(--border-radius-small);
    font-weight: 500;
    transition: all 0.3s ease;
    cursor: pointer;
}

.modern-logout-btn:hover {
    background: linear-gradient(45deg, #c82333, #dc2626);
    transform: translateY(-2px);
    box-shadow: 0 4px 15px rgba(220, 53, 69, 0.4);
}

.modern-logout-btn i {
    margin-right: 0.5rem;
}

.modern-auth-link {
    display: flex;
    align-items: center;
    padding: 0.5rem 1rem;
    border-radius: var(--border-radius-small);
    text-decoration: none;
    font-weight: 500;
    transition: all 0.3s ease;
    margin: 0 0.25rem;
}

.modern-auth-link i {
    margin-right: 0.5rem;
}

.modern-auth-link.register {
    background: var(--success-gradient);
    color: white;
}

.modern-auth-link.register:hover {
    background: linear-gradient(45deg, #218838, #1ea080);
    transform: translateY(-2px);
    box-shadow: 0 4px 15px rgba(40, 167, 69, 0.4);
    color: white;
    text-decoration: none;
}

.modern-auth-link.login {
    background: var(--info-gradient);
    color: white;
}

.modern-auth-link.login:hover {
    background: linear-gradient(45deg, #0056b3, #004085);
    transform: translateY(-2px);
    box-shadow: 0 4px 15px rgba(0, 123, 255, 0.4);
    color: white;
    text-decoration: none;
}

/* Responsive Auth */
@media (max-width: 768px) {
    .modern-auth-nav {
        flex-direction: column;
        gap: 0.5rem;
    }

    .modern-user-info {
        padding: 0.5rem;
    }

    .user-name {
        font-size: 0.9rem;
    }

    .modern-auth-link,
    .modern-logout-btn {
        padding: 0.5rem 0.75rem;
        font-size: 0.9rem;
    }
}

/* Filter Styles */
.modern-filters-form {
    margin: 0;
}

.filters-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 1.5rem;
    align-items: end;
}

.filter-group {
    display: flex;
    flex-direction: column;
}

.filter-actions {
    display: flex;
    gap: 1rem;
    align-items: center;
    justify-content: center;
}

.modern-form-label {
    display: flex;
    align-items: center;
    margin-bottom: 0.5rem;
    font-weight: 600;
    color: var(--text-primary);
    font-size: 0.95rem;
}

.modern-form-label i {
    margin-right: 0.5rem;
    color: #667eea;
    font-size: 0.9rem;
}

/* Task Table Styles */
.tasks-table-container {
    background: var(--background-overlay);
    border-radius: var(--border-radius);
    overflow: hidden;
    box-shadow: var(--card-shadow);
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.2);
    margin-top: 2rem;
}

.tasks-table-header {
    background: var(--primary-gradient);
    color: white;
    padding: 1.5rem;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.tasks-table-header h3 {
    margin: 0;
    font-size: 1.3rem;
    font-weight: 600;
}

.tasks-table-header i {
    margin-right: 0.5rem;
    color: #ffd700;
}

.add-task-btn {
    background: rgba(255, 255, 255, 0.2);
    color: white;
    border: 1px solid rgba(255, 255, 255, 0.3);
    padding: 0.75rem 1.5rem;
    border-radius: var(--border-radius-small);
    text-decoration: none;
    font-weight: 600;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
}

.add-task-btn:hover {
    background: rgba(255, 255, 255, 0.3);
    color: white;
    text-decoration: none;
    transform: translateY(-2px);
}

.add-task-btn i {
    margin-right: 0.5rem;
}

.task-row {
    transition: all 0.3s ease;
    border-bottom: 1px solid #f1f3f4;
}

.task-row:hover {
    background: linear-gradient(135deg, #f8f9ff 0%, #f0f2ff 100%);
    transform: scale(1.01);
    box-shadow: 0 4px 12px rgba(102, 126, 234, 0.1);
}

.task-status {
    display: inline-block;
    padding: 0.25rem 0.75rem;
    border-radius: 15px;
    font-size: 0.8rem;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.task-status.active {
    background: var(--success-gradient);
    color: white;
}

.task-status.pending {
    background: var(--warning-gradient);
    color: white;
}

.task-status.completed {
    background: var(--info-gradient);
    color: white;
}

.task-number {
    font-weight: 700;
    color: #667eea;
    font-size: 1.1rem;
}

.task-price {
    font-weight: 700;
    color: #28a745;
    font-size: 1rem;
}

.task-deadline {
    color: var(--text-secondary);
    font-weight: 500;
}

.task-deadline.urgent {
    color: #dc3545;
    font-weight: 700;
}

.task-actions {
    display: flex;
    gap: 0.5rem;
    justify-content: center;
}

.task-action-btn {
    padding: 0.5rem 1rem;
    border-radius: var(--border-radius-small);
    text-decoration: none;
    font-weight: 500;
    font-size: 0.9rem;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
}

.task-action-btn i {
    margin-right: 0.5rem;
}

.task-action-btn.edit {
    background: var(--info-gradient);
    color: white;
}

.task-action-btn.edit:hover {
    background: linear-gradient(45deg, #0056b3, #004085);
    transform: translateY(-2px);
    color: white;
    text-decoration: none;
}

.task-action-btn.view {
    background: var(--success-gradient);
    color: white;
}

.task-action-btn.view:hover {
    background: linear-gradient(45deg, #218838, #1ea080);
    transform: translateY(-2px);
    color: white;
    text-decoration: none;
}

/* Empty State */
.empty-tasks-state {
    text-align: center;
    padding: 4rem 2rem;
    color: var(--text-secondary);
}

.empty-tasks-state i {
    font-size: 4rem;
    color: #ddd;
    margin-bottom: 1rem;
}

.empty-tasks-state h3 {
    color: var(--text-primary);
    margin-bottom: 1rem;
    font-size: 1.5rem;
}

.empty-tasks-state p {
    font-size: 1.1rem;
    margin-bottom: 2rem;
}

/* Responsive Filters */
@media (max-width: 768px) {
    .filters-grid {
        grid-template-columns: 1fr;
        gap: 1rem;
    }

    .filter-actions {
        flex-direction: column;
        width: 100%;
    }

    .filter-actions .modern-btn {
        width: 100%;
    }

    .tasks-table-header {
        flex-direction: column;
        gap: 1rem;
        text-align: center;
    }

    .task-actions {
        flex-direction: column;
        gap: 0.25rem;
    }

    .task-action-btn {
        font-size: 0.8rem;
        padding: 0.5rem 0.75rem;
    }
}

@media (max-width: 480px) {
    .filters-grid {
        gap: 0.75rem;
    }

    .modern-form-label {
        font-size: 0.9rem;
    }

    .modern-form-control {
        padding: 0.75rem;
        font-size: 0.9rem;
    }
}

/* GeoTask Edit Styles */
.modern-project-header {
    background: var(--primary-gradient);
    color: white;
    padding: 2rem;
    border-radius: var(--border-radius);
    margin-bottom: 2rem;
    display: flex;
    justify-content: space-between;
    align-items: center;
    box-shadow: var(--card-shadow);
}

.project-info .project-title {
    margin: 0;
    font-size: 2rem;
    font-weight: 700;
    display: flex;
    align-items: center;
}

.project-info .project-title i {
    margin-right: 1rem;
    color: #ffd700;
    font-size: 1.8rem;
}

.project-info .project-date {
    margin: 0.5rem 0 0 0;
    opacity: 0.9;
    font-size: 1.1rem;
    display: flex;
    align-items: center;
}

.project-info .project-date i {
    margin-right: 0.5rem;
}

.project-actions {
    display: flex;
    gap: 1rem;
    align-items: center;
    flex-wrap: wrap;
}

.calendar-integration {
    display: flex;
    align-items: center;
    gap: 1rem;
}

.calendar-status {
    background: rgba(255, 255, 255, 0.2);
    padding: 0.5rem 1rem;
    border-radius: var(--border-radius-small);
    font-weight: 600;
    display: flex;
    align-items: center;
}

.calendar-status i {
    margin-right: 0.5rem;
    color: #28a745;
}

.modern-project-form {
    display: flex;
    flex-direction: column;
    gap: 2rem;
}

.modern-form-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 1.5rem;
}

.form-group {
    display: flex;
    flex-direction: column;
}

.modern-form-label {
    display: flex;
    align-items: center;
    margin-bottom: 0.5rem;
    font-weight: 600;
    color: var(--text-primary);
    font-size: 0.95rem;
}

.modern-form-label i {
    margin-right: 0.5rem;
    color: #667eea;
    font-size: 0.9rem;
}

.required {
    color: #dc3545;
    font-weight: 700;
    margin-left: 0.25rem;
}

.modern-form-control {
    padding: 0.875rem 1rem;
    border: 2px solid #e2e8f0;
    border-radius: var(--border-radius-small);
    font-size: 1rem;
    transition: all 0.3s ease;
    background: white;
    color: var(--text-primary);
}

.modern-form-control:focus {
    outline: none;
    border-color: #667eea;
    box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
    transform: translateY(-1px);
}

.modern-textarea {
    resize: vertical;
    min-height: 120px;
}

.modern-validation-error {
    color: #dc3545;
    font-size: 0.875rem;
    margin-top: 0.25rem;
    font-weight: 500;
}

.total-price-display {
    background: var(--success-gradient);
    color: white;
    padding: 1.5rem;
    border-radius: var(--border-radius);
    margin-top: 1rem;
    text-align: center;
}

.price-calculation {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 1rem;
    font-size: 1.2rem;
    font-weight: 700;
}

.price-calculation i {
    color: #ffd700;
    font-size: 1.4rem;
}

.price-value {
    font-size: 1.5rem;
    text-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
}

/* Client Data Styles */
.client-selector-section {
    margin-bottom: 2rem;
    padding-bottom: 2rem;
    border-bottom: 2px solid #f1f3f4;
}

.client-info-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 1rem;
    margin-bottom: 2rem;
}

.client-info-item {
    display: flex;
    align-items: center;
    padding: 1rem;
    background: linear-gradient(135deg, #f8f9ff 0%, #f0f2ff 100%);
    border-radius: var(--border-radius);
    border: 1px solid rgba(102, 126, 234, 0.1);
    transition: all 0.3s ease;
}

.client-info-item:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(102, 126, 234, 0.15);
}

.info-icon {
    background: var(--primary-gradient);
    color: white;
    width: 40px;
    height: 40px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-right: 1rem;
    flex-shrink: 0;
}

.info-content {
    display: flex;
    flex-direction: column;
}

.info-label {
    font-size: 0.875rem;
    color: var(--text-secondary);
    font-weight: 600;
    margin-bottom: 0.25rem;
}

.info-value {
    font-size: 1rem;
    color: var(--text-primary);
    font-weight: 600;
}

.notes-section {
    margin-top: 2rem;
}

/* Comments Styles */
.card-header-actions {
    display: flex;
    gap: 1rem;
}

.comments-list {
    display: flex;
    flex-direction: column;
    gap: 1.5rem;
}

.comment-card {
    background: linear-gradient(135deg, #f8f9ff 0%, #f0f2ff 100%);
    border: 1px solid rgba(102, 126, 234, 0.1);
    border-radius: var(--border-radius);
    padding: 1.5rem;
    transition: all 0.3s ease;
}

.comment-card:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(102, 126, 234, 0.15);
}

.comment-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 1rem;
}

.comment-author {
    display: flex;
    align-items: center;
}

.author-avatar {
    background: var(--primary-gradient);
    color: white;
    width: 50px;
    height: 50px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-weight: 700;
    font-size: 1.1rem;
    margin-right: 1rem;
    text-transform: uppercase;
}

.author-info {
    display: flex;
    flex-direction: column;
}

.author-name {
    margin: 0;
    font-size: 1.1rem;
    font-weight: 600;
    color: var(--text-primary);
}

.comment-date {
    font-size: 0.875rem;
    color: var(--text-secondary);
    margin-top: 0.25rem;
}

.comment-actions {
    display: flex;
    gap: 0.5rem;
}

.comment-edit-btn {
    background: var(--info-gradient);
    color: white;
    padding: 0.5rem 1rem;
    border-radius: var(--border-radius-small);
    text-decoration: none;
    font-size: 0.875rem;
    font-weight: 600;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
}

.comment-edit-btn:hover {
    background: linear-gradient(45deg, #0056b3, #004085);
    transform: translateY(-2px);
    color: white;
    text-decoration: none;
}

.comment-edit-btn i {
    margin-right: 0.5rem;
}

.comment-content {
    background: white;
    padding: 1rem;
    border-radius: var(--border-radius-small);
    border: 1px solid #e2e8f0;
}

.comment-content p {
    margin: 0;
    line-height: 1.6;
    color: var(--text-primary);
}

.empty-comments-state {
    text-align: center;
    padding: 3rem 2rem;
    color: var(--text-secondary);
}

.empty-comments-state i {
    font-size: 3rem;
    color: #ddd;
    margin-bottom: 1rem;
}

.empty-comments-state h4 {
    color: var(--text-primary);
    margin-bottom: 1rem;
}

.empty-comments-state p {
    font-size: 1.1rem;
    margin-bottom: 2rem;
}

/* Responsive Design for GeoTask */
@media (max-width: 768px) {
    .modern-project-header {
        flex-direction: column;
        gap: 1.5rem;
        text-align: center;
    }

    .project-actions {
        justify-content: center;
        width: 100%;
    }

    .modern-form-grid {
        grid-template-columns: 1fr;
        gap: 1rem;
    }

    .client-info-grid {
        grid-template-columns: 1fr;
    }

    .comment-header {
        flex-direction: column;
        gap: 1rem;
        align-items: flex-start;
    }

    .comment-actions {
        align-self: stretch;
    }

    .comment-edit-btn {
        width: 100%;
        justify-content: center;
    }
}

@media (max-width: 480px) {
    .project-info .project-title {
        font-size: 1.5rem;
    }

    .project-actions {
        flex-direction: column;
        width: 100%;
    }

    .calendar-integration {
        flex-direction: column;
        width: 100%;
    }

    .modern-btn {
        width: 100%;
        justify-content: center;
    }
}

/* MyTasks Specific Styles */
.task-number-link {
    text-decoration: none;
    color: inherit;
    transition: all 0.3s ease;
}

.task-number-link:hover {
    text-decoration: none;
    color: inherit;
}

.task-number-link:hover .task-number {
    color: #667eea;
    transform: scale(1.05);
}

.client-info,
.service-info {
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.client-info i,
.service-info i {
    color: #667eea;
    font-size: 0.9rem;
}

.quantity-badge {
    background: linear-gradient(45deg, #667eea, #764ba2);
    color: white;
    padding: 0.25rem 0.75rem;
    border-radius: 15px;
    font-size: 0.875rem;
    font-weight: 600;
    display: inline-block;
}

.task-deadline {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    font-weight: 600;
    padding: 0.25rem 0.75rem;
    border-radius: 15px;
    font-size: 0.875rem;
}

.task-deadline.urgent {
    background: linear-gradient(45deg, #dc3545, #c82333);
    color: white;
}

.task-deadline.warning {
    background: linear-gradient(45deg, #ffc107, #e0a800);
    color: #212529;
}

.task-deadline.normal {
    background: linear-gradient(45deg, #28a745, #20c997);
    color: white;
}

.task-deadline i {
    font-size: 0.8rem;
}

/* Modern Table Styles */
.modern-table-container {
    background: var(--background-overlay);
    border-radius: var(--border-radius);
    overflow: hidden;
    box-shadow: var(--card-shadow);
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.2);
}

.modern-table-header {
    background: var(--primary-gradient);
    color: white;
    padding: 1.5rem;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.modern-table-header h3 {
    margin: 0;
    font-size: 1.3rem;
    font-weight: 600;
    display: flex;
    align-items: center;
}

.modern-table-header h3 i {
    margin-right: 0.5rem;
    color: #ffd700;
}

.modern-table {
    width: 100%;
    border-collapse: collapse;
    background: white;
}

.modern-table thead {
    background: linear-gradient(135deg, #f8f9ff 0%, #f0f2ff 100%);
}

.modern-table th {
    padding: 1rem;
    text-align: left;
    font-weight: 600;
    color: var(--text-primary);
    border-bottom: 2px solid #e2e8f0;
    font-size: 0.95rem;
}

.modern-table th i {
    margin-right: 0.5rem;
    color: #667eea;
}

.modern-table td {
    padding: 1rem;
    border-bottom: 1px solid #f1f3f4;
    vertical-align: middle;
}

.modern-table tbody tr {
    transition: all 0.3s ease;
}

.modern-table tbody tr:hover {
    background: linear-gradient(135deg, #f8f9ff 0%, #f0f2ff 100%);
    transform: scale(1.01);
    box-shadow: 0 4px 12px rgba(102, 126, 234, 0.1);
}

/* Responsive Table */
@media (max-width: 768px) {
    .modern-table-header {
        flex-direction: column;
        gap: 1rem;
        text-align: center;
    }

    .modern-table {
        font-size: 0.875rem;
    }

    .modern-table th,
    .modern-table td {
        padding: 0.75rem 0.5rem;
    }

    .client-info,
    .service-info {
        flex-direction: column;
        gap: 0.25rem;
        align-items: flex-start;
    }

    .task-deadline {
        flex-direction: column;
        gap: 0.25rem;
        text-align: center;
    }
}

@media (max-width: 480px) {
    .modern-table-container {
        overflow-x: auto;
    }

    .modern-table {
        min-width: 600px;
    }

    .quantity-badge,
    .task-deadline {
        font-size: 0.75rem;
        padding: 0.2rem 0.5rem;
    }
}

/* Form Actions */
.form-actions {
    display: flex;
    gap: 1rem;
    justify-content: center;
    align-items: center;
    padding: 1rem 0;
}

@media (max-width: 768px) {
    .form-actions {
        flex-direction: column;
        gap: 0.75rem;
    }

    .form-actions .modern-btn {
        width: 100%;
        justify-content: center;
    }
}

/* Tasks for Check Specific Styles */
.check-task-row {
    border-left: 4px solid #667eea;
}

.check-task-row:hover {
    border-left-color: #ffd700;
}

.task-actions {
    display: flex;
    gap: 0.5rem;
    flex-wrap: wrap;
}

.task-action-btn {
    padding: 0.5rem 1rem;
    border-radius: var(--border-radius-small);
    text-decoration: none;
    font-size: 0.875rem;
    font-weight: 600;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    gap: 0.5rem;
    border: none;
    cursor: pointer;
}

.task-action-btn.check {
    background: var(--success-gradient);
    color: white;
}

.task-action-btn.edit {
    background: var(--info-gradient);
    color: white;
}

.task-action-btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
    text-decoration: none;
    color: white;
}

.header-actions {
    display: flex;
    align-items: center;
    gap: 1rem;
}

.status-indicator {
    padding: 0.5rem 1rem;
    border-radius: 20px;
    font-size: 0.875rem;
    font-weight: 600;
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.status-indicator.pending {
    background: linear-gradient(45deg, #ffc107, #e0a800);
    color: #212529;
}

.status-indicator.new {
    background: linear-gradient(45deg, #28a745, #20c997);
    color: white;
}

/* Clients Specific Styles */
.search-form {
    margin-bottom: 0;
}

.search-controls {
    display: grid;
    grid-template-columns: 1fr 200px auto;
    gap: 1.5rem;
    align-items: end;
}

.search-button-group {
    display: flex;
    gap: 0.5rem;
}

.empty-clients-state,
.empty-requests-state,
.empty-types-state {
    text-align: center;
    padding: 3rem 2rem;
    color: var(--text-secondary);
}

.empty-clients-state i,
.empty-requests-state i,
.empty-types-state i {
    font-size: 3rem;
    color: #ddd;
    margin-bottom: 1rem;
}

.empty-clients-state h3,
.empty-requests-state h3,
.empty-types-state h3 {
    color: var(--text-primary);
    margin-bottom: 1rem;
}

.clients-table-container,
.requests-container,
.types-container {
    background: var(--background-overlay);
    border-radius: var(--border-radius);
    overflow: hidden;
    box-shadow: var(--card-shadow);
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.2);
}

.clients-table-header,
.requests-header,
.types-header {
    background: var(--primary-gradient);
    color: white;
    padding: 1.5rem;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.clients-table-header h3,
.requests-header h3,
.types-header h3 {
    margin: 0;
    font-size: 1.3rem;
    font-weight: 600;
    display: flex;
    align-items: center;
}

.clients-table-header h3 i,
.requests-header h3 i,
.types-header h3 i {
    margin-right: 0.5rem;
    color: #ffd700;
}

.add-client-btn,
.add-type-btn {
    background: rgba(255, 255, 255, 0.2);
    color: white;
    padding: 0.75rem 1.5rem;
    border-radius: var(--border-radius-small);
    text-decoration: none;
    font-weight: 600;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.add-client-btn:hover,
.add-type-btn:hover {
    background: rgba(255, 255, 255, 0.3);
    transform: translateY(-2px);
    color: white;
    text-decoration: none;
}

.clients-grid,
.requests-grid,
.types-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(350px, 1fr));
    gap: 1.5rem;
    padding: 1.5rem;
}

.client-card,
.request-card,
.type-card {
    background: white;
    border-radius: var(--border-radius);
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    transition: all 0.3s ease;
    overflow: hidden;
}

.client-card:hover,
.request-card:hover,
.type-card:hover {
    transform: translateY(-4px);
    box-shadow: 0 8px 25px rgba(102, 126, 234, 0.15);
}

.client-card-header,
.request-card-header,
.type-card-header {
    padding: 1.5rem;
    background: linear-gradient(135deg, #f8f9ff 0%, #f0f2ff 100%);
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.client-avatar,
.request-card .client-avatar {
    background: var(--primary-gradient);
    color: white;
    width: 50px;
    height: 50px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-weight: 700;
    font-size: 1.1rem;
    text-transform: uppercase;
}

.client-info {
    display: flex;
    align-items: center;
    gap: 1rem;
}

.client-name {
    margin: 0;
    font-size: 1.2rem;
    font-weight: 600;
    color: var(--text-primary);
}

.client-name a {
    color: inherit;
    text-decoration: none;
    transition: color 0.3s ease;
}

.client-name a:hover {
    color: #667eea;
    text-decoration: none;
}

.client-representative {
    color: var(--text-secondary);
    font-size: 0.9rem;
}

.client-card-body,
.request-card-body,
.type-card-body {
    padding: 1.5rem;
}

.contact-info {
    display: flex;
    flex-direction: column;
    gap: 0.75rem;
}

.contact-item {
    display: flex;
    align-items: center;
    gap: 0.75rem;
    color: var(--text-primary);
}

.contact-item i {
    color: #667eea;
    width: 16px;
}

.client-card-actions,
.request-card-footer,
.type-card-footer {
    padding: 1rem 1.5rem;
    background: #f8f9fa;
    display: flex;
    gap: 0.5rem;
    flex-wrap: wrap;
}

.client-action-btn,
.request-action-btn,
.type-action-btn {
    padding: 0.5rem 1rem;
    border-radius: var(--border-radius-small);
    text-decoration: none;
    font-size: 0.875rem;
    font-weight: 600;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    gap: 0.5rem;
    border: none;
    cursor: pointer;
    flex: 1;
    justify-content: center;
}

.client-action-btn.edit,
.request-action-btn.details,
.type-action-btn.details {
    background: var(--info-gradient);
    color: white;
}

.client-action-btn.call,
.request-action-btn.call {
    background: var(--success-gradient);
    color: white;
}

.client-action-btn.email {
    background: var(--warning-gradient);
    color: white;
}

.request-action-btn.process,
.type-action-btn.info {
    background: var(--primary-gradient);
    color: white;
}

.client-action-btn:hover,
.request-action-btn:hover,
.type-action-btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
    text-decoration: none;
    color: white;
}

/* Request Specific Styles */
.request-status {
    display: flex;
    align-items: center;
}

.status-badge {
    padding: 0.5rem 1rem;
    border-radius: 20px;
    font-size: 0.875rem;
    font-weight: 600;
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.status-badge.new {
    background: linear-gradient(45deg, #28a745, #20c997);
    color: white;
}

.request-actions {
    display: flex;
    gap: 0.5rem;
}

.request-action-btn.view {
    background: var(--info-gradient);
    color: white;
    width: 40px;
    height: 40px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    text-decoration: none;
}

.request-description {
    margin-top: 1rem;
}

.request-description h5 {
    color: var(--text-primary);
    margin-bottom: 0.5rem;
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.request-description p {
    color: var(--text-secondary);
    line-height: 1.6;
    margin: 0;
}

/* Type Specific Styles */
.type-id {
    display: flex;
    align-items: center;
}

.id-badge {
    background: var(--primary-gradient);
    color: white;
    padding: 0.5rem 1rem;
    border-radius: 20px;
    font-size: 0.875rem;
    font-weight: 600;
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.type-actions {
    display: flex;
    gap: 0.5rem;
}

.type-action-btn.edit {
    background: var(--info-gradient);
    color: white;
    width: 40px;
    height: 40px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    text-decoration: none;
}

.type-icon {
    text-align: center;
    margin-bottom: 1rem;
}

.type-icon i {
    font-size: 2.5rem;
    color: #667eea;
}

.type-name {
    text-align: center;
    color: var(--text-primary);
    margin-bottom: 0.5rem;
}

.type-description {
    text-align: center;
    color: var(--text-secondary);
    font-size: 0.9rem;
}

/* Form Help Text */
.form-help {
    margin-top: 0.5rem;
    font-size: 0.875rem;
    color: var(--text-secondary);
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.form-help i {
    color: #667eea;
}

/* Type Preview */
.type-preview {
    text-align: center;
    padding: 2rem;
}

.preview-item {
    margin-bottom: 1rem;
}

.type-badge.preview {
    background: var(--primary-gradient);
    color: white;
    padding: 0.75rem 1.5rem;
    border-radius: 25px;
    font-size: 1rem;
    font-weight: 600;
    display: inline-flex;
    align-items: center;
    gap: 0.5rem;
    transition: all 0.3s ease;
}

.preview-description {
    color: var(--text-secondary);
    font-style: italic;
}

/* Updated Form Animation */
.modern-form-control.updated {
    border-color: #28a745;
    box-shadow: 0 0 0 3px rgba(40, 167, 69, 0.1);
    animation: pulse 0.5s ease-in-out;
}

@keyframes pulse {
    0% { transform: scale(1); }
    50% { transform: scale(1.02); }
    100% { transform: scale(1); }
}

/* Personal File Specific Styles */
.employee-header {
    background: var(--primary-gradient);
    color: white;
    padding: 2rem;
    border-radius: var(--border-radius);
    margin-bottom: 2rem;
    display: flex;
    justify-content: space-between;
    align-items: center;
    box-shadow: var(--card-shadow);
}

.employee-info {
    display: flex;
    align-items: center;
    gap: 1.5rem;
}

.employee-avatar {
    background: rgba(255, 255, 255, 0.2);
    color: white;
    width: 80px;
    height: 80px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-weight: 700;
    font-size: 1.8rem;
    text-transform: uppercase;
    border: 3px solid rgba(255, 255, 255, 0.3);
}

.employee-details {
    display: flex;
    flex-direction: column;
}

/* Modern Charts Container */
.charts-container {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(500px, 1fr));
    gap: 2rem;
    margin-top: 2rem;
}

.chart-card {
    background: white;
    border-radius: var(--border-radius);
    box-shadow: var(--card-shadow);
    overflow: hidden;
    transition: all 0.3s ease;
    border: 1px solid rgba(255, 255, 255, 0.1);
}

.chart-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
}

.chart-card.full-width {
    grid-column: 1 / -1;
}

.chart-header {
    background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
    padding: 1.5rem 2rem;
    border-bottom: 1px solid #e2e8f0;
}

.chart-header h4 {
    margin: 0;
    color: #1e293b;
    font-size: 1.25rem;
    font-weight: 600;
    display: flex;
    align-items: center;
    gap: 0.75rem;
}

.chart-header h4 i {
    color: #667eea;
    font-size: 1.1rem;
}

.chart-description {
    margin: 0.5rem 0 0 0;
    color: #64748b;
    font-size: 0.875rem;
    font-weight: 400;
}

.chart-body {
    padding: 1.5rem;
}

/* KPI Cards */
.kpi-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 1.5rem;
}

.kpi-card {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    padding: 1.5rem;
    border-radius: var(--border-radius);
    display: flex;
    align-items: center;
    gap: 1rem;
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;
}

.kpi-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(135deg, rgba(255, 255, 255, 0.1) 0%, rgba(255, 255, 255, 0.05) 100%);
    opacity: 0;
    transition: opacity 0.3s ease;
}

.kpi-card:hover::before {
    opacity: 1;
}

.kpi-card:hover {
    transform: translateY(-3px);
    box-shadow: 0 15px 30px rgba(102, 126, 234, 0.3);
}

.kpi-icon {
    background: rgba(255, 255, 255, 0.2);
    width: 50px;
    height: 50px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.25rem;
    flex-shrink: 0;
}

.kpi-content {
    flex: 1;
}

.kpi-value {
    font-size: 1.75rem;
    font-weight: 700;
    line-height: 1;
    margin-bottom: 0.25rem;
}

.kpi-label {
    font-size: 0.875rem;
    opacity: 0.9;
    font-weight: 500;
}

/* Salary Grid */
.salary-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 1.5rem;
}

.salary-item {
    display: flex;
    align-items: center;
    gap: 1rem;
    padding: 1rem;
    background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
    border-radius: var(--border-radius);
    border: 1px solid #e2e8f0;
}

.salary-icon {
    background: var(--primary-gradient);
    color: white;
    width: 50px;
    height: 50px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.25rem;
    flex-shrink: 0;
}

.salary-info {
    flex: 1;
}

.salary-label {
    display: block;
    font-size: 0.875rem;
    color: #64748b;
    font-weight: 500;
    margin-bottom: 0.25rem;
}

.salary-value {
    display: block;
    font-size: 1.5rem;
    font-weight: 700;
    color: #1e293b;
}

/* Performance Grid */
.performance-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 1.5rem;
}

.performance-item {
    display: flex;
    align-items: center;
    gap: 1rem;
    padding: 1rem;
    background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
    border-radius: var(--border-radius);
    border: 1px solid #e2e8f0;
}

.performance-icon {
    width: 50px;
    height: 50px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.25rem;
    flex-shrink: 0;
    color: white;
}

.performance-icon.success {
    background: linear-gradient(135deg, #10b981 0%, #059669 100%);
}

.performance-icon.info {
    background: linear-gradient(135deg, #3b82f6 0%, #1d4ed8 100%);
}

.performance-info {
    flex: 1;
}

.performance-label {
    display: block;
    font-size: 0.875rem;
    color: #64748b;
    font-weight: 500;
    margin-bottom: 0.25rem;
}

.performance-value {
    display: block;
    font-size: 1.25rem;
    font-weight: 600;
    color: #1e293b;
}

/* Analytics Form */
.analytics-form {
    display: flex;
    align-items: end;
    gap: 1rem;
    flex-wrap: wrap;
}

.analytics-form .form-group {
    flex: 1;
    min-width: 200px;
}

/* Kanban Board Styles */
.kanban-board {
    display: flex;
    gap: 1.5rem;
    overflow-x: auto;
    padding: 1rem 0;
    min-height: 70vh;
}

.kanban-column {
    flex: 0 0 320px;
    background: white;
    border-radius: var(--border-radius);
    box-shadow: var(--card-shadow);
    display: flex;
    flex-direction: column;
    max-height: 80vh;
}

.kanban-column-header {
    padding: 1rem 1.5rem;
    background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
    border-radius: var(--border-radius) var(--border-radius) 0 0;
    display: flex;
    justify-content: space-between;
    align-items: center;
    border-bottom: 1px solid #e2e8f0;
}

.column-title {
    margin: 0;
    font-size: 1.1rem;
    font-weight: 600;
    color: #1e293b;
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.column-actions {
    display: flex;
    align-items: center;
    gap: 0.75rem;
}

.card-count {
    background: var(--primary-gradient);
    color: white;
    padding: 0.25rem 0.75rem;
    border-radius: 12px;
    font-size: 0.875rem;
    font-weight: 600;
}

.add-card-btn {
    background: var(--success-gradient);
    color: white;
    border: none;
    width: 32px;
    height: 32px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    transition: all 0.3s ease;
}

.add-card-btn:hover {
    transform: scale(1.1);
    box-shadow: 0 4px 15px rgba(16, 185, 129, 0.3);
}

.kanban-cards-container {
    flex: 1;
    padding: 1rem;
    overflow-y: auto;
    display: flex;
    flex-direction: column;
    gap: 1rem;
}

.kanban-card {
    background: white;
    border: 1px solid #e2e8f0;
    border-radius: var(--border-radius);
    padding: 1rem;
    cursor: grab;
    transition: all 0.3s ease;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
}

.kanban-card:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
    border-color: #667eea;
}

.kanban-card:active {
    cursor: grabbing;
}

.card-header {
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    margin-bottom: 0.75rem;
}

.card-title-section {
    flex: 1;
}

.geotask-badge {
    display: inline-flex;
    align-items: center;
    gap: 0.25rem;
    background: var(--primary-gradient);
    color: white;
    padding: 0.25rem 0.5rem;
    border-radius: 4px;
    font-size: 0.75rem;
    font-weight: 600;
    margin-bottom: 0.5rem;
}

.card-title {
    margin: 0;
    font-size: 1rem;
    font-weight: 600;
    color: #1e293b;
    line-height: 1.4;
}

.card-actions {
    display: flex;
    gap: 0.25rem;
    opacity: 0;
    transition: opacity 0.3s ease;
}

.kanban-card:hover .card-actions {
    opacity: 1;
}

.card-action-btn {
    background: none;
    border: none;
    color: #64748b;
    padding: 0.25rem;
    border-radius: 4px;
    cursor: pointer;
    transition: all 0.3s ease;
    width: 28px;
    height: 28px;
    display: flex;
    align-items: center;
    justify-content: center;
}

.card-action-btn:hover {
    background: #f1f5f9;
    color: #1e293b;
}

.card-action-btn.edit-card:hover {
    background: #dbeafe;
    color: #3b82f6;
}

.card-action-btn.delete-card:hover {
    background: #fee2e2;
    color: #ef4444;
}

.card-description {
    color: #64748b;
    font-size: 0.875rem;
    line-height: 1.5;
    margin-bottom: 0.75rem;
}

.card-footer {
    display: flex;
    flex-wrap: wrap;
    gap: 0.5rem;
    align-items: center;
    font-size: 0.75rem;
}

.card-assignee {
    display: flex;
    align-items: center;
    gap: 0.25rem;
    color: #64748b;
    background: #f1f5f9;
    padding: 0.25rem 0.5rem;
    border-radius: 4px;
}

.card-due-date {
    display: flex;
    align-items: center;
    gap: 0.25rem;
    color: #64748b;
    background: #f1f5f9;
    padding: 0.25rem 0.5rem;
    border-radius: 4px;
}

.card-due-date.overdue {
    background: #fee2e2;
    color: #ef4444;
}

.card-labels {
    display: flex;
    flex-wrap: wrap;
    gap: 0.25rem;
}

.card-label {
    background: var(--primary-gradient);
    color: white;
    padding: 0.125rem 0.375rem;
    border-radius: 3px;
    font-size: 0.625rem;
    font-weight: 500;
}

/* Page Actions */
.page-actions {
    display: flex;
    gap: 1rem;
    align-items: center;
}

/* Team Boards Grid */
.team-boards-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(350px, 1fr));
    gap: 2rem;
    margin-top: 2rem;
}

.member-board-card {
    background: white;
    border-radius: var(--border-radius);
    box-shadow: var(--card-shadow);
    padding: 1.5rem;
    transition: all 0.3s ease;
    border: 1px solid #e2e8f0;
}

.member-board-card:hover {
    transform: translateY(-4px);
    box-shadow: 0 12px 40px rgba(0, 0, 0, 0.1);
    border-color: #667eea;
}

.member-info {
    display: flex;
    align-items: center;
    gap: 1rem;
    margin-bottom: 1.5rem;
}

.member-avatar {
    width: 60px;
    height: 60px;
    background: var(--primary-gradient);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: 2rem;
}

.member-details h3 {
    margin: 0;
    font-size: 1.25rem;
    font-weight: 600;
    color: #1e293b;
}

.member-details p {
    margin: 0.25rem 0 0 0;
    color: #64748b;
    font-size: 0.875rem;
}

.board-stats {
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    gap: 1rem;
    margin-bottom: 1.5rem;
}

.stat-item {
    text-align: center;
    padding: 1rem;
    background: #f8fafc;
    border-radius: 8px;
    border: 1px solid #e2e8f0;
}

.stat-number {
    display: block;
    font-size: 1.5rem;
    font-weight: 700;
    color: #1e293b;
    margin-bottom: 0.25rem;
}

.stat-label {
    font-size: 0.75rem;
    color: #64748b;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.board-actions {
    margin-bottom: 1rem;
}

.board-actions .modern-btn {
    width: 100%;
    justify-content: center;
}

.last-activity {
    text-align: center;
    padding-top: 1rem;
    border-top: 1px solid #e2e8f0;
}

.back-btn {
    display: inline-flex;
    align-items: center;
    gap: 0.5rem;
    color: #64748b;
    text-decoration: none;
    font-size: 0.875rem;
    margin-bottom: 1rem;
    transition: color 0.3s ease;
}

.back-btn:hover {
    color: #3b82f6;
}

.page-header-left {
    flex: 1;
}

/* Kanban Layout with Sidebar */
.kanban-layout {
    display: flex;
    min-height: calc(100vh - 120px);
    gap: 0;
}

.team-sidebar {
    width: 280px;
    background: white;
    border-right: 1px solid #e2e8f0;
    box-shadow: 2px 0 10px rgba(0, 0, 0, 0.05);
    display: flex;
    flex-direction: column;
}

.sidebar-header {
    padding: 1.5rem;
    border-bottom: 1px solid #e2e8f0;
    background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
}

.sidebar-header h3 {
    margin: 0;
    font-size: 1.1rem;
    font-weight: 600;
    color: #1e293b;
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.team-members-list {
    flex: 1;
    padding: 1rem 0;
    overflow-y: auto;
}

.team-member-item {
    display: flex;
    align-items: center;
    gap: 1rem;
    padding: 1rem 1.5rem;
    text-decoration: none;
    color: #64748b;
    transition: all 0.3s ease;
    border-left: 3px solid transparent;
    position: relative;
}

.team-member-item:hover {
    background: #f8fafc;
    color: #1e293b;
    text-decoration: none;
}

.team-member-item.active {
    background: linear-gradient(135deg, #e0f2fe 0%, #e1f5fe 100%);
    color: #0369a1;
    border-left-color: #0369a1;
    font-weight: 600;
}

.team-member-item .member-avatar {
    width: 40px;
    height: 40px;
    background: var(--primary-gradient);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: 1.25rem;
    flex-shrink: 0;
}

.team-member-item.active .member-avatar {
    background: linear-gradient(135deg, #0369a1 0%, #0284c7 100%);
}

.team-member-item .member-info {
    flex: 1;
    min-width: 0;
}

.team-member-item .member-name {
    font-size: 0.875rem;
    font-weight: 500;
    margin-bottom: 0.25rem;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
}

.team-member-item .member-position {
    font-size: 0.75rem;
    color: #94a3b8;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
}

.team-member-item.active .member-position {
    color: #0369a1;
}

.active-indicator {
    color: #10b981;
    font-size: 1rem;
    flex-shrink: 0;
}

.board-content {
    flex: 1;
    padding: 1.5rem;
    overflow-x: auto;
}

.board-content .modern-page-header {
    margin-bottom: 2rem;
}

.board-content .kanban-board {
    margin-top: 0;
}

/* Responsive Design for Charts */
@media (max-width: 1200px) {
    .charts-container {
        grid-template-columns: 1fr;
    }

    .chart-card.full-width {
        grid-column: 1;
    }

    .kanban-board {
        gap: 1rem;
    }

    .kanban-column {
        flex: 0 0 280px;
    }
}

@media (max-width: 768px) {
    .employee-header {
        flex-direction: column;
        text-align: center;
        gap: 1.5rem;
    }

    .employee-actions {
        display: flex;
        flex-direction: column;
        gap: 0.75rem;
        width: 100%;
    }

    .kpi-grid {
        grid-template-columns: 1fr;
    }

    .salary-grid,
    .performance-grid {
        grid-template-columns: 1fr;
    }

    .analytics-form {
        flex-direction: column;
        align-items: stretch;
    }

    .chart-header {
        padding: 1rem 1.5rem;
    }

    .chart-body {
        padding: 1rem;
    }
}

.employee-name {
    margin: 0;
    font-size: 2.2rem;
    font-weight: 700;
}

.employee-title {
    margin: 0.5rem 0 0 0;
    opacity: 0.9;
    font-size: 1.1rem;
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.employee-actions {
    display: flex;
    gap: 1rem;
    flex-wrap: wrap;
}

.salary-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 1.5rem;
}

.salary-item {
    display: flex;
    align-items: center;
    padding: 1.5rem;
    background: linear-gradient(135deg, #f8f9ff 0%, #f0f2ff 100%);
    border-radius: var(--border-radius);
    border: 1px solid rgba(102, 126, 234, 0.1);
    transition: all 0.3s ease;
}

.salary-item:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(102, 126, 234, 0.15);
}

.salary-icon {
    background: var(--primary-gradient);
    color: white;
    width: 50px;
    height: 50px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-right: 1rem;
    flex-shrink: 0;
}

.salary-info {
    display: flex;
    flex-direction: column;
}

.salary-label {
    font-size: 0.875rem;
    color: var(--text-secondary);
    font-weight: 600;
    margin-bottom: 0.25rem;
}

.salary-value {
    font-size: 1.5rem;
    color: var(--text-primary);
    font-weight: 700;
}

.analytics-form {
    display: flex;
    gap: 1.5rem;
    align-items: end;
}

.analytics-form .form-group {
    flex: 1;
    max-width: 300px;
}

.charts-container {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));
    gap: 2rem;
    margin-bottom: 2rem;
}

.chart-card {
    background: white;
    border-radius: var(--border-radius);
    box-shadow: var(--card-shadow);
    overflow: hidden;
    transition: all 0.3s ease;
}

.chart-card:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(102, 126, 234, 0.15);
}

.chart-header {
    background: linear-gradient(135deg, #f8f9ff 0%, #f0f2ff 100%);
    padding: 1.5rem;
    border-bottom: 1px solid #e2e8f0;
}

.chart-header h4 {
    margin: 0;
    color: var(--text-primary);
    font-size: 1.1rem;
    font-weight: 600;
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.chart-header i {
    color: #667eea;
}

.chart-body {
    padding: 1.5rem;
    min-height: 300px;
}

.performance-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 1.5rem;
}

.performance-item {
    display: flex;
    align-items: center;
    padding: 1.5rem;
    background: linear-gradient(135deg, #f8f9ff 0%, #f0f2ff 100%);
    border-radius: var(--border-radius);
    border: 1px solid rgba(102, 126, 234, 0.1);
    transition: all 0.3s ease;
}

.performance-item:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(102, 126, 234, 0.15);
}

.performance-icon {
    width: 50px;
    height: 50px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-right: 1rem;
    flex-shrink: 0;
    color: white;
}

.performance-icon.success {
    background: var(--success-gradient);
}

.performance-icon.info {
    background: var(--info-gradient);
}

.performance-info {
    display: flex;
    flex-direction: column;
}

.performance-label {
    font-size: 0.875rem;
    color: var(--text-secondary);
    font-weight: 600;
    margin-bottom: 0.25rem;
}

.performance-value {
    font-size: 1.2rem;
    color: var(--text-primary);
    font-weight: 600;
}

/* User Status Badges */
.user-status-badges {
    display: flex;
    gap: 0.5rem;
    flex-wrap: wrap;
}

.status-badge.admin {
    background: linear-gradient(45deg, #dc3545, #c82333);
    color: white;
}

.status-badge.worker {
    background: linear-gradient(45deg, #28a745, #20c997);
    color: white;
}

.status-badge.user {
    background: linear-gradient(45deg, #6c757d, #5a6268);
    color: white;
}

.user-form-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 1.5rem;
    margin-bottom: 2rem;
}

.user-form-grid .full-width {
    grid-column: 1 / -1;
}

.admin-actions-section {
    background: linear-gradient(135deg, #f8f9ff 0%, #f0f2ff 100%);
    padding: 2rem;
    border-radius: var(--border-radius);
    margin: 2rem 0;
    border: 1px solid rgba(102, 126, 234, 0.1);
}

.admin-actions-section h4 {
    color: var(--text-primary);
    margin-bottom: 1.5rem;
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.action-buttons {
    display: flex;
    gap: 1rem;
    flex-wrap: wrap;
}

/* Responsive Design for New Views */
@media (max-width: 768px) {
    .employee-header {
        flex-direction: column;
        gap: 1.5rem;
        text-align: center;
    }

    .employee-actions {
        justify-content: center;
        width: 100%;
    }

    .search-controls {
        grid-template-columns: 1fr;
        gap: 1rem;
    }

    .search-button-group {
        justify-content: center;
    }

    .clients-grid,
    .requests-grid,
    .types-grid {
        grid-template-columns: 1fr;
        padding: 1rem;
    }

    .analytics-form {
        flex-direction: column;
        align-items: stretch;
    }

    .charts-container {
        grid-template-columns: 1fr;
        gap: 1rem;
    }

    .action-buttons {
        flex-direction: column;
    }

    .user-form-grid {
        grid-template-columns: 1fr;
    }
}

@media (max-width: 480px) {
    .employee-name {
        font-size: 1.8rem;
    }

    .employee-actions {
        flex-direction: column;
        width: 100%;
    }

    .modern-btn {
        width: 100%;
        justify-content: center;
    }

    .client-card-actions,
    .request-card-footer,
    .type-card-footer {
        flex-direction: column;
    }

    .client-action-btn,
    .request-action-btn,
    .type-action-btn {
        width: 100%;
    }
}
