﻿@using static TaskManager.Common.NotificationMessages;
@model ClientFormModel

@{
    ViewData["Title"] = "Добави клиент";
}

<div class="modern-container">
    <div class="modern-page-header">
        <h1 class="modern-page-title">
            <i class="fas fa-user-plus"></i>
            Добави нов клиент
        </h1>
        <p class="modern-page-subtitle">
            Въведете информацията за новия клиент
        </p>
    </div>

    <div class="modern-card">
        <div class="modern-card-header">
            <h3>
                <i class="fas fa-address-card"></i>
                Данни за клиента
            </h3>
        </div>
        <div class="modern-card-body">
            <form method="post" class="modern-client-form">
                <div class="client-form-grid">
                    <div class="form-group">
                        <label asp-for="Name" class="modern-form-label">
                            <i class="fas fa-building"></i>
                            Име на клиента <span class="required">*</span>
                        </label>
                        <input asp-for="Name" class="modern-form-control" placeholder="Въведете име на клиента...">
                        <span asp-validation-for="Name" class="modern-validation-error"></span>
                    </div>

                    <div class="form-group">
                        <label asp-for="CustomerRepresentative" class="modern-form-label">
                            <i class="fas fa-user-tie"></i>
                            Представител
                        </label>
                        <input asp-for="CustomerRepresentative" class="modern-form-control" placeholder="Въведете име на представителя...">
                        <span asp-validation-for="CustomerRepresentative" class="modern-validation-error"></span>
                    </div>

                    <div class="form-group">
                        <label asp-for="PhoneNumber" class="modern-form-label">
                            <i class="fas fa-phone"></i>
                            Телефон <span class="required">*</span>
                        </label>
                        <input asp-for="PhoneNumber" class="modern-form-control" placeholder="+359 888 123 456">
                        <span asp-validation-for="PhoneNumber" class="modern-validation-error"></span>
                    </div>

                    <div class="form-group">
                        <label asp-for="Email" class="modern-form-label">
                            <i class="fas fa-envelope"></i>
                            Имейл адрес <span class="required">*</span>
                        </label>
                        <input asp-for="Email" type="email" class="modern-form-control" placeholder="<EMAIL>">
                        <span asp-validation-for="Email" class="modern-validation-error"></span>
                    </div>
                </div>

                <div class="form-actions">
                    <button type="submit" class="modern-btn modern-btn-primary">
                        <i class="fas fa-save"></i>
                        Добави клиент
                    </button>
                    <a asp-controller="Client" asp-action="Clients" class="modern-btn modern-btn-secondary">
                        <i class="fas fa-arrow-left"></i>
                        Назад към списъка
                    </a>
                </div>
            </form>
        </div>
    </div>
</div>

@section Scripts
{
    <partial name="_ValidationScriptsPartial">
}
