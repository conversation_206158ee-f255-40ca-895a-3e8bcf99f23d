﻿@model IEnumerable<RequestViewModel>;

@{
    ViewData["Title"] = "Поръчки";
}

<div class="modern-container">
    <div class="modern-page-header">
        <h1 class="modern-page-title">
            <i class="fas fa-clipboard-list"></i>
            Поръчки
        </h1>
        <p class="modern-page-subtitle">
            Управление на входящи заявки и поръчки от клиенти
        </p>
    </div>

    @if (!Model.Any())
    {
        <!-- Empty State -->
        <div class="modern-card">
            <div class="modern-card-body">
                <div class="empty-requests-state">
                    <i class="fas fa-inbox"></i>
                    <h3>Няма активни поръчки</h3>
                    <p>В момента няма нови заявки или поръчки за обработка.</p>
                    <a class="modern-btn modern-btn-primary" asp-controller="Request" asp-action="CreateRequest">
                        <i class="fas fa-plus"></i>
                        Създай нова поръчка
                    </a>
                </div>
            </div>
        </div>
    }
    else
    {
        <!-- Requests Grid -->
        <div class="requests-container">
            <div class="requests-header">
                <h3>
                    <i class="fas fa-list-alt"></i>
                    Активни поръчки (@Model.Count())
                </h3>
                <div class="header-actions">
                    <span class="status-indicator new">
                        <i class="fas fa-bell"></i>
                        Нови заявки
                    </span>
                </div>
            </div>

            <div class="requests-grid">
                @foreach (RequestViewModel request in Model)
                {
                    <div class="request-card">
                        <div class="request-card-header">
                            <div class="request-status">
                                <span class="status-badge new">
                                    <i class="fas fa-star"></i>
                                    Нова
                                </span>
                            </div>
                            <div class="request-actions">
                                <a asp-controller="Request" asp-action="Details" asp-route-id="@request.Id" class="request-action-btn view">
                                    <i class="fas fa-eye"></i>
                                </a>
                            </div>
                        </div>

                        <div class="request-card-body">
                            <div class="client-info">
                                <div class="client-avatar">
                                    @{
                                        var initials = request.Name.Split(' ').Take(2).Select(n => n.FirstOrDefault()).ToArray();
                                        string requestInitials = string.Join("", initials).ToUpper();
                                    }
                                    @requestInitials
                                </div>
                                <div class="client-details">
                                    <h4 class="client-name">
                                        <a asp-controller="Request" asp-action="Details" asp-route-id="@request.Id">
                                            @request.Name
                                        </a>
                                    </h4>
                                    <div class="contact-info">
                                        <span class="phone">
                                            <i class="fas fa-phone"></i>
                                            @request.PhoneNumber
                                        </span>
                                    </div>
                                </div>
                            </div>

                            <div class="request-description">
                                <h5>
                                    <i class="fas fa-file-alt"></i>
                                    Описание на задачата
                                </h5>
                                <p>@request.Description</p>
                            </div>
                        </div>

                        <div class="request-card-footer">
                            <a asp-controller="Request" asp-action="Details" asp-route-id="@request.Id" class="request-action-btn details">
                                <i class="fas fa-info-circle"></i>
                                Детайли
                            </a>
                            <a href="tel:@request.PhoneNumber" class="request-action-btn call">
                                <i class="fas fa-phone"></i>
                                Обади се
                            </a>
                            <button class="request-action-btn process" onclick="processRequest('@request.Id')">
                                <i class="fas fa-cogs"></i>
                                Обработи
                            </button>
                        </div>
                    </div>
                }
            </div>
        </div>
    }
</div>

<script>
function processRequest(requestId) {
    if (confirm('Сигурни ли сте, че искате да обработите тази поръчка?')) {
        // Add processing logic here
        window.location.href = '@Url.Action("Details", "Request")/' + requestId;
    }
}
</script>