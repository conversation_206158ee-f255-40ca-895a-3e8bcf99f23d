﻿/* Modern Calendar Integration */
.calendar-container {
  background: white;
  border-radius: 15px;
  overflow: hidden;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.05);
  min-height: 600px;
}

/* Reset and base styles for calendar */
.calendar-container * {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

.calendar-container {
  font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
  color: #1e293b;
}

.app {
  min-height: 100vh;
  display: flex;
  flex-direction: column;
}

/* Header styles */
.app-header {
  background: white;
  border-bottom: 1px solid #e2e8f0;
  padding: 1rem 2rem;
  display: flex;
  align-items: center;
  justify-content: space-between;
  flex-wrap: wrap;
  gap: 1rem;
  box-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1);
}

.app-header h1 {
  color: #1e293b;
  font-size: 1.5rem;
  font-weight: 600;
}

/* View selector */
.view-selector {
  display: flex;
  gap: 0.25rem;
  background: #f1f5f9;
  padding: 0.25rem;
  border-radius: 0.5rem;
}

.view-btn {
  padding: 0.5rem 1rem;
  border: none;
  background: transparent;
  border-radius: 0.375rem;
  cursor: pointer;
  font-weight: 500;
  transition: all 0.2s;
  color: #64748b;
}

.view-btn:hover {
  background: #e2e8f0;
  color: #1e293b;
}

.view-btn.active {
  background: white;
  color: #3b82f6;
  box-shadow: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
}

/* Date navigation */
.date-navigation {
  display: flex;
  align-items: center;
  gap: 1rem;
}

.date-navigation button {
  width: 2.5rem;
  height: 2.5rem;
  border: 1px solid #d1d5db;
  background: white;
  border-radius: 0.375rem;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 1.125rem;
  transition: all 0.2s;
}

.date-navigation button:hover {
  background: #f9fafb;
  border-color: #9ca3af;
}

.today-btn {
  background: #3b82f6 !important;
  color: white !important;
  font-size: 0.875rem;
  padding: 0.5rem 1rem;
  margin-left: 1rem;
}

.today-btn:hover {
  background: #2563eb !important;
}

.current-date {
  font-weight: 600;
  color: #1e293b;
  min-width: 200px;
  text-align: center;
}

/* Calendar container */
.calendar-container {
  flex: 1;
  display: flex;
  background: white;
  margin: 1rem auto;
  border-radius: 0.5rem;
  box-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1);
  overflow: hidden;
  max-width: 2470px;
  min-width: 1600px;
  width: calc(100% - 2rem);
}

/* Team sidebar */
.team-sidebar {
  width: 280px;
  background: #f8fafc;
  border-right: 1px solid #e2e8f0;
  padding: 1.5rem;
  overflow-y: auto;
}

.team-sidebar h3 {
  margin-bottom: 1rem;
  color: #1e293b;
  font-size: 1.125rem;
  font-weight: 600;
}

.team-member {
  margin-bottom: 1rem;
  display: flex;
  flex-direction: column;
  gap: 0;
}

/* Ensure worker color palette is always horizontal */
.team-member .worker-color-palette {
  display: flex !important;
  flex-direction: row !important;
  flex-wrap: nowrap !important;
}

.member-controls {
  display: flex;
  align-items: center;
  gap: 0.75rem;
  padding: 0.5rem;
  background: white;
  border-radius: 0.5rem;
  border: 1px solid #e2e8f0;
  transition: all 0.2s;
}

.member-controls:hover {
  border-color: #cbd5e1;
  box-shadow: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
}

.member-avatar {
  width: 2rem;
  height: 2rem;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-weight: 600;
  font-size: 0.75rem;
  flex-shrink: 0;
}

.visibility-toggle {
  background: none;
  border: none;
  cursor: pointer;
  padding: 0.25rem;
  border-radius: 0.25rem;
  color: #64748b;
  transition: all 0.2s ease;
  display: flex;
  align-items: center;
  justify-content: center;
  min-height: 32px;
  min-width: 32px;
}

.visibility-toggle:hover {
  background: #f1f5f9;
  color: #334155;
}

.member-name-button {
  background: none;
  border: none;
  cursor: pointer;
  padding: 0.25rem 0.5rem;
  border-radius: 0.25rem;
  font-weight: 500;
  transition: all 0.2s ease;
  text-align: left;
  flex: 1;
  min-height: 36px;
}

.member-name-button:hover {
  background: rgba(0, 0, 0, 0.05);
  transform: translateX(2px);
}

.member-name-button.highlighted {
  background: rgba(59, 130, 246, 0.1);
  border: 1px solid rgba(59, 130, 246, 0.3);
  font-weight: 600;
  box-shadow: 0 0 8px rgba(59, 130, 246, 0.3);
}

/* Calendar content */
.calendar-content {
  flex: 1;
  overflow: auto;
  display: flex;
  justify-content: center;
  align-items: flex-start;
  padding: 1rem;
  min-height: 600px;
  width: 100%;
  max-width: 1900px;
  margin: 0 auto;
}

/* Time grid styles */
.time-grid {
  display: flex;
  flex-direction: column;
  min-height: 480px;
  height: 100%;
  max-width: 2470px;
  min-width: 0;
  width: 100%;
  margin: 0 auto;
  border: 1px solid #e2e8f0;
  border-radius: 8px;
  overflow: hidden;
  background: white;
  flex: 1;
}

.time-header {
  display: flex;
  background: #f8fafc;
  border-bottom: 2px solid #e2e8f0;
  position: sticky;
  top: 0;
  z-index: 10;
  align-items: stretch;
  flex-wrap: nowrap;
  width: 100%;
  justify-content: flex-start;
}

.time-header .time-label {
  width: 104px;
  padding: 1rem 0.5rem;
  border-right: 1px solid #e2e8f0;
  font-weight: 600;
  font-size: 0.875rem;
  color: #64748b;
  display: flex;
  align-items: center;
  justify-content: center;
  text-align: center;
  box-sizing: border-box;
  flex-shrink: 0;
}

.day-header {
  flex: 1 1 0%;
  min-width: 141px;
  max-width: none;
  padding: 0.75rem 4px;
  text-align: center;
  font-weight: 600;
  color: #1e293b;
  border-right: 1px solid #e2e8f0;
  font-size: 0.8rem;
  line-height: 1.2;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  display: flex;
  align-items: center;
  justify-content: center;
  box-sizing: border-box;
}

.day-header:last-child {
  border-right: none;
}

.time-row {
  display: flex;
  border-bottom: 1px solid #f1f5f9;
  min-height: 42px;
  flex-wrap: nowrap;
  width: 100%;
  align-items: stretch;
  justify-content: flex-start;
}

.time-row:hover {
  background: #fafbfc;
}

.time-row .time-label {
  width: 104px;
  padding: 0.6rem 0.5rem;
  border-right: 1px solid #e2e8f0;
  font-size: 0.75rem;
  color: #64748b;
  display: flex;
  align-items: center;
  justify-content: center;
  background: #f8fafc;
  box-sizing: border-box;
  flex-shrink: 0;
}

.time-slot {
  flex: 1;
  border-right: 1px solid #e2e8f0;
  display: flex;
  flex-direction: column;
  position: relative;
}

.time-slot:last-child {
  border-right: none;
}

.unified-slot {
  min-height: 42px;
  flex: 1 1 0%;
  min-width: 141px;
  max-width: none;
  border-bottom: 1px solid #f1f5f9;
  border-right: 1px solid #e2e8f0;
  position: relative;
  cursor: pointer;
  transition: background-color 0.2s;
  padding: 4px;
  overflow: visible;
  box-sizing: border-box;
}

.unified-slot:hover {
  background: rgba(59, 130, 246, 0.05);
}

.unified-slot:last-child {
  border-right: none;
}

.tasks-container {
  position: relative;
  height: 100%;
  width: 100%;
}

/* Task items */
.task-item {
  margin: 1px;
  padding: 4px 6px;
  border-radius: 4px;
  color: white;
  font-size: 0.7rem;
  line-height: 1.2;
  cursor: pointer;
  transition: all 0.2s;
  overflow: hidden;
  display: flex;
  flex-direction: column;
  min-height: 115px;
  position: relative;
  box-sizing: border-box;
}

.task-item:hover {
  opacity: 0.8;
  transform: translateY(-1px);
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
}

.task-spanning {
  border: 1px solid rgba(255, 255, 255, 0.3);
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.task-time {
  font-size: 0.7rem;
  font-weight: 700;
  opacity: 1;
  margin-bottom: 2px;
  text-align: center;
  background: rgba(255, 255, 255, 0.2);
  padding: 1px 4px;
  border-radius: 2px;
}

.task-title {
  font-weight: 600;
  font-size: 0.8rem;
  margin-bottom: 2px;
  text-align: center;
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: center;
  line-height: 1.2;
}

.task-description {
  font-size: 0.75rem;
  opacity: 0.95;
  text-align: center;
  margin-bottom: 2px;
  flex: 1;
  font-weight: 500;
  line-height: 1.2;
  display: flex;
  align-items: center;
  justify-content: center;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.task-members {
  display: flex;
  gap: 2px;
  justify-content: center;
  margin-top: auto;
  flex-wrap: wrap;
}

.member-initial {
  font-size: 0.6rem;
  font-weight: 700;
  color: white;
  padding: 2px 4px;
  border-radius: 2px;
  min-width: 16px;
  text-align: center;
  display: inline-block;
}

/* Task highlighting effects */
.highlighted-task {
  animation: taskGlow 2s ease-in-out infinite alternate;
  box-shadow: 0 0 15px rgba(59, 130, 246, 0.6) !important;
  border: 2px solid #3b82f6 !important;
  transform: scale(1.02);
  z-index: 20 !important;
}

@keyframes taskGlow {
  0% {
    box-shadow: 0 0 15px rgba(59, 130, 246, 0.6);
  }
  100% {
    box-shadow: 0 0 25px rgba(59, 130, 246, 0.9);
  }
}

/* Non-working days styling */
.non-working-day {
  background-color: #fee2e2 !important;
  border-color: #fca5a5 !important;
}

.non-working-day.unified-slot {
  background-color: #fef2f2 !important;
}

.non-working-day.unified-slot:hover {
  background-color: #fee2e2 !important;
}

.day-header.non-working-day {
  background-color: #dc2626 !important;
  color: white !important;
  font-weight: bold;
}

/* Modal styles */
.modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
}

.modal-content {
  background: white;
  border-radius: 0.75rem;
  box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
  width: 85%;
  max-width: 650px;
  max-height: 85vh;
  overflow-y: auto;
  padding: 1.5rem;
}

.modal-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 1.5rem;
  border-bottom: 1px solid #e2e8f0;
}

.modal-header h2 {
  margin: 0;
  color: #1e293b;
  font-size: 1.25rem;
  font-weight: 600;
}

.close-btn {
  background: none;
  border: none;
  font-size: 1.5rem;
  cursor: pointer;
  color: #64748b;
  padding: 0;
  width: 2rem;
  height: 2rem;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 0.25rem;
  transition: all 0.2s;
}

.close-btn:hover {
  background: #f1f5f9;
  color: #1e293b;
}

.task-form {
  padding: 0;
  display: flex;
  flex-direction: column;
  gap: 1rem;
}

.form-group {
  margin-bottom: 0.75rem;
}

.form-row {
  display: flex;
  gap: 1rem;
  align-items: end;
}

.form-row .form-group {
  flex: 1;
  margin-bottom: 0;
}

.form-group label {
  display: block;
  margin-bottom: 0.5rem;
  font-weight: 500;
  color: #374151;
  font-size: 0.875rem;
}

.form-group input,
.form-group textarea,
.form-group select {
  width: 100%;
  padding: 0.5rem;
  border: 1px solid #d1d5db;
  border-radius: 0.375rem;
  font-size: 0.875rem;
  transition: border-color 0.2s;
}

.form-group input:focus,
.form-group textarea:focus,
.form-group select:focus {
  outline: none;
  border-color: #3b82f6;
  box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
}

.color-palette {
  display: flex;
  gap: 0.5rem;
  flex-wrap: wrap;
  margin-top: 0.5rem;
}

.color-option {
  width: 2rem;
  height: 2rem;
  border-radius: 50%;
  border: 2px solid transparent;
  cursor: pointer;
  transition: all 0.2s;
}

.color-option:hover {
  transform: scale(1.1);
  border-color: #374151;
}

.color-option.selected {
  border-color: #1f2937;
  border-width: 3px;
  transform: scale(1.1);
}

.member-dropdown {
  border: 1px solid #d1d5db;
  border-radius: 0.375rem;
  max-height: 200px;
  overflow-y: auto;
  background-color: white;
  width: 100%;
}

.member-checkbox-item {
  display: flex !important;
  flex-direction: row !important;
  align-items: center !important;
  justify-content: flex-start !important;
  padding: 0.75rem 1rem !important;
  cursor: pointer;
  border-bottom: 1px solid #f3f4f6;
  transition: background-color 0.2s;
  gap: 0.75rem !important;
  min-height: 3.5rem;
  width: 100%;
  box-sizing: border-box;
  flex-wrap: nowrap !important;
}

.member-checkbox-item:hover {
  background-color: #f9fafb;
}

.member-checkbox-item:last-child {
  border-bottom: none;
}

.member-checkbox-item input[type="checkbox"] {
  width: 1.125rem !important;
  height: 1.125rem !important;
  flex-shrink: 0 !important;
  margin: 0 !important;
  display: inline-block !important;
}

.member-avatar-small {
  flex-shrink: 0 !important;
  width: 2rem !important;
  height: 2rem !important;
  display: flex !important;
  align-items: center !important;
  justify-content: center !important;
  border-radius: 50% !important;
  font-size: 0.75rem !important;
  font-weight: 600 !important;
  color: white !important;
}

.member-checkbox-item span {
  flex-grow: 1 !important;
  font-weight: 500 !important;
  font-size: 0.875rem !important;
  white-space: nowrap !important;
  overflow: hidden !important;
  text-overflow: ellipsis !important;
  display: inline-block !important;
}

.modal-actions {
  display: flex;
  gap: 0.5rem;
  justify-content: flex-end;
  margin-top: 1.5rem;
  padding-top: 1rem;
  border-top: 1px solid #e2e8f0;
}

.modal-actions button {
  padding: 0.5rem 1rem;
  border-radius: 0.375rem;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s;
  border: 1px solid transparent;
}

.save-btn {
  background: #3b82f6;
  color: white;
  border-color: #3b82f6;
}

.save-btn:hover {
  background: #2563eb;
  border-color: #2563eb;
}

.cancel-btn {
  background: white;
  color: #64748b;
  border-color: #d1d5db;
}

.cancel-btn:hover {
  background: #f9fafb;
  border-color: #9ca3af;
}

.delete-btn {
  background: #ef4444;
  color: white;
  border-color: #ef4444;
  margin-right: auto;
}

.delete-btn:hover {
  background: #dc2626;
  border-color: #dc2626;
}

/* Calendar Integration Styles for GeoTask Views */
.calendar-integration {
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.calendar-status {
  font-size: 0.875rem;
  font-weight: 500;
}

.calendar-status i {
  margin-right: 0.25rem;
}

#calendarBtn {
  transition: all 0.3s ease;
  border: none;
  padding: 0.5rem 1rem;
  border-radius: 0.375rem;
  font-weight: 500;
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

#calendarBtn:hover {
  transform: translateY(-1px);
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
}

#calendarBtn i {
  font-size: 1rem;
}

.modal-header {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  border-bottom: none;
}

.modal-header .btn-close {
  filter: invert(1);
}

.form-control-color {
  width: 100%;
  height: 38px;
  border-radius: 0.375rem;
}

/* GeoTask Comments Section */
.geotask-comments-section {
  background: #f8fafc;
  border: 1px solid #e2e8f0;
  border-radius: 0.5rem;
  padding: 1rem;
  margin-top: 1rem;
}

.comments-section {
  margin-top: 1rem;
  border-top: 1px solid #e2e8f0;
  padding-top: 1rem;
}

.comments-title {
  color: #1e293b;
  font-size: 0.95rem;
  font-weight: 600;
  margin-bottom: 0.75rem;
}

.comments-list {
  max-height: 200px;
  overflow-y: auto;
  border: 1px solid #e2e8f0;
  border-radius: 0.375rem;
  background: white;
}

.comment-item {
  padding: 0.75rem;
  border-bottom: 1px solid #f1f5f9;
}

.comment-item:last-child {
  border-bottom: none;
}

.comment-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 0.5rem;
}

.comment-header strong {
  color: #1f2937;
  font-size: 0.875rem;
}

.comment-date {
  color: #6b7280;
  font-size: 0.75rem;
}

.comment-text {
  color: #374151;
  font-size: 0.875rem;
  line-height: 1.4;
  white-space: pre-wrap;
}

/* Add Comment Form */
.add-comment-form {
  margin-top: 1rem;
  padding-top: 1rem;
  border-top: 1px solid #e2e8f0;
}

.comment-input {
  resize: vertical;
  min-height: 60px;
}

.no-comments {
  text-align: center;
  color: #6b7280;
  font-style: italic;
  padding: 1rem;
  background: white;
  border: 1px solid #e2e8f0;
  border-radius: 0.375rem;
}

.team-list {
	position: absolute;
	left: 10px;
	top: 10px;
	background-color: white;
	padding: 10px;
	border: 1px solid #ccc;
	border-radius: 5px;
}

.team-member {
	display: flex;
	align-items: center;
	margin-bottom: 5px;
}

.member-initials {
	width: 30px;
	height: 30px;
	border-radius: 50%;
	text-align: center;
	line-height: 30px;
	margin-right: 10px;
}

.member-name {
	font-weight: bold;
	margin-right: 5px;
}

.team-checkbox {
	margin-right: 5px;
}

.calendar-container {
	display: flex;
	max-width: 95%;
}

.team-players {
	flex: 0 0 200px; /* Set the width of the team players column as needed */
	padding: 10px;
	background-color: #f9f9f9;
	border-right: 1px solid #ccc;
}

#calendar {
	flex: 1;
}

/* Month View Styles */
.month-view {
  padding: 1rem;
  background: white;
  border-radius: 8px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  width: 100%;
  max-width: 1200px;
  margin: 0 auto;
}

.month-header {
  display: grid;
  grid-template-columns: repeat(7, 1fr);
  gap: 1px;
  margin-bottom: 1px;
  background: #e5e7eb;
}

.month-day-header {
  background: #f3f4f6;
  padding: 0.75rem;
  text-align: center;
  font-weight: 600;
  color: #374151;
  border: 1px solid #e5e7eb;
}

.month-grid {
  display: grid;
  grid-template-columns: repeat(7, 1fr);
  gap: 1px;
  background: #e5e7eb;
}

.month-cell {
  background: white;
  min-height: 120px;
  padding: 0.5rem;
  border: 1px solid #e5e7eb;
  cursor: pointer;
  transition: background-color 0.2s;
}

.month-cell:hover {
  background: #f9fafb;
}

.month-cell.empty {
  background: #f9fafb;
  cursor: default;
}

.month-cell.today {
  background: #eff6ff;
  border-color: #3b82f6;
}

.month-day-number {
  font-weight: 600;
  color: #374151;
  margin-bottom: 0.5rem;
}

.month-cell.today .month-day-number {
  color: #3b82f6;
}

.month-tasks {
  display: flex;
  flex-direction: column;
  gap: 2px;
}

.month-task {
  background: #3b82f6;
  color: white;
  padding: 2px 6px;
  border-radius: 4px;
  font-size: 0.75rem;
  cursor: pointer;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  transition: opacity 0.2s;
}

.month-task:hover {
  opacity: 0.8;
}

.month-task-more {
  color: #6b7280;
  font-size: 0.75rem;
  font-style: italic;
  padding: 2px 6px;
}

/* Overlapping tasks positioning fix */
.task-spanning {
  position: absolute !important;
  top: 0 !important;
  margin: 0 !important;
}

.tasks-container {
  position: relative !important;
  height: 100% !important;
  width: 100% !important;
}

/* Member color indicator */
.member-color-indicator {
  width: 20px;
  height: 20px;
  border-radius: 4px;
  border: 2px solid white;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.2);
  flex-shrink: 0;
  transition: all 0.2s ease;
}

.member-color-indicator:hover {
  transform: scale(1.1);
  box-shadow: 0 2px 6px rgba(0, 0, 0, 0.3);
}

/* Worker color palette styles */
.worker-color-palette {
  display: flex !important;
  flex-direction: row !important;
  gap: 3px;
  flex-wrap: nowrap !important;
  justify-content: flex-start;
  align-items: center;
  margin-top: 4px;
  margin-bottom: 0;
  padding: 0;
  background: transparent;
  border-radius: 0;
  transition: all 0.2s ease;
  width: auto;
  max-width: 200px;
  box-sizing: border-box;
}

.worker-color-option {
  width: 12px !important;
  height: 12px !important;
  border-radius: 2px;
  cursor: pointer;
  border: 1px solid rgba(255, 255, 255, 0.8);
  transition: all 0.15s ease;
  box-sizing: border-box;
  flex-shrink: 0 !important;
  flex-grow: 0 !important;
  flex-basis: auto !important;
  box-shadow: 0 1px 2px rgba(0, 0, 0, 0.2);
  display: inline-block !important;
  float: none !important;
}

.worker-color-option:hover {
  transform: scale(1.3);
  border-color: #fff;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
  z-index: 10;
  position: relative;
}

.worker-color-option.selected {
  border-color: #000;
  border-width: 2px;
  transform: scale(1.1);
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.4);
}

/* Admin-only elements */
.admin-only {
  display: block;
}

.non-admin .admin-only {
  display: none !important;
}