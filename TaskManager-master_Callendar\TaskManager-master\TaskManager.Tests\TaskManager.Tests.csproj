<Project Sdk="Microsoft.NET.Sdk">

  <PropertyGroup>
    <TargetFramework>net6.0</TargetFramework>
    <ImplicitUsings>enable</ImplicitUsings>
    <Nullable>enable</Nullable>

    <IsPackable>false</IsPackable>
  </PropertyGroup>

  <ItemGroup>
    <PackageReference Include="Microsoft.NET.Test.Sdk" Version="17.3.2" />
    <PackageReference Include="xunit" Version="2.4.2" />
    <PackageReference Include="xunit.runner.visualstudio" Version="2.4.3">
      <IncludeAssets>runtime; build; native; contentfiles; analyzers; buildtransitive</IncludeAssets>
      <PrivateAssets>all</PrivateAssets>
    </PackageReference>
    <PackageReference Include="coverlet.collector" Version="3.1.2">
      <IncludeAssets>runtime; build; native; contentfiles; analyzers; buildtransitive</IncludeAssets>
      <PrivateAssets>all</PrivateAssets>
    </PackageReference>
    <PackageReference Include="Moq" Version="4.18.4" />
    <PackageReference Include="Microsoft.EntityFrameworkCore.InMemory" Version="6.0.10" />
  </ItemGroup>

  <ItemGroup>
    <ProjectReference Include="..\TaskManager\TaskManager.csproj" />
    <ProjectReference Include="..\TaskManager.Data\TaskManager.Data.csproj" />
    <ProjectReference Include="..\TaskManager.Data.Models\TaskManager.Data.Models.csproj" />
    <ProjectReference Include="..\TaskManager.Services.Data\TaskManager.Services.Data.csproj" />
    <ProjectReference Include="..\TaskManager.Web.ViewModels\TaskManager.Web.ViewModels.csproj" />
  </ItemGroup>

</Project>
