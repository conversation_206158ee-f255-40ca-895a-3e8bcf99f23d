namespace TaskManager.Data.Models
{
    using System.ComponentModel.DataAnnotations;
    using System.ComponentModel.DataAnnotations.Schema;
    using static Common.EntityValidationConstants.CalendarTask;

    public class CalendarTask
    {
        [Key]
        public Guid Id { get; set; } = Guid.NewGuid();

        [Required]
        [MaxLength(TitleMaxLength)]
        public string Title { get; set; } = null!;

        [MaxLength(DescriptionMaxLength)]
        public string Description { get; set; } = "";

        [Required]
        public DateTime Date { get; set; }

        [Required]
        public TimeSpan StartTime { get; set; }

        [Required]
        public TimeSpan EndTime { get; set; }

        [MaxLength(ColorMaxLength)]
        public string Color { get; set; } = "#3b82f6";

        [Required]
        public DateTime CreatedDate { get; set; } = DateTime.UtcNow;

        public DateTime? ModifiedDate { get; set; }

        // Optional relationship to GeoTask (one-to-one)
        public Guid? GeoTaskId { get; set; }

        public GeoTask? GeoTask { get; set; }

        // Many-to-many relationship with Workers
        public ICollection<CalendarTaskWorker> AssignedWorkers { get; set; } = new HashSet<CalendarTaskWorker>();
    }
}
