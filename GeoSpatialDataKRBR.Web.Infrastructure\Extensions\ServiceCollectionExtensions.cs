namespace GeoSpatialDataKRBR.Web.Infrastructure.Extensions
{
    using Microsoft.Extensions.DependencyInjection;
    using GeoSpatialDataKRBR.Services.Data;
    using GeoSpatialDataKRBR.Services.Data.Interfaces;

    public static class ServiceCollectionExtensions
    {
        public static IServiceCollection AddApplicationServices(this IServiceCollection services)
        {
            // Register application services
            services.AddScoped<IGeoLayerService, GeoLayerService>();
            services.AddScoped<IUserLayerPreferenceService, UserLayerPreferenceService>();
            services.AddScoped<IGeoServerService, GeoServerService>();

            return services;
        }

        public static IServiceCollection AddHttpClients(this IServiceCollection services)
        {
            // Register HTTP clients for GeoServer communication
            services.AddHttpClient("GeoServer", client =>
            {
                client.Timeout = TimeSpan.FromSeconds(30);
                client.DefaultRequestHeaders.Add("User-Agent", "GeoSpatialDataKRBR/1.0");
            });

            return services;
        }
    }
}
