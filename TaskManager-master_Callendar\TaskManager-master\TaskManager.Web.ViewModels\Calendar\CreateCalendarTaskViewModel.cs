using System.ComponentModel.DataAnnotations;

namespace TaskManager.Web.ViewModels.Calendar
{
    public class CreateCalendarTaskViewModel
    {
        [Required]
        [StringLength(200, MinimumLength = 3)]
        public string Title { get; set; } = null!;
        
        [StringLength(1000)]
        public string Description { get; set; } = "";
        
        public List<string> AssignedMemberIds { get; set; } = new List<string>();
        
        [Required]
        public DateTime Date { get; set; }
        
        [Required]
        public string StartTime { get; set; } = null!;
        
        [Required]
        public string EndTime { get; set; } = null!;
        
        public string Color { get; set; } = "#3b82f6";

        // Optional field for linking to existing GeoTask
        public string? GeoTaskId { get; set; }

        // Optional fields for creating actual GeoTasks
        public string? ClientId { get; set; }
        
        public string? Address { get; set; }
        
        public int? ProjectNumber { get; set; }
        
        public decimal? Price { get; set; }
        
        public int? Quantity { get; set; }
        
        public int? TypeId { get; set; }
        
        public string? IdKKKR { get; set; }
        
        public string? Note { get; set; }
    }
}
