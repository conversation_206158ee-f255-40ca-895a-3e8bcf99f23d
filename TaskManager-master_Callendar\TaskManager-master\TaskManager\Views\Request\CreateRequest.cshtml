﻿@using static TaskManager.Common.NotificationMessages;
@model CreateRequestFormModel

 <div class="row">
   <div class="col-sm-12 offset-lg-2 col-lg-8 offset-xl-3 col-xl-6">
      <form method="post">
         <div class="form-group">
            <label asp-for="Name"></label>
            <input asp-for="Name" class="form-control" placeholder="Име...">
            <span asp-validation-for="Name" class="small text-danger"></span>
         </div>
         <div class="form-group">
            <label asp-for="PhoneNumber"></label>
            <input asp-for="PhoneNumber" class="form-control"
               placeholder="+359...">
            <span asp-validation-for="PhoneNumber" class="small text-danger"></span>
         </div>
         <div class="form-group" data-untrustedinput="@Model.Description">
            <label asp-for="Description"></label>
            <textarea asp-for="Description" rows="4" class="form-control"
               placeholder="Описание на задачата..."></textarea>
            <span asp-validation-for="Description" class="text-danger"></span>
         </div>
         <div class="text-center">
            <input class="btn btn-primary mt-3" type="submit" value="Изпрати" />
         </div>
      </form>
   </div>
</div>

@section Scripts
    {
        <partial name ="_ValidationScriptsPartial">
}
