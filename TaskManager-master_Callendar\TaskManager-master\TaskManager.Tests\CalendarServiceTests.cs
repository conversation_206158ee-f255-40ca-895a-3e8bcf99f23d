using Microsoft.EntityFrameworkCore;
using TaskManager.Data;
using TaskManager.Data.Models;
using TaskManager.Services.Data;
using TaskManager.Web.ViewModels.Calendar;
using Xunit;

namespace TaskManager.Tests
{
    public class CalendarServiceTests : IDisposable
    {
        private readonly TaskManagerDbContext context;
        private readonly CalendarService calendarService;

        public CalendarServiceTests()
        {
            var options = new DbContextOptionsBuilder<TaskManagerDbContext>()
                .UseInMemoryDatabase(databaseName: Guid.NewGuid().ToString())
                .Options;

            context = new TaskManagerDbContext(options);
            calendarService = new CalendarService(context);

            // Seed test data
            SeedTestData();
        }

        private void SeedTestData()
        {
            // Create test users first
            var user1 = new ApplicationUser
            {
                Id = Guid.Parse("11111111-1111-1111-1111-111111111111"),
                FirstName = "Test",
                LastName = "Worker1",
                Email = "<EMAIL>",
                UserName = "<EMAIL>"
            };

            var user2 = new ApplicationUser
            {
                Id = Guid.Parse("*************-2222-2222-************"),
                FirstName = "Test",
                LastName = "Worker2",
                Email = "<EMAIL>",
                UserName = "<EMAIL>"
            };

            context.Users.AddRange(user1, user2);

            // Add test workers with correct properties
            var worker1 = new Worker
            {
                Id = Guid.Parse("*************-3333-3333-************"),
                UserId = user1.Id,
                PhoneNumber = "0888123456",
                Position = "Геодезист",
                Color = "#3B82F6"
            };

            var worker2 = new Worker
            {
                Id = Guid.Parse("*************-4444-4444-************"),
                UserId = user2.Id,
                PhoneNumber = "0888654321",
                Position = "Инженер",
                Color = "#EF4444"
            };

            context.Workers.AddRange(worker1, worker2);

            // Add test client
            var client = new Client
            {
                Id = Guid.Parse("cccccccc-cccc-cccc-cccc-cccccccccccc"),
                Name = "Test Client",
                Email = "<EMAIL>",
                PhoneNumber = "0888999555",
                CustomerRepresentative = "Test Rep"
            };

            context.Clients.Add(client);

            // Add test GeoTask
            var geoTask = new GeoTask
            {
                Id = Guid.Parse("dddddddd-dddd-dddd-dddd-dddddddddddd"),
                ProjectNumber = 1,
                Adrress = "Test Address",
                CreateDate = DateTime.Now,
                EndDate = DateTime.Now.AddDays(30),
                Price = 1000,
                quantity = 1,
                IdKKKR = "68134.905.dd",
                Note = "Test GeoTask Note",
                WorkerId = worker1.Id,
                CheckerId = worker1.Id,
                ClientId = client.Id,
                StatusId = 1,
                TypeId = 1
            };

            context.GeoTasks.Add(geoTask);
            context.SaveChanges();
        }

        [Fact]
        public async Task CreateTaskAsync_ShouldCreateCalendarTask_WhenValidDataProvided()
        {
            // Arrange
            var createModel = new CreateCalendarTaskViewModel
            {
                Title = "Test Calendar Task",
                Description = "Test Description",
                Date = DateTime.Today.AddDays(1),
                StartTime = "09:00",
                EndTime = "10:00",
                Color = "#3b82f6",
                AssignedMemberIds = new List<string> { "worker1", "worker2" }
            };

            // Act
            var result = await calendarService.CreateTaskAsync(createModel);

            // Assert
            Assert.NotNull(result);
            Assert.Equal("Test Calendar Task", result.Title);
            Assert.Equal("Test Description", result.Description);
            Assert.Equal(DateTime.Today.AddDays(1), result.Date);
            Assert.Equal("09:00", result.StartTime);
            Assert.Equal("10:00", result.EndTime);
            Assert.Equal("#3b82f6", result.Color);
            Assert.Equal(2, result.AssignedMemberIds.Count);
            Assert.Contains("worker1", result.AssignedMemberIds);
            Assert.Contains("worker2", result.AssignedMemberIds);

            // Verify in database
            var taskInDb = await context.CalendarTasks.FirstOrDefaultAsync(t => t.Id == result.Id);
            Assert.NotNull(taskInDb);
            Assert.Equal("Test Calendar Task", taskInDb.Title);
        }

        [Fact]
        public async Task CreateTaskAsync_ShouldCreateTaskWithGeoTaskId_WhenGeoTaskIdProvided()
        {
            // Arrange
            var createModel = new CreateCalendarTaskViewModel
            {
                Title = "GeoTask Calendar Task",
                Description = "Test Description",
                Date = DateTime.Today.AddDays(1),
                StartTime = "09:00",
                EndTime = "10:00",
                Color = "#3b82f6",
                GeoTaskId = "geotask1",
                AssignedMemberIds = new List<string> { "worker1" }
            };

            // Act
            var result = await calendarService.CreateTaskAsync(createModel);

            // Assert
            Assert.NotNull(result);
            Assert.Equal("geotask1", result.GeoTaskId);

            // Verify GeoTask link
            var isLinked = await calendarService.IsGeoTaskLinkedToCalendarAsync("geotask1");
            Assert.True(isLinked);
        }

        [Fact]
        public async Task GetTasksAsync_ShouldReturnAllTasks_WhenNoDateRangeProvided()
        {
            // Arrange
            await CreateTestCalendarTask("Task 1", DateTime.Today);
            await CreateTestCalendarTask("Task 2", DateTime.Today.AddDays(1));
            await CreateTestCalendarTask("Task 3", DateTime.Today.AddDays(-1));

            // Act
            var tasks = await calendarService.GetTasksAsync();

            // Assert
            Assert.NotNull(tasks);
            Assert.True(tasks.Count() >= 3);
        }

        [Fact]
        public async Task GetTasksAsync_ShouldReturnFilteredTasks_WhenDateRangeProvided()
        {
            // Arrange
            await CreateTestCalendarTask("Task Today", DateTime.Today);
            await CreateTestCalendarTask("Task Tomorrow", DateTime.Today.AddDays(1));
            await CreateTestCalendarTask("Task Yesterday", DateTime.Today.AddDays(-1));

            // Act
            var tasks = await calendarService.GetTasksAsync(DateTime.Today, DateTime.Today.AddDays(1));

            // Assert
            Assert.NotNull(tasks);
            var taskList = tasks.ToList();
            Assert.True(taskList.Count >= 2);
            Assert.Contains(taskList, t => t.Title == "Task Today");
            Assert.Contains(taskList, t => t.Title == "Task Tomorrow");
            Assert.DoesNotContain(taskList, t => t.Title == "Task Yesterday");
        }

        [Fact]
        public async Task IsGeoTaskLinkedToCalendarAsync_ShouldReturnTrue_WhenGeoTaskIsLinked()
        {
            // Arrange
            var createModel = new CreateCalendarTaskViewModel
            {
                Title = "Linked Task",
                Description = "Test",
                Date = DateTime.Today,
                StartTime = "09:00",
                EndTime = "10:00",
                Color = "#3b82f6",
                GeoTaskId = "geotask1",
                AssignedMemberIds = new List<string>()
            };

            await calendarService.CreateTaskAsync(createModel);

            // Act
            var isLinked = await calendarService.IsGeoTaskLinkedToCalendarAsync("geotask1");

            // Assert
            Assert.True(isLinked);
        }

        [Fact]
        public async Task IsGeoTaskLinkedToCalendarAsync_ShouldReturnFalse_WhenGeoTaskIsNotLinked()
        {
            // Act
            var isLinked = await calendarService.IsGeoTaskLinkedToCalendarAsync("nonexistent");

            // Assert
            Assert.False(isLinked);
        }

        [Fact]
        public async Task GetCalendarTaskIdByGeoTaskIdAsync_ShouldReturnTaskId_WhenGeoTaskIsLinked()
        {
            // Arrange
            var createModel = new CreateCalendarTaskViewModel
            {
                Title = "Linked Task",
                Description = "Test",
                Date = DateTime.Today,
                StartTime = "09:00",
                EndTime = "10:00",
                Color = "#3b82f6",
                GeoTaskId = "geotask1",
                AssignedMemberIds = new List<string>()
            };

            var createdTask = await calendarService.CreateTaskAsync(createModel);

            // Act
            var calendarTaskId = await calendarService.GetCalendarTaskIdByGeoTaskIdAsync("geotask1");

            // Assert
            Assert.Equal(createdTask.Id, calendarTaskId);
        }

        [Fact]
        public async Task RemoveTaskAsync_ShouldRemoveTask_WhenTaskExists()
        {
            // Arrange
            var task = await CreateTestCalendarTask("Task to Remove", DateTime.Today);

            // Act
            var result = await calendarService.RemoveTaskAsync(task.Id);

            // Assert
            Assert.True(result);

            // Verify task is removed
            var taskInDb = await context.CalendarTasks.FirstOrDefaultAsync(t => t.Id == task.Id);
            Assert.Null(taskInDb);
        }

        private async Task<CalendarTaskViewModel> CreateTestCalendarTask(string title, DateTime date)
        {
            var createModel = new CreateCalendarTaskViewModel
            {
                Title = title,
                Description = "Test Description",
                Date = date,
                StartTime = "09:00",
                EndTime = "10:00",
                Color = "#3b82f6",
                AssignedMemberIds = new List<string> { "worker1" }
            };

            return await calendarService.CreateTaskAsync(createModel);
        }

        public void Dispose()
        {
            context.Dispose();
        }
    }
}
