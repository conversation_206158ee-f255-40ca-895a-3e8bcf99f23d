﻿@using TaskManager.Services.Data.Interfaces;
@using TaskManager.Web.Infrastructure.Extentions;
@using static TaskManager.Common.NotificationMessages;

@model WorkerFormModel

 <div class="row">
   <div class="col-sm-12 offset-lg-4 col-lg-8 offset-xl-3 col-xl-6">
      <form method="post">
         <div class="form-group">
            <label asp-for="PhoneNumber"></label>
            <input asp-for="PhoneNumber" class="form-control" />
            <span asp-validation-for="PhoneNumber" class="small text-danger"></span>
         </div>
         <div class="form-group">
            <label asp-for="Position"></label>
            <input asp-for="Position" class="form-control" id="LastName"/>
            <span asp-validation-for="Position" class="small text-danger"></span>
         </div>
         <div>
             <label asp-for="RoleId"></label>
             <select asp-for="RoleId" class="form-control">
                    @foreach (RolesViewModel role in Model.Roles)
                    {
                        <option value="@role.Id">@role.Name</option>
                    }
                </select>
         </div>
         <div>
             <span>Заплата</span>
             <input asp-for="Salary" type="text" class="form-control" style="width:300px"/>
             <span asp-validation-for="Salary" class="text-danger"></span>
         </div>

            <input class="btn btn-primary" type="submit" value="Добави">
      </form>
   </div>
</div>

@section Scripts
{
    <partial name ="_ValidationScriptsPartial">
}