namespace TaskManager.Web.Infrastructure.Helpers
{
    using Microsoft.AspNetCore.Http;

    /// <summary>
    /// Helper class for detecting which form button was pressed
    /// </summary>
    public static class FormButtonHelper
    {
        /// <summary>
        /// Determines which button was pressed based on form keys
        /// </summary>
        /// <param name="formKeys">Collection of form keys</param>
        /// <returns>ButtonType indicating which button was pressed</returns>
        public static ButtonType DetectButtonPressed(ICollection<string> formKeys)
        {
            if (formKeys == null || !formKeys.Any())
            {
                return ButtonType.None;
            }

            // Check for Discard first (highest priority for cancellation)
            if (formKeys.Contains("Discard"))
            {
                return ButtonType.Discard;
            }

            // Check for SaveAndClose (high priority)
            if (formKeys.Contains("SaveAndClose"))
            {
                return ButtonType.SaveAndClose;
            }

            // Check for Save/Edit button
            if (formKeys.Contains("Edit") || formKeys.Contains("Save"))
            {
                return ButtonType.Save;
            }

            // No recognized buttons found
            return ButtonType.None;
        }

        /// <summary>
        /// Determines which button was pressed from HttpRequest
        /// </summary>
        /// <param name="request">HTTP request containing form data</param>
        /// <returns>ButtonType indicating which button was pressed</returns>
        public static ButtonType DetectButtonPressed(HttpRequest request)
        {
            if (request?.Form == null)
            {
                return ButtonType.None;
            }

            return DetectButtonPressed(request.Form.Keys);
        }

        /// <summary>
        /// Checks if Save button was pressed
        /// </summary>
        /// <param name="formKeys">Collection of form keys</param>
        /// <returns>True if Save button was pressed</returns>
        public static bool IsSaveButtonPressed(ICollection<string> formKeys)
        {
            return DetectButtonPressed(formKeys) == ButtonType.Save;
        }

        /// <summary>
        /// Checks if SaveAndClose button was pressed
        /// </summary>
        /// <param name="formKeys">Collection of form keys</param>
        /// <returns>True if SaveAndClose button was pressed</returns>
        public static bool IsSaveAndCloseButtonPressed(ICollection<string> formKeys)
        {
            return DetectButtonPressed(formKeys) == ButtonType.SaveAndClose;
        }

        /// <summary>
        /// Checks if any valid button was pressed
        /// </summary>
        /// <param name="formKeys">Collection of form keys</param>
        /// <returns>True if any valid button was pressed</returns>
        public static bool IsAnyButtonPressed(ICollection<string> formKeys)
        {
            return DetectButtonPressed(formKeys) != ButtonType.None;
        }
    }

    /// <summary>
    /// Enumeration of possible button types
    /// </summary>
    public enum ButtonType
    {
        /// <summary>
        /// No button detected
        /// </summary>
        None,

        /// <summary>
        /// Save button (Edit/Save) was pressed
        /// </summary>
        Save,

        /// <summary>
        /// Save and Close button was pressed
        /// </summary>
        SaveAndClose,

        /// <summary>
        /// Discard button was pressed
        /// </summary>
        Discard
    }
}
