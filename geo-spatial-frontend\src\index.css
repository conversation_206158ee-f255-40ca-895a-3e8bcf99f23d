:root {
  font-family: Inter, system-ui, Avenir, Helvetica, Arial, sans-serif;
  line-height: 1.5;
  font-weight: 400;

  color-scheme: light dark;
  color: rgba(255, 255, 255, 0.87);
  background-color: #242424;

  font-synthesis: none;
  text-rendering: optimizeLegibility;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  -webkit-text-size-adjust: 100%;
}

* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

body {
  margin: 0;
  display: flex;
  place-items: center;
  min-width: 320px;
  min-height: 100vh;
  font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
  background-color: #f5f5f5;
  color: #333;
}

#root {
  width: 100%;
  height: 100vh;
  margin: 0;
  padding: 0;
}

.map-container {
  height: 100vh;
  width: 100%;
  position: relative;
}

.leaflet-container {
  height: 100%;
  width: 100%;
}

.layer-control {
  position: absolute;
  top: 10px;
  right: 10px;
  background: white;
  border-radius: 8px;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
  padding: 15px;
  max-width: 320px;
  z-index: 1000;
  max-height: 80vh;
  overflow-y: auto;
}

.layer-control-header h3 {
  margin: 0 0 15px 0;
  font-size: 18px;
  color: #333;
  border-bottom: 2px solid #007bff;
  padding-bottom: 8px;
}

.layer-section {
  margin-bottom: 20px;
}

.section-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  cursor: pointer;
  padding: 8px 0;
  border-bottom: 1px solid #eee;
  margin-bottom: 10px;
}

.section-header h4 {
  margin: 0;
  font-size: 14px;
  color: #555;
  font-weight: 600;
}

.expand-icon {
  transition: transform 0.2s;
  font-size: 12px;
  color: #666;
}

.expand-icon.expanded {
  transform: rotate(180deg);
}

.section-content {
  padding-left: 5px;
}

.layer-item {
  margin-bottom: 12px;
  padding: 10px;
  border-radius: 6px;
  transition: background-color 0.2s;
  border: 1px solid #f0f0f0;
}

.layer-item:hover {
  background-color: #f8f9fa;
  border-color: #e0e0e0;
}

.layer-header {
  display: flex;
  align-items: flex-start;
  gap: 8px;
}

.visibility-toggle {
  background: none;
  border: none;
  cursor: pointer;
  padding: 2px;
  border-radius: 3px;
  color: #666;
  transition: color 0.2s, background-color 0.2s;
  flex-shrink: 0;
}

.visibility-toggle:hover {
  background-color: #e9ecef;
  color: #333;
}

.layer-info {
  flex: 1;
  min-width: 0;
}

.layer-name {
  font-weight: 500;
  color: #333;
  margin-bottom: 3px;
  font-size: 14px;
  line-height: 1.3;
}

.layer-description {
  font-size: 12px;
  color: #666;
  margin-bottom: 4px;
  line-height: 1.3;
}

.layer-details {
  font-size: 11px;
  color: #888;
}

.layer-type {
  background-color: #e9ecef;
  padding: 2px 6px;
  border-radius: 3px;
  font-weight: 500;
}

.layer-workspace {
  font-family: monospace;
}

.info-button {
  background: none;
  border: none;
  cursor: pointer;
  padding: 2px;
  border-radius: 3px;
  color: #666;
  transition: color 0.2s, background-color 0.2s;
  flex-shrink: 0;
}

.info-button:hover {
  background-color: #e9ecef;
  color: #007bff;
}

.opacity-control {
  display: flex;
  align-items: center;
  margin-top: 8px;
  font-size: 12px;
  color: #666;
  gap: 8px;
}

.opacity-control span:first-child {
  min-width: 70px;
  font-weight: 500;
}

.opacity-slider {
  flex: 1;
  height: 4px;
  background: #ddd;
  border-radius: 2px;
  outline: none;
  -webkit-appearance: none;
}

.opacity-slider::-webkit-slider-thumb {
  -webkit-appearance: none;
  width: 16px;
  height: 16px;
  background: #007bff;
  border-radius: 50%;
  cursor: pointer;
}

.opacity-slider::-moz-range-thumb {
  width: 16px;
  height: 16px;
  background: #007bff;
  border-radius: 50%;
  cursor: pointer;
  border: none;
}

.opacity-value {
  min-width: 35px;
  text-align: right;
  font-weight: 500;
}

.layer-control-footer {
  border-top: 1px solid #eee;
  padding-top: 10px;
  margin-top: 15px;
  font-size: 12px;
  color: #666;
  display: flex;
  justify-content: space-between;
}

.empty-state {
  text-align: center;
  padding: 20px;
  color: #666;
  font-style: italic;
}

.loading {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 100vh;
  font-size: 18px;
  color: #666;
}

.error {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 100vh;
  flex-direction: column;
  color: #d32f2f;
}

.error h2 {
  margin-bottom: 10px;
}

.btn {
  padding: 8px 16px;
  border: none;
  border-radius: 4px;
  cursor: pointer;
  font-size: 14px;
  transition: background-color 0.2s;
}

.btn-primary {
  background-color: #007bff;
  color: white;
}

.btn-primary:hover {
  background-color: #0056b3;
}

.btn-secondary {
  background-color: #6c757d;
  color: white;
}

.btn-secondary:hover {
  background-color: #545b62;
}

.feature-popup {
  max-width: 300px;
}

.feature-popup h4 {
  margin: 0 0 10px 0;
  color: #333;
  border-bottom: 1px solid #eee;
  padding-bottom: 5px;
}

.feature-popup table {
  width: 100%;
  border-collapse: collapse;
}

.feature-popup td {
  padding: 4px 8px;
  border-bottom: 1px solid #f0f0f0;
  font-size: 12px;
}

.feature-popup td:first-child {
  font-weight: 500;
  color: #666;
  width: 40%;
}

@media (max-width: 768px) {
  .layer-control {
    top: 10px;
    right: 10px;
    left: 10px;
    max-width: none;
    max-height: 50vh;
  }
}
