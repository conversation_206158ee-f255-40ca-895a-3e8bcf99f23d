import React, { useState } from 'react'
import './App.css'
import {
  BULGARIAN_LOCALE,
  isNonWorkingDay,
  formatBulgarianDate,
  getBulgarianDayName
} from './localization/bulgarian.js'

// Team members data structure
const TEAM_MEMBERS = [
  {
    id: 1,
    name: '<PERSON><PERSON>',
    initials: 'G<PERSON>',
    color: '#3B82F6', // Blue
    visible: true
  },
  {
    id: 2,
    name: '<PERSON>',
    initials: 'II',
    color: '#10B981', // Green
    visible: true
  },
  {
    id: 3,
    name: '<PERSON><PERSON>',
    initials: 'PP',
    color: '#F59E0B', // Yellow
    visible: true
  },
  {
    id: 4,
    name: '<PERSON><PERSON><PERSON>',
    initials: 'DD',
    color: '#EF4444', // Red
    visible: true
  }
]

// Calendar view types
const VIEW_TYPES = {
  DAY: 'day',
  WEEK: 'week',
  MONTH: 'month',
  YEAR: 'year'
}

// Predefined color palette for tasks
const TASK_COLORS = [
  '#3B82F6', // Blue
  '#10B981', // Green
  '#F59E0B', // Yellow
  '#EF4444', // Red
  '#8B5CF6', // Purple
  '#F97316', // Orange
  '#06B6D4', // Cyan
  '#84CC16', // Lime
  '#EC4899', // Pink
  '#6B7280'  // Gray
]

// Time slots for day/week view (08:00 to 18:00 with 30-min intervals)
const generateTimeSlots = () => {
  const slots = []
  for (let hour = 8; hour <= 18; hour++) {
    slots.push(`${hour.toString().padStart(2, '0')}:00`)
    if (hour < 18) {
      slots.push(`${hour.toString().padStart(2, '0')}:30`)
    }
  }
  return slots
}

const TIME_SLOTS = generateTimeSlots()

// Sample tasks for demonstration
const SAMPLE_TASKS = [
  {
    id: 1,
    title: 'Team Meeting',
    description: 'Weekly team sync meeting',
    assignedMembers: [1, 2, 3, 4],
    date: new Date().toISOString().split('T')[0],
    startTime: '09:00',
    endTime: '10:00',
    color: '#3b82f6'
  },
  {
    id: 2,
    title: 'Code Review',
    description: 'Review pull requests',
    assignedMembers: [1, 2],
    date: new Date().toISOString().split('T')[0],
    startTime: '14:00',
    endTime: '15:30',
    color: '#10b981'
  },
  {
    id: 3,
    title: 'Client Call',
    description: 'Discuss project requirements',
    assignedMembers: [3],
    date: new Date(Date.now() + 86400000).toISOString().split('T')[0], // Tomorrow
    startTime: '11:00',
    endTime: '12:00',
    color: '#f59e0b'
  }
]

function App() {
  const [currentView, setCurrentView] = useState(VIEW_TYPES.WEEK)
  const [currentDate, setCurrentDate] = useState(new Date())
  const [teamMembers, setTeamMembers] = useState(TEAM_MEMBERS)
  const [tasks, setTasks] = useState(SAMPLE_TASKS)
  const [selectedSlot, setSelectedSlot] = useState(null)
  const [showTaskModal, setShowTaskModal] = useState(false)
  const [editingTask, setEditingTask] = useState(null)
  const [draggedTask, setDraggedTask] = useState(null)
  const [dragOverSlot, setDragOverSlot] = useState(null)
  const [resizingTask, setResizingTask] = useState(null)
  const [resizeDirection, setResizeDirection] = useState(null)
  const [showConflictDialog, setShowConflictDialog] = useState(false)
  const [conflictData, setConflictData] = useState(null)
  const [selectedWorkers, setSelectedWorkers] = useState(new Set()) // Track selected workers for highlighting
  const [isLoading, setIsLoading] = useState(false) // Loading state for better UX

  // Toggle team member visibility
  const toggleMemberVisibility = (memberId) => {
    setTeamMembers(prev =>
      prev.map(member =>
        member.id === memberId
          ? { ...member, visible: !member.visible }
          : member
      )
    )
  }

  // Change team member color
  const changeMemberColor = (memberId, newColor) => {
    setTeamMembers(prev =>
      prev.map(member =>
        member.id === memberId
          ? { ...member, color: newColor }
          : member
      )
    )
  }

  // Toggle worker selection for task highlighting
  const toggleWorkerSelection = (workerId) => {
    setSelectedWorkers(prev => {
      const newSelected = new Set(prev)
      if (newSelected.has(workerId)) {
        newSelected.delete(workerId)
      } else {
        newSelected.add(workerId)
      }
      return newSelected
    })
  }

  // Check if a task should be highlighted (has selected workers)
  const shouldHighlightTask = (task) => {
    if (selectedWorkers.size === 0) return false
    return task.assignedMembers.some(memberId => selectedWorkers.has(memberId))
  }

  // Navigation functions for calendar views
  const navigateToWeekView = (date) => {
    setCurrentDate(new Date(date))
    setCurrentView(VIEW_TYPES.WEEK)
  }

  const navigateToMonthView = (year, month) => {
    setCurrentDate(new Date(year, month, 1))
    setCurrentView(VIEW_TYPES.MONTH)
  }

  // Task management functions
  // Check for time conflicts
  const checkTimeConflicts = (taskData, excludeTaskId = null) => {
    const conflicts = []

    for (const memberId of taskData.assignedMembers) {
      const memberTasks = tasks.filter(task =>
        task.id !== excludeTaskId &&
        task.date === taskData.date &&
        task.assignedMembers.includes(memberId)
      )

      for (const existingTask of memberTasks) {
        const newStart = new Date(`2000-01-01T${taskData.startTime}:00`)
        const newEnd = new Date(`2000-01-01T${taskData.endTime}:00`)
        const existingStart = new Date(`2000-01-01T${existingTask.startTime}:00`)
        const existingEnd = new Date(`2000-01-01T${existingTask.endTime}:00`)

        // Check for overlap
        if (newStart < existingEnd && newEnd > existingStart) {
          const member = teamMembers.find(m => m.id === memberId)
          conflicts.push({
            member: member,
            existingTask: existingTask,
            timeOverlap: {
              start: Math.max(newStart.getTime(), existingStart.getTime()),
              end: Math.min(newEnd.getTime(), existingEnd.getTime())
            }
          })
        }
      }
    }

    return conflicts
  }

  const createTask = async (taskData) => {
    setIsLoading(true)
    try {
      const conflicts = checkTimeConflicts(taskData)

      if (conflicts.length > 0) {
        setConflictData({ taskData, conflicts, action: 'create' })
        setShowConflictDialog(true)
        return
      }

      // Simulate API call for better UX
      await new Promise(resolve => setTimeout(resolve, 300))

      const newTask = {
        id: Date.now(),
        title: taskData.title,
        description: taskData.description,
        assignedMembers: taskData.assignedMembers,
        date: taskData.date,
        startTime: taskData.startTime,
        endTime: taskData.endTime,
        color: taskData.color || '#3b82f6'
      }
      setTasks(prev => [...prev, newTask])
      setShowTaskModal(false)
      setSelectedSlot(null)
    } catch (error) {
      console.error('Error creating task:', error)
    } finally {
      setIsLoading(false)
    }
  }

  const updateTask = (taskId, taskData) => {
    const conflicts = checkTimeConflicts(taskData, taskId)

    if (conflicts.length > 0) {
      setConflictData({ taskData, conflicts, action: 'update', taskId })
      setShowConflictDialog(true)
      return
    }

    setTasks(prev =>
      prev.map(task =>
        task.id === taskId
          ? { ...task, ...taskData }
          : task
      )
    )
    setShowTaskModal(false)
    setEditingTask(null)
  }

  const deleteTask = (taskId) => {
    setTasks(prev => prev.filter(task => task.id !== taskId))
    setShowTaskModal(false)
    setEditingTask(null)
  }

  const handleSlotClick = (date, time, memberId) => {
    setSelectedSlot({ date, time, memberId })
    setShowTaskModal(true)
    setEditingTask(null)
  }

  const handleTaskClick = (task) => {
    setEditingTask(task)
    setShowTaskModal(true)
    setSelectedSlot(null)
  }

  // Drag and drop handlers
  const handleDragStart = (e, task) => {
    setDraggedTask(task)
    e.dataTransfer.effectAllowed = 'move'
    e.dataTransfer.setData('text/html', e.target.outerHTML)
    e.target.classList.add('dragging')
  }

  const handleDragEnd = (e) => {
    e.target.classList.remove('dragging')
    setDraggedTask(null)
    setDragOverSlot(null)
  }

  const handleDragOver = (e) => {
    e.preventDefault()
    e.dataTransfer.dropEffect = 'move'
  }

  const handleDragEnter = (e, date, time, memberId = 1) => {
    e.preventDefault()
    setDragOverSlot({ date, time, memberId })
  }

  const handleDragLeave = (e) => {
    e.preventDefault()
    setDragOverSlot(null)
  }

  const handleDrop = (e, date, time, memberId = 1) => {
    e.preventDefault()
    setDragOverSlot(null)

    if (draggedTask) {
      const updatedTask = {
        ...draggedTask,
        date: new Date(date).toISOString().split('T')[0],
        startTime: time,
        // Calculate end time based on original duration
        endTime: calculateEndTime(time, draggedTask.startTime, draggedTask.endTime),
        // Keep original assigned members instead of replacing
        assignedMembers: draggedTask.assignedMembers
      }

      updateTask(draggedTask.id, updatedTask)
      setDraggedTask(null)
    }
  }

  const calculateEndTime = (newStartTime, originalStartTime, originalEndTime) => {
    const [startHour, startMin] = originalStartTime.split(':').map(Number)
    const [endHour, endMin] = originalEndTime.split(':').map(Number)
    const [newStartHour, newStartMin] = newStartTime.split(':').map(Number)

    const originalDurationMinutes = (endHour * 60 + endMin) - (startHour * 60 + startMin)
    const newEndMinutes = (newStartHour * 60 + newStartMin) + originalDurationMinutes

    const newEndHour = Math.floor(newEndMinutes / 60)
    const newEndMin = newEndMinutes % 60

    return `${String(newEndHour).padStart(2, '0')}:${String(newEndMin).padStart(2, '0')}`
  }

  // Resize handlers
  const handleResizeStart = (e, task, direction) => {
    e.stopPropagation()
    e.preventDefault()
    setResizingTask(task)
    setResizeDirection(direction)

    const handleMouseMove = (moveEvent) => {
      const rect = e.target.closest('.member-slot').getBoundingClientRect()
      const relativeY = moveEvent.clientY - rect.top
      const slotHeight = rect.height
      const timeSlotIndex = Math.floor(relativeY / (slotHeight / TIME_SLOTS.length))
      const newTime = TIME_SLOTS[Math.max(0, Math.min(timeSlotIndex, TIME_SLOTS.length - 1))]

      if (direction === 'top') {
        // Resize from top (change start time)
        if (newTime < task.endTime) {
          updateTask(task.id, { ...task, startTime: newTime })
        }
      } else {
        // Resize from bottom (change end time)
        if (newTime > task.startTime) {
          updateTask(task.id, { ...task, endTime: newTime })
        }
      }
    }

    const handleMouseUp = () => {
      setResizingTask(null)
      setResizeDirection(null)
      document.removeEventListener('mousemove', handleMouseMove)
      document.removeEventListener('mouseup', handleMouseUp)
    }

    document.addEventListener('mousemove', handleMouseMove)
    document.addEventListener('mouseup', handleMouseUp)
  }

  // Handle conflict resolution
  const handleConflictConfirm = () => {
    if (!conflictData) return

    const { taskData, action, taskId } = conflictData

    if (action === 'create') {
      const newTask = {
        id: Date.now(),
        title: taskData.title,
        description: taskData.description,
        assignedMembers: taskData.assignedMembers,
        date: taskData.date,
        startTime: taskData.startTime,
        endTime: taskData.endTime,
        color: taskData.color || '#3b82f6'
      }
      setTasks(prev => [...prev, newTask])
      setShowTaskModal(false)
      setSelectedSlot(null)
    } else if (action === 'update') {
      setTasks(prev =>
        prev.map(task =>
          task.id === taskId
            ? { ...task, ...taskData }
            : task
        )
      )
      setShowTaskModal(false)
      setEditingTask(null)
    }

    setShowConflictDialog(false)
    setConflictData(null)
  }

  const handleConflictCancel = () => {
    setShowConflictDialog(false)
    setConflictData(null)
  }

  // Get tasks for a specific date, time, and member - only return tasks that START at this time
  const getTasksForSlot = (date, time, memberId) => {
    return tasks.filter(task => {
      const taskDate = new Date(task.date).toDateString()
      const slotDate = new Date(date).toDateString()
      return taskDate === slotDate &&
             task.startTime === time &&
             task.assignedMembers.includes(memberId)
    })
  }

  // Get all tasks that are active (overlapping) at a specific time for any visible member
  const getAllTasksForTimeSlot = (date, time) => {
    const dateStr = new Date(date).toISOString().split('T')[0]
    return tasks.filter(task => {
      if (task.date !== dateStr) return false

      // Check if task is active during this time slot
      const taskStart = new Date(`2000-01-01T${task.startTime}:00`)
      const taskEnd = new Date(`2000-01-01T${task.endTime}:00`)
      const slotTime = new Date(`2000-01-01T${time}:00`)
      const slotEndTime = new Date(slotTime.getTime() + 30 * 60000) // 30 minutes later

      const isOverlapping = taskStart < slotEndTime && taskEnd > slotTime

      return isOverlapping && task.assignedMembers.some(memberId =>
        teamMembers.find(m => m.id === memberId && m.visible)
      )
    })
  }

  // Get tasks that start at a specific time (for positioning)
  const getTasksStartingAtTime = (date, time) => {
    const dateStr = new Date(date).toISOString().split('T')[0]
    return tasks.filter(task => {
      return task.date === dateStr &&
             task.startTime === time &&
             task.assignedMembers.some(memberId =>
               teamMembers.find(m => m.id === memberId && m.visible)
             )
    })
  }

  // Check if a task spans across a specific time slot (for visual positioning)
  const isTaskActiveInSlot = (task, date, time, memberId) => {
    const taskDate = new Date(task.date).toDateString()
    const slotDate = new Date(date).toDateString()
    return taskDate === slotDate &&
           task.startTime <= time &&
           task.endTime > time &&
           task.assignedMembers.includes(memberId)
  }

  // Calculate task height based on duration
  const calculateTaskHeight = (startTime, endTime) => {
    const [startHour, startMin] = startTime.split(':').map(Number)
    const [endHour, endMin] = endTime.split(':').map(Number)

    const startMinutes = startHour * 60 + startMin
    const endMinutes = endHour * 60 + endMin
    const durationMinutes = endMinutes - startMinutes

    // Each 30-minute slot is 35px (unified-slot min-height)
    const slotHeight = 35

    // Calculate how many 30-minute slots the task spans
    const thirtyMinuteSlots = durationMinutes / 30

    // For tasks that end exactly on a time boundary (like 9:00-10:00),
    // we need to extend to cover the full duration including the end time slot
    // A task from 9:00-10:00 should span 2 full slots (9:00-9:30 and 9:30-10:00)
    // and reach the 10:00 line, so we need the full calculated height
    const baseHeight = slotHeight * thirtyMinuteSlots

    // Add extra pixels to ensure the task reaches the end time line
    // This ensures visual coverage of the end time slot
    // For a 9:00-10:00 task (2 slots * 35px = 70px), we need to reach the 10:00 line
    return Math.max(slotHeight, baseHeight + 30)
  }

  // Calculate task position from top based on start time
  const calculateTaskPosition = (startTime, currentTime) => {
    const [startHour, startMin] = startTime.split(':').map(Number)
    const [currentHour, currentMin] = currentTime.split(':').map(Number)

    const startMinutes = startHour * 60 + startMin
    const currentMinutes = currentHour * 60 + currentMin
    const offsetMinutes = startMinutes - currentMinutes

    // Each 30-minute slot is 70px
    const slotHeight = 70
    return (offsetMinutes / 30) * slotHeight
  }

  // Get all tasks that span across multiple time slots for a member on a date
  const getTasksForMemberDay = (date, memberId) => {
    const dateStr = new Date(date).toISOString().split('T')[0]
    return tasks.filter(task =>
      task.date === dateStr &&
      task.assignedMembers.includes(memberId)
    ).sort((a, b) => a.startTime.localeCompare(b.startTime))
  }

  // Check if a task spans across a specific time slot
  const taskSpansTimeSlot = (task, timeSlot) => {
    const [taskStartHour, taskStartMin] = task.startTime.split(':').map(Number)
    const [taskEndHour, taskEndMin] = task.endTime.split(':').map(Number)
    const [slotHour, slotMin] = timeSlot.split(':').map(Number)

    const taskStartMinutes = taskStartHour * 60 + taskStartMin
    const taskEndMinutes = taskEndHour * 60 + taskEndMin
    const slotMinutes = slotHour * 60 + slotMin

    return slotMinutes >= taskStartMinutes && slotMinutes < taskEndMinutes
  }

  // Navigation functions
  const navigateDate = (direction) => {
    const newDate = new Date(currentDate)

    switch (currentView) {
      case VIEW_TYPES.DAY:
        newDate.setDate(newDate.getDate() + (direction === 'next' ? 1 : -1))
        break
      case VIEW_TYPES.WEEK:
        newDate.setDate(newDate.getDate() + (direction === 'next' ? 7 : -7))
        break
      case VIEW_TYPES.MONTH:
        newDate.setMonth(newDate.getMonth() + (direction === 'next' ? 1 : -1))
        break
      case VIEW_TYPES.YEAR:
        newDate.setFullYear(newDate.getFullYear() + (direction === 'next' ? 1 : -1))
        break
    }

    setCurrentDate(newDate)
  }

  return (
    <div className="app">
      <header className="app-header">
        <h1>{BULGARIAN_LOCALE.ui.appTitle}</h1>

        {/* View Type Selector */}
        <div className="view-selector">
          {Object.values(VIEW_TYPES).map(view => (
            <button
              key={view}
              className={`view-btn ${currentView === view ? 'active' : ''}`}
              onClick={() => setCurrentView(view)}
            >
              {BULGARIAN_LOCALE.ui[view]}
            </button>
          ))}
        </div>

        {/* Date Navigation */}
        <div className="date-navigation">
          <button onClick={() => navigateDate('prev')} title="Предишен период">←</button>
          <span className="current-date">
            {formatBulgarianDate(currentDate, 'long')}
          </span>
          <button onClick={() => navigateDate('next')} title="Следващ период">→</button>
          <button
            onClick={() => setCurrentDate(new Date())}
            className="today-btn"
            title="Днес"
          >
            Днес
          </button>
        </div>
      </header>

      <div className="calendar-container">
        {/* Team Members Sidebar */}
        <div className="team-sidebar">
          <h3>{BULGARIAN_LOCALE.ui.teamMembers}</h3>
          {teamMembers.map(member => (
            <div key={member.id} className="team-member">
              <div className="member-controls">
                <button
                  className="visibility-toggle"
                  onClick={() => toggleMemberVisibility(member.id)}
                  title={member.visible ? "Скрий работника" : "Покажи работника"}
                >
                  {member.visible ? (
                    <svg width="16" height="16" viewBox="0 0 24 24" fill="currentColor">
                      <path d="M12 4.5C7 4.5 2.73 7.61 1 12c1.73 4.39 6 7.5 11 7.5s9.27-3.11 11-7.5c-1.73-4.39-6-7.5-11-7.5zM12 17c-2.76 0-5-2.24-5-5s2.24-5 5-5 5 2.24 5 5-2.24 5-5 5zm0-8c-1.66 0-3 1.34-3 3s1.34 3 3 3 3-1.34 3-3-1.34-3-3-3z"/>
                    </svg>
                  ) : (
                    <svg width="16" height="16" viewBox="0 0 24 24" fill="currentColor">
                      <path d="M12 7c2.76 0 5 2.24 5 5 0 .65-.13 1.26-.36 1.83l2.92 2.92c1.51-1.26 2.7-2.89 3.43-4.75-1.73-4.39-6-7.5-11-7.5-1.4 0-2.74.25-3.98.7l2.16 2.16C10.74 7.13 11.35 7 12 7zM2 4.27l2.28 2.28.46.46C3.08 8.3 1.78 10.02 1 12c1.73 4.39 6 7.5 11 7.5 1.55 0 3.03-.3 4.38-.84l.42.42L19.73 22 21 20.73 3.27 3 2 4.27zM7.53 9.8l1.55 1.55c-.05.21-.08.43-.08.65 0 1.66 1.34 3 3 3 .22 0 .44-.03.65-.08l1.55 1.55c-.67.33-1.41.53-2.2.53-2.76 0-5-2.24-5-5 0-.79.2-1.53.53-2.2zm4.31-.78l3.15 3.15.02-.16c0-1.66-1.34-3-3-3l-.17.01z"/>
                    </svg>
                  )}
                </button>
                <div
                  className="member-avatar"
                  style={{ backgroundColor: member.color }}
                  data-testid={`sidebar-avatar-${member.initials}`}
                >
                  {member.initials}
                </div>
                <button
                  className={`member-name-button ${selectedWorkers.has(member.id) ? 'highlighted' : ''}`}
                  style={{ color: member.color }}
                  onClick={() => toggleWorkerSelection(member.id)}
                  title={selectedWorkers.has(member.id) ? "Скрий задачите" : "Покажи задачите"}
                >
                  {member.name}
                </button>
                <input
                  type="color"
                  value={member.color}
                  onChange={(e) => changeMemberColor(member.id, e.target.value)}
                  className="color-picker"
                />
              </div>
            </div>
          ))}
        </div>

        {/* Calendar Content */}
        <div className="calendar-content">
          {(currentView === VIEW_TYPES.DAY || currentView === VIEW_TYPES.WEEK) && (
            <div className="time-grid">
              <div className="time-header">
                <div className="time-label">Time</div>
                {currentView === VIEW_TYPES.WEEK ? (
                  // Week view - show 7 days starting from Monday
                  Array.from({ length: 7 }, (_, i) => {
                    const date = new Date(currentDate)
                    // Get Monday as start of week (getDay() returns 0 for Sunday, 1 for Monday, etc.)
                    const dayOfWeek = date.getDay()
                    const daysFromMonday = dayOfWeek === 0 ? 6 : dayOfWeek - 1 // Convert Sunday (0) to 6, others subtract 1
                    const startOfWeek = new Date(date.setDate(date.getDate() - daysFromMonday))
                    const dayDate = new Date(startOfWeek.setDate(startOfWeek.getDate() + i))
                    const isNonWorking = isNonWorkingDay(dayDate)
                    return (
                      <div key={i} className={`day-header ${isNonWorking ? 'non-working-day' : ''}`}>
                        {getBulgarianDayName(dayDate, 'abbreviated')}, {formatBulgarianDate(dayDate, 'dayMonth')}
                      </div>
                    )
                  })
                ) : (
                  // Day view - show single day with Bulgarian formatting
                  <div className={`day-header ${isNonWorkingDay(currentDate) ? 'non-working-day' : ''}`}>
                    {getBulgarianDayName(currentDate, 'long')}, {formatBulgarianDate(currentDate, 'dayMonth')}
                  </div>
                )}
              </div>

              {/* Time slots */}
              {TIME_SLOTS.map(time => (
                <div key={time} className="time-row">
                  <div className="time-label">{time}</div>
                  {currentView === VIEW_TYPES.WEEK ? (
                    // Week view - 7 columns starting from Monday
                    Array.from({ length: 7 }, (_, dayIndex) => {
                      const date = new Date(currentDate)
                      // Get Monday as start of week
                      const dayOfWeek = date.getDay()
                      const daysFromMonday = dayOfWeek === 0 ? 6 : dayOfWeek - 1
                      const startOfWeek = new Date(date.setDate(date.getDate() - daysFromMonday))
                      const dayDate = new Date(startOfWeek.setDate(startOfWeek.getDate() + dayIndex))
                      const dayTasks = getAllTasksForTimeSlot(dayDate, time)
                      const isNonWorking = isNonWorkingDay(dayDate)

                      return (
                        <div key={dayIndex} className={`time-slot ${isNonWorking ? 'non-working-day' : ''}`}>
                          <div
                            className={`unified-slot ${dragOverSlot &&
                              dragOverSlot.date.toDateString() === dayDate.toDateString() &&
                              dragOverSlot.time === time ? 'drag-over' : ''} ${isNonWorking ? 'non-working-day' : ''}`}
                            onClick={() => handleSlotClick(dayDate, time, 1)}
                            onDragOver={handleDragOver}
                            onDragEnter={(e) => handleDragEnter(e, dayDate, time, 1)}
                            onDragLeave={handleDragLeave}
                            onDrop={(e) => handleDrop(e, dayDate, time, 1)}
                            data-testid={`day-time-slot-${dayDate.toISOString().split('T')[0]}-${time}`}
                          >
                            {dragOverSlot &&
                              dragOverSlot.date.toDateString() === dayDate.toDateString() &&
                              dragOverSlot.time === time && (
                                <div className="drop-zone-indicator" />
                              )}
                            <div className="tasks-container">
                              {dayTasks.filter(task => task.startTime === time).map((task, index) => {
                                const assignedMembers = task.assignedMembers
                                  .map(memberId => teamMembers.find(m => m.id === memberId))
                                  .filter(Boolean)

                                const taskHeight = calculateTaskHeight(task.startTime, task.endTime)

                                // Calculate overlapping tasks for width calculation
                                const overlappingTasks = dayTasks.filter(otherTask => {
                                  const taskStart = new Date(`2000-01-01T${task.startTime}:00`)
                                  const taskEnd = new Date(`2000-01-01T${task.endTime}:00`)
                                  const otherStart = new Date(`2000-01-01T${otherTask.startTime}:00`)
                                  const otherEnd = new Date(`2000-01-01T${otherTask.endTime}:00`)
                                  return taskStart < otherEnd && taskEnd > otherStart
                                })

                                const tasksStartingHere = dayTasks.filter(t => t.startTime === time)
                                const taskIndex = tasksStartingHere.findIndex(t => t.id === task.id)

                                const taskWidth = `${Math.floor(100 / Math.max(overlappingTasks.length, 1))}%`
                                const taskLeft = `${(taskIndex * 100) / Math.max(overlappingTasks.length, 1)}%`

                                // Calculate position from top based on start time
                                const [taskStartHour, taskStartMin] = task.startTime.split(':').map(Number)
                                const [slotHour, slotMin] = time.split(':').map(Number)
                                const taskStartMinutes = taskStartHour * 60 + taskStartMin
                                const slotMinutes = slotHour * 60 + slotMin
                                const offsetMinutes = taskStartMinutes - slotMinutes
                                const topOffset = (offsetMinutes / 30) * 35 // 35px per 30-minute slot

                                return (
                                  <div
                                    key={task.id}
                                    className={`task-item task-spanning ${shouldHighlightTask(task) ? 'highlighted-task' : ''}`}
                                    style={{
                                      backgroundColor: task.color,
                                      height: `${taskHeight}px`,
                                      width: taskWidth,
                                      left: taskLeft,
                                      position: 'absolute',
                                      top: `${Math.max(0, topOffset)}px`,
                                      zIndex: 10
                                    }}
                                    draggable
                                    onDragStart={(e) => handleDragStart(e, task)}
                                    onDragEnd={handleDragEnd}
                                    onClick={(e) => {
                                      e.stopPropagation()
                                      handleTaskClick(task)
                                    }}
                                  >
                                    <div
                                      className="resize-handle resize-handle-top"
                                      onMouseDown={(e) => handleResizeStart(e, task, 'top')}
                                    />
                                    <div className="task-time">
                                      {task.startTime} - {task.endTime}
                                    </div>
                                    <div className="task-title">{task.title}</div>
                                    <div className="task-description">{task.description}</div>
                                    <div className="task-members">
                                      {assignedMembers.map(member => (
                                        <span
                                          key={member.id}
                                          className="member-initial"
                                          style={{ backgroundColor: member.color }}
                                          data-testid={`task-member-${member.initials}-${task.id}`}
                                        >
                                          {member.initials}
                                        </span>
                                      ))}
                                    </div>
                                    <div
                                      className="resize-handle resize-handle-bottom"
                                      onMouseDown={(e) => handleResizeStart(e, task, 'bottom')}
                                    />
                                  </div>
                                )
                              })}
                            </div>
                          </div>
                        </div>
                      )
                    })
                  ) : (
                    // Day view - single column
                    <div className={`time-slot ${isNonWorkingDay(currentDate) ? 'non-working-day' : ''}`}>
                      <div
                        className={`unified-slot ${dragOverSlot &&
                          dragOverSlot.date.toDateString() === currentDate.toDateString() &&
                          dragOverSlot.time === time ? 'drag-over' : ''} ${isNonWorkingDay(currentDate) ? 'non-working-day' : ''}`}
                        onClick={() => handleSlotClick(currentDate, time, 1)}
                        onDragOver={handleDragOver}
                        onDragEnter={(e) => handleDragEnter(e, currentDate, time, 1)}
                        onDragLeave={handleDragLeave}
                        onDrop={(e) => handleDrop(e, currentDate, time, 1)}
                      >
                        {dragOverSlot &&
                          dragOverSlot.date.toDateString() === currentDate.toDateString() &&
                          dragOverSlot.time === time && (
                            <div className="drop-zone-indicator" />
                          )}
                        <div className="tasks-container">
                          {getAllTasksForTimeSlot(currentDate, time).filter(task => task.startTime === time).map((task, index) => {
                            const assignedMembers = task.assignedMembers
                              .map(memberId => teamMembers.find(m => m.id === memberId))
                              .filter(Boolean)

                            const taskHeight = calculateTaskHeight(task.startTime, task.endTime)

                            // Calculate overlapping tasks for width calculation
                            const allTasks = getAllTasksForTimeSlot(currentDate, time)
                            const overlappingTasks = allTasks.filter(otherTask => {
                              const taskStart = new Date(`2000-01-01T${task.startTime}:00`)
                              const taskEnd = new Date(`2000-01-01T${task.endTime}:00`)
                              const otherStart = new Date(`2000-01-01T${otherTask.startTime}:00`)
                              const otherEnd = new Date(`2000-01-01T${otherTask.endTime}:00`)
                              return taskStart < otherEnd && taskEnd > otherStart
                            })

                            const tasksStartingHere = allTasks.filter(t => t.startTime === time)
                            const taskIndex = tasksStartingHere.findIndex(t => t.id === task.id)

                            const taskWidth = `${Math.floor(100 / Math.max(overlappingTasks.length, 1))}%`
                            const taskLeft = `${(taskIndex * 100) / Math.max(overlappingTasks.length, 1)}%`

                            // Calculate position from top based on start time
                            const [taskStartHour, taskStartMin] = task.startTime.split(':').map(Number)
                            const [slotHour, slotMin] = time.split(':').map(Number)
                            const taskStartMinutes = taskStartHour * 60 + taskStartMin
                            const slotMinutes = slotHour * 60 + slotMin
                            const offsetMinutes = taskStartMinutes - slotMinutes
                            const topOffset = (offsetMinutes / 30) * 35 // 35px per 30-minute slot

                            return (
                              <div
                                key={task.id}
                                className={`task-item task-spanning ${shouldHighlightTask(task) ? 'highlighted-task' : ''}`}
                                style={{
                                  backgroundColor: task.color,
                                  height: `${taskHeight}px`,
                                  width: taskWidth,
                                  left: taskLeft,
                                  position: 'absolute',
                                  top: `${Math.max(0, topOffset)}px`,
                                  zIndex: 10
                                }}
                                draggable
                                onDragStart={(e) => handleDragStart(e, task)}
                                onDragEnd={handleDragEnd}
                                onClick={(e) => {
                                  e.stopPropagation()
                                  handleTaskClick(task)
                                }}
                              >
                                <div
                                  className="resize-handle resize-handle-top"
                                  onMouseDown={(e) => handleResizeStart(e, task, 'top')}
                                />
                                <div className="task-time">
                                  {task.startTime} - {task.endTime}
                                </div>
                                <div className="task-title">{task.title}</div>
                                <div className="task-description">{task.description}</div>
                                <div className="task-members">
                                  {assignedMembers.map(member => (
                                    <span
                                      key={member.id}
                                      className="member-initial"
                                      style={{ backgroundColor: member.color }}
                                      data-testid={`week-task-member-${member.initials}-${task.id}`}
                                    >
                                      {member.initials}
                                    </span>
                                  ))}
                                </div>
                                <div
                                  className="resize-handle resize-handle-bottom"
                                  onMouseDown={(e) => handleResizeStart(e, task, 'bottom')}
                                />
                              </div>
                            )
                          })}
                        </div>
                      </div>
                    </div>
                  )}
                </div>
              ))}
            </div>
          )}

          {currentView === VIEW_TYPES.MONTH && (
            <MonthView
              currentDate={currentDate}
              tasks={tasks}
              teamMembers={teamMembers}
              onSlotClick={handleSlotClick}
              onTaskClick={handleTaskClick}
              getTasksForSlot={getTasksForSlot}
              onDayClick={navigateToWeekView}
            />
          )}

          {currentView === VIEW_TYPES.YEAR && (
            <YearView
              currentDate={currentDate}
              tasks={tasks}
              teamMembers={teamMembers}
              onMonthClick={navigateToMonthView}
            />
          )}
        </div>
      </div>

      {/* Task Modal */}
      {showTaskModal && (
        <TaskModal
          isOpen={showTaskModal}
          onClose={() => {
            setShowTaskModal(false)
            setSelectedSlot(null)
            setEditingTask(null)
          }}
          onSave={editingTask ? updateTask : createTask}
          onDelete={editingTask ? deleteTask : null}
          task={editingTask}
          selectedSlot={selectedSlot}
          teamMembers={teamMembers}
          isLoading={isLoading}
        />
      )}

      {/* Conflict Dialog */}
      {showConflictDialog && conflictData && (
        <ConflictDialog
          conflicts={conflictData.conflicts}
          onConfirm={handleConflictConfirm}
          onCancel={handleConflictCancel}
        />
      )}
    </div>
  )
}

// Task Modal Component
function TaskModal({ isOpen, onClose, onSave, onDelete, task, selectedSlot, teamMembers, isLoading }) {
  const [formData, setFormData] = useState({
    title: '',
    description: '',
    assignedMembers: [],
    date: '',
    startTime: '09:00',
    endTime: '10:00',
    color: '#3b82f6'
  })

  // Initialize form data when modal opens
  React.useEffect(() => {
    if (task) {
      // Editing existing task
      setFormData({
        title: task.title,
        description: task.description,
        assignedMembers: task.assignedMembers,
        date: new Date(task.date).toISOString().split('T')[0],
        startTime: task.startTime,
        endTime: task.endTime,
        color: task.color
      })
    } else if (selectedSlot) {
      // Creating new task
      setFormData({
        title: '',
        description: '',
        assignedMembers: [selectedSlot.memberId],
        date: new Date(selectedSlot.date).toISOString().split('T')[0],
        startTime: selectedSlot.time,
        endTime: selectedSlot.time === '18:00' ? '18:30' : TIME_SLOTS[TIME_SLOTS.indexOf(selectedSlot.time) + 1] || '18:00',
        color: '#3b82f6'
      })
    }
  }, [task, selectedSlot])

  // Keyboard support
  React.useEffect(() => {
    const handleKeyDown = (e) => {
      if (e.key === 'Escape') {
        onClose()
      }
    }

    if (isOpen) {
      document.addEventListener('keydown', handleKeyDown)
      return () => document.removeEventListener('keydown', handleKeyDown)
    }
  }, [isOpen, onClose])

  const handleSubmit = (e) => {
    e.preventDefault()
    if (task) {
      onSave(task.id, formData)
    } else {
      onSave(formData)
    }
  }

  const handleMemberToggle = (memberId) => {
    setFormData(prev => ({
      ...prev,
      assignedMembers: prev.assignedMembers.includes(memberId)
        ? prev.assignedMembers.filter(id => id !== memberId)
        : [...prev.assignedMembers, memberId]
    }))
  }

  if (!isOpen) return null

  return (
    <div className="modal-overlay" onClick={onClose} data-testid="task-modal-overlay">
      <div className="modal-content" onClick={(e) => e.stopPropagation()} data-testid="task-modal-content">
        <div className="modal-header">
          <h2 data-testid="task-modal-title">{task ? BULGARIAN_LOCALE.ui.editTask : BULGARIAN_LOCALE.ui.createTask}</h2>
          <button className="close-btn" onClick={onClose} data-testid="task-modal-close">×</button>
        </div>

        <form onSubmit={handleSubmit} className="task-form">
          <div className="form-group">
            <label>{BULGARIAN_LOCALE.ui.title}</label>
            <input
              type="text"
              value={formData.title}
              onChange={(e) => setFormData(prev => ({ ...prev, title: e.target.value }))}
              required
              data-testid="task-title-input"
            />
          </div>

          <div className="form-group">
            <label>{BULGARIAN_LOCALE.ui.description}</label>
            <textarea
              value={formData.description}
              onChange={(e) => setFormData(prev => ({ ...prev, description: e.target.value }))}
              rows="3"
              data-testid="task-description-input"
            />
          </div>

          <div className="form-group">
            <label>{BULGARIAN_LOCALE.ui.date}</label>
            <input
              type="date"
              value={formData.date}
              onChange={(e) => setFormData(prev => ({ ...prev, date: e.target.value }))}
              required
              data-testid="task-date-input"
            />
          </div>

          <div className="form-row">
            <div className="form-group">
              <label>{BULGARIAN_LOCALE.ui.startTime}</label>
              <select
                value={formData.startTime}
                onChange={(e) => setFormData(prev => ({ ...prev, startTime: e.target.value }))}
                required
                data-testid="task-start-time-select"
              >
                {TIME_SLOTS.map(time => (
                  <option key={time} value={time}>{time}</option>
                ))}
              </select>
            </div>

            <div className="form-group">
              <label>{BULGARIAN_LOCALE.ui.endTime}</label>
              <select
                value={formData.endTime}
                onChange={(e) => setFormData(prev => ({ ...prev, endTime: e.target.value }))}
                required
                data-testid="task-end-time-select"
              >
                {TIME_SLOTS.map(time => (
                  <option key={time} value={time}>{time}</option>
                ))}
              </select>
            </div>
          </div>

          <div className="form-group">
            <label>{BULGARIAN_LOCALE.ui.selectColor}</label>
            <div className="color-palette">
              {TASK_COLORS.map(color => (
                <button
                  key={color}
                  type="button"
                  className={`color-option ${formData.color === color ? 'selected' : ''}`}
                  style={{ backgroundColor: color }}
                  onClick={() => setFormData(prev => ({ ...prev, color }))}
                  title={color}
                />
              ))}
            </div>
          </div>

          <div className="form-group">
            <label>{BULGARIAN_LOCALE.ui.assignToTeamMembers}</label>
            <div className="member-dropdown">
              {teamMembers.map(member => (
                <label key={member.id} className="member-checkbox-item">
                  <input
                    type="checkbox"
                    checked={formData.assignedMembers.includes(member.id)}
                    onChange={() => {
                      setFormData(prev => ({
                        ...prev,
                        assignedMembers: prev.assignedMembers.includes(member.id)
                          ? prev.assignedMembers.filter(id => id !== member.id)
                          : [...prev.assignedMembers, member.id]
                      }))
                    }}
                  />
                  <div
                    className="member-avatar-small"
                    style={{ backgroundColor: member.color }}
                  >
                    {member.initials}
                  </div>
                  <span>{member.name}</span>
                </label>
              ))}
            </div>
          </div>

          <div className="modal-actions">
            {task && onDelete && (
              <button
                type="button"
                className="delete-btn"
                onClick={() => onDelete(task.id)}
                data-testid="task-delete-button"
              >
                {BULGARIAN_LOCALE.ui.delete}
              </button>
            )}
            <button type="button" className="cancel-btn" onClick={onClose} data-testid="task-cancel-button">
              {BULGARIAN_LOCALE.ui.cancel}
            </button>
            <button type="submit" className="save-btn" data-testid="task-submit-button" disabled={isLoading}>
              {isLoading ? 'Запазване...' : (task ? BULGARIAN_LOCALE.ui.update : BULGARIAN_LOCALE.ui.createTask)}
            </button>
          </div>
        </form>
      </div>
    </div>
  )
}

// Month View Component
function MonthView({ currentDate, tasks, teamMembers, onSlotClick, onTaskClick, getTasksForSlot, onDayClick }) {
  const getDaysInMonth = (date) => {
    const year = date.getFullYear()
    const month = date.getMonth()
    const firstDay = new Date(year, month, 1)
    const lastDay = new Date(year, month + 1, 0)
    const daysInMonth = lastDay.getDate()
    const startingDayOfWeek = firstDay.getDay()

    const days = []

    // Add empty cells for days before the first day of the month
    // Convert Sunday (0) to 6, Monday (1) to 0, etc. for Monday-first week
    const mondayBasedStartDay = startingDayOfWeek === 0 ? 6 : startingDayOfWeek - 1
    for (let i = 0; i < mondayBasedStartDay; i++) {
      days.push(null)
    }

    // Add all days of the month
    for (let day = 1; day <= daysInMonth; day++) {
      days.push(new Date(year, month, day))
    }

    return days
  }

  const getTasksForDay = (date) => {
    if (!date) return []
    return tasks.filter(task => {
      const taskDate = new Date(task.date).toDateString()
      const dayDate = date.toDateString()
      return taskDate === dayDate
    })
  }

  const days = getDaysInMonth(currentDate)
  // Bulgarian week days starting from Monday
  const weekDays = BULGARIAN_LOCALE.days.short

  return (
    <div className="month-view">
      <div className="month-grid">
        {/* Week day headers */}
        {weekDays.map(day => (
          <div key={day} className="month-header-cell">
            {day}
          </div>
        ))}

        {/* Calendar days */}
        {days.map((date, index) => {
          const dayTasks = date ? getTasksForDay(date) : []
          const isNonWorking = date ? isNonWorkingDay(date) : false

          return (
            <div
              key={index}
              className={`month-cell ${!date ? 'empty' : ''} ${date && date.toDateString() === new Date().toDateString() ? 'today' : ''} ${isNonWorking ? 'non-working-day' : ''}`}
              onClick={() => {
                if (date) {
                  if (onDayClick) {
                    onDayClick(date)
                  } else {
                    onSlotClick(date, '09:00', 1)
                  }
                }
              }}
            >
              {date && (
                <>
                  <div className="month-day-number">
                    {date.getDate()}
                  </div>
                  <div className="month-tasks">
                    {dayTasks.slice(0, 3).map(task => (
                      <div
                        key={task.id}
                        className="month-task"
                        style={{ backgroundColor: task.color }}
                        onClick={(e) => {
                          e.stopPropagation()
                          onTaskClick(task)
                        }}
                      >
                        {task.title}
                      </div>
                    ))}
                    {dayTasks.length > 3 && (
                      <div className="month-task-more">
                        +{dayTasks.length - 3} more
                      </div>
                    )}
                  </div>
                </>
              )}
            </div>
          )
        })}
      </div>
    </div>
  )
}

// Year View Component
function YearView({ currentDate, tasks, teamMembers, onMonthClick }) {
  const getMonthsInYear = (date) => {
    const year = date.getFullYear()
    const months = []

    for (let month = 0; month < 12; month++) {
      months.push(new Date(year, month, 1))
    }

    return months
  }

  const getTasksForMonth = (monthDate) => {
    return tasks.filter(task => {
      const taskDate = new Date(task.date)
      return taskDate.getFullYear() === monthDate.getFullYear() &&
             taskDate.getMonth() === monthDate.getMonth()
    })
  }

  const months = getMonthsInYear(currentDate)

  return (
    <div className="year-view">
      <div className="year-grid">
        {months.map(month => {
          const monthTasks = getTasksForMonth(month)

          return (
            <div
              key={month.getMonth()}
              className="year-month"
              onClick={() => onMonthClick && onMonthClick(month.getFullYear(), month.getMonth())}
              style={{ cursor: onMonthClick ? 'pointer' : 'default' }}
            >
              <div className="year-month-header">
                {month.toLocaleDateString('en-US', { month: 'long' })}
              </div>
              <div className="year-month-content">
                <div className="year-task-count">
                  {monthTasks.length} tasks
                </div>
                <div className="year-team-summary">
                  {teamMembers.map(member => {
                    const memberTasks = monthTasks.filter(task =>
                      task.assignedMembers.includes(member.id)
                    )
                    return memberTasks.length > 0 ? (
                      <div
                        key={member.id}
                        className="year-member-indicator"
                        style={{ backgroundColor: member.color }}
                        title={`${member.name}: ${memberTasks.length} tasks`}
                      >
                        {member.initials}
                      </div>
                    ) : null
                  })}
                </div>
              </div>
            </div>
          )
        })}
      </div>
    </div>
  )
}

// Conflict Dialog Component
function ConflictDialog({ conflicts, onConfirm, onCancel }) {
  const formatTime = (timestamp) => {
    const date = new Date(timestamp)
    return date.toLocaleTimeString('en-US', {
      hour: '2-digit',
      minute: '2-digit',
      hour12: false
    })
  }

  return (
    <div className="modal-overlay">
      <div className="modal-content conflict-dialog">
        <div className="modal-header">
          <h2>⚠️ Schedule Conflict Detected</h2>
        </div>

        <div className="modal-body">
          <p>The following team members have conflicting schedules:</p>

          <div className="conflicts-list">
            {conflicts.map((conflict, index) => (
              <div key={index} className="conflict-item">
                <div className="conflict-member">
                  <span
                    className="member-initial"
                    style={{ backgroundColor: conflict.member.color }}
                  >
                    {conflict.member.initials}
                  </span>
                  <span className="member-name">{conflict.member.name}</span>
                </div>

                <div className="conflict-details">
                  <div className="existing-task">
                    <strong>Existing:</strong> {conflict.existingTask.title}
                    <br />
                    <span className="time-range">
                      {conflict.existingTask.startTime} - {conflict.existingTask.endTime}
                    </span>
                  </div>

                  <div className="overlap-time">
                    <strong>Overlap:</strong> {formatTime(conflict.timeOverlap.start)} - {formatTime(conflict.timeOverlap.end)}
                  </div>
                </div>
              </div>
            ))}
          </div>

          <p className="conflict-question">
            Do you want to proceed anyway? This will create overlapping schedules.
          </p>
        </div>

        <div className="modal-footer">
          <button
            className="btn btn-secondary"
            onClick={onCancel}
          >
            Cancel
          </button>
          <button
            className="btn btn-danger"
            onClick={onConfirm}
          >
            Proceed Anyway
          </button>
        </div>
      </div>
    </div>
  )
}

export default App
