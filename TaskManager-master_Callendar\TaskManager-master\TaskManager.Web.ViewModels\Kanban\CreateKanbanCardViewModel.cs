namespace TaskManager.Web.ViewModels.Kanban
{
    using System.ComponentModel.DataAnnotations;
    using static TaskManager.Common.EntityValidationConstants.KanbanCard;

    /// <summary>
    /// View model for creating a new Kanban card
    /// </summary>
    public class CreateKanbanCardViewModel
    {
        [Required(ErrorMessage = "Заглавието е задължително")]
        [StringLength(TitleMaxLength, MinimumLength = TitleMinLength, 
            ErrorMessage = "Заглавието трябва да бъде между {2} и {1} символа")]
        [Display(Name = "Заглавие")]
        public string Title { get; set; } = null!;

        [StringLength(DescriptionMaxLength,
            ErrorMessage = "Описанието не може да бъде повече от {1} символа")]
        [Display(Name = "Описание")]
        public string? Description { get; set; }

        [Required(ErrorMessage = "Колоната е задължителна")]
        [Display(Name = "Колона")]
        public string ColumnId { get; set; } = null!;

        [Display(Name = "Цвят")]
        [StringLength(ColorMaxLength, MinimumLength = ColorMinLength)]
        public string? Color { get; set; }

        [Display(Name = "Етикети")]
        [StringLength(LabelsMaxLength, MinimumLength = LabelsMinLength)]
        public string? Labels { get; set; }

        [Display(Name = "Краен срок")]
        [DataType(DataType.DateTime)]
        public DateTime? DueDate { get; set; }

        [Display(Name = "Възложено на")]
        public string? AssignedToId { get; set; }

        // Hidden field for GeoTask connection
        public string? GeoTaskId { get; set; }
    }
}
