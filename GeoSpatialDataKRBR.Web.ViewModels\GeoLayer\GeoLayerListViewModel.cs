namespace GeoSpatialDataKRBR.Web.ViewModels.GeoLayer
{
    public class GeoLayerListViewModel
    {
        public Guid Id { get; set; }
        public string Name { get; set; } = null!;
        public string? Description { get; set; }
        public string LayerName { get; set; } = null!;
        public string Workspace { get; set; } = null!;
        public string LayerType { get; set; } = null!;
        public bool IsVisible { get; set; }
        public bool IsBaseLayer { get; set; }
        public int DisplayOrder { get; set; }
        public double? Opacity { get; set; }
        public string? GeoServerConfigurationName { get; set; }
        public DateTime CreatedOn { get; set; }
    }
}
