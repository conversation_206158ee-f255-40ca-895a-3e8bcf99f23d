namespace TaskManager.Data.Models
{
    using System.ComponentModel.DataAnnotations;
    using System.ComponentModel.DataAnnotations.Schema;

    public class CalendarTaskWorker
    {
        [Required]
        public Guid CalendarTaskId { get; set; }

        [Required]
        [ForeignKey(nameof(CalendarTaskId))]
        public CalendarTask CalendarTask { get; set; } = null!;

        [Required]
        public Guid WorkerId { get; set; }

        [Required]
        [ForeignKey(nameof(WorkerId))]
        public Worker Worker { get; set; } = null!;

        [Required]
        public DateTime AssignedDate { get; set; } = DateTime.UtcNow;
    }
}
