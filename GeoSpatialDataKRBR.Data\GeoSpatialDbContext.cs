namespace GeoSpatialDataKRBR.Data
{
    using Microsoft.AspNetCore.Identity;
    using Microsoft.AspNetCore.Identity.EntityFrameworkCore;
    using Microsoft.EntityFrameworkCore;
    using GeoSpatialDataKRBR.Data.Models;
    using System.Reflection;

    public class GeoSpatialDbContext : IdentityDbContext<ApplicationUser, IdentityRole<Guid>, Guid>
    {
        public GeoSpatialDbContext(DbContextOptions<GeoSpatialDbContext> options)
            : base(options)
        {
        }

        public DbSet<GeoLayer> GeoLayers { get; set; } = null!;
        public DbSet<GeoServerConfiguration> GeoServerConfigurations { get; set; } = null!;
        public DbSet<GeoLayerGroup> GeoLayerGroups { get; set; } = null!;
        public DbSet<GeoLayerGroupLayer> GeoLayerGroupLayers { get; set; } = null!;
        public DbSet<UserLayerPreference> UserLayerPreferences { get; set; } = null!;

        protected override void OnModelCreating(ModelBuilder builder)
        {
            // Apply all configurations from the current assembly
            builder.ApplyConfigurationsFromAssembly(Assembly.GetExecutingAssembly());

            base.OnModelCreating(builder);
        }
    }
}
