﻿@model IEnumerable<FrontDescriptionTypeViewModel>
@{
    ViewData["Title"] = "Начало";
}

<div class="modern-container">
    <!-- Hero Section -->
    <div class="hero-section">
        <div class="hero-content">
            <h1 class="hero-title">
                <i class="fas fa-mountain"></i>
                Добре дошли в Geodesy
            </h1>
            <p class="hero-subtitle">
                Професионални геодезически услуги с модерни технологии и експертен екип
            </p>
            <div class="hero-stats">
                <div class="hero-stat">
                    <i class="fas fa-award"></i>
                    <span>15+ години опит</span>
                </div>
                <div class="hero-stat">
                    <i class="fas fa-users"></i>
                    <span>500+ доволни клиенти</span>
                </div>
                <div class="hero-stat">
                    <i class="fas fa-map-marked-alt"></i>
                    <span>1000+ проекта</span>
                </div>
            </div>
        </div>
    </div>

    <!-- Services Section -->
    <div class="services-section">
        <div class="section-header">
            <h2 class="section-title">
                <i class="fas fa-concierge-bell"></i>
                Нашите услуги
            </h2>
            <p class="section-subtitle">
                Предлагаме широк спектър от геодезически услуги с гарантирано качество
            </p>
        </div>

        <div class="services-grid">
            @foreach (FrontDescriptionTypeViewModel viewModel in Model)
            {
                <div class="service-card">
                    <div class="service-card-header">
                        <div class="service-icon">
                            <i class="fas fa-drafting-compass"></i>
                        </div>
                        <h3 class="service-title">@viewModel.Title</h3>
                    </div>
                    <div class="service-card-body">
                        <div class="service-details">
                            <div class="service-detail">
                                <i class="fas fa-tag"></i>
                                <span class="detail-label">Цена:</span>
                                <span class="detail-value price">@($"{viewModel.Price:F2}") лв.</span>
                            </div>
                            <div class="service-detail">
                                <i class="fas fa-clock"></i>
                                <span class="detail-label">Срок:</span>
                                <span class="detail-value">@viewModel.TermDays работни дни</span>
                            </div>
                        </div>
                        <div class="service-actions">
                            <a class="modern-btn modern-btn-primary" asp-controller="Home" asp-action="Details" asp-route-id="@viewModel.Id">
                                <i class="fas fa-info-circle"></i>
                                Повече информация
                            </a>
                        </div>
                    </div>
                </div>
            }
        </div>
    </div>

    <!-- Call to Action Section -->
    <div class="cta-section">
        <div class="cta-content">
            <h2 class="cta-title">
                <i class="fas fa-rocket"></i>
                Готови за вашия проект?
            </h2>
            <p class="cta-subtitle">
                Свържете се с нас за безплатна консултация и оферта
            </p>
            <div class="cta-actions">
                <a class="modern-btn modern-btn-warning" asp-controller="Request" asp-action="CreateRequest">
                    <i class="fas fa-paper-plane"></i>
                    Изпратете заявка
                </a>
                <a class="modern-btn modern-btn-secondary" href="tel:+359888123456">
                    <i class="fas fa-phone"></i>
                    Обадете се
                </a>
            </div>
        </div>
    </div>
</div>