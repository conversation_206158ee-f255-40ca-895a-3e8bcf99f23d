﻿@model IEnumerable<AllWorkersViewModel>;

@{
    ViewData["Title"] = "Всички работници";
}

<div class="admin-page-container">
    <div class="admin-page-header">
        <div class="page-header-content">
            <a href="@Url.Action("AdminPanel", "Admin")" class="back-btn">
                <i class="fas fa-arrow-left"></i>
                Назад към панела
            </a>
            <h1 class="page-title">
                <i class="fas fa-hard-hat"></i>
                Всички работници
            </h1>
            <p class="page-subtitle">Управление и преглед на всички работници в системата</p>
        </div>
    </div>

    <div class="admin-content">
        <div class="users-table-container">
            <div class="table-header">
                <h3>
                    <i class="fas fa-users-cog"></i>
                    Списък с работници (@Model.Count())
                </h3>
            </div>

            <div class="modern-table-wrapper">
                <table class="modern-table">
                    <thead>
                        <tr>
                            <th>
                                <i class="fas fa-user"></i>
                                Име
                            </th>
                            <th>
                                <i class="fas fa-phone"></i>
                                Телефон
                            </th>
                            <th>
                                <i class="fas fa-envelope"></i>
                                Имейл
                            </th>
                            <th>
                                <i class="fas fa-briefcase"></i>
                                Позиция
                            </th>
                            <th>
                                <i class="fas fa-cogs"></i>
                                Действия
                            </th>
                        </tr>
                    </thead>
                    <tbody>
                        @foreach (AllWorkersViewModel worker in Model)
                        {
                            <tr class="table-row">
                                <td class="user-name">
                                    <div class="user-avatar">
                                        <i class="fas fa-hard-hat"></i>
                                    </div>
                                    <span>@($"{worker.FirstName} {worker.LastName}")</span>
                                </td>
                                <td class="worker-phone">@worker.PhoneNumer</td>
                                <td class="user-email">@worker.Email</td>
                                <td class="worker-position">
                                    <span class="position-badge">@worker.Position</span>
                                </td>
                                <td class="user-actions">
                                    <a class="action-btn edit" asp-controller="Admin" asp-action="PersonalFile" asp-route-userId="@worker.UserId">
                                        <i class="fas fa-user-circle"></i>
                                        Профил
                                    </a>
                                    <a class="action-btn delete" asp-controller="Admin" asp-action="UsersEdit" asp-route-id="@worker.UserId">
                                        <i class="fas fa-user-times"></i>
                                        Управление
                                    </a>
                                </td>
                            </tr>
                        }
                    </tbody>
                </table>
            </div>
        </div>
    </div>
</div>
      

