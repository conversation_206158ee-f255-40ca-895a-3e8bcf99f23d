{"ConnectionStrings": {"DefaultConnection": "Server=(localdb)\\mssqllocaldb;Database=aspnet-TaskManager-********-ab3d-4d5d-9ee4-e0957111f6f5;Trusted_Connection=True;MultipleActiveResultSets=true"}, "Identity": {"SignIn": {"RequireConfirmedAccount": false}, "Password": {"RequireNonAlphanumeric": false, "RequireLowercase": false, "RequireUppercase": false, "RequiredLength": 4}}, "Logging": {"LogLevel": {"Default": "Information", "Microsoft.AspNetCore": "Warning"}}, "AllowedHosts": "*"}