using Microsoft.EntityFrameworkCore;
using TaskManager.Data;
using TaskManager.Data.Models;
using TaskManager.Services.Data;
using TaskManager.Web.ViewModels.Calendar;
using Xunit;

namespace TaskManager.Tests
{
    public class GeoTaskCalendarIntegrationTests : IDisposable
    {
        private readonly TaskManagerDbContext context;
        private readonly CalendarService calendarService;

        public GeoTaskCalendarIntegrationTests()
        {
            var options = new DbContextOptionsBuilder<TaskManagerDbContext>()
                .UseInMemoryDatabase(databaseName: Guid.NewGuid().ToString())
                .Options;

            context = new TaskManagerDbContext(options);
            calendarService = new CalendarService(context);

            // Seed test data
            SeedTestData();
        }

        private void SeedTestData()
        {
            // Create test user first
            var user1 = new ApplicationUser
            {
                Id = Guid.Parse("11111111-1111-1111-1111-111111111111"),
                FirstName = "Test",
                LastName = "Worker1",
                Email = "<EMAIL>",
                UserName = "<EMAIL>"
            };

            context.Users.Add(user1);

            // Add test worker with correct properties
            var worker1 = new Worker
            {
                Id = Guid.Parse("*************-3333-3333-************"),
                UserId = user1.Id,
                PhoneNumber = "0888123456",
                Position = "Геодезист",
                Color = "#3B82F6"
            };

            context.Workers.Add(worker1);

            // Add test client
            var client = new Client
            {
                Id = Guid.Parse("*************-9999-9999-************"),
                Name = "Test Client",
                Email = "<EMAIL>",
                PhoneNumber = "0888999666",
                CustomerRepresentative = "Test Rep"
            };

            context.Clients.Add(client);

            // Add test GeoTasks
            var geoTask1 = new GeoTask
            {
                Id = Guid.Parse("aaaaaaaa-aaaa-aaaa-aaaa-aaaaaaaaaaaa"),
                ProjectNumber = 1,
                Adrress = "Test Address 1",
                CreateDate = DateTime.Now,
                EndDate = DateTime.Now.AddDays(30),
                Price = 1000,
                quantity = 1,
                IdKKKR = "68134.905.aa",
                Note = "Test GeoTask 1 Note",
                WorkerId = worker1.Id,
                CheckerId = worker1.Id,
                ClientId = client.Id,
                StatusId = 1,
                TypeId = 1
            };

            var geoTask2 = new GeoTask
            {
                Id = Guid.Parse("bbbbbbbb-bbbb-bbbb-bbbb-bbbbbbbbbbbb"),
                ProjectNumber = 2,
                Adrress = "Test Address 2",
                CreateDate = DateTime.Now,
                EndDate = DateTime.Now.AddDays(30),
                Price = 1500,
                quantity = 1,
                IdKKKR = "68134.905.bb",
                Note = "Test GeoTask 2 Note",
                WorkerId = worker1.Id,
                CheckerId = worker1.Id,
                ClientId = client.Id,
                StatusId = 1,
                TypeId = 1
            };

            context.GeoTasks.AddRange(geoTask1, geoTask2);
            context.SaveChanges();
        }

        [Fact]
        public async Task CreateCalendarTaskFromGeoTask_ShouldLinkGeoTaskToCalendar()
        {
            // Arrange
            var createModel = new CreateCalendarTaskViewModel
            {
                Title = "Calendar Task for PRJ001",
                Description = "Task created from GeoTask",
                Date = DateTime.Today.AddDays(1),
                StartTime = "09:00",
                EndTime = "10:00",
                Color = "#3b82f6",
                GeoTaskId = "aaaaaaaa-aaaa-aaaa-aaaa-aaaaaaaaaaaa",
                AssignedMemberIds = new List<string> { "worker1" }
            };

            // Act
            var calendarTask = await calendarService.CreateTaskAsync(createModel);

            // Assert
            Assert.NotNull(calendarTask);
            Assert.Equal("geotask1", calendarTask.GeoTaskId);

            // Verify the link exists
            var isLinked = await calendarService.IsGeoTaskLinkedToCalendarAsync("geotask1");
            Assert.True(isLinked);

            // Verify we can get the calendar task ID from GeoTask ID
            var calendarTaskId = await calendarService.GetCalendarTaskIdByGeoTaskIdAsync("geotask1");
            Assert.Equal(calendarTask.Id, calendarTaskId);
        }

        [Fact]
        public async Task CreateCalendarTaskFromGeoTask_ShouldIncludeProjectNumberInTitle()
        {
            // Arrange
            var geoTask = await context.GeoTasks.FirstAsync(g => g.Id == "geotask1");
            var createModel = new CreateCalendarTaskViewModel
            {
                Title = $"Task for {geoTask.ProjectNumber}",
                Description = "Task created from GeoTask",
                Date = DateTime.Today.AddDays(1),
                StartTime = "09:00",
                EndTime = "10:00",
                Color = "#3b82f6",
                GeoTaskId = "aaaaaaaa-aaaa-aaaa-aaaa-aaaaaaaaaaaa",
                AssignedMemberIds = new List<string> { "*************-3333-3333-************" }
            };

            // Act
            var calendarTask = await calendarService.CreateTaskAsync(createModel);

            // Assert
            Assert.Contains("PRJ001", calendarTask.Title);
        }

        [Fact]
        public async Task GeoTaskShouldNotBeLinkedToMultipleCalendarTasks()
        {
            // Arrange
            var createModel1 = new CreateCalendarTaskViewModel
            {
                Title = "First Calendar Task",
                Description = "First task",
                Date = DateTime.Today.AddDays(1),
                StartTime = "09:00",
                EndTime = "10:00",
                Color = "#3b82f6",
                GeoTaskId = "aaaaaaaa-aaaa-aaaa-aaaa-aaaaaaaaaaaa",
                AssignedMemberIds = new List<string> { "*************-3333-3333-************" }
            };

            var createModel2 = new CreateCalendarTaskViewModel
            {
                Title = "Second Calendar Task",
                Description = "Second task",
                Date = DateTime.Today.AddDays(2),
                StartTime = "11:00",
                EndTime = "12:00",
                Color = "#ef4444",
                GeoTaskId = "aaaaaaaa-aaaa-aaaa-aaaa-aaaaaaaaaaaa", // Same GeoTask
                AssignedMemberIds = new List<string> { "*************-3333-3333-************" }
            };

            // Act
            var firstTask = await calendarService.CreateTaskAsync(createModel1);
            
            // This should either fail or replace the first link
            var exception = await Record.ExceptionAsync(async () =>
            {
                var secondTask = await calendarService.CreateTaskAsync(createModel2);
            });

            // Assert
            // The behavior depends on implementation - either it should throw an exception
            // or it should allow only one link per GeoTask
            var isLinked = await calendarService.IsGeoTaskLinkedToCalendarAsync("geotask1");
            Assert.True(isLinked);

            var calendarTaskId = await calendarService.GetCalendarTaskIdByGeoTaskIdAsync("geotask1");
            Assert.NotNull(calendarTaskId);
        }

        [Fact]
        public async Task RemoveCalendarTask_ShouldUnlinkFromGeoTask()
        {
            // Arrange
            var createModel = new CreateCalendarTaskViewModel
            {
                Title = "Task to Remove",
                Description = "This task will be removed",
                Date = DateTime.Today.AddDays(1),
                StartTime = "09:00",
                EndTime = "10:00",
                Color = "#3b82f6",
                GeoTaskId = "aaaaaaaa-aaaa-aaaa-aaaa-aaaaaaaaaaaa",
                AssignedMemberIds = new List<string> { "*************-3333-3333-************" }
            };

            var calendarTask = await calendarService.CreateTaskAsync(createModel);

            // Verify link exists
            var isLinkedBefore = await calendarService.IsGeoTaskLinkedToCalendarAsync("geotask1");
            Assert.True(isLinkedBefore);

            // Act
            var removed = await calendarService.RemoveTaskAsync(calendarTask.Id);

            // Assert
            Assert.True(removed);

            // Verify link is removed
            var isLinkedAfter = await calendarService.IsGeoTaskLinkedToCalendarAsync("geotask1");
            Assert.False(isLinkedAfter);

            var calendarTaskId = await calendarService.GetCalendarTaskIdByGeoTaskIdAsync("geotask1");
            Assert.Null(calendarTaskId);
        }

        [Fact]
        public async Task GetTasksAsync_ShouldReturnTasksWithGeoTaskInformation()
        {
            // Arrange
            var createModel = new CreateCalendarTaskViewModel
            {
                Title = "GeoTask Calendar Task",
                Description = "Task linked to GeoTask",
                Date = DateTime.Today.AddDays(1),
                StartTime = "09:00",
                EndTime = "10:00",
                Color = "#3b82f6",
                GeoTaskId = "aaaaaaaa-aaaa-aaaa-aaaa-aaaaaaaaaaaa",
                AssignedMemberIds = new List<string> { "*************-3333-3333-************" }
            };

            await calendarService.CreateTaskAsync(createModel);

            // Act
            var tasks = await calendarService.GetTasksAsync();

            // Assert
            var geoTaskLinkedTask = tasks.FirstOrDefault(t => t.GeoTaskId == "aaaaaaaa-aaaa-aaaa-aaaa-aaaaaaaaaaaa");
            Assert.NotNull(geoTaskLinkedTask);
            Assert.Equal("aaaaaaaa-aaaa-aaaa-aaaa-aaaaaaaaaaaa", geoTaskLinkedTask.GeoTaskId);
        }

        [Fact]
        public async Task MultipleGeoTasks_ShouldHaveIndependentCalendarLinks()
        {
            // Arrange
            var createModel1 = new CreateCalendarTaskViewModel
            {
                Title = "Task for GeoTask 1",
                Description = "First task",
                Date = DateTime.Today.AddDays(1),
                StartTime = "09:00",
                EndTime = "10:00",
                Color = "#3b82f6",
                GeoTaskId = "aaaaaaaa-aaaa-aaaa-aaaa-aaaaaaaaaaaa",
                AssignedMemberIds = new List<string> { "*************-3333-3333-************" }
            };

            var createModel2 = new CreateCalendarTaskViewModel
            {
                Title = "Task for GeoTask 2",
                Description = "Second task",
                Date = DateTime.Today.AddDays(2),
                StartTime = "11:00",
                EndTime = "12:00",
                Color = "#ef4444",
                GeoTaskId = "bbbbbbbb-bbbb-bbbb-bbbb-bbbbbbbbbbbb",
                AssignedMemberIds = new List<string> { "*************-3333-3333-************" }
            };

            // Act
            var task1 = await calendarService.CreateTaskAsync(createModel1);
            var task2 = await calendarService.CreateTaskAsync(createModel2);

            // Assert
            var isLinked1 = await calendarService.IsGeoTaskLinkedToCalendarAsync("geotask1");
            var isLinked2 = await calendarService.IsGeoTaskLinkedToCalendarAsync("geotask2");

            Assert.True(isLinked1);
            Assert.True(isLinked2);

            var calendarTaskId1 = await calendarService.GetCalendarTaskIdByGeoTaskIdAsync("geotask1");
            var calendarTaskId2 = await calendarService.GetCalendarTaskIdByGeoTaskIdAsync("geotask2");

            Assert.Equal(task1.Id, calendarTaskId1);
            Assert.Equal(task2.Id, calendarTaskId2);
            Assert.NotEqual(calendarTaskId1, calendarTaskId2);
        }

        public void Dispose()
        {
            context.Dispose();
        }
    }
}
