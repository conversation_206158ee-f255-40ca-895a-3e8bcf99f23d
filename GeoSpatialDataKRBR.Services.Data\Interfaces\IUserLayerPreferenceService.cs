namespace GeoSpatialDataKRBR.Services.Data.Interfaces
{
    using GeoSpatialDataKRBR.Data.Models;

    public interface IUserLayerPreferenceService
    {
        Task<IEnumerable<UserLayerPreference>> GetUserPreferencesAsync(Guid userId);
        Task<UserLayerPreference?> GetUserLayerPreferenceAsync(Guid userId, Guid layerId);
        Task<bool> SaveUserPreferenceAsync(UserLayerPreference preference);
        Task<bool> UpdateUserPreferenceAsync(UserLayerPreference preference);
        Task<bool> DeleteUserPreferenceAsync(Guid userId, Guid layerId);
        Task<bool> ToggleLayerVisibilityAsync(Guid userId, Guid layerId);
        Task<bool> UpdateLayerOpacityAsync(Guid userId, Guid layerId, double opacity);
        Task<bool> UpdateLayerOrderAsync(Guid userId, Guid layerId, int displayOrder);
        Task<bool> ResetUserPreferencesAsync(Guid userId);
    }
}
