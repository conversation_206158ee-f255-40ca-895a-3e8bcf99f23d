/* Reset and base styles */
* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

/* Ensure consistent box model for calendar elements */
.time-header,
.time-row,
.day-header,
.unified-slot,
.time-label {
  box-sizing: border-box;
}

/* Ensure perfect alignment between header and content rows */
.time-header,
.time-row {
  display: flex;
  flex-wrap: nowrap;
  align-items: stretch;
  width: 100%;
}

/* Ensure day headers and time slots have identical flex behavior */
.day-header,
.unified-slot {
  flex: 1 1 0%; /* Allow grow and shrink with 0% basis */
  min-width: 141px; /* Reduced: 240px ÷ 1.7 = 141px */
  max-width: none; /* No maximum width constraint */
  box-sizing: border-box;
  border-right: 1px solid #e2e8f0;
}

.day-header:last-child,
.unified-slot:last-child {
  border-right: none;
}

body {
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', sans-serif;
  background-color: #f8fafc;
  color: #1e293b;
  /* Mobile-first responsive design */
  -webkit-text-size-adjust: 100%;
  -webkit-tap-highlight-color: transparent;
  touch-action: manipulation;
  justify-content: center
}

.app {
  min-height: 100vh;
  display: flex;
  flex-direction: column;
}

/* Header styles */
.app-header {
  background: white;
  border-bottom: 1px solid #e2e8f0;
  padding: 1rem 2rem;
  display: flex;
  align-items: center;
  justify-content: space-between;
  flex-wrap: wrap;
  gap: 1rem;
  box-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1);
}

.app-header h1 {
  color: #1e293b;
  font-size: 1.5rem;
  font-weight: 600;
}

/* View selector */
.view-selector {
  display: flex;
  gap: 0.25rem;
  background: #f1f5f9;
  padding: 0.25rem;
  border-radius: 0.5rem;
}

.view-btn {
  padding: 0.5rem 1rem;
  border: none;
  background: transparent;
  border-radius: 0.375rem;
  cursor: pointer;
  font-weight: 500;
  transition: all 0.2s;
  color: #64748b;
}

.view-btn:hover {
  background: #e2e8f0;
  color: #1e293b;
}

.view-btn.active {
  background: white;
  color: #3b82f6;
  box-shadow: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
}

/* Date navigation */
.date-navigation {
  display: flex;
  align-items: center;
  gap: 1rem;
}

.date-navigation button {
  width: 2.5rem;
  height: 2.5rem;
  border: 1px solid #d1d5db;
  background: white;
  border-radius: 0.375rem;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 1.125rem;
  transition: all 0.2s;
}

.date-navigation button:hover {
  background: #f9fafb;
  border-color: #9ca3af;
}

.today-btn {
  background: #3b82f6 !important;
  color: white !important;
  font-size: 0.875rem;
  padding: 0.5rem 1rem;
  margin-left: 1rem;
}

.today-btn:hover {
  background: #2563eb !important;
}

.current-date {
  font-weight: 600;
  color: #1e293b;
  min-width: 200px;
  text-align: center;
}

/* Calendar container */
.calendar-container {
  flex: 1;
  display: flex;
  background: white;
  margin: 1rem auto;
  border-radius: 0.5rem;
  box-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1);
  overflow: hidden;
  max-width: 2470px; /* Increased by 1.3x from 1900px */
  min-width: 1600px; /* Allow flexible sizing */
  width: calc(100% - 2rem); /* Responsive width with margins */
}

/* Team sidebar */
.team-sidebar {
  width: 280px;
  background: #f8fafc;
  border-right: 1px solid #e2e8f0;
  padding: 1.5rem;
  overflow-y: auto;
}

.team-sidebar h3 {
  margin-bottom: 1rem;
  color: #1e293b;
  font-size: 1.125rem;
  font-weight: 600;
}

.team-member {
  margin-bottom: 1rem;
}

.member-controls {
  display: flex;
  align-items: center;
  gap: 0.75rem;
  padding: 0.5rem;
  background: white;
  border-radius: 0.5rem;
  border: 1px solid #e2e8f0;
  transition: all 0.2s;
}

.member-controls:hover {
  border-color: #cbd5e1;
  box-shadow: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
}

/* Removed redundant checkbox styles - now using eye icons and clickable names */

.member-avatar {
  width: 2rem;
  height: 2rem;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-weight: 600;
  font-size: 0.75rem;
  flex-shrink: 0;
}

.member-name {
  flex: 1;
  font-weight: 500;
  font-size: 0.875rem;
}

.color-picker {
  width: 1.5rem;
  height: 1.5rem;
  border: none;
  border-radius: 0.25rem;
  cursor: pointer;
  padding: 0;
}

/* Calendar content */
.calendar-content {
  flex: 1;
  overflow: auto;
  display: flex;
  justify-content: center;
  align-items: flex-start;
  padding: 1rem;
  min-height: 600px; /* Consistent minimum height for all views */
  width: 100%;
  max-width: 1900px; /* Support larger views */
  margin: 0 auto; /* Center the content */
}

.calendar-layout {
  display: flex;
  height: 100%;
  width: 100%;
}

/* Team sidebar */
.team-sidebar {
  width: 300px;
  background: #f8fafc;
  border-right: 1px solid #e2e8f0;
  padding: 1rem;
  overflow-y: auto;
}

.team-sidebar h3 {
  margin: 0 0 1rem 0;
  font-size: 1rem;
  color: #1e293b;
  font-weight: 600;
}

.team-member-item {
  display: flex;
  align-items: center;
  gap: 0.75rem;
  padding: 0.5rem;
  border-radius: 0.375rem;
  cursor: pointer;
  transition: all 0.2s;
  margin-bottom: 0.5rem;
}

.team-member-item:hover {
  background: #e2e8f0;
}

.team-member-item.hidden {
  opacity: 0.5;
}

.member-initials {
  width: 32px;
  height: 32px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-weight: 600;
  font-size: 0.75rem;
}

.member-name {
  font-size: 0.875rem;
  color: #374151;
  font-weight: 500;
}

/* Time grid styles - standardized for all views */
.time-grid {
  display: flex;
  flex-direction: column;
  min-height: 480px; /* Increased to accommodate taller fields */
  height: 100%; /* Full height for all views */
  max-width: 2470px; /* Increased by 1.3x from 1900px */
  min-width: 0; /* Allow flexible sizing */
  width: 100%;
  margin: 0 auto;
  border: 1px solid #e2e8f0;
  border-radius: 8px;
  overflow: hidden;
  background: white;
  flex: 1; /* Take available space */
}

.time-header {
  display: flex;
  background: #f8fafc;
  border-bottom: 2px solid #e2e8f0;
  position: sticky;
  top: 0;
  z-index: 10;
  align-items: stretch; /* Ensure proper alignment */
  flex-wrap: nowrap; /* Prevent wrapping */
  width: 100%; /* Ensure full width */
  justify-content: flex-start; /* Align items to start */
}

.time-header .time-label {
  width: 104px; /* Increased by 1.3x from 80px */
  padding: 1rem 0.5rem;
  border-right: 1px solid #e2e8f0;
  font-weight: 600;
  font-size: 0.875rem;
  color: #64748b;
  display: flex;
  align-items: center;
  justify-content: center;
  text-align: center;
  box-sizing: border-box; /* Ensure consistent box model */
  flex-shrink: 0; /* Prevent shrinking */
}

.day-header {
  flex: 1 1 0%; /* Allow grow and shrink with 0% basis */
  min-width: 141px; /* Reduced: 240px ÷ 1.7 = 141px */
  max-width: none; /* No maximum width constraint */
  padding: 0.75rem 4px; /* Adjusted padding for smaller fields */
  text-align: center;
  font-weight: 600;
  color: #1e293b;
  border-right: 1px solid #e2e8f0;
  font-size: 0.8rem; /* Adjusted font size for smaller fields */
  line-height: 1.2;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  display: flex;
  align-items: center;
  justify-content: center;
  box-sizing: border-box; /* Ensure consistent box model */
}

.day-header:last-child {
  border-right: none;
}

.time-row {
  display: flex;
  border-bottom: 1px solid #f1f5f9;
  min-height: 42px; /* Match the unified-slot height */
  flex-wrap: nowrap; /* Prevent wrapping */
  width: 100%; /* Ensure full width */
  align-items: stretch; /* Match time-header alignment */
  justify-content: flex-start; /* Align items to start */
}

.time-row:hover {
  background: #fafbfc;
}

.time-row .time-label {
  width: 104px; /* Increased by 1.3x from 80px */
  padding: 0.6rem 0.5rem; /* Increased vertical padding */
  border-right: 1px solid #e2e8f0;
  font-size: 0.75rem;
  color: #64748b;
  display: flex;
  align-items: center; /* Center alignment for better appearance */
  justify-content: center;
  background: #f8fafc;
  box-sizing: border-box; /* Ensure consistent box model */
  flex-shrink: 0; /* Prevent shrinking */
}

.time-slot {
  flex: 1;
  border-right: 1px solid #e2e8f0;
  display: flex;
  flex-direction: column;
  position: relative;
}

.time-slot:last-child {
  border-right: none;
}

.member-slot {
  flex: 1;
  min-height: 70px;
  border-bottom: 1px solid #f1f5f9;
  position: relative;
  cursor: pointer;
  transition: background-color 0.2s;
  padding: 2px;
  overflow: visible;
}

.member-slot:hover {
  background: rgba(59, 130, 246, 0.05);
}

.member-slot:last-child {
  border-bottom: none;
}

.member-slot.drag-over {
  background: rgba(59, 130, 246, 0.2);
  border: 2px dashed #3b82f6;
  animation: pulse 1s infinite;
}

/* Unified slot for simplified structure */
.unified-slot {
  min-height: 42px; /* Increased by 1.2x from 35px */
  flex: 1 1 0%; /* Allow grow and shrink with 0% basis - match day-header */
  min-width: 141px; /* Reduced: 240px ÷ 1.7 = 141px */
  max-width: none; /* No maximum width constraint */
  border-bottom: 1px solid #f1f5f9;
  border-right: 1px solid #e2e8f0; /* Match day-header border */
  position: relative;
  cursor: pointer;
  transition: background-color 0.2s;
  padding: 4px; /* Adjusted padding for smaller fields */
  overflow: visible;
  box-sizing: border-box; /* Ensure consistent box model */
  /* Touch-friendly interactions */
  -webkit-tap-highlight-color: rgba(59, 130, 246, 0.2);
  touch-action: manipulation;
}

.unified-slot:hover {
  background: rgba(59, 130, 246, 0.05);
}

.unified-slot:last-child {
  border-right: none; /* Match day-header behavior */
}

.unified-slot.drag-over {
  background: rgba(59, 130, 246, 0.2);
  border: 2px dashed #3b82f6;
  animation: pulse 1s infinite;
}

.tasks-container {
  position: relative;
  height: 100%;
  width: 100%;
}

@keyframes pulse {
  0% { background: rgba(59, 130, 246, 0.1); }
  50% { background: rgba(59, 130, 246, 0.3); }
  100% { background: rgba(59, 130, 246, 0.1); }
}

/* Task items */
.task-item {
  margin: 1px;
  padding: 4px 6px;
  border-radius: 4px;
  color: white;
  font-size: 0.7rem;
  line-height: 1.2;
  cursor: pointer;
  transition: all 0.2s;
  overflow: hidden;
  display: flex;
  flex-direction: column;
  min-height: 115px;
  position: relative;
  box-sizing: border-box;
}

.task-item:hover {
  opacity: 0.8;
  transform: translateY(-1px);
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
}

.task-item[draggable="true"] {
  cursor: grab;
}

.task-item[draggable="true"]:active {
  cursor: grabbing;
}

.task-item.dragging {
  opacity: 0.5;
  transform: rotate(5deg);
  z-index: 1000;
  box-shadow: 0 8px 16px rgba(0, 0, 0, 0.3);
}

.task-spanning {
  border: 1px solid rgba(255, 255, 255, 0.3);
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.task-time {
  font-size: 0.65rem;
  font-weight: 700;
  opacity: 1;
  margin-bottom: 2px;
  text-align: center;
  background: rgba(255, 255, 255, 0.2);
  padding: 1px 4px;
  border-radius: 2px;
}

.task-title {
  font-weight: 600;
  font-size: 0.75rem;
  margin-bottom: 2px;
  text-align: center;
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: center;
}

.task-description {
  font-size: 0.6rem;
  opacity: 0.9;
  text-align: center;
  margin-bottom: 2px;
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: center;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.task-members {
  display: flex;
  gap: 2px;
  justify-content: center;
  margin-top: auto;
  flex-wrap: wrap;
}

.member-initial {
  font-size: 0.6rem;
  font-weight: 700;
  color: white;
  padding: 2px 4px;
  border-radius: 2px;
  min-width: 16px;
  text-align: center;
  display: inline-block;
}

/* Resize handles */
.resize-handle {
  position: absolute;
  left: 0;
  right: 0;
  height: 4px;
  background: rgba(255, 255, 255, 0.3);
  cursor: ns-resize;
  opacity: 0;
  transition: opacity 0.2s;
  z-index: 10;
}

.task-item:hover .resize-handle {
  opacity: 1;
}

.resize-handle-top {
  top: 0;
  border-top-left-radius: 4px;
  border-top-right-radius: 4px;
}

.resize-handle-bottom {
  bottom: 0;
  border-bottom-left-radius: 4px;
  border-bottom-right-radius: 4px;
}

.resize-handle:hover {
  background: rgba(255, 255, 255, 0.6);
}

/* Drop zone indicator */
.drop-zone-indicator {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(59, 130, 246, 0.1);
  border: 2px dashed #3b82f6;
  border-radius: 4px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 0.7rem;
  color: #3b82f6;
  font-weight: 600;
  pointer-events: none;
  z-index: 5;
}

.drop-zone-indicator::before {
  content: "Drop task here";
}

/* Conflict Dialog */
.conflict-dialog {
  max-width: 600px;
}

.conflicts-list {
  margin: 1rem 0;
  max-height: 300px;
  overflow-y: auto;
}

.conflict-item {
  background: #fef2f2;
  border: 1px solid #fecaca;
  border-radius: 0.5rem;
  padding: 1rem;
  margin-bottom: 1rem;
}

.conflict-member {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  margin-bottom: 0.75rem;
  font-weight: 600;
}

.conflict-details {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 1rem;
  font-size: 0.875rem;
}

.existing-task {
  background: #fff1f2;
  padding: 0.5rem;
  border-radius: 0.25rem;
}

.overlap-time {
  background: #fee2e2;
  padding: 0.5rem;
  border-radius: 0.25rem;
  color: #dc2626;
  font-weight: 600;
}

.time-range {
  color: #6b7280;
  font-size: 0.8rem;
}

.conflict-question {
  margin-top: 1rem;
  font-weight: 600;
  color: #dc2626;
}

.btn-danger {
  background: #dc2626;
  color: white;
}

.btn-danger:hover {
  background: #b91c1c;
}

/* Modal styles */
.modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
}

.modal-content {
  background: white;
  border-radius: 0.75rem;
  box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
  width: 85%;
  max-width: 650px;
  max-height: 85vh;
  overflow-y: auto;
  padding: 1.5rem;
}

.modal-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 1.5rem;
  border-bottom: 1px solid #e2e8f0;
}

.modal-header h2 {
  margin: 0;
  color: #1e293b;
  font-size: 1.25rem;
  font-weight: 600;
}

.close-btn {
  background: none;
  border: none;
  font-size: 1.5rem;
  cursor: pointer;
  color: #64748b;
  padding: 0;
  width: 2rem;
  height: 2rem;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 0.25rem;
  transition: all 0.2s;
}

.close-btn:hover {
  background: #f1f5f9;
  color: #1e293b;
}

.task-form {
  padding: 0;
  display: flex;
  flex-direction: column;
  gap: 1rem;
}

.form-group {
  margin-bottom: 0.75rem;
}

/* Compact form layout for desktop */
.form-row {
  display: flex;
  gap: 1rem;
  align-items: end;
}

.form-row .form-group {
  flex: 1;
  margin-bottom: 0;
}

.form-group label {
  display: block;
  margin-bottom: 0.5rem;
  font-weight: 500;
  color: #374151;
  font-size: 0.875rem;
}

.form-group input,
.form-group textarea,
.form-group select {
  width: 100%;
  padding: 0.5rem;
  border: 1px solid #d1d5db;
  border-radius: 0.375rem;
  font-size: 0.875rem;
  transition: border-color 0.2s;
}

.form-group input:focus,
.form-group textarea:focus,
.form-group select:focus {
  outline: none;
  border-color: #3b82f6;
  box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
}

.form-row {
  display: flex;
  gap: 1rem;
}

.form-row .form-group {
  flex: 1;
}

.member-checkboxes {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
}

.member-checkbox {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0.5rem;
  border: 1px solid #e2e8f0;
  border-radius: 0.375rem;
  cursor: pointer;
  transition: all 0.2s;
}

.member-checkbox:hover {
  background: #f8fafc;
  border-color: #cbd5e1;
}

.member-checkbox input[type="checkbox"] {
  width: auto;
  margin: 0;
}

/* Color palette styles */
.color-palette {
  display: flex;
  gap: 0.5rem;
  flex-wrap: wrap;
  margin-top: 0.5rem;
}

.color-option {
  width: 2rem;
  height: 2rem;
  border-radius: 50%;
  border: 2px solid transparent;
  cursor: pointer;
  transition: all 0.2s;
}

.color-option:hover {
  transform: scale(1.1);
  border-color: #374151;
}

.color-option.selected {
  border-color: #1f2937;
  border-width: 3px;
  transform: scale(1.1);
}

/* Team member dropdown styles */
.team-member-select {
  width: 100%;
  padding: 0.5rem;
  border: 1px solid #d1d5db;
  border-radius: 0.375rem;
  font-size: 0.875rem;
  background-color: white;
  min-height: 120px;
}

.team-member-select:focus {
  outline: none;
  border-color: #3b82f6;
  box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
}

.team-member-select option {
  padding: 0.5rem;
  font-size: 0.875rem;
}

.help-text {
  font-size: 0.75rem;
  color: #6b7280;
  margin-top: 0.25rem;
  display: block;
}

/* Member dropdown with checkboxes */
.member-dropdown {
  border: 1px solid #d1d5db;
  border-radius: 0.375rem;
  max-height: 200px;
  overflow-y: auto;
  background-color: white;
  width: 100%;
}

.member-dropdown * {
  box-sizing: border-box;
}

.member-checkbox-item {
  display: flex !important;
  flex-direction: row !important;
  align-items: center !important;
  justify-content: flex-start !important;
  padding: 0.75rem 1rem !important;
  cursor: pointer;
  border-bottom: 1px solid #f3f4f6;
  transition: background-color 0.2s;
  gap: 0.75rem !important;
  min-height: 3.5rem;
  width: 100%;
  box-sizing: border-box;
  flex-wrap: nowrap !important;
}

.member-checkbox-item:hover {
  background-color: #f9fafb;
}

.member-checkbox-item:last-child {
  border-bottom: none;
}

.member-checkbox-item input[type="checkbox"] {
  width: 1.125rem !important;
  height: 1.125rem !important;
  flex-shrink: 0 !important;
  margin: 0 !important;
  display: inline-block !important;
}

.member-checkbox-item .member-avatar-small {
  flex-shrink: 0 !important;
  width: 2rem !important;
  height: 2rem !important;
  display: flex !important;
  align-items: center !important;
  justify-content: center !important;
  border-radius: 50% !important;
  font-size: 0.75rem !important;
  font-weight: 600 !important;
  color: white !important;
}

.member-checkbox-item span {
  flex-grow: 1 !important;
  font-weight: 500 !important;
  font-size: 0.875rem !important;
  white-space: nowrap !important;
  overflow: hidden !important;
  text-overflow: ellipsis !important;
  display: inline-block !important;
}

.member-avatar-small {
  width: 1.5rem;
  height: 1.5rem;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-weight: 600;
  font-size: 0.6rem;
  flex-shrink: 0;
}

.modal-actions {
  display: flex;
  gap: 0.5rem;
  justify-content: flex-end;
  margin-top: 1.5rem;
  padding-top: 1rem;
  border-top: 1px solid #e2e8f0;
}

.modal-actions button {
  padding: 0.5rem 1rem;
  border-radius: 0.375rem;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s;
  border: 1px solid transparent;
}

.save-btn {
  background: #3b82f6;
  color: white;
  border-color: #3b82f6;
}

.save-btn:hover {
  background: #2563eb;
  border-color: #2563eb;
}

.cancel-btn {
  background: white;
  color: #64748b;
  border-color: #d1d5db;
}

.cancel-btn:hover {
  background: #f9fafb;
  border-color: #9ca3af;
}

.delete-btn {
  background: #ef4444;
  color: white;
  border-color: #ef4444;
  margin-right: auto;
}

.delete-btn:hover {
  background: #dc2626;
  border-color: #dc2626;
}

/* Month view styles - responsive sizing */
.month-view {
  padding: 0.5rem; /* Reduced padding for mobile */
  height: 100%;
  min-height: 400px; /* Reduced for mobile */
  max-width: 1800px; /* Desktop maximum */
  min-width: 300px; /* Mobile minimum */
  width: 100%;
  margin: 0 auto; /* Center the content */
  overflow: auto;
  display: flex;
  flex-direction: column;
  flex: 1; /* Take available space */
}

.month-grid {
  display: grid;
  grid-template-columns: repeat(7, 1fr);
  gap: 1px;
  background: #e2e8f0;
  border: 1px solid #e2e8f0;
  border-radius: 0.5rem;
  overflow: hidden;
  max-width: 1800px; /* Desktop maximum */
  min-width: 300px; /* Mobile minimum */
  width: 100%;
  margin: 0 auto;
  flex: 1; /* Take available space */
}

.month-header-cell {
  background: #f8fafc;
  padding: 1rem;
  text-align: center;
  font-weight: 600;
  color: #64748b;
  font-size: 0.875rem;
  border-bottom: 2px solid #e2e8f0;
}

.month-cell {
  background: white;
  min-height: 120px;
  padding: 0.5rem;
  cursor: pointer;
  transition: background-color 0.2s;
  display: flex;
  flex-direction: column;
}

.month-cell:hover {
  background: #f8fafc;
}

.month-cell.empty {
  background: #f1f5f9;
  cursor: default;
}

.month-cell.today {
  background: #eff6ff;
  border: 2px solid #3b82f6;
}

.month-day-number {
  font-weight: 600;
  color: #1e293b;
  margin-bottom: 0.25rem;
  font-size: 0.875rem;
}

.month-tasks {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 1px;
}

.month-task {
  padding: 1px 4px;
  border-radius: 2px;
  color: white;
  font-size: 0.7rem;
  line-height: 1.2;
  cursor: pointer;
  transition: opacity 0.2s;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.month-task:hover {
  opacity: 0.8;
}

.month-task-more {
  font-size: 0.6rem;
  color: #64748b;
  padding: 1px 4px;
  font-style: italic;
}

/* Year view styles - responsive sizing */
.year-view {
  padding: 0.5rem; /* Reduced padding for mobile */
  height: 100%;
  min-height: 400px; /* Reduced for mobile */
  max-width: 1200px; /* Desktop maximum */
  min-width: 300px; /* Mobile minimum */
  width: 100%;
  margin: 0 auto; /* Center the content */
  overflow: auto;
  display: flex;
  flex-direction: column;
  flex: 1; /* Take available space */
}

.year-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(150px, 1fr)); /* Smaller minimum for mobile */
  gap: 0.5rem; /* Reduced gap for mobile */
  max-width: 1800px; /* Desktop maximum */
  min-width: 300px; /* Mobile minimum */
  width: 100%;
  margin: 0 auto;
  flex: 1; /* Take available space */
}

.year-month {
  background: white;
  border: 1px solid #e2e8f0;
  border-radius: 0.5rem;
  overflow: hidden;
  transition: all 0.2s;
}

.year-month:hover {
  border-color: #3b82f6;
  box-shadow: 0 4px 12px rgba(59, 130, 246, 0.15);
  transform: translateY(-2px);
  cursor: pointer;
}

.year-month-header {
  background: #f8fafc;
  padding: 1rem;
  font-weight: 600;
  color: #1e293b;
  text-align: center;
  border-bottom: 1px solid #e2e8f0;
}

.year-month-content {
  padding: 1rem;
}

.year-task-count {
  font-size: 1.5rem;
  font-weight: 600;
  color: #3b82f6;
  text-align: center;
  margin-bottom: 1rem;
}

.year-team-summary {
  display: flex;
  flex-wrap: wrap;
  gap: 0.5rem;
  justify-content: center;
}

.year-member-indicator {
  width: 2rem;
  height: 2rem;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-weight: 600;
  font-size: 0.75rem;
  cursor: pointer;
  transition: transform 0.2s;
}

.year-member-indicator:hover {
  transform: scale(1.1);
}

/* Enhanced Mobile Responsive Design */
@media (max-width: 768px) {
  .app-header {
    flex-direction: column;
    align-items: stretch;
    padding: 0.5rem;
  }

  .calendar-container {
    flex-direction: column;
    margin: 0.25rem;
    border-radius: 0.25rem;
  }

  .team-sidebar {
    width: 100%;
    max-height: 150px;
    padding: 0.75rem;
    overflow-x: auto;
  }

  .team-members {
    display: flex;
    gap: 0.5rem;
    overflow-x: auto;
    padding-bottom: 0.5rem;
  }

  .team-member {
    flex-shrink: 0;
    margin-bottom: 0;
  }

  .member-controls {
    padding: 0.25rem;
    gap: 0.25rem;
    min-width: 120px;
  }

  .member-name-button {
    font-size: 0.7rem;
    padding: 0.25rem;
  }

  .member-avatar {
    width: 1.5rem;
    height: 1.5rem;
    font-size: 0.6rem;
  }

  .time-header .time-label {
    width: 52px; /* Increased by 1.3x from 40px */
    padding: 0.5rem 0.25rem;
    font-size: 0.7rem;
  }

  .time-row .time-label {
    width: 52px; /* Increased by 1.3x from 40px */
    padding: 0.25rem;
    font-size: 0.65rem;
  }

  .day-header {
    padding: 0.5rem 4px; /* Adjusted padding for smaller fields */
    font-size: 0.75rem; /* Adjusted font for mobile */
    flex: 1 1 0%; /* Maintain consistent flex behavior */
    min-width: 94px; /* Reduced: 160px ÷ 1.7 = 94px */
    max-width: none;
  }

  .time-row {
    min-height: 30px; /* 1.2x of 25px */
  }

  .unified-slot {
    min-height: 30px; /* 1.2x of 25px */
    padding: 4px; /* Adjusted padding for smaller fields */
    flex: 1 1 0%; /* Maintain consistent flex behavior */
    min-width: 94px; /* Reduced: 160px ÷ 1.7 = 94px */
    max-width: none;
  }

  .task-card {
    padding: 0.25rem;
    font-size: 0.7rem;
    border-radius: 0.25rem;
  }

  .task-title {
    font-size: 0.7rem;
    margin-bottom: 0.1rem;
  }

  .task-time {
    font-size: 0.6rem;
  }

  .task-members {
    gap: 0.1rem;
  }

  .task-member {
    width: 1rem;
    height: 1rem;
    font-size: 0.5rem;
  }

  .month-cell {
    min-height: 60px;
    padding: 0.25rem;
  }

  .month-day-number {
    font-size: 0.8rem;
  }

  .year-grid {
    grid-template-columns: repeat(auto-fit, minmax(120px, 1fr));
    gap: 0.25rem;
  }

  .year-month {
    min-height: 80px;
  }

  .year-month-header {
    padding: 0.5rem;
    font-size: 0.75rem;
  }

  .year-month-content {
    padding: 0.5rem;
  }

  .year-task-count {
    font-size: 1rem;
  }

  .modal-content {
    width: 95%;
    margin: 0.5rem;
    padding: 1rem;
    max-height: 95vh;
  }

  .form-row {
    flex-direction: column;
  }

  .form-group {
    margin-bottom: 0.5rem;
  }

  .form-group label {
    font-size: 0.875rem;
  }

  .form-group input,
  .form-group select,
  .form-group textarea {
    font-size: 0.875rem;
    padding: 0.5rem;
  }
}

/* Extra small mobile devices */
@media (max-width: 480px) {
  .calendar-container {
    margin: 0.1rem;
  }

  .team-sidebar {
    max-height: 120px;
    padding: 0.5rem;
  }

  .member-controls {
    min-width: 100px;
  }

  .time-header .time-label,
  .time-row .time-label {
    width: 30px;
    font-size: 0.6rem;
  }

  .day-header {
    font-size: 0.7rem; /* Adjusted font for small mobile */
    padding: 0.25rem 4px; /* Adjusted padding for smaller fields */
    flex: 1 1 0%; /* Maintain consistent flex behavior */
    min-width: 82px; /* Reduced: 140px ÷ 1.7 = 82px */
    max-width: none;
  }

  .time-row {
    min-height: 24px; /* 1.2x of 20px */
  }

  .unified-slot {
    min-height: 24px; /* 1.2x of 20px */
    padding: 4px; /* Adjusted padding for smaller fields */
    flex: 1 1 0%; /* Maintain consistent flex behavior */
    min-width: 82px; /* Reduced: 140px ÷ 1.7 = 82px */
    max-width: none;
  }

  .task-card {
    padding: 0.2rem;
    font-size: 0.65rem;
  }

  .month-cell {
    min-height: 50px;
    padding: 0.2rem;
  }

  .year-grid {
    grid-template-columns: repeat(auto-fit, minmax(100px, 1fr));
  }

  .year-month {
    min-height: 70px;
  }

  .modal-content {
    width: 98%;
    margin: 0.2rem;
    padding: 0.75rem;
  }
}

/* Large tablets and small desktops */
@media (min-width: 769px) and (max-width: 1024px) {
  .day-header {
    font-size: 0.8rem; /* Adjusted font for tablet */
    flex: 1 1 0%; /* Maintain consistent flex behavior */
    padding: 0.75rem 4px; /* Adjusted padding for smaller fields */
    min-width: 118px; /* Reduced: 200px ÷ 1.7 = 118px */
    max-width: none;
  }

  .time-header .time-label,
  .time-row .time-label {
    width: 91px; /* Increased by 1.3x from 70px */
  }

  .time-row {
    min-height: 38px; /* 1.2x of ~32px */
  }

  .unified-slot {
    min-height: 38px; /* 1.2x of ~32px */
    padding: 4px; /* Adjusted padding for smaller fields */
    flex: 1 1 0%; /* Maintain consistent flex behavior */
    min-width: 118px; /* Reduced: 200px ÷ 1.7 = 118px */
    max-width: none;
  }

  .member-controls {
    padding: 0.4rem;
  }
}

/* Large desktops */
@media (min-width: 1200px) {
  .day-header {
    padding: 0.75rem 4px; /* Adjusted padding for smaller fields */
    font-size: 0.85rem; /* Adjusted font for desktop */
    flex: 1 1 0%; /* Maintain consistent flex behavior */
    min-width: 141px; /* Reduced: 240px ÷ 1.7 = 141px */
    max-width: none;
  }

  .time-header .time-label,
  .time-row .time-label {
    width: 104px; /* Increased by 1.3x from 80px */
  }

  .member-controls {
    padding: 0.5rem;
    gap: 0.75rem;
  }

  .member-name-button {
    font-size: 1rem;
  }
}

/* Bulgarian localization and non-working days styling */
.non-working-day {
  background-color: #fee2e2 !important; /* Light red background */
  border-color: #fca5a5 !important; /* Red border */
}

.non-working-day.unified-slot {
  background-color: #fef2f2 !important; /* Very light red for time slots */
}

.non-working-day.unified-slot:hover {
  background-color: #fee2e2 !important; /* Slightly darker red on hover */
}

.day-header.non-working-day {
  background-color: #dc2626 !important; /* Dark red for headers */
  color: white !important;
  font-weight: bold;
}

.month-cell.non-working-day {
  background-color: #fee2e2 !important; /* Light red for month view */
  color: #991b1b; /* Dark red text */
}

.month-cell.non-working-day.today {
  background-color: #dc2626 !important; /* Dark red for today if it's non-working */
  color: white !important;
}

/* Weekend and holiday indicators */
.non-working-day::before {
  content: '';
  position: absolute;
  top: 2px;
  right: 2px;
  width: 6px;
  height: 6px;
  background-color: #dc2626;
  border-radius: 50%;
  z-index: 1;
}

/* Bulgarian calendar specific styles */
.bulgarian-calendar {
  font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
}

.bulgarian-date {
  font-weight: 500;
}

/* Enhanced styling for non-working days in time slots */
.time-slot.non-working-day {
  position: relative;
}

.time-slot.non-working-day .unified-slot {
  background: linear-gradient(135deg, #fef2f2 0%, #fee2e2 100%);
  border-left: 3px solid #dc2626;
}

/* Month view non-working day styling */
.month-cell.non-working-day {
  position: relative;
  background: linear-gradient(135deg, #fee2e2 0%, #fecaca 100%);
}

.month-cell.non-working-day .month-day-number {
  color: #991b1b;
  font-weight: bold;
}

/* Enhanced worker controls */
.visibility-toggle {
  background: none;
  border: none;
  cursor: pointer;
  padding: 0.25rem;
  border-radius: 0.25rem;
  color: #64748b;
  transition: all 0.2s ease;
  display: flex;
  align-items: center;
  justify-content: center;
  min-height: 32px; /* Reduced for better layout */
  min-width: 32px; /* Reduced for better layout */
  -webkit-tap-highlight-color: rgba(59, 130, 246, 0.2);
  touch-action: manipulation;
}

.visibility-toggle:hover {
  background: #f1f5f9;
  color: #334155;
}

.member-name-button {
  background: none;
  border: none;
  cursor: pointer;
  padding: 0.25rem 0.5rem;
  border-radius: 0.25rem;
  font-weight: 500;
  transition: all 0.2s ease;
  text-align: left;
  flex: 1;
  min-height: 36px; /* Reduced for better layout */
  -webkit-tap-highlight-color: rgba(59, 130, 246, 0.2);
  touch-action: manipulation;
}

.member-name-button:hover {
  background: rgba(0, 0, 0, 0.05);
  transform: translateX(2px);
}

.member-name-button.highlighted {
  background: rgba(59, 130, 246, 0.1);
  border: 1px solid rgba(59, 130, 246, 0.3);
  font-weight: 600;
  box-shadow: 0 0 8px rgba(59, 130, 246, 0.3);
}

/* Task highlighting effects */
.highlighted-task {
  animation: taskGlow 2s ease-in-out infinite alternate;
  box-shadow: 0 0 15px rgba(59, 130, 246, 0.6) !important;
  border: 2px solid #3b82f6 !important;
  transform: scale(1.02);
  z-index: 20 !important;
}

@keyframes taskGlow {
  0% {
    box-shadow: 0 0 15px rgba(59, 130, 246, 0.6);
  }
  100% {
    box-shadow: 0 0 25px rgba(59, 130, 246, 0.9);
  }
}

/* Enhanced task highlighting for better visibility */
.highlighted-task::before {
  content: '';
  position: absolute;
  top: -2px;
  left: -2px;
  right: -2px;
  bottom: -2px;
  background: linear-gradient(45deg, #3b82f6, #8b5cf6, #3b82f6);
  border-radius: 6px;
  z-index: -1;
  animation: borderGlow 3s ease-in-out infinite;
}

@keyframes borderGlow {
  0%, 100% {
    opacity: 0.7;
  }
  50% {
    opacity: 1;
  }
}
