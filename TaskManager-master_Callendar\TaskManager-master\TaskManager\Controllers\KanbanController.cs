namespace TaskManager.Controllers
{
    using Microsoft.AspNetCore.Authorization;
    using Microsoft.AspNetCore.Mvc;
    using TaskManager.Services.Data.Interfaces;
    using TaskManager.Web.ViewModels.Kanban;

    /// <summary>
    /// Controller for Kanban board operations
    /// </summary>
    [Authorize]
    public class KanbanController : Controller
    {
        private readonly IKanbanService kanbanService;
        private readonly IUserService userService;

        public KanbanController(IKanbanService kanbanService, IUserService userService)
        {
            this.kanbanService = kanbanService;
            this.userService = userService;
        }

        /// <summary>
        /// Simple test action to verify controller is working
        /// </summary>
        [HttpGet]
        public IActionResult Test()
        {
            return Json(new { message = "Kanban controller is working!", timestamp = DateTime.Now });
        }

        /// <summary>
        /// Debug endpoint to check columns for a specific user
        /// </summary>
        [HttpGet]
        public async Task<IActionResult> DebugColumns(string userId)
        {
            try
            {
                // Get all workers to find the one with this userId
                var allWorkers = await userService.GetAllWorkersAsync();
                var worker = allWorkers.FirstOrDefault(w => w.UserId == userId);

                if (worker == null)
                {
                    return Json(new { error = "Worker not found", userId = userId });
                }

                // Initialize board to make sure columns exist
                await kanbanService.InitializeDefaultBoardAsync();

                // Get columns using the service method
                var columns = await kanbanService.GetWorkerBoardColumnsAsync(userId);

                return Json(new {
                    success = true,
                    worker = new {
                        WorkerId = worker.Id,
                        UserId = worker.UserId,
                        Name = $"{worker.FirstName} {worker.LastName}"
                    },
                    columns = columns.Select(c => new {
                        Id = c.Id,
                        Name = c.Name ?? "NULL_NAME",
                        Description = c.Description ?? "NULL_DESC",
                        Position = c.Position,
                        Color = c.Color ?? "NULL_COLOR"
                    }).ToList()
                });
            }
            catch (Exception ex)
            {
                return Json(new { error = ex.Message, stackTrace = ex.StackTrace });
            }
        }

        /// <summary>
        /// Debug endpoint to check worker data
        /// </summary>
        [HttpGet]
        public async Task<IActionResult> DebugWorkers()
        {
            try
            {
                var allWorkers = await userService.GetAllWorkersAsync();
                var currentUser = User?.Identity?.Name;

                var debugData = new
                {
                    CurrentUser = currentUser,
                    TotalWorkers = allWorkers.Count(),
                    Workers = allWorkers.Select(w => new
                    {
                        WorkerId = w.Id,
                        UserId = w.UserId,
                        Email = w.Email,
                        FullName = $"{w.FirstName} {w.LastName}",
                        Position = w.Position,
                        IsCurrentUser = w.Email == currentUser
                    }).ToList()
                };

                return Json(debugData);
            }
            catch (Exception ex)
            {
                return Json(new { error = ex.Message, stackTrace = ex.StackTrace });
            }
        }

        /// <summary>
        /// Creates cards for existing GeoTasks that don't have cards yet
        /// </summary>
        [HttpGet]
        public async Task<IActionResult> CreateCardsForExistingGeoTasks()
        {
            try
            {
                // Get all GeoTasks that don't have associated Kanban cards
                var geoTasksWithoutCards = await kanbanService.GetGeoTasksWithoutCardsAsync();
                var createdCards = new List<string>();

                foreach (var geoTask in geoTasksWithoutCards)
                {
                    try
                    {
                        var cardTitle = $"Проект #{geoTask.ProjectNumber}";
                        await kanbanService.CreateCardForGeoTaskAsync(geoTask.Id.ToString(), cardTitle);
                        createdCards.Add(geoTask.Id.ToString());
                    }
                    catch (Exception ex)
                    {
                        // Continue with other tasks even if one fails
                        continue;
                    }
                }

                return Json(new {
                    success = true,
                    message = $"Създадени {createdCards.Count} карти за съществуващи задачи",
                    createdCards = createdCards.Count
                });
            }
            catch (Exception ex)
            {
                return Json(new {
                    success = false,
                    message = ex.Message,
                    innerException = ex.InnerException?.Message
                });
            }
        }

        /// <summary>
        /// Displays the team Kanban board - redirects to current user's board
        /// </summary>
        [HttpGet]
        public async Task<IActionResult> TeamBoard()
        {
            try
            {
                // Get current user's worker ID
                var currentUserEmail = User.Identity?.Name;
                if (string.IsNullOrEmpty(currentUserEmail))
                {
                    return RedirectToAction("Login", "Account");
                }

                var workers = await userService.GetAllWorkersAsync();
                var currentWorker = workers.FirstOrDefault(w => w.Email == currentUserEmail);

                if (currentWorker == null)
                {
                    TempData["ErrorMessage"] = "Не сте регистриран като работник";
                    return RedirectToAction("AllTasks", "GeoTask");
                }

                // Redirect to current user's board using UserId
                return RedirectToAction("Board", new { memberId = currentWorker.UserId });
            }
            catch (Exception ex)
            {
                TempData["ErrorMessage"] = "Възникна грешка при зареждането на дъската: " + ex.Message;
                return RedirectToAction("AllTasks", "GeoTask");
            }
        }

        /// <summary>
        /// Displays a specific member's Kanban board with team sidebar
        /// </summary>
        [HttpGet]
        public async Task<IActionResult> Board(string memberId)
        {
            try
            {
                // Debug: Log the memberId being used
                var debugInfo = new
                {
                    MemberId = memberId,
                    IsEmpty = string.IsNullOrEmpty(memberId),
                    CurrentUser = User?.Identity?.Name
                };

                // Initialize default board if it doesn't exist
                await kanbanService.InitializeDefaultBoardAsync();

                // Get all workers for debugging
                var allWorkers = await userService.GetAllWorkersAsync();
                var workerExists = allWorkers.Any(w => w.UserId == memberId);

                if (!workerExists)
                {
                    // If no specific memberId provided or worker not found, use current user
                    if (string.IsNullOrEmpty(memberId) || !workerExists)
                    {
                        // Try to get current user's worker record
                        var currentUserWorker = allWorkers.FirstOrDefault(w => w.Email == User?.Identity?.Name);
                        if (currentUserWorker != null)
                        {
                            memberId = currentUserWorker.UserId;
                        }
                        else
                        {
                            // Fallback: use first available worker
                            var firstWorker = allWorkers.FirstOrDefault();
                            if (firstWorker != null)
                            {
                                memberId = firstWorker.UserId;
                            }
                            else
                            {
                                TempData["ErrorMessage"] = "Няма налични работници в системата.";
                                return RedirectToAction("AllTasks", "GeoTask");
                            }
                        }
                    }
                }

                // Force initialization of member board using UserId
                var board = await kanbanService.GetMemberBoardByUserIdAsync(memberId);

                ViewBag.Workers = allWorkers;
                ViewBag.AllTeamMembers = allWorkers;
                ViewBag.CurrentMemberId = memberId;

                // Get current member info by UserId
                var currentMember = allWorkers.FirstOrDefault(w => w.UserId == memberId);
                ViewBag.CurrentMemberName = currentMember != null ? $"{currentMember.FirstName} {currentMember.LastName}" : "Неизвестен";

                return View(board);
            }
            catch (Exception ex)
            {
                TempData["ErrorMessage"] = "Възникна грешка при зареждането на дъската: " + ex.Message;
                return RedirectToAction("AllTasks", "GeoTask");
            }
        }

        /// <summary>
        /// Creates a new Kanban card
        /// </summary>
        [HttpPost]
        [ValidateAntiForgeryToken]
        public async Task<IActionResult> CreateCard(CreateKanbanCardViewModel model)
        {
            try
            {
                // Debug: Log the received model
                var debugInfo = new
                {
                    Title = model?.Title,
                    TitleLength = model?.Title?.Length,
                    Description = model?.Description,
                    DescriptionLength = model?.Description?.Length,
                    ColumnId = model?.ColumnId,
                    AssignedToId = model?.AssignedToId,
                    ModelStateValid = ModelState.IsValid,
                    ModelStateErrorCount = ModelState.ErrorCount
                };

                if (!ModelState.IsValid)
                {
                    var errors = ModelState
                        .Where(x => x.Value.Errors.Count > 0)
                        .Select(x => new { Field = x.Key, Errors = x.Value.Errors.Select(e => e.ErrorMessage) })
                        .ToList();

                    return Json(new {
                        success = false,
                        message = "Невалидни данни",
                        errors = errors,
                        debugInfo = debugInfo
                    });
                }

                var cardId = await kanbanService.CreateCardAsync(model);
                return Json(new { success = true, cardId = cardId });
            }
            catch (Exception ex)
            {
                return Json(new {
                    success = false,
                    message = ex.Message,
                    innerException = ex.InnerException?.Message,
                    stackTrace = ex.StackTrace
                });
            }
        }

        /// <summary>
        /// Creates a card without anti-forgery token for testing
        /// </summary>
        [HttpPost]
        public async Task<IActionResult> CreateCardSimple(CreateKanbanCardViewModel model)
        {
            try
            {
                if (string.IsNullOrEmpty(model.Title))
                {
                    return Json(new { success = false, message = "Title is required" });
                }

                if (string.IsNullOrEmpty(model.ColumnId))
                {
                    return Json(new { success = false, message = "ColumnId is required" });
                }

                var cardId = await kanbanService.CreateCardAsync(model);
                return Json(new { success = true, cardId = cardId });
            }
            catch (Exception ex)
            {
                return Json(new { success = false, message = ex.Message, stackTrace = ex.StackTrace });
            }
        }

        /// <summary>
        /// Updates an existing Kanban card
        /// </summary>
        [HttpPost]
        [ValidateAntiForgeryToken]
        public async Task<IActionResult> UpdateCard(UpdateKanbanCardViewModel model)
        {
            if (!ModelState.IsValid)
            {
                return Json(new { success = false, message = "Невалидни данни" });
            }

            try
            {
                await kanbanService.UpdateCardAsync(model);
                return Json(new { success = true });
            }
            catch (Exception ex)
            {
                return Json(new { success = false, message = ex.Message });
            }
        }

        /// <summary>
        /// Moves a card to a different column or position
        /// </summary>
        [HttpPost]
        [ValidateAntiForgeryToken]
        public async Task<IActionResult> MoveCard(MoveCardRequest request)
        {
            try
            {
                await kanbanService.MoveCardAsync(request.CardId, request.NewColumnId, request.NewPosition);
                return Json(new { success = true });
            }
            catch (Exception ex)
            {
                return Json(new { success = false, message = ex.Message });
            }
        }

        /// <summary>
        /// Deletes a Kanban card
        /// </summary>
        [HttpPost]
        [ValidateAntiForgeryToken]
        public async Task<IActionResult> DeleteCard(string cardId)
        {
            try
            {
                await kanbanService.DeleteCardAsync(cardId);
                return Json(new { success = true });
            }
            catch (Exception ex)
            {
                return Json(new { success = false, message = ex.Message });
            }
        }

        /// <summary>
        /// Gets card details for editing
        /// </summary>
        [HttpGet]
        public async Task<IActionResult> GetCard(string id)
        {
            try
            {
                var card = await kanbanService.GetCardByIdAsync(id);
                if (card == null)
                {
                    return NotFound();
                }

                return Json(card);
            }
            catch (Exception ex)
            {
                return Json(new { success = false, message = ex.Message });
            }
        }

        /// <summary>
        /// Shows the create card modal
        /// </summary>
        [HttpGet]
        public async Task<IActionResult> CreateCardModal(string columnId)
        {
            var model = new CreateKanbanCardViewModel
            {
                ColumnId = columnId
            };

            ViewBag.Workers = await userService.GetAllWorkersAsync();
            return PartialView("_CreateCardModal", model);
        }

        /// <summary>
        /// Gets columns for a worker's personal board
        /// </summary>
        [HttpGet]
        public async Task<IActionResult> GetWorkerColumns(string userId)
        {
            try
            {
                // Debug info
                var debugInfo = new
                {
                    UserId = userId,
                    IsEmpty = string.IsNullOrEmpty(userId)
                };

                var columns = await kanbanService.GetWorkerBoardColumnsAsync(userId);

                return Json(new {
                    success = true,
                    columns = columns,
                    debug = debugInfo,
                    columnCount = columns.Count
                });
            }
            catch (Exception ex)
            {
                return Json(new {
                    success = false,
                    message = ex.Message,
                    innerException = ex.InnerException?.Message,
                    stackTrace = ex.StackTrace
                });
            }
        }

        /// <summary>
        /// Gets detailed information about a specific card for editing
        /// </summary>
        [HttpGet]
        public async Task<IActionResult> GetCardDetails(string cardId)
        {
            try
            {
                var card = await kanbanService.GetCardDetailsAsync(cardId);
                return Json(new { success = true, card = card });
            }
            catch (Exception ex)
            {
                return Json(new { success = false, message = ex.Message });
            }
        }

        /// <summary>
        /// Gets comments for a specific card
        /// </summary>
        [HttpGet]
        public async Task<IActionResult> GetCardComments(string cardId)
        {
            try
            {
                var comments = await kanbanService.GetCardCommentsAsync(cardId);
                return Json(new { success = true, comments = comments });
            }
            catch (Exception ex)
            {
                return Json(new { success = false, message = ex.Message });
            }
        }

        /// <summary>
        /// Adds a comment to a card
        /// </summary>
        [HttpPost]
        [ValidateAntiForgeryToken]
        public async Task<IActionResult> AddCardComment(string cardId, string comment)
        {
            try
            {
                var currentUserEmail = User.Identity?.Name;
                if (string.IsNullOrEmpty(currentUserEmail))
                {
                    return Json(new { success = false, message = "Не сте влезли в системата" });
                }

                await kanbanService.AddCardCommentAsync(cardId, comment, currentUserEmail);
                return Json(new { success = true, message = "Коментарът беше добавен успешно" });
            }
            catch (Exception ex)
            {
                return Json(new { success = false, message = ex.Message });
            }
        }

        /// <summary>
        /// Shows the edit card modal
        /// </summary>
        [HttpGet]
        public async Task<IActionResult> EditCardModal(string id)
        {
            try
            {
                var card = await kanbanService.GetCardByIdAsync(id);
                if (card == null)
                {
                    return NotFound();
                }

                var model = new UpdateKanbanCardViewModel
                {
                    Id = card.Id,
                    Title = card.Title,
                    Description = card.Description,
                    Color = card.Color,
                    Labels = card.Labels,
                    DueDate = card.DueDate,
                    AssignedToId = card.AssignedToId,
                    ColumnId = card.ColumnId
                };

                ViewBag.Workers = await userService.GetAllWorkersAsync();
                return PartialView("_EditCardModal", model);
            }
            catch (Exception ex)
            {
                return Json(new { success = false, message = ex.Message });
            }
        }

        /// <summary>
        /// Debug action to check Kanban board state
        /// </summary>
        [HttpGet]
        public async Task<IActionResult> Debug()
        {
            try
            {
                // Initialize boards
                await kanbanService.InitializeDefaultBoardAsync();

                var currentUserEmail = User.Identity?.Name;
                var workers = await userService.GetAllWorkersAsync();
                var currentWorker = workers.FirstOrDefault(w => w.Email == currentUserEmail);

                var debugInfo = new
                {
                    CurrentUser = currentUserEmail,
                    CurrentWorker = currentWorker,
                    AllWorkers = workers.Count(),
                    WorkersList = workers.Select(w => new { w.Id, w.FirstName, w.LastName, w.Email }).ToList()
                };

                if (currentWorker != null)
                {
                    var board = await kanbanService.GetMemberBoardAsync(currentWorker.Id);
                    var boardInfo = new
                    {
                        BoardId = board.Id,
                        BoardName = board.Name,
                        ColumnsCount = board.Columns.Count(),
                        Columns = board.Columns.Select(c => new
                        {
                            c.Id,
                            c.Name,
                            c.Position,
                            CardsCount = c.Cards.Count(),
                            Cards = c.Cards.Select(card => new { card.Id, card.Title, card.Description }).ToList()
                        }).ToList()
                    };

                    return Json(new { debugInfo, boardInfo });
                }

                return Json(new { debugInfo, error = "Current worker not found" });
            }
            catch (Exception ex)
            {
                return Json(new { error = ex.Message, stackTrace = ex.StackTrace });
            }
        }

        /// <summary>
        /// Creates test cards for debugging
        /// </summary>
        [HttpGet]
        public async Task<IActionResult> CreateTestCards()
        {
            try
            {
                var currentUserEmail = User.Identity?.Name;
                var workers = await userService.GetAllWorkersAsync();
                var currentWorker = workers.FirstOrDefault(w => w.Email == currentUserEmail);

                if (currentWorker == null)
                {
                    return Json(new { success = false, message = "Current worker not found" });
                }

                // Initialize board
                await kanbanService.InitializeDefaultBoardAsync();
                var board = await kanbanService.GetMemberBoardAsync(currentWorker.Id.ToString());

                // Debug info
                var debugInfo = new
                {
                    WorkerId = currentWorker.Id.ToString(),
                    WorkerEmail = currentWorker.Email,
                    BoardId = board.Id,
                    ColumnsCount = board.Columns.Count,
                    ColumnNames = board.Columns.Select(c => c.Name).ToArray()
                };

                // Create test cards in different columns
                var testCards = new[]
                {
                    new { Title = "Тестова карта 1", ColumnName = "Текущи" },
                    new { Title = "Тестова карта 2", ColumnName = "В процес на работа" },
                    new { Title = "Тестова карта 3", ColumnName = "За проверка" }
                };

                var createdCards = new List<string>();

                foreach (var testCard in testCards)
                {
                    var column = board.Columns.FirstOrDefault(c => c.Name == testCard.ColumnName);
                    if (column != null)
                    {
                        var createModel = new CreateKanbanCardViewModel
                        {
                            Title = testCard.Title,
                            Description = "Автоматично създадена тестова карта",
                            ColumnId = column.Id.ToString(),
                            AssignedToId = currentWorker.UserId.ToString()
                        };

                        var cardId = await kanbanService.CreateCardAsync(createModel);
                        createdCards.Add(cardId);
                    }
                }

                return Json(new { success = true, message = $"Създадени {createdCards.Count} тестови карти", cardIds = createdCards });
            }
            catch (Exception ex)
            {
                return Json(new {
                    success = false,
                    message = ex.Message,
                    innerException = ex.InnerException?.Message,
                    stackTrace = ex.StackTrace
                });
            }
        }
    }

    /// <summary>
    /// Request model for moving cards
    /// </summary>
    public class MoveCardRequest
    {
        public string CardId { get; set; } = null!;
        public string NewColumnId { get; set; } = null!;
        public int NewPosition { get; set; }
    }

    /// <summary>
    /// Request model for deleting cards
    /// </summary>
    public class DeleteCardRequest
    {
        public string CardId { get; set; } = null!;
    }
}
