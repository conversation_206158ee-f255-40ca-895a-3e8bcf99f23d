namespace GeoSpatialDataKRBR.Common
{
    public static class NotificationMessages
    {
        public const string ErrorMessage = "ErrorMessage";
        public const string WarningMessage = "WarningMessage";
        public const string InformationMessage = "InformationMessage";
        public const string SuccessMessage = "SuccessMessage";

        // Layer related messages
        public static class Layer
        {
            public const string LayerAddedSuccessfully = "Слоят беше добавен успешно!";
            public const string LayerUpdatedSuccessfully = "Слоят беше обновен успешно!";
            public const string LayerDeletedSuccessfully = "Слоят беше изтрит успешно!";
            public const string LayerNotFound = "Слоят не беше намерен!";
            public const string LayerLoadError = "Грешка при зареждане на слоя!";
            public const string LayerConnectionError = "Грешка при свързване с GeoServer!";
        }

        // User related messages
        public static class User
        {
            public const string LoginSuccessful = "Успешно влизане в системата!";
            public const string LogoutSuccessful = "Успешно излизане от системата!";
            public const string AccessDenied = "Нямате права за достъп до тази страница!";
            public const string UserNotFound = "Потребителят не беше намерен!";
        }

        // GeoServer related messages
        public static class GeoServer
        {
            public const string ConnectionSuccessful = "Успешна връзка с GeoServer!";
            public const string ConnectionFailed = "Неуспешна връзка с GeoServer!";
            public const string InvalidCredentials = "Невалидни данни за достъп до GeoServer!";
            public const string ServiceUnavailable = "GeoServer услугата не е достъпна!";
        }

        // General messages
        public static class General
        {
            public const string UnexpectedError = "Възникна неочаквана грешка!";
            public const string InvalidData = "Невалидни данни!";
            public const string OperationSuccessful = "Операцията беше извършена успешно!";
            public const string OperationFailed = "Операцията не беше извършена!";
        }
    }
}
