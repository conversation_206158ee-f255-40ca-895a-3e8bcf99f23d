﻿@model AllClientQueryModel;

@{
    ViewData["Title"] = "Клиенти";
}

<div class="modern-container">
    <div class="modern-page-header">
        <h1 class="modern-page-title">
            <i class="fas fa-users"></i>
            Клиенти
        </h1>
        <p class="modern-page-subtitle">
            Управление на клиентската база и контактна информация
        </p>
    </div>

    <!-- Search and Filter Section -->
    <div class="modern-card">
        <div class="modern-card-header">
            <h3>
                <i class="fas fa-search"></i>
                Търсене и филтриране
            </h3>
        </div>
        <div class="modern-card-body">
            <form method="get" class="search-form">
                <div class="search-controls">
                    <div class="form-group">
                        <label asp-for="SearchString" class="modern-form-label">
                            <i class="fas fa-search"></i>
                            Търсене по име
                        </label>
                        <input asp-for="SearchString" class="modern-form-control" placeholder="Въведете име на клиент...">
                    </div>

                    <div class="form-group">
                        <label asp-for="ClientPerPage" class="modern-form-label">
                            <i class="fas fa-list-ol"></i>
                            Клиенти на страница
                        </label>
                        <select asp-for="ClientPerPage" class="modern-form-control">
                            <option value="5">5</option>
                            <option value="10">10</option>
                            <option value="15">15</option>
                            <option value="25">25</option>
                        </select>
                    </div>

                    <div class="form-group search-button-group">
                        <button type="submit" class="modern-btn modern-btn-primary">
                            <i class="fas fa-search"></i>
                            Търси
                        </button>
                        <a href="@Url.Action("Clients", "Client")" class="modern-btn modern-btn-secondary">
                            <i class="fas fa-times"></i>
                            Изчисти
                        </a>
                    </div>
                </div>
            </form>
        </div>
    </div>

    @if (!Model.Client.Any())
    {
        <!-- Empty State -->
        <div class="modern-card">
            <div class="modern-card-body">
                <div class="empty-clients-state">
                    <i class="fas fa-user-plus"></i>
                    <h3>Няма намерени клиенти</h3>
                    <p>Не са намерени клиенти, отговарящи на зададените критерии.</p>
                    <a class="modern-btn modern-btn-primary" asp-controller="Client" asp-action="Add">
                        <i class="fas fa-plus"></i>
                        Добави нов клиент
                    </a>
                </div>
            </div>
        </div>
    }
    else
    {
        <!-- Clients Table -->
        <div class="clients-table-container">
            <div class="clients-table-header">
                <h3>
                    <i class="fas fa-address-book"></i>
                    Списък с клиенти (@Model.TotalTaskss)
                </h3>
                <a class="add-client-btn" asp-controller="Client" asp-action="Add">
                    <i class="fas fa-user-plus"></i>
                    Нов клиент
                </a>
            </div>

            <div class="clients-grid">
                @foreach (ClientViewModel client in Model.Client)
                {
                    <div class="client-card">
                        <div class="client-card-header">
                            <div class="client-avatar">
                                @{
                                    var initials = client.Name.Split(' ').Take(2).Select(n => n.FirstOrDefault()).ToArray();
                                    string clientInitials = string.Join("", initials).ToUpper();
                                }
                                @clientInitials
                            </div>
                            <div class="client-info">
                                <h4 class="client-name">
                                    <a asp-controller="Client" asp-action="Edit" asp-route-id="@client.Id">
                                        @client.Name
                                    </a>
                                </h4>
                                <span class="client-representative">@client.CustomerRepresentative</span>
                            </div>
                        </div>

                        <div class="client-card-body">
                            <div class="contact-info">
                                <div class="contact-item">
                                    <i class="fas fa-phone"></i>
                                    <span>@client.PhoneNumber</span>
                                </div>
                                <div class="contact-item">
                                    <i class="fas fa-envelope"></i>
                                    <span>@client.Email</span>
                                </div>
                            </div>
                        </div>

                        <div class="client-card-actions">
                            <a asp-controller="Client" asp-action="Edit" asp-route-id="@client.Id" class="client-action-btn edit">
                                <i class="fas fa-edit"></i>
                                Редактирай
                            </a>
                            <a href="tel:@client.PhoneNumber" class="client-action-btn call">
                                <i class="fas fa-phone"></i>
                                Обади се
                            </a>
                            <a href="mailto:@client.Email" class="client-action-btn email">
                                <i class="fas fa-envelope"></i>
                                Имейл
                            </a>
                        </div>
                    </div>
                }
            </div>
        </div>
    }
}
@{
    int previousPage = Model.CurrentPage - 1;
    if (previousPage < 1)
    {
        previousPage = 1;
    }
    int clientPerPage = Model.ClientPerPage;
    //int totaltask = Model.TotalTaskss;
    int maxPage = (int)Math.Ceiling((double)Model.TotalTaskss / Model.ClientPerPage);
}
<div class="row mb-5">
   <div class="col-md-6 d-grid gap-2 d-md-flex justify-content-md-start">
      <a class="btn btn-success @(Model.CurrentPage == 1 ? "disabled" :
      string.Empty)"
      asp-controller="Client"
      asp-action="Clients"
      asp-route-currentPage="@previousPage"
      asp-route-clientPerPage="@clientPerPage"
      asp-route-searchTerm="@Model.SearchString">
      <<</a>
   </div>
    @{
        bool shouldNextPageBeDisabled = Model.CurrentPage == maxPage ||
        !Model.Client.Any();
    }
   <div class="col-md-6 d-grid gap-2 d-md-flex justify-content-md-end">
      <a class="btn btn-success
      @(shouldNextPageBeDisabled ? "disabled" : string.Empty)"
      asp-controller="Client"
      asp-action="Clients"
      asp-route-currentPage="@(Model.CurrentPage + 1)"
      asp-route-clientPerPage="@clientPerPage"
      asp-route-searchTerm="@Model.SearchString">
        >></a>
   </div>
</div>