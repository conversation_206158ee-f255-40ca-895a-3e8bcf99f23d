namespace TaskManager.Web.ViewModels.Kanban
{
    /// <summary>
    /// View model for displaying a Kanban card
    /// </summary>
    public class KanbanCardViewModel
    {
        public string Id { get; set; } = null!;

        public string Title { get; set; } = null!;

        public string? Description { get; set; }

        public int Position { get; set; }

        public string? Color { get; set; }

        public string? Labels { get; set; }

        public DateTime? DueDate { get; set; }

        public DateTime CreatedOn { get; set; }

        public string? AssignedToId { get; set; }

        public string? AssignedToName { get; set; }

        public string? CreatedById { get; set; }

        public string? CreatedByName { get; set; }

        public string? GeoTaskId { get; set; }

        public int? GeoTaskProjectNumber { get; set; }

        public string ColumnId { get; set; } = null!;

        public bool HasGeoTask => !string.IsNullOrEmpty(GeoTaskId);

        public bool IsOverdue => DueDate.HasValue && DueDate.Value < DateTime.UtcNow;

        public string DisplayTitle => HasGeoTask && GeoTaskProjectNumber.HasValue 
            ? $"#{GeoTaskProjectNumber} - {Title}" 
            : Title;
    }
}
