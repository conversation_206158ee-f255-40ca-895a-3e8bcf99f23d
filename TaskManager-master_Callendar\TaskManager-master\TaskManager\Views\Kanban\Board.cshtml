@model TaskManager.Web.ViewModels.Kanban.KanbanBoardViewModel

@{
    ViewData["Title"] = "Екипна дъска";
}

@section Styles {
    <style>
        /* Full Screen Kanban Layout */
        .kanban-layout {
            display: flex;
            height: calc(100vh - 80px);
            gap: 1rem;
            padding: 1rem;
            overflow: hidden;
            max-width: none;
            width: 100%;
        }

        /* Compact Team Sidebar */
        .team-sidebar {
            width: 200px;
            background: white;
            border-radius: 12px;
            box-shadow: 0 4px 20px rgba(0,0,0,0.1);
            overflow-y: auto;
            flex-shrink: 0;
            flex-direction: column;
            max-height: 80vh;
            padding-top:0.5rem;
        }

        .sidebar-header {
            padding: 1.5rem;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border-radius: 12px 12px 0 0;
        }

        .sidebar-header h3 {
            margin: 0;
            font-size: 1.4rem;
            font-weight: 600;
            display: flex;
            align-items: center;
            gap: 0.75rem;
        }

        .team-member {
            display: block;
            padding: 1rem 1.5rem;
            border-bottom: 1px solid #f1f5f9;
            cursor: pointer;
            transition: all 0.3s ease;
            font-size: 1rem;
            text-decoration: none;
            color: #475569;
            font-weight: 500;
            text-align: center;
        }

        .team-member:hover {
            background: #f8fafc;
            text-decoration: none;
            color: #1e293b;
            transform: translateX(4px);
        }

        .team-member.active {
            background: #e0e7ff;
            border-left: 4px solid #667eea;
            color: #667eea;
            font-weight: 600;
        }

        /* Full Width Kanban Board */
        .kanban-board {
            flex: 1;
            display: flex;
            gap: 1rem;
            overflow-x: auto;
            overflow-y: hidden;
            padding: 0.5rem;
            padding-top: 0rem;
            width: 100%;
            min-height: 0;
        }

        /* Compact Columns for More Screen Usage */
        .kanban-column {
            flex: 0 0 220px;
            background: white;
            border-radius: 12px;
            box-shadow: 0 4px 20px rgba(0,0,0,0.1);
            display: flex;
            flex-direction: column;
            height: 100%;
            min-width: 220px;
        }

        .kanban-column-header {
            padding: 0.75rem 1rem;
            background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
            border-radius: 12px 12px 0 0;
            display: flex;
            justify-content: space-between;
            align-items: center;
            border-bottom: 2px solid #e2e8f0;
            min-height: 55px;
        }

        .column-title {
            margin: 0;
            font-size: 1rem;
            font-weight: 600;
            color: #1e293b;
            display: flex;
            align-items: center;
            gap: 0.5rem;
        }

        .column-actions {
            display: flex;
            align-items: center;
            gap: 0.75rem;
        }

        .card-count {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 0.5rem 0.75rem;
            border-radius: 15px;
            font-size: 0.9rem;
            font-weight: 600;
            min-width: 30px;
            text-align: center;
        }

        .add-card-btn {
            background: linear-gradient(135deg, #10b981 0%, #059669 100%);
            color: white;
            border: none;
            width: 36px;
            height: 36px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            cursor: pointer;
            transition: all 0.3s ease;
            font-size: 1rem;
        }

        .add-card-btn:hover {
            transform: scale(1.1);
            box-shadow: 0 4px 15px rgba(16, 185, 129, 0.3);
        }

        /* Larger Cards Container */
        .kanban-cards-container {
            flex: 1;
            padding: 1rem;
            overflow-y: auto;
            display: flex;
            flex-direction: column;
            gap: 1rem;
        }

        /* Larger Cards for Big Screens */
        .kanban-card {
            background: white;
            border: 2px solid #e2e8f0;
            border-radius: 10px;
            padding: 1.25rem;
            cursor: pointer;
            transition: all 0.3s ease;
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.08);
            min-height: 140px;
            max-height: 220px;
            display: flex;
            flex-direction: column;
        }

        .kanban-card:hover {
            transform: translateY(-3px);
            box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
            border-color: #667eea;
        }

        /* Larger Card Header */
        .card-header {
            display: flex;
            justify-content: space-between;
            align-items: flex-start;
            margin-bottom: 0.75rem;
        }

        .card-title-section {
            flex: 1;
        }

        .card-title {
            font-size: 1.1rem !important;
            font-weight: 600 !important;
            margin: 0 !important;
            line-height: 1.4 !important;
            color: #1e293b !important;
        }

        .project-number {
            color: #667eea;
            font-weight: 700;
            margin-right: 0.5rem;
            font-size: 1rem;
        }

        .card-actions {
            display: flex;
            gap: 0.5rem;
            opacity: 0;
            transition: opacity 0.3s ease;
        }

        .kanban-card:hover .card-actions {
            opacity: 1;
        }

        .card-action-btn {
            background: none;
            border: none;
            color: #64748b;
            padding: 0.5rem;
            border-radius: 6px;
            cursor: pointer;
            transition: all 0.3s ease;
            width: 32px;
            height: 32px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 0.9rem;
        }

        .card-action-btn:hover {
            background: #f1f5f9;
            color: #1e293b;
            transform: scale(1.1);
        }

        /* Larger Comments Section */
        .card-comments {
            flex: 1;
            margin: 0.75rem 0;
            padding: 0.75rem;
            background: #f8fafc;
            border-radius: 8px;
            min-height: 60px;
            max-height: 100px;
            overflow-y: auto;
        }

        .comment-text {
            font-size: 0.95rem;
            color: #475569;
            line-height: 1.5;
        }

        .no-comments {
            font-size: 0.9rem;
            color: #94a3b8;
            font-style: italic;
            text-align: center;
            padding: 0.75rem 0;
        }

        /* Larger Footer */
        .card-footer {
            margin-top: auto;
            padding-top: 0.75rem;
            border-top: 2px solid #e2e8f0;
        }

        .card-due-date {
            font-size: 0.9rem;
            color: #64748b;
            display: flex;
            align-items: center;
            gap: 0.5rem;
            font-weight: 500;
        }

        .card-due-date.overdue {
            color: #ef4444;
            font-weight: 600;
        }

        .card-due-date i {
            font-size: 0.85rem;
        }

        /* Larger scrollbars for better visibility */
        .kanban-cards-container::-webkit-scrollbar,
        .team-sidebar::-webkit-scrollbar,
        .kanban-board::-webkit-scrollbar {
            width: 8px;
            height: 8px;
        }

        .kanban-cards-container::-webkit-scrollbar-track,
        .team-sidebar::-webkit-scrollbar-track,
        .kanban-board::-webkit-scrollbar-track {
            background: #f1f5f9;
            border-radius: 4px;
        }

        .kanban-cards-container::-webkit-scrollbar-thumb,
        .team-sidebar::-webkit-scrollbar-thumb,
        .kanban-board::-webkit-scrollbar-thumb {
            background: #cbd5e1;
            border-radius: 4px;
        }

        .kanban-cards-container::-webkit-scrollbar-thumb:hover,
        .team-sidebar::-webkit-scrollbar-thumb:hover,
        .kanban-board::-webkit-scrollbar-thumb:hover {
            background: #94a3b8;
        }


    </style>
}

<div class="kanban-layout" style="width: 100vw; margin-left: calc(-50vw + 50%);">
    <!-- Team Members Sidebar -->
    <div class="team-sidebar">
        <div class="sidebar-header">
            <h3>
                <i class="fas fa-users"></i>
                Екип
            </h3>
        </div>

        <div class="team-members">
            @foreach (var member in ViewBag.AllTeamMembers as IEnumerable<TaskManager.Web.ViewModels.Admin.AllWorkersViewModel>)
            {
                <a href="@Url.Action("Board", "Kanban", new { memberId = member.UserId })"
                   class="team-member @(member.UserId == ViewBag.CurrentMemberId ? "active" : "")">
                    @member.FirstName @member.LastName
                </a>
            }
        </div>
    </div>

    <!-- Compact Kanban Board -->
    <div class="kanban-board" id="kanbanBoard">
        @foreach (var column in Model.Columns.OrderBy(c => c.Position))
        {
            <div class="kanban-column" data-column-id="@column.Id">
                <div class="kanban-column-header" style="border-left: 4px solid @column.Color">
                    <h3 class="column-title">
                        <i class="fas fa-grip-vertical"></i>
                        @column.Name
                    </h3>
                    <div class="column-actions">
                        <span class="card-count">@column.CardCount</span>
                        <button class="add-card-btn" data-column-id="@column.Id" title="Добави карта">
                            <i class="fas fa-plus"></i>
                        </button>
                    </div>
                </div>
                
                <div class="kanban-cards-container" data-column-id="@column.Id">
                    @foreach (var card in column.Cards.OrderBy(c => c.Position))
                    {
                        <div class="kanban-card" data-card-id="@card.Id" data-position="@card.Position">
                            <!-- Project Title Header -->
                            <div class="card-header">
                                <div class="card-title-section">
                                    <h4 class="card-title">
                                        @if (card.HasGeoTask)
                                        {
                                            <span class="project-number">#@card.GeoTaskProjectNumber</span>
                                        }
                                        @card.Title
                                    </h4>
                                </div>
                                <div class="card-actions">
                                    <button class="card-action-btn edit-card" data-card-id="@card.Id" title="Редактирай">
                                        <i class="fas fa-edit"></i>
                                    </button>
                                    <button class="card-action-btn delete-card" data-card-id="@card.Id" title="Изтрий">
                                        <i class="fas fa-trash"></i>
                                    </button>
                                </div>
                            </div>

                            <!-- Comments Section (Main Content Area) -->
                            <div class="card-comments">
                                @if (!string.IsNullOrEmpty(card.Description))
                                {
                                    <div class="comment-text">@card.Description</div>
                                }
                                else
                                {
                                    <div class="no-comments">Няма коментари</div>
                                }
                            </div>

                            <!-- End Date Footer -->
                            <div class="card-footer">
                                @if (card.DueDate.HasValue)
                                {
                                    <div class="card-due-date @(card.IsOverdue ? "overdue" : "")">
                                        <i class="fas fa-calendar-alt"></i>
                                        @card.DueDate.Value.ToString("dd.MM.yyyy")
                                    </div>
                                }
                            </div>
                        </div>
                    }
                </div>
            </div>
        }
        </div>
    </div>
</div>

<!-- Add Card Modal Placeholder -->
<div id="addCardModal" class="modal fade" tabindex="-1" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <!-- Modal content will be loaded via AJAX -->
        </div>
    </div>
</div>

<!-- Edit Card Modal Placeholder -->
<div id="editCardModal" class="modal fade" tabindex="-1" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <!-- Modal content will be loaded via AJAX -->
        </div>
    </div>
</div>

@section Scripts {
    <script>
        // Basic Kanban functionality
        $(document).ready(function() {
            // Add card button click
            $('.add-card-btn, #addCardBtn').on('click', function() {
                var columnId = $(this).data('column-id');

                // If no column ID, get the first column ID from the page
                if (!columnId) {
                    columnId = $('.kanban-column').first().data('column-id');
                }

                console.log('Selected column ID:', columnId);
                
                // Load create card modal
                $.get('@Url.Action("CreateCardModal", "Kanban")', { columnId: columnId })
                    .done(function(data) {
                        $('#addCardModal .modal-content').html(data);
                        $('#addCardModal').modal('show');
                    })
                    .fail(function() {
                        toastr.error('Грешка при зареждането на формата за нова карта');
                    });
            });
            
            // Edit card button click
            $('.edit-card').on('click', function() {
                var cardId = $(this).data('card-id');
                
                // Load edit card modal
                $.get('@Url.Action("EditCardModal", "Kanban")', { id: cardId })
                    .done(function(data) {
                        $('#editCardModal .modal-content').html(data);
                        $('#editCardModal').modal('show');
                    })
                    .fail(function() {
                        toastr.error('Грешка при зареждането на формата за редактиране');
                    });
            });
            
            // Delete card button click
            $('.delete-card').on('click', function() {
                var cardId = $(this).data('card-id');
                
                if (confirm('Сигурни ли сте, че искате да изтриете тази карта?')) {
                    $.post('@Url.Action("DeleteCard", "Kanban")', 
                        { 
                            cardId: cardId,
                            __RequestVerificationToken: $('input[name="__RequestVerificationToken"]').val()
                        })
                        .done(function(response) {
                            if (response.success) {
                                location.reload(); // Refresh the board
                                toastr.success('Картата беше изтрита успешно');
                            } else {
                                toastr.error(response.message || 'Грешка при изтриването на картата');
                            }
                        })
                        .fail(function() {
                            toastr.error('Грешка при изтриването на картата');
                        });
                }
            });
            
            // Refresh board button
            $('#refreshBoardBtn').on('click', function() {
                location.reload();
            });
            
            // Initialize tooltips
            $('[title]').tooltip();
        });

        // Make cards clickable to open edit modal
        $(document).on('click', '.kanban-card', function(e) {
            // Prevent opening modal when clicking on buttons or other interactive elements
            if ($(e.target).closest('button, .btn, .dropdown').length > 0) {
                return;
            }

            const cardId = $(this).data('card-id');
            if (cardId) {
                // Load edit card modal directly
                $.get('@Url.Action("EditCardModal", "Kanban")', { id: cardId })
                    .done(function(data) {
                        $('#editCardModal .modal-content').html(data);
                        $('#editCardModal').modal('show');
                    })
                    .fail(function() {
                        toastr.error('Грешка при зареждането на формата за редактиране');
                    });
            }
        });

        // Add hover effect to cards
        $(document).on('mouseenter', '.kanban-card', function() {
            $(this).addClass('card-hover');
        }).on('mouseleave', '.kanban-card', function() {
            $(this).removeClass('card-hover');
        });
    </script>
}

<!-- Include modals -->
@await Html.PartialAsync("_ColumnSelectionModal")

<!-- Edit Card Modal -->
<div class="modal fade" id="editCardModal" tabindex="-1" aria-labelledby="editCardModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            @await Html.PartialAsync("_EditCardModal", new TaskManager.Web.ViewModels.Kanban.UpdateKanbanCardViewModel())
        </div>
    </div>
</div>
