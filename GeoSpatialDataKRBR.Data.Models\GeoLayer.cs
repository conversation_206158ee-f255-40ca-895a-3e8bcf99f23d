namespace GeoSpatialDataKRBR.Data.Models
{
    using System.ComponentModel.DataAnnotations;
    using System.ComponentModel.DataAnnotations.Schema;
    using static Common.EntityValidationConstants.GeoLayer;

    public class GeoLayer
    {
        public GeoLayer()
        {
            this.Id = Guid.NewGuid();
            this.UserLayerPreferences = new HashSet<UserLayerPreference>();
            this.GeoLayerGroupLayers = new HashSet<GeoLayerGroupLayer>();
        }

        [Key]
        public Guid Id { get; set; }

        [Required]
        [MaxLength(NameMaxLength)]
        public string Name { get; set; } = null!;

        [MaxLength(DescriptionMaxLength)]
        public string? Description { get; set; }

        [Required]
        [MaxLength(LayerNameMaxLength)]
        public string LayerName { get; set; } = null!;

        [Required]
        [MaxLength(WorkspaceMaxLength)]
        public string Workspace { get; set; } = null!;

        [Required]
        [MaxLength(WmsUrlMaxLength)]
        public string WmsUrl { get; set; } = null!;

        [MaxLength(WfsUrlMaxLength)]
        public string? WfsUrl { get; set; }

        [MaxLength(StyleNameMaxLength)]
        public string? StyleName { get; set; }

        [Required]
        public string LayerType { get; set; } = null!; // WMS, WFS, TILE

        public bool IsVisible { get; set; } = true;

        public bool IsBaseLayer { get; set; } = false;

        public int DisplayOrder { get; set; } = 0;

        public double? MinZoom { get; set; }

        public double? MaxZoom { get; set; }

        public double? Opacity { get; set; } = 1.0;

        public DateTime CreatedOn { get; set; } = DateTime.UtcNow;

        public DateTime? ModifiedOn { get; set; }

        public Guid? GeoServerConfigurationId { get; set; }

        public virtual GeoServerConfiguration? GeoServerConfiguration { get; set; }

        // Navigation properties
        public virtual ICollection<UserLayerPreference> UserLayerPreferences { get; set; }
        public virtual ICollection<GeoLayerGroupLayer> GeoLayerGroupLayers { get; set; }
    }
}
