/* _content/GeoSpatialDataKRBR/Views/Shared/_Layout.cshtml.rz.scp.css */
/* Please see documentation at https://docs.microsoft.com/aspnet/core/client-side/bundling-and-minification
for details on configuring this project to bundle and minify static web assets. */

a.navbar-brand[b-zu8bfs4g8e] {
  white-space: normal;
  text-align: center;
  word-break: break-all;
}

a[b-zu8bfs4g8e] {
  color: #0077cc;
}

.btn-primary[b-zu8bfs4g8e] {
  color: #fff;
  background-color: #1b6ec2;
  border-color: #1861ac;
}

.nav-pills .nav-link.active[b-zu8bfs4g8e], .nav-pills .show > .nav-link[b-zu8bfs4g8e] {
  color: #fff;
  background-color: #1b6ec2;
  border-color: #1861ac;
}

.border-top[b-zu8bfs4g8e] {
  border-top: 1px solid #e5e5e5;
}
.border-bottom[b-zu8bfs4g8e] {
  border-bottom: 1px solid #e5e5e5;
}

.box-shadow[b-zu8bfs4g8e] {
  box-shadow: 0 .25rem .75rem rgba(0, 0, 0, .05);
}

button.accept-policy[b-zu8bfs4g8e] {
  font-size: 1rem;
  line-height: inherit;
}

.footer[b-zu8bfs4g8e] {
  position: absolute;
  bottom: 0;
  width: 100%;
  white-space: nowrap;
  line-height: 60px;
}
