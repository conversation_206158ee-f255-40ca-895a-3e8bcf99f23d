﻿@using static TaskManager.Common.NotificationMessages;

@if (TempData.ContainsKey(ErrorMessage))
{
        <script> toastr.error('@TempData[ErrorMessage]'); </script>
}
@if (TempData.ContainsKey(WarningMessage))
{
        <script> toastr.warning('@TempData[WarningMessage]'); </script>
}
@if (TempData.ContainsKey(InfoMessage))
{
        <script> toastr.info('@TempData[InfoMessage]'); </script>
}
@if (TempData.ContainsKey(SuccsessMessage))
{
        <script> toastr.success('@TempData[SuccsessMessage]'); </script>
}