﻿namespace TaskManager.Services.Tests
{
    using Microsoft.EntityFrameworkCore;
    using Moq;
    using TaskManager.Data;
    using TaskManager.Data.Models;
    using TaskManager.Services.Data;
    using TaskManager.Services.Data.Interfaces;
    using TaskManager.Web.ViewModels.GeoTask;
    using Type = TaskManager.Data.Models.Type;

    public class GeoTaskServiceTests
    {
        private DbContextOptions<TaskManagerDbContext> dbContextOptions;
        private TaskManagerDbContext dbContext;
        private Mock<IKanbanService> mockKanbanService;

        private IGeoTaskService geoTaskService;
        private ITypeService typeService;

        [OneTimeSetUp]
        public void OneTimeSetUp()
        {
            this.dbContextOptions = new DbContextOptionsBuilder<TaskManagerDbContext>().UseInMemoryDatabase("TaskManagerInMemory" + Guid.NewGuid().ToString()).Options;

            this.dbContext = new TaskManagerDbContext(this.dbContextOptions);
            this.dbContext.Database.EnsureCreated();

            this.mockKanbanService = new Mock<IKanbanService>();
            this.geoTaskService = new GeoTaskService(this.dbContext, this.mockKanbanService.Object);
            this.typeService = new TypeService(this.dbContext);
        }

        [SetUp]
        public void Setup()
        {

        }
        [Test]
        public async Task GetAllGeoTaskFilteredAsyncShoudReturnAllGeoTaskFilteredAndPageServiceModel()
        {
            IEnumerable<TaskViewModel> geoTaskViewModels = await this.geoTaskService.GetAllGeoTaskAsync();
            IEnumerable<string> typeViewModels = await this.typeService.GetAllTypeNamesAsync();

            AllTaskQueryModel allTaskQueryModel = new AllTaskQueryModel()
            {
                CurrentPage= 1,
                SearchString="",
                TaskPerPage=10,
                Tasks=geoTaskViewModels,
                TotalTaskss=25,
                Types=typeViewModels,
            };

            AllGeoTaskFilteredAndPageServiceModel result = await this.geoTaskService.GetAllGeoTaskFilteredAsync(allTaskQueryModel);

            Assert.That(result.GetType(), Is.EqualTo(typeof(AllGeoTaskFilteredAndPageServiceModel)));
        }
        [Test]
        public async Task GetAllGeoTaskAsyncShoudReturnIEnumOfTaskViewModel()
        {
            IEnumerable<TaskViewModel> geoTaskViewModels = await this.geoTaskService.GetAllGeoTaskAsync();

            CollectionAssert.AllItemsAreInstancesOfType(geoTaskViewModels, typeof(TaskViewModel));
            CollectionAssert.AllItemsAreNotNull(geoTaskViewModels);
            CollectionAssert.AllItemsAreUnique(geoTaskViewModels);
        }
        [Test]
        public async Task GetGeoTaskByIdAsyncShoudReturnEditGeoTaskViewModel()
        {
            GeoTask geoTask = await this.dbContext.GeoTasks.FirstAsync();
            string geoTaskId = geoTask.Id.ToString();

            EditGeoTaskViewModel result = await this.geoTaskService.GetGeoTaskByIdAsync(geoTaskId);

            Assert.That(result.GetType(), Is.EqualTo(typeof(EditGeoTaskViewModel)));
        }
        [Test]
        public async Task IsTaskExistByIdAsyncShoudReturnTrue()
        {
            GeoTask geoTask = await this.dbContext.GeoTasks.FirstAsync();
            string geoTaskId = geoTask.Id.ToString();

            bool result = await this.geoTaskService.IsTaskExistByIdAsync(geoTaskId);

            Assert.IsTrue(result);
        }
        [Test]
        public async Task IsTaskExistByIdAsyncShoudReturnFalse()
        {
            GeoTask geoTask = await this.dbContext.GeoTasks.FirstAsync();
            string geoTaskId = geoTask.Id.ToString()+"tr";

            bool result = await this.geoTaskService.IsTaskExistByIdAsync(geoTaskId);

            Assert.IsFalse(result);
        }
        [Test]
        public async Task GetMyTaskByWorkerIdAsyncShoudReturnIEnumOfTaskViewModel()
        {
            GeoTask geoTask = await this.dbContext.GeoTasks.FirstAsync();
            string workerId = geoTask.WorkerId.ToString();

            IEnumerable<TaskViewModel> geoTaskViewModels = await this.geoTaskService.GetMyTaskByWorkerIdAsync(workerId);

            CollectionAssert.AllItemsAreInstancesOfType(geoTaskViewModels, typeof(TaskViewModel));
            CollectionAssert.AllItemsAreNotNull(geoTaskViewModels);
            CollectionAssert.AllItemsAreUnique(geoTaskViewModels);
        }

        [Test]
        public async Task AddNewTask_ShouldCreateNewGeoTaskSuccessfully()
        {
            // Arrange
            int initialTaskCount = await this.dbContext.GeoTasks.CountAsync();

            // Act
            EditGeoTaskViewModel result = await this.geoTaskService.AddNewTask();

            // Assert
            Assert.IsNotNull(result);
            Assert.IsNotNull(result.Id);
            Assert.That(result.ProjectNumber, Is.EqualTo(initialTaskCount + 1));

            // Verify task was actually saved to database
            int finalTaskCount = await this.dbContext.GeoTasks.CountAsync();
            Assert.That(finalTaskCount, Is.EqualTo(initialTaskCount + 1));

            // Verify the task exists in database
            bool taskExists = await this.geoTaskService.IsTaskExistByIdAsync(result.Id);
            Assert.IsTrue(taskExists);
        }

        [Test]
        public async Task EditGeoTaskByIdAsync_ShouldUpdateTaskSuccessfully()
        {
            // Arrange
            GeoTask existingTask = await this.dbContext.GeoTasks.FirstAsync();
            string taskId = existingTask.Id.ToString();

            EditGeoTaskViewModel editModel = new EditGeoTaskViewModel()
            {
                Id = taskId,
                Adrress = "Updated Address 123",
                ProjectNumber = existingTask.ProjectNumber,
                Price = 999.99m,
                CreateDate = existingTask.CreateDate,
                EndDate = DateTime.UtcNow.AddDays(30),
                IdKKKR = "UPDATED123",
                quantity = 5,
                Note = "Updated note for testing",
                ClientId = existingTask.ClientId.ToString(),
                WorkerId = existingTask.WorkerId.ToString(),
                CheckerId = existingTask.CheckerId.ToString(),
                StatusId = existingTask.StatusId,
                TypeId = existingTask.TypeId
            };

            // Act
            await this.geoTaskService.EditGeoTaskByIdAsync(editModel);

            // Assert
            GeoTask updatedTask = await this.dbContext.GeoTasks.FirstAsync(gt => gt.Id.ToString() == taskId);

            Assert.That(updatedTask.Adrress, Is.EqualTo("Updated Address 123"));
            Assert.That(updatedTask.Price, Is.EqualTo(999.99m));
            Assert.That(updatedTask.IdKKKR, Is.EqualTo("UPDATED123"));
            Assert.That(updatedTask.quantity, Is.EqualTo(5));
            Assert.That(updatedTask.Note, Is.EqualTo("Updated note for testing"));
        }

        [Test]
        public void EditGeoTaskByIdAsync_WithInvalidId_ShouldThrowException()
        {
            // Arrange
            EditGeoTaskViewModel editModel = new EditGeoTaskViewModel()
            {
                Id = "invalid-id-that-does-not-exist",
                Adrress = "Test Address",
                ProjectNumber = 1,
                Price = 100m,
                CreateDate = DateTime.UtcNow,
                EndDate = DateTime.UtcNow.AddDays(7),
                IdKKKR = "TEST123",
                quantity = 1,
                Note = "Test note",
                ClientId = Guid.NewGuid().ToString(),
                WorkerId = Guid.NewGuid().ToString(),
                CheckerId = Guid.NewGuid().ToString(),
                StatusId = 1,
                TypeId = 1
            };

            // Act & Assert
            Assert.ThrowsAsync<InvalidOperationException>(async () =>
                await this.geoTaskService.EditGeoTaskByIdAsync(editModel));
        }

        [Test]
        public void EditGeoTaskByIdAsync_WithInvalidGuidFormat_ShouldThrowException()
        {
            // Arrange
            var existingTask = this.dbContext.GeoTasks.First();

            EditGeoTaskViewModel editModel = new EditGeoTaskViewModel()
            {
                Id = existingTask.Id.ToString(),
                Adrress = "Test Address",
                ProjectNumber = existingTask.ProjectNumber,
                Price = 100m,
                CreateDate = existingTask.CreateDate,
                EndDate = DateTime.UtcNow.AddDays(7),
                IdKKKR = "TEST123",
                quantity = 1,
                Note = "Test note",
                ClientId = "invalid-guid-format", // Invalid GUID
                WorkerId = existingTask.WorkerId.ToString(),
                CheckerId = existingTask.CheckerId.ToString(),
                StatusId = existingTask.StatusId,
                TypeId = existingTask.TypeId
            };

            // Act & Assert
            Assert.ThrowsAsync<FormatException>(async () =>
                await this.geoTaskService.EditGeoTaskByIdAsync(editModel));
        }

        [Test]
        public async Task EditGeoTaskByIdAsync_WithEmptyValues_ShouldUpdateSuccessfully()
        {
            // Arrange
            var existingTask = this.dbContext.GeoTasks.First();
            string taskId = existingTask.Id.ToString();

            EditGeoTaskViewModel editModel = new EditGeoTaskViewModel()
            {
                Id = taskId,
                Adrress = "", // Empty string - this should be allowed and updated
                ProjectNumber = existingTask.ProjectNumber,
                Price = 100m,
                CreateDate = existingTask.CreateDate,
                EndDate = DateTime.UtcNow.AddDays(7),
                IdKKKR = "", // Empty string - this should be allowed and updated
                quantity = 1,
                Note = "", // Empty string - this should be allowed and updated
                ClientId = existingTask.ClientId.ToString(),
                WorkerId = existingTask.WorkerId.ToString(),
                CheckerId = existingTask.CheckerId.ToString(),
                StatusId = existingTask.StatusId,
                TypeId = existingTask.TypeId
            };

            // Act
            await this.geoTaskService.EditGeoTaskByIdAsync(editModel);

            // Assert
            var updatedTask = await this.dbContext.GeoTasks.FirstAsync(gt => gt.Id.ToString() == taskId);

            Assert.That(updatedTask.Adrress, Is.EqualTo(""), "Empty address should be saved");
            Assert.That(updatedTask.IdKKKR, Is.EqualTo(""), "Empty IdKKKR should be saved");
            Assert.That(updatedTask.Note, Is.EqualTo(""), "Empty note should be saved");
            Assert.That(updatedTask.Price, Is.EqualTo(100m), "Price should be updated");
        }

        [Test]
        public async Task EditGeoTaskByIdAsync_WithNonExistentClientId_ShouldUpdateSuccessfully()
        {
            // Arrange
            var existingTask = this.dbContext.GeoTasks.First();
            string taskId = existingTask.Id.ToString();

            EditGeoTaskViewModel editModel = new EditGeoTaskViewModel()
            {
                Id = taskId,
                Adrress = "Valid Address",
                ProjectNumber = existingTask.ProjectNumber,
                Price = 100m,
                CreateDate = existingTask.CreateDate,
                EndDate = DateTime.UtcNow.AddDays(7),
                IdKKKR = "VALID123",
                quantity = 1,
                Note = "Valid note",
                ClientId = "00000000-0000-0000-0000-000000000000", // Non-existent but valid GUID format
                WorkerId = existingTask.WorkerId.ToString(),
                CheckerId = existingTask.CheckerId.ToString(),
                StatusId = existingTask.StatusId,
                TypeId = existingTask.TypeId
            };

            // Act - The service allows this because it doesn't validate foreign key existence
            await this.geoTaskService.EditGeoTaskByIdAsync(editModel);

            // Assert - Verify the update was successful even with non-existent ClientId
            var updatedTask = await this.dbContext.GeoTasks.FirstAsync(gt => gt.Id.ToString() == taskId);
            Assert.That(updatedTask.Adrress, Is.EqualTo("Valid Address"), "Address should be updated");
            Assert.That(updatedTask.ClientId.ToString(), Is.EqualTo("00000000-0000-0000-0000-000000000000"), "ClientId should be updated");
        }

        [Test]
        public async Task GetGeoTaskByIdAsync_AfterEdit_ShouldReturnUpdatedData()
        {
            // Arrange
            GeoTask existingTask = await this.dbContext.GeoTasks.FirstAsync();
            string taskId = existingTask.Id.ToString();

            EditGeoTaskViewModel editModel = new EditGeoTaskViewModel()
            {
                Id = taskId,
                Adrress = "Verification Address 456",
                ProjectNumber = existingTask.ProjectNumber,
                Price = 777.77m,
                CreateDate = existingTask.CreateDate,
                EndDate = DateTime.UtcNow.AddDays(15),
                IdKKKR = "VERIFY789",
                quantity = 3,
                Note = "Verification note",
                ClientId = existingTask.ClientId.ToString(),
                WorkerId = existingTask.WorkerId.ToString(),
                CheckerId = existingTask.CheckerId.ToString(),
                StatusId = existingTask.StatusId,
                TypeId = existingTask.TypeId
            };

            // Act
            await this.geoTaskService.EditGeoTaskByIdAsync(editModel);
            EditGeoTaskViewModel retrievedTask = await this.geoTaskService.GetGeoTaskByIdAsync(taskId);

            // Assert
            Assert.That(retrievedTask.Adrress, Is.EqualTo("Verification Address 456"));
            Assert.That(retrievedTask.Price, Is.EqualTo(777.77m));
            Assert.That(retrievedTask.IdKKKR, Is.EqualTo("VERIFY789"));
            Assert.That(retrievedTask.quantity, Is.EqualTo(3));
            Assert.That(retrievedTask.Note, Is.EqualTo("Verification note"));
        }
    }

}