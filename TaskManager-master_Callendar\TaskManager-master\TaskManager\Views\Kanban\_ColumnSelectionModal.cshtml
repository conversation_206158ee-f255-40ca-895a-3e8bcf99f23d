<!-- Simple Column Selection Modal -->
<div class="modal fade" id="columnSelectionModal" tabindex="-1" aria-labelledby="columnSelectionModalLabel" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="columnSelectionModalLabel">
                    <i class="fas fa-columns"></i>
                    Изберете колона
                </h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <p class="mb-3">
                    <strong>Картата ще бъде създадена в личната дъска на:</strong>
                    <span id="selectedWorkerName" class="text-primary"></span>
                </p>

                <div class="mb-3">
                    <label for="columnSelect" class="form-label">Изберете колона:</label>
                    <select id="columnSelect" class="form-select">
                        <option value="">-- Изберете колона --</option>
                    </select>
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">
                    <i class="fas fa-times"></i>
                    Отказ
                </button>
                <button type="button" id="confirmColumnSelection" class="btn btn-primary" disabled>
                    <i class="fas fa-check"></i>
                    Създай карта
                </button>
            </div>
        </div>
    </div>
</div>

<script>
let selectedColumnId = null;
let pendingCardData = null;

function showColumnSelectionModal(workerUserId, workerName, cardData) {
    pendingCardData = cardData;
    $('#selectedWorkerName').text(workerName);

    console.log('Loading columns for worker:', { userId: workerUserId, name: workerName });

    // Load worker's columns
    $.get('@Url.Action("GetWorkerColumns", "Kanban")', { userId: workerUserId })
        .done(function(response) {
            console.log('Column response:', response);

            if (response.success) {
                if (response.columns && response.columns.length > 0) {
                    populateColumnDropdown(response.columns);
                    $('#columnSelectionModal').modal('show');
                } else {
                    toastr.error('Няма налични колони за този работник');
                    console.error('No columns returned:', response);
                }
            } else {
                toastr.error('Грешка при зареждането на колоните: ' + response.message);
                console.error('API error:', response);
            }
        })
        .fail(function(xhr, status, error) {
            toastr.error('Грешка при зареждането на колоните');
            console.error('AJAX error:', { xhr, status, error });
        });
}

function populateColumnDropdown(columns) {
    console.log('Populating dropdown with columns:', columns);

    const columnSelect = $('#columnSelect');
    columnSelect.empty();
    columnSelect.append('<option value="">-- Изберете колона --</option>');

    if (!columns || columns.length === 0) {
        columnSelect.append('<option value="">Няма налични колони</option>');
        return;
    }

    columns.forEach(function(column) {
        console.log('Adding column:', column);

        // Handle both possible property names (Id/id, Name/name)
        const columnId = column.Id || column.id;
        const columnName = column.Name || column.name || 'Неименувана колона';

        if (columnId) {
            const option = $(`<option value="${columnId}">${columnName}</option>`);
            columnSelect.append(option);
        } else {
            console.warn('Column missing ID:', column);
        }
    });

    // Reset selection
    selectedColumnId = null;
    $('#confirmColumnSelection').prop('disabled', true);
}

// Handle dropdown change
$(document).on('change', '#columnSelect', function() {
    selectedColumnId = $(this).val();
    $('#confirmColumnSelection').prop('disabled', !selectedColumnId);
});

// Handle confirm button click
$('#confirmColumnSelection').on('click', function() {
    if (selectedColumnId && pendingCardData) {
        // Update the card data with the selected column
        pendingCardData.ColumnId = selectedColumnId;
        
        // Create the card
        $.post('@Url.Action("CreateCard", "Kanban")', pendingCardData)
            .done(function(response) {
                if (response.success) {
                    $('#columnSelectionModal').modal('hide');
                    $('#addCardModal').modal('hide');
                    toastr.success('Картата беше създадена успешно в личната дъска на работника');
                    
                    // Optionally redirect to the worker's board
                    if (confirm('Искате ли да отидете до личната дъска на работника?')) {
                        window.location.href = '@Url.Action("Board", "Kanban")' + '?memberId=' + pendingCardData.AssignedToId;
                    } else {
                        location.reload(); // Refresh current board
                    }
                } else {
                    toastr.error(response.message || 'Грешка при създаването на картата');
                }
            })
            .fail(function() {
                toastr.error('Грешка при създаването на картата');
            });
    }
});

// Reset modal state when closed
$('#columnSelectionModal').on('hidden.bs.modal', function() {
    selectedColumnId = null;
    pendingCardData = null;
    $('#confirmColumnSelection').prop('disabled', true);
    $('#columnSelect').val('');
});
</script>
