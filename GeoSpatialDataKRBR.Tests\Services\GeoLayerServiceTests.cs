using Microsoft.EntityFrameworkCore;
using Xunit;
using FluentAssertions;
using GeoSpatialDataKRBR.Data;
using GeoSpatialDataKRBR.Data.Models;
using GeoSpatialDataKRBR.Services.Data;
using GeoSpatialDataKRBR.Services.Data.Interfaces;
using GeoSpatialDataKRBR.Web.ViewModels.GeoLayer;

namespace GeoSpatialDataKRBR.Tests.Services
{
    public class GeoLayerServiceTests : IDisposable
    {
        private readonly GeoSpatialDbContext _context;
        private readonly IGeoLayerService _geoLayerService;

        public GeoLayerServiceTests()
        {
            var options = new DbContextOptionsBuilder<GeoSpatialDbContext>()
                .UseInMemoryDatabase(databaseName: Guid.NewGuid().ToString())
                .Options;

            _context = new GeoSpatialDbContext(options);
            _geoLayerService = new GeoLayerService(_context);
        }

        [Fact]
        public async Task GetAllLayersAsync_ShouldReturnAllLayers()
        {
            // Arrange
            var layers = new List<GeoLayer>
            {
                new GeoLayer
                {
                    Id = Guid.NewGuid(),
                    Name = "Test Layer 1",
                    LayerName = "test1",
                    Workspace = "test",
                    WmsUrl = "http://test.com/wms",
                    LayerType = "WMS",
                    IsVisible = true,
                    IsBaseLayer = false,
                    DisplayOrder = 1
                },
                new GeoLayer
                {
                    Id = Guid.NewGuid(),
                    Name = "Test Layer 2",
                    LayerName = "test2",
                    Workspace = "test",
                    WmsUrl = "http://test.com/wms2",
                    LayerType = "TILE",
                    IsVisible = false,
                    IsBaseLayer = true,
                    DisplayOrder = 2
                }
            };

            await _context.GeoLayers.AddRangeAsync(layers);
            await _context.SaveChangesAsync();

            // Act
            var result = await _geoLayerService.GetAllLayersAsync();

            // Assert
            result.Should().NotBeNull();
            result.Should().HaveCount(2);
            result.First().Name.Should().Be("Test Layer 1");
        }

        [Fact]
        public async Task GetLayerByIdAsync_WithValidId_ShouldReturnLayer()
        {
            // Arrange
            var layerId = Guid.NewGuid();
            var layer = new GeoLayer
            {
                Id = layerId,
                Name = "Test Layer",
                LayerName = "test",
                Workspace = "test",
                WmsUrl = "http://test.com/wms",
                LayerType = "WMS",
                IsVisible = true,
                IsBaseLayer = false,
                DisplayOrder = 1
            };

            await _context.GeoLayers.AddAsync(layer);
            await _context.SaveChangesAsync();

            // Act
            var result = await _geoLayerService.GetLayerByIdAsync(layerId);

            // Assert
            result.Should().NotBeNull();
            result!.Id.Should().Be(layerId);
            result.Name.Should().Be("Test Layer");
        }

        [Fact]
        public async Task GetLayerByIdAsync_WithInvalidId_ShouldReturnNull()
        {
            // Arrange
            var invalidId = Guid.NewGuid();

            // Act
            var result = await _geoLayerService.GetLayerByIdAsync(invalidId);

            // Assert
            result.Should().BeNull();
        }

        [Fact]
        public async Task GetVisibleLayersAsync_ShouldReturnOnlyVisibleLayers()
        {
            // Arrange
            var layers = new List<GeoLayer>
            {
                new GeoLayer
                {
                    Id = Guid.NewGuid(),
                    Name = "Visible Layer",
                    LayerName = "visible",
                    Workspace = "test",
                    WmsUrl = "http://test.com/wms",
                    LayerType = "TILE",
                    IsVisible = true,
                    IsBaseLayer = true,
                    DisplayOrder = 1
                },
                new GeoLayer
                {
                    Id = Guid.NewGuid(),
                    Name = "Hidden Layer",
                    LayerName = "hidden",
                    Workspace = "test",
                    WmsUrl = "http://test.com/wms2",
                    LayerType = "WMS",
                    IsVisible = false,
                    IsBaseLayer = false,
                    DisplayOrder = 2
                }
            };

            await _context.GeoLayers.AddRangeAsync(layers);
            await _context.SaveChangesAsync();

            // Act
            var result = await _geoLayerService.GetVisibleLayersAsync();

            // Assert
            result.Should().NotBeNull();
            result.Should().HaveCount(1);
            result.First().Name.Should().Be("Visible Layer");
            result.First().IsVisible.Should().BeTrue();
        }

        [Fact]
        public async Task GetLayersByUserPreferencesAsync_ShouldReturnLayersBasedOnUserPreferences()
        {
            // Arrange
            var userId = Guid.NewGuid();
            var layers = new List<GeoLayer>
            {
                new GeoLayer
                {
                    Id = Guid.NewGuid(),
                    Name = "User Layer 1",
                    LayerName = "user1",
                    Workspace = "test",
                    WmsUrl = "http://test.com/wms",
                    LayerType = "TILE",
                    IsVisible = true,
                    IsBaseLayer = true,
                    DisplayOrder = 1
                },
                new GeoLayer
                {
                    Id = Guid.NewGuid(),
                    Name = "User Layer 2",
                    LayerName = "user2",
                    Workspace = "test",
                    WmsUrl = "http://test.com/wms2",
                    LayerType = "WMS",
                    IsVisible = true,
                    IsBaseLayer = false,
                    DisplayOrder = 2
                }
            };

            await _context.GeoLayers.AddRangeAsync(layers);
            await _context.SaveChangesAsync();

            // Act
            var result = await _geoLayerService.GetLayersByUserPreferencesAsync(userId);

            // Assert
            result.Should().NotBeNull();
            result.Should().HaveCount(2);
            result.First().Name.Should().Be("User Layer 1");
            result.Should().Contain(l => l.Name == "User Layer 2");
        }

        public void Dispose()
        {
            _context.Dispose();
        }
    }
}
