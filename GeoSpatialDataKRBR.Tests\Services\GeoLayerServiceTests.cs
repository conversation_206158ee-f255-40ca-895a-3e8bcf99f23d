using Microsoft.EntityFrameworkCore;
using Xunit;
using FluentAssertions;
using GeoSpatialDataKRBR.Data;
using GeoSpatialDataKRBR.Data.Models;
using GeoSpatialDataKRBR.Services.Data;
using GeoSpatialDataKRBR.Services.Data.Interfaces;
using GeoSpatialDataKRBR.Web.ViewModels.GeoLayer;

namespace GeoSpatialDataKRBR.Tests.Services
{
    public class GeoLayerServiceTests : IDisposable
    {
        private readonly GeoSpatialDbContext _context;
        private readonly IGeoLayerService _geoLayerService;

        public GeoLayerServiceTests()
        {
            var options = new DbContextOptionsBuilder<GeoSpatialDbContext>()
                .UseInMemoryDatabase(databaseName: Guid.NewGuid().ToString())
                .Options;

            _context = new GeoSpatialDbContext(options);
            _geoLayerService = new GeoLayerService(_context);
        }

        [Fact]
        public async Task GetAllLayersAsync_ShouldReturnAllLayers()
        {
            // Arrange
            var layers = new List<GeoLayer>
            {
                new GeoLayer
                {
                    Id = Guid.NewGuid(),
                    Name = "Test Layer 1",
                    LayerName = "test1",
                    Workspace = "test",
                    WmsUrl = "http://test.com/wms",
                    LayerType = "WMS",
                    IsVisible = true,
                    IsBaseLayer = false,
                    DisplayOrder = 1
                },
                new GeoLayer
                {
                    Id = Guid.NewGuid(),
                    Name = "Test Layer 2",
                    LayerName = "test2",
                    Workspace = "test",
                    WmsUrl = "http://test.com/wms2",
                    LayerType = "TILE",
                    IsVisible = false,
                    IsBaseLayer = true,
                    DisplayOrder = 2
                }
            };

            await _context.GeoLayers.AddRangeAsync(layers);
            await _context.SaveChangesAsync();

            // Act
            var result = await _geoLayerService.GetAllLayersAsync();

            // Assert
            result.Should().NotBeNull();
            result.Should().HaveCount(2);
            result.First().Name.Should().Be("Test Layer 1");
        }

        [Fact]
        public async Task GetLayerByIdAsync_WithValidId_ShouldReturnLayer()
        {
            // Arrange
            var layerId = Guid.NewGuid();
            var layer = new GeoLayer
            {
                Id = layerId,
                Name = "Test Layer",
                LayerName = "test",
                Workspace = "test",
                WmsUrl = "http://test.com/wms",
                LayerType = "WMS",
                IsVisible = true,
                IsBaseLayer = false,
                DisplayOrder = 1
            };

            await _context.GeoLayers.AddAsync(layer);
            await _context.SaveChangesAsync();

            // Act
            var result = await _geoLayerService.GetLayerByIdAsync(layerId);

            // Assert
            result.Should().NotBeNull();
            result!.Id.Should().Be(layerId);
            result.Name.Should().Be("Test Layer");
        }

        [Fact]
        public async Task GetLayerByIdAsync_WithInvalidId_ShouldReturnNull()
        {
            // Arrange
            var invalidId = Guid.NewGuid();

            // Act
            var result = await _geoLayerService.GetLayerByIdAsync(invalidId);

            // Assert
            result.Should().BeNull();
        }

        [Fact]
        public async Task GetVisibleLayersAsync_ShouldReturnOnlyVisibleLayers()
        {
            // Arrange
            var layers = new List<GeoLayer>
            {
                new GeoLayer
                {
                    Id = Guid.NewGuid(),
                    Name = "Visible Layer",
                    LayerName = "visible",
                    Workspace = "test",
                    WmsUrl = "http://test.com/wms",
                    LayerType = "TILE",
                    IsVisible = true,
                    IsBaseLayer = true,
                    DisplayOrder = 1
                },
                new GeoLayer
                {
                    Id = Guid.NewGuid(),
                    Name = "Hidden Layer",
                    LayerName = "hidden",
                    Workspace = "test",
                    WmsUrl = "http://test.com/wms2",
                    LayerType = "WMS",
                    IsVisible = false,
                    IsBaseLayer = false,
                    DisplayOrder = 2
                }
            };

            await _context.GeoLayers.AddRangeAsync(layers);
            await _context.SaveChangesAsync();

            // Act
            var result = await _geoLayerService.GetVisibleLayersAsync();

            // Assert
            result.Should().NotBeNull();
            result.Should().HaveCount(1);
            result.First().Name.Should().Be("Visible Layer");
            result.First().IsVisible.Should().BeTrue();
        }

        [Fact]
        public async Task GetLayersByUserPreferencesAsync_ShouldReturnLayersBasedOnUserPreferences()
        {
            // Arrange
            var userId = Guid.NewGuid();
            var layers = new List<GeoLayer>
            {
                new GeoLayer
                {
                    Id = Guid.NewGuid(),
                    Name = "User Layer 1",
                    LayerName = "user1",
                    Workspace = "test",
                    WmsUrl = "http://test.com/wms",
                    LayerType = "TILE",
                    IsVisible = true,
                    IsBaseLayer = true,
                    DisplayOrder = 1
                },
                new GeoLayer
                {
                    Id = Guid.NewGuid(),
                    Name = "User Layer 2",
                    LayerName = "user2",
                    Workspace = "test",
                    WmsUrl = "http://test.com/wms2",
                    LayerType = "WMS",
                    IsVisible = true,
                    IsBaseLayer = false,
                    DisplayOrder = 2
                }
            };

            await _context.GeoLayers.AddRangeAsync(layers);
            await _context.SaveChangesAsync();

            // Act
            var result = await _geoLayerService.GetLayersByUserPreferencesAsync(userId);

            // Assert
            result.Should().NotBeNull();
            result.Should().HaveCount(2);
            result.First().Name.Should().Be("User Layer 1");
            result.Should().Contain(l => l.Name == "User Layer 2");
        }

        [Fact]
        public async Task GetAllLayersAsync_ShouldSeparateBaseAndGeneralLayers()
        {
            // Arrange
            var baseLayers = new List<GeoLayer>
            {
                new GeoLayer
                {
                    Id = Guid.NewGuid(),
                    Name = "Basemap",
                    LayerName = "basemap",
                    Workspace = "base",
                    WmsUrl = "http://test.com/basemap",
                    LayerType = "Tile",
                    IsVisible = true,
                    IsBaseLayer = true,
                    DisplayOrder = 1,
                    Description = "Basic map layer"
                },
                new GeoLayer
                {
                    Id = Guid.NewGuid(),
                    Name = "Транспортна карта",
                    LayerName = "transport_map",
                    Workspace = "transport",
                    WmsUrl = "http://test.com/transport",
                    LayerType = "Tile",
                    IsVisible = false,
                    IsBaseLayer = true,
                    DisplayOrder = 2,
                    Description = "Transport focused map"
                }
            };

            var generalLayers = new List<GeoLayer>
            {
                new GeoLayer
                {
                    Id = Guid.NewGuid(),
                    Name = "Велоалеи",
                    LayerName = "bike_lanes",
                    Workspace = "transport",
                    WmsUrl = "http://test.com/bikes",
                    LayerType = "WMS",
                    IsVisible = false,
                    IsBaseLayer = false,
                    DisplayOrder = 10,
                    Description = "Bicycle lanes in Bulgaria"
                },
                new GeoLayer
                {
                    Id = Guid.NewGuid(),
                    Name = "Железопътни линии",
                    LayerName = "railways",
                    Workspace = "transport",
                    WmsUrl = "http://test.com/railways",
                    LayerType = "WMS",
                    IsVisible = false,
                    IsBaseLayer = false,
                    DisplayOrder = 11,
                    Description = "Railway network in Bulgaria"
                }
            };

            await _context.GeoLayers.AddRangeAsync(baseLayers.Concat(generalLayers));
            await _context.SaveChangesAsync();

            // Act
            var result = await _geoLayerService.GetAllLayersAsync();

            // Assert
            result.Should().HaveCount(4);

            var baseLayerResults = result.Where(l => l.IsBaseLayer).ToList();
            var generalLayerResults = result.Where(l => !l.IsBaseLayer).ToList();

            baseLayerResults.Should().HaveCount(2);
            baseLayerResults.Should().Contain(l => l.Name == "Basemap");
            baseLayerResults.Should().Contain(l => l.Name == "Транспортна карта");

            generalLayerResults.Should().HaveCount(2);
            generalLayerResults.Should().Contain(l => l.Name == "Велоалеи");
            generalLayerResults.Should().Contain(l => l.Name == "Железопътни линии");
        }

        [Fact]
        public async Task GetAllLayersAsync_ShouldIncludeDescriptions()
        {
            // Arrange
            var layer = new GeoLayer
            {
                Id = Guid.NewGuid(),
                Name = "Test Layer with Description",
                LayerName = "test_desc",
                Workspace = "test",
                WmsUrl = "http://test.com/desc",
                LayerType = "WMS",
                IsVisible = false,
                IsBaseLayer = false,
                DisplayOrder = 1,
                Description = "This is a test layer with detailed description for info modal"
            };

            await _context.GeoLayers.AddAsync(layer);
            await _context.SaveChangesAsync();

            // Act
            var result = await _geoLayerService.GetAllLayersAsync();

            // Assert
            result.Should().HaveCount(1);
            var resultLayer = result.First();
            resultLayer.Description.Should().Be("This is a test layer with detailed description for info modal");
            resultLayer.Name.Should().Be("Test Layer with Description");
        }

        [Fact]
        public async Task GetAllLayersAsync_ShouldOrderByDisplayOrderThenName()
        {
            // Arrange
            var layers = new List<GeoLayer>
            {
                new GeoLayer
                {
                    Id = Guid.NewGuid(),
                    Name = "Z Layer",
                    LayerName = "z_layer",
                    Workspace = "test",
                    WmsUrl = "http://test.com/z",
                    LayerType = "WMS",
                    IsVisible = false,
                    IsBaseLayer = false,
                    DisplayOrder = 1
                },
                new GeoLayer
                {
                    Id = Guid.NewGuid(),
                    Name = "A Layer",
                    LayerName = "a_layer",
                    Workspace = "test",
                    WmsUrl = "http://test.com/a",
                    LayerType = "WMS",
                    IsVisible = false,
                    IsBaseLayer = false,
                    DisplayOrder = 2
                },
                new GeoLayer
                {
                    Id = Guid.NewGuid(),
                    Name = "B Layer",
                    LayerName = "b_layer",
                    Workspace = "test",
                    WmsUrl = "http://test.com/b",
                    LayerType = "WMS",
                    IsVisible = false,
                    IsBaseLayer = false,
                    DisplayOrder = 1
                }
            };

            await _context.GeoLayers.AddRangeAsync(layers);
            await _context.SaveChangesAsync();

            // Act
            var result = await _geoLayerService.GetAllLayersAsync();

            // Assert
            result.Should().HaveCount(3);

            // Should be ordered by DisplayOrder first, then by Name
            result.ElementAt(0).Name.Should().Be("B Layer"); // DisplayOrder 1, Name B
            result.ElementAt(1).Name.Should().Be("Z Layer"); // DisplayOrder 1, Name Z
            result.ElementAt(2).Name.Should().Be("A Layer"); // DisplayOrder 2, Name A
        }

        public void Dispose()
        {
            _context.Dispose();
        }
    }
}
