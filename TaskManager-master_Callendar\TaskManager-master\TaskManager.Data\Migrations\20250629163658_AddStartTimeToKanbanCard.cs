﻿using System;
using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace TaskManager.Data.Migrations
{
    public partial class AddStartTimeToKanbanCard : Migration
    {
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.AddColumn<DateTime>(
                name: "StartTime",
                table: "KanbanCards",
                type: "datetime2",
                nullable: true);

            migrationBuilder.UpdateData(
                table: "AspNetUsers",
                keyColumn: "Id",
                keyValue: new Guid("296193d2-f02c-4410-b8fe-9eab4ffb4801"),
                columns: new[] { "ConcurrencyStamp", "PasswordHash" },
                values: new object[] { "f9d757ab-3213-40f8-9e4b-75e3dbca83d8", "AQAAAAEAACcQAAAAEI1y2H2HQZOUcf9OQIlpl5/oeBJwuKODKUaTypnbwarMfQ2KSsGDOIjGbBSSUbVyxA==" });

            migrationBuilder.UpdateData(
                table: "AspNetUsers",
                keyColumn: "Id",
                keyValue: new Guid("35679ef5-bb0b-4d6a-bb37-6b0bfb32e801"),
                columns: new[] { "ConcurrencyStamp", "PasswordHash" },
                values: new object[] { "329292f2-1c1a-4129-bfa2-1a81c55f0f8c", "AQAAAAEAACcQAAAAEHNEin4ZpP8zIdn2Hd0b14+USD4xrP+oPE+8fM/dt9puuhe5lHqfGRx/x2PNEJmELw==" });

            migrationBuilder.UpdateData(
                table: "AspNetUsers",
                keyColumn: "Id",
                keyValue: new Guid("6cef6e21-5151-4e88-be5c-f4f78953bf84"),
                columns: new[] { "ConcurrencyStamp", "PasswordHash" },
                values: new object[] { "1858b83a-6c12-4293-bdfe-c2a3fbec3fb7", "AQAAAAEAACcQAAAAEFIxkqFnpqxfAClD2LpPIehuojLE+fT/ggaOMXWAHg0Z2T+F3b0Sun4z8A66vVyoNw==" });

            migrationBuilder.UpdateData(
                table: "AspNetUsers",
                keyColumn: "Id",
                keyValue: new Guid("6e70d07b-eaa7-4384-b233-c5ef9c0bf8de"),
                columns: new[] { "ConcurrencyStamp", "PasswordHash" },
                values: new object[] { "5b270c16-0eac-468a-bc0d-1dc454de0124", "AQAAAAEAACcQAAAAEE9pLhoj+KYAjnDi8xSeEIScu9HfQ0PeXzI/t0e745EIsBFX1NGcl6/Au25HVdAA3A==" });

            migrationBuilder.UpdateData(
                table: "AspNetUsers",
                keyColumn: "Id",
                keyValue: new Guid("bcb4f072-ecca-43c9-ab26-c060c6f364e4"),
                columns: new[] { "ConcurrencyStamp", "PasswordHash" },
                values: new object[] { "c6302cfd-9b1c-409a-b18a-1558d04a5ffe", "AQAAAAEAACcQAAAAEL/GMoNchx3IW6BirfsLHN9lamHGzOcNqXJE5auYyDPOC3o6+9QLd/81g4qJXJ2PDA==" });

            migrationBuilder.UpdateData(
                table: "Comentars",
                keyColumn: "Id",
                keyValue: 1,
                column: "CreateDate",
                value: new DateTime(2025, 6, 29, 19, 36, 57, 831, DateTimeKind.Local).AddTicks(8338));

            migrationBuilder.UpdateData(
                table: "Comentars",
                keyColumn: "Id",
                keyValue: 2,
                column: "CreateDate",
                value: new DateTime(2025, 6, 29, 19, 36, 57, 831, DateTimeKind.Local).AddTicks(8374));

            migrationBuilder.UpdateData(
                table: "Comentars",
                keyColumn: "Id",
                keyValue: 3,
                column: "CreateDate",
                value: new DateTime(2025, 6, 29, 19, 36, 57, 831, DateTimeKind.Local).AddTicks(8378));

            migrationBuilder.UpdateData(
                table: "Comentars",
                keyColumn: "Id",
                keyValue: 4,
                column: "CreateDate",
                value: new DateTime(2025, 6, 29, 19, 36, 57, 831, DateTimeKind.Local).AddTicks(8381));

            migrationBuilder.UpdateData(
                table: "GeoTasks",
                keyColumn: "Id",
                keyValue: new Guid("6e6de662-426f-4ff2-9886-984fa1c46a81"),
                columns: new[] { "CreateDate", "EndDate" },
                values: new object[] { new DateTime(2025, 6, 29, 19, 36, 57, 832, DateTimeKind.Local).AddTicks(1858), new DateTime(2025, 6, 29, 19, 36, 57, 832, DateTimeKind.Local).AddTicks(1859) });

            migrationBuilder.UpdateData(
                table: "GeoTasks",
                keyColumn: "Id",
                keyValue: new Guid("*************-4962-9c31-d32e86f6ec0c"),
                columns: new[] { "CreateDate", "EndDate" },
                values: new object[] { new DateTime(2025, 6, 29, 19, 36, 57, 832, DateTimeKind.Local).AddTicks(1825), new DateTime(2025, 6, 29, 19, 36, 57, 832, DateTimeKind.Local).AddTicks(1832) });

            migrationBuilder.UpdateData(
                table: "Salaries",
                keyColumn: "Id",
                keyValue: 1,
                column: "ChangeDate",
                value: new DateTime(2025, 6, 29, 19, 36, 57, 832, DateTimeKind.Local).AddTicks(5880));

            migrationBuilder.UpdateData(
                table: "Salaries",
                keyColumn: "Id",
                keyValue: 2,
                column: "ChangeDate",
                value: new DateTime(2025, 6, 29, 19, 36, 57, 832, DateTimeKind.Local).AddTicks(5887));

            migrationBuilder.UpdateData(
                table: "Salaries",
                keyColumn: "Id",
                keyValue: 3,
                column: "ChangeDate",
                value: new DateTime(2025, 6, 29, 19, 36, 57, 832, DateTimeKind.Local).AddTicks(5890));
        }

        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropColumn(
                name: "StartTime",
                table: "KanbanCards");

            migrationBuilder.UpdateData(
                table: "AspNetUsers",
                keyColumn: "Id",
                keyValue: new Guid("296193d2-f02c-4410-b8fe-9eab4ffb4801"),
                columns: new[] { "ConcurrencyStamp", "PasswordHash" },
                values: new object[] { "d3c59d6b-3e13-429a-920b-f6d8612caff9", "AQAAAAEAACcQAAAAEHbmR+oq25IoxJSNhrXLvE8DrPUx6ZZo6mTP/Ynm7NtXI1czw9mWef35+lOT6v/Vmg==" });

            migrationBuilder.UpdateData(
                table: "AspNetUsers",
                keyColumn: "Id",
                keyValue: new Guid("35679ef5-bb0b-4d6a-bb37-6b0bfb32e801"),
                columns: new[] { "ConcurrencyStamp", "PasswordHash" },
                values: new object[] { "26f96a78-e522-431a-b563-f2b52791c5d6", "AQAAAAEAACcQAAAAEE/lt6CSjvJRJnN3i+iUrz+t9nbAtmCRsJzB0J5vk9wkWIRZkihZtFDpWs8wYnQ9WA==" });

            migrationBuilder.UpdateData(
                table: "AspNetUsers",
                keyColumn: "Id",
                keyValue: new Guid("6cef6e21-5151-4e88-be5c-f4f78953bf84"),
                columns: new[] { "ConcurrencyStamp", "PasswordHash" },
                values: new object[] { "09448cbc-70fc-4307-a61f-e54a6aebbfb7", "AQAAAAEAACcQAAAAEFbQ+yopAa3eRWjHwGqoTvzOwGdW9MRoPYZjI3Yx+Ku6My/DgqJfEMeO8c5DcUcfGA==" });

            migrationBuilder.UpdateData(
                table: "AspNetUsers",
                keyColumn: "Id",
                keyValue: new Guid("6e70d07b-eaa7-4384-b233-c5ef9c0bf8de"),
                columns: new[] { "ConcurrencyStamp", "PasswordHash" },
                values: new object[] { "cdbeca77-15d2-4c61-9daf-c532e3579aa2", "AQAAAAEAACcQAAAAEKzfwDiEuydXLlL7kXUDN05kgRBJR/qnR53VvFcVqqfeWAFpp5cu/jTGCRbRrTIpVg==" });

            migrationBuilder.UpdateData(
                table: "AspNetUsers",
                keyColumn: "Id",
                keyValue: new Guid("bcb4f072-ecca-43c9-ab26-c060c6f364e4"),
                columns: new[] { "ConcurrencyStamp", "PasswordHash" },
                values: new object[] { "2dc149c8-4f28-45fa-b2fa-a5bb26d6c7e3", "AQAAAAEAACcQAAAAEMVjwssIuGkvS3AUJaZdVS2I2YsCcbaJ7PPQ0/3Br7hA3oyqdSakNcslxMkuOf1gvQ==" });

            migrationBuilder.UpdateData(
                table: "Comentars",
                keyColumn: "Id",
                keyValue: 1,
                column: "CreateDate",
                value: new DateTime(2025, 6, 29, 12, 15, 57, 107, DateTimeKind.Local).AddTicks(8359));

            migrationBuilder.UpdateData(
                table: "Comentars",
                keyColumn: "Id",
                keyValue: 2,
                column: "CreateDate",
                value: new DateTime(2025, 6, 29, 12, 15, 57, 107, DateTimeKind.Local).AddTicks(8395));

            migrationBuilder.UpdateData(
                table: "Comentars",
                keyColumn: "Id",
                keyValue: 3,
                column: "CreateDate",
                value: new DateTime(2025, 6, 29, 12, 15, 57, 107, DateTimeKind.Local).AddTicks(8398));

            migrationBuilder.UpdateData(
                table: "Comentars",
                keyColumn: "Id",
                keyValue: 4,
                column: "CreateDate",
                value: new DateTime(2025, 6, 29, 12, 15, 57, 107, DateTimeKind.Local).AddTicks(8400));

            migrationBuilder.UpdateData(
                table: "GeoTasks",
                keyColumn: "Id",
                keyValue: new Guid("6e6de662-426f-4ff2-9886-984fa1c46a81"),
                columns: new[] { "CreateDate", "EndDate" },
                values: new object[] { new DateTime(2025, 6, 29, 12, 15, 57, 108, DateTimeKind.Local).AddTicks(1585), new DateTime(2025, 6, 29, 12, 15, 57, 108, DateTimeKind.Local).AddTicks(1586) });

            migrationBuilder.UpdateData(
                table: "GeoTasks",
                keyColumn: "Id",
                keyValue: new Guid("*************-4962-9c31-d32e86f6ec0c"),
                columns: new[] { "CreateDate", "EndDate" },
                values: new object[] { new DateTime(2025, 6, 29, 12, 15, 57, 108, DateTimeKind.Local).AddTicks(1563), new DateTime(2025, 6, 29, 12, 15, 57, 108, DateTimeKind.Local).AddTicks(1574) });

            migrationBuilder.UpdateData(
                table: "Salaries",
                keyColumn: "Id",
                keyValue: 1,
                column: "ChangeDate",
                value: new DateTime(2025, 6, 29, 12, 15, 57, 108, DateTimeKind.Local).AddTicks(5280));

            migrationBuilder.UpdateData(
                table: "Salaries",
                keyColumn: "Id",
                keyValue: 2,
                column: "ChangeDate",
                value: new DateTime(2025, 6, 29, 12, 15, 57, 108, DateTimeKind.Local).AddTicks(5284));

            migrationBuilder.UpdateData(
                table: "Salaries",
                keyColumn: "Id",
                keyValue: 3,
                column: "ChangeDate",
                value: new DateTime(2025, 6, 29, 12, 15, 57, 108, DateTimeKind.Local).AddTicks(5287));
        }
    }
}
