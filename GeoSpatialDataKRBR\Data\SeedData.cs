namespace GeoSpatialDataKRBR.Data
{
    using Microsoft.AspNetCore.Identity;
    using Microsoft.EntityFrameworkCore;
    using GeoSpatialDataKRBR.Data.Models;
    using GeoSpatialDataKRBR.Common;

    public static class SeedData
    {
        public static async Task Initialize(IServiceProvider serviceProvider)
        {
            using var context = new GeoSpatialDbContext(
                serviceProvider.GetRequiredService<DbContextOptions<GeoSpatialDbContext>>());

            var userManager = serviceProvider.GetRequiredService<UserManager<ApplicationUser>>();
            var roleManager = serviceProvider.GetRequiredService<RoleManager<IdentityRole<Guid>>>();

            // Ensure database is created
            await context.Database.EnsureCreatedAsync();

            // Seed roles
            await SeedRoles(roleManager);

            // Seed admin user
            await SeedAdminUser(userManager);

            // Seed sample GeoServer configuration
            await SeedGeoServerConfiguration(context);

            // Seed sample layers
            await SeedSampleLayers(context);
        }

        private static async Task SeedRoles(RoleManager<IdentityRole<Guid>> roleManager)
        {
            var roles = new[]
            {
                GeneralApplicationConstants.AdminRoleName,
                GeneralApplicationConstants.UserRoleName
            };

            foreach (var roleName in roles)
            {
                if (!await roleManager.RoleExistsAsync(roleName))
                {
                    var role = new IdentityRole<Guid>
                    {
                        Id = Guid.NewGuid(),
                        Name = roleName,
                        NormalizedName = roleName.ToUpper()
                    };
                    await roleManager.CreateAsync(role);
                }
            }
        }

        private static async Task SeedAdminUser(UserManager<ApplicationUser> userManager)
        {
            const string adminEmail = "<EMAIL>";
            const string adminPassword = "Admin123!";

            var adminUser = await userManager.FindByEmailAsync(adminEmail);
            if (adminUser == null)
            {
                adminUser = new ApplicationUser
                {
                    UserName = adminEmail,
                    Email = adminEmail,
                    EmailConfirmed = true,
                    FirstName = "Администратор",
                    LastName = "Система",
                    IsActive = true,
                    CreatedOn = DateTime.UtcNow
                };

                var result = await userManager.CreateAsync(adminUser, adminPassword);
                if (result.Succeeded)
                {
                    await userManager.AddToRoleAsync(adminUser, GeneralApplicationConstants.AdminRoleName);
                }
            }
        }

        private static async Task SeedGeoServerConfiguration(GeoSpatialDbContext context)
        {
            if (!await context.GeoServerConfigurations.AnyAsync())
            {
                var geoServerConfig = new GeoServerConfiguration
                {
                    Name = "Локален GeoServer КККР",
                    BaseUrl = "http://localhost:8080/geoserver",
                    Username = "admin",
                    Password = "geoserver",
                    DefaultWorkspace = "cite",
                    IsActive = true,
                    CreatedOn = DateTime.UtcNow
                };

                await context.GeoServerConfigurations.AddAsync(geoServerConfig);
                await context.SaveChangesAsync();
            }
        }

        private static async Task SeedSampleLayers(GeoSpatialDbContext context)
        {
            // Always reseed layers to ensure we have the latest ones
            // Remove existing layers first
            var existingLayers = await context.GeoLayers.ToListAsync();
            if (existingLayers.Any())
            {
                context.GeoLayers.RemoveRange(existingLayers);
                await context.SaveChangesAsync();
            }

            // Add new layers
            {
                var geoServerConfig = await context.GeoServerConfigurations.FirstAsync();

                var sampleLayers = new[]
                {
                    // Base layers
                    new GeoLayer
                    {
                        Name = "Basemap",
                        Description = "Основна карта с базова информация",
                        LayerName = "basemap",
                        Workspace = "base",
                        WmsUrl = "https://tile.openstreetmap.org/{z}/{x}/{y}.png",
                        LayerType = GeneralApplicationConstants.TileLayerType,
                        IsVisible = true,
                        IsBaseLayer = true,
                        DisplayOrder = 1,
                        Opacity = 1.0,
                        GeoServerConfigurationId = geoServerConfig.Id,
                        CreatedOn = DateTime.UtcNow
                    },
                    new GeoLayer
                    {
                        Name = "Транспортна карта",
                        Description = "Карта с акцент върху транспортната инфраструктура",
                        LayerName = "transport_map",
                        Workspace = "transport",
                        WmsUrl = "https://{s}.tile.opentopomap.org/{z}/{x}/{y}.png",
                        LayerType = GeneralApplicationConstants.TileLayerType,
                        IsVisible = false,
                        IsBaseLayer = true,
                        DisplayOrder = 2,
                        Opacity = 1.0,
                        GeoServerConfigurationId = geoServerConfig.Id,
                        CreatedOn = DateTime.UtcNow
                    },
                    // General layers
                    new GeoLayer
                    {
                        Name = "Велоалеи",
                        Description = "Велосипедни алеи и пътеки в България",
                        LayerName = "bike_lanes",
                        Workspace = "transport",
                        WmsUrl = $"{geoServerConfig.BaseUrl}/wms",
                        WfsUrl = $"{geoServerConfig.BaseUrl}/wfs",
                        LayerType = GeneralApplicationConstants.WmsLayerType,
                        IsVisible = false,
                        IsBaseLayer = false,
                        DisplayOrder = 10,
                        Opacity = 0.8,
                        GeoServerConfigurationId = geoServerConfig.Id,
                        CreatedOn = DateTime.UtcNow
                    },
                    new GeoLayer
                    {
                        Name = "Железопътни линии",
                        Description = "Железопътна мрежа на България",
                        LayerName = "railways",
                        Workspace = "transport",
                        WmsUrl = $"{geoServerConfig.BaseUrl}/wms",
                        WfsUrl = $"{geoServerConfig.BaseUrl}/wfs",
                        LayerType = GeneralApplicationConstants.WmsLayerType,
                        IsVisible = false,
                        IsBaseLayer = false,
                        DisplayOrder = 11,
                        Opacity = 0.9,
                        GeoServerConfigurationId = geoServerConfig.Id,
                        CreatedOn = DateTime.UtcNow
                    }
                };

                await context.GeoLayers.AddRangeAsync(sampleLayers);
                await context.SaveChangesAsync();
            }
        }
    }
}
