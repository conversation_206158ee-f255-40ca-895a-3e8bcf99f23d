namespace GeoSpatialDataKRBR.Data
{
    using Microsoft.AspNetCore.Identity;
    using Microsoft.EntityFrameworkCore;
    using GeoSpatialDataKRBR.Data.Models;
    using GeoSpatialDataKRBR.Common;

    public static class SeedData
    {
        public static async Task Initialize(IServiceProvider serviceProvider)
        {
            using var context = new GeoSpatialDbContext(
                serviceProvider.GetRequiredService<DbContextOptions<GeoSpatialDbContext>>());

            var userManager = serviceProvider.GetRequiredService<UserManager<ApplicationUser>>();
            var roleManager = serviceProvider.GetRequiredService<RoleManager<IdentityRole<Guid>>>();

            // Ensure database is created
            await context.Database.EnsureCreatedAsync();

            // Seed roles
            await SeedRoles(roleManager);

            // Seed admin user
            await SeedAdminUser(userManager);

            // Seed sample GeoServer configuration
            await SeedGeoServerConfiguration(context);

            // Seed sample layers
            await SeedSampleLayers(context);
        }

        private static async Task SeedRoles(RoleManager<IdentityRole<Guid>> roleManager)
        {
            var roles = new[]
            {
                GeneralApplicationConstants.AdminRoleName,
                GeneralApplicationConstants.UserRoleName
            };

            foreach (var roleName in roles)
            {
                if (!await roleManager.RoleExistsAsync(roleName))
                {
                    var role = new IdentityRole<Guid>
                    {
                        Id = Guid.NewGuid(),
                        Name = roleName,
                        NormalizedName = roleName.ToUpper()
                    };
                    await roleManager.CreateAsync(role);
                }
            }
        }

        private static async Task SeedAdminUser(UserManager<ApplicationUser> userManager)
        {
            const string adminEmail = "<EMAIL>";
            const string adminPassword = "Admin123!";

            var adminUser = await userManager.FindByEmailAsync(adminEmail);
            if (adminUser == null)
            {
                adminUser = new ApplicationUser
                {
                    UserName = adminEmail,
                    Email = adminEmail,
                    EmailConfirmed = true,
                    FirstName = "Администратор",
                    LastName = "Система",
                    IsActive = true,
                    CreatedOn = DateTime.UtcNow
                };

                var result = await userManager.CreateAsync(adminUser, adminPassword);
                if (result.Succeeded)
                {
                    await userManager.AddToRoleAsync(adminUser, GeneralApplicationConstants.AdminRoleName);
                }
            }
        }

        private static async Task SeedGeoServerConfiguration(GeoSpatialDbContext context)
        {
            if (!await context.GeoServerConfigurations.AnyAsync())
            {
                var geoServerConfig = new GeoServerConfiguration
                {
                    Name = "Локален GeoServer КККР",
                    BaseUrl = "http://localhost:8080/geoserver",
                    Username = "admin",
                    Password = "geoserver",
                    DefaultWorkspace = "cite",
                    IsActive = true,
                    CreatedOn = DateTime.UtcNow
                };

                await context.GeoServerConfigurations.AddAsync(geoServerConfig);
                await context.SaveChangesAsync();
            }
        }

        private static async Task SeedSampleLayers(GeoSpatialDbContext context)
        {
            if (!await context.GeoLayers.AnyAsync())
            {
                var geoServerConfig = await context.GeoServerConfigurations.FirstAsync();

                var sampleLayers = new[]
                {
                    new GeoLayer
                    {
                        Name = "OpenStreetMap",
                        Description = "Базов слой OpenStreetMap",
                        LayerName = "osm",
                        Workspace = "osm",
                        WmsUrl = "https://tile.openstreetmap.org/{z}/{x}/{y}.png",
                        LayerType = GeneralApplicationConstants.TileLayerType,
                        IsVisible = true,
                        IsBaseLayer = true,
                        DisplayOrder = 0,
                        Opacity = 1.0,
                        GeoServerConfigurationId = geoServerConfig.Id,
                        CreatedOn = DateTime.UtcNow
                    },
                    new GeoLayer
                    {
                        Name = "Сгради OSM",
                        Description = "Сгради от OpenStreetMap Buildings",
                        LayerName = "buildings",
                        Workspace = "osm",
                        WmsUrl = "https://tiles.osmbuildings.org/0.2/anonymous/{z}/{x}/{y}.png",
                        LayerType = GeneralApplicationConstants.TileLayerType,
                        IsVisible = false,
                        IsBaseLayer = false,
                        DisplayOrder = 1,
                        Opacity = 0.7,
                        GeoServerConfigurationId = geoServerConfig.Id,
                        CreatedOn = DateTime.UtcNow
                    },
                    new GeoLayer
                    {
                        Name = "Сателитни снимки",
                        Description = "Сателитни снимки от Esri",
                        LayerName = "satellite",
                        Workspace = "esri",
                        WmsUrl = "https://server.arcgisonline.com/ArcGIS/rest/services/World_Imagery/MapServer/tile/{z}/{y}/{x}",
                        LayerType = GeneralApplicationConstants.TileLayerType,
                        IsVisible = false,
                        IsBaseLayer = true,
                        DisplayOrder = 1,
                        Opacity = 1.0,
                        GeoServerConfigurationId = geoServerConfig.Id,
                        CreatedOn = DateTime.UtcNow
                    },
                    new GeoLayer
                    {
                        Name = "Топографска карта",
                        Description = "Топографска карта от OpenTopoMap",
                        LayerName = "topo",
                        Workspace = "osm",
                        WmsUrl = "https://tile.opentopomap.org/{z}/{x}/{y}.png",
                        LayerType = GeneralApplicationConstants.TileLayerType,
                        IsVisible = false,
                        IsBaseLayer = true,
                        DisplayOrder = 2,
                        Opacity = 1.0,
                        GeoServerConfigurationId = geoServerConfig.Id,
                        CreatedOn = DateTime.UtcNow
                    },
                    new GeoLayer
                    {
                        Name = "Кадастрални парцели",
                        Description = "Кадастрални парцели от OpenStreetMap",
                        LayerName = "cadastral",
                        Workspace = "cite",
                        WmsUrl = "http://localhost:8080/geoserver/cite/wms",
                        WfsUrl = "http://localhost:8080/geoserver/cite/wfs",
                        LayerType = GeneralApplicationConstants.WmsLayerType,
                        IsVisible = false,
                        IsBaseLayer = false,
                        DisplayOrder = 3,
                        Opacity = 1.0,
                        GeoServerConfigurationId = geoServerConfig.Id,
                        CreatedOn = DateTime.UtcNow
                    }
                };

                await context.GeoLayers.AddRangeAsync(sampleLayers);
                await context.SaveChangesAsync();
            }
        }
    }
}
