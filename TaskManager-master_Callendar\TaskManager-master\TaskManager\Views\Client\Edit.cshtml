﻿@using static TaskManager.Common.NotificationMessages;
@model ClientFormModel

 <div class="row">
   <div class="col-sm-12 offset-lg-4 col-lg-8 offset-xl-3 col-xl-6">
      <form method="post">
         <div class="form-group">
            <label asp-for="Name"></label>
            <input asp-for="Name" class="form-control" placeholder="Име...">
            <span asp-validation-for="Name" class="small text-danger"></span>
         </div>
         <div class="form-group" data-untrustedinput="@Model.CustomerRepresentative">
            <label asp-for="CustomerRepresentative"></label>
            <input asp-for="CustomerRepresentative" class="form-control" placeholder="Име...">
            <span asp-validation-for="CustomerRepresentative" class="small text-danger"></span>
         </div>
         <div class="form-group">
            <label asp-for="PhoneNumber"></label>
            <input asp-for="PhoneNumber" class="form-control"
               placeholder="+359...">
            <span asp-validation-for="PhoneNumber" class="small text-danger"></span>
         </div>
         <div class="form-group">
            <label asp-for="Email"></label>
            <input asp-for="Email" class="form-control"
               placeholder="<EMAIL>...">
            <span asp-validation-for="Email" class="small text-danger"></span>
         </div>
         <div class="text-center">
            <input class="btn btn-primary mt-3" type="submit" value="Промени" />
         </div>
      </form>
   </div>
</div>

@section Scripts
    {
            <partial name ="_ValidationScriptsPartial">
}
