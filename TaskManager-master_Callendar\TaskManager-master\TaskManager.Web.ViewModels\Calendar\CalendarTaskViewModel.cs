using System.ComponentModel.DataAnnotations;
using TaskManager.Web.ViewModels.Comentar;

namespace TaskManager.Web.ViewModels.Calendar
{
    public class CalendarTaskViewModel
    {
        public string Id { get; set; } = null!;

        public string Title { get; set; } = null!;

        public string Description { get; set; } = null!;

        public List<string> AssignedMemberIds { get; set; } = new List<string>();

        public string Date { get; set; } = null!;

        public string StartTime { get; set; } = null!;

        public string EndTime { get; set; } = null!;

        public string Color { get; set; } = "#3b82f6";

        public DateTime CreatedDate { get; set; }

        public DateTime? ModifiedDate { get; set; }

        // GeoTask related properties (if connected)
        public string? GeoTaskId { get; set; }

        public string? ClientName { get; set; }

        public string? TaskType { get; set; }

        public string? Status { get; set; }

        public int? ProjectNumber { get; set; }

        public string? Address { get; set; }

        public decimal? Price { get; set; }

        public int? Quantity { get; set; }

        public string? Note { get; set; }

        // Comments from GeoTask (if connected)
        public List<ComentarViewModel> Comments { get; set; } = new List<ComentarViewModel>();

        // Assigned workers details
        public List<CalendarWorkerViewModel> AssignedWorkers { get; set; } = new List<CalendarWorkerViewModel>();
    }
}
