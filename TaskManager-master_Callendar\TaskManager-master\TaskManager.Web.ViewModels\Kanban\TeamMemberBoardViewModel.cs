namespace TaskManager.Web.ViewModels.Kanban
{
    /// <summary>
    /// View model for displaying team member board information
    /// </summary>
    public class TeamMemberBoardViewModel
    {
        public string MemberId { get; set; } = null!;

        public string MemberName { get; set; } = null!;

        public string MemberPosition { get; set; } = null!;

        public string BoardId { get; set; } = null!;

        public string BoardName { get; set; } = null!;

        public int TotalCards { get; set; }

        public int InProgressCards { get; set; }

        public int CompletedCards { get; set; }

        public DateTime LastActivity { get; set; }

        public bool IsCurrentUser { get; set; }
    }
}
