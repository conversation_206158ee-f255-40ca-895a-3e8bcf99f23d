﻿@using static TaskManager.Common.NotificationMessages;
@model TypeViewModel

@{
    ViewData["Title"] = "Добави тип задача";
}

<div class="modern-container">
    <div class="modern-page-header">
        <h1 class="modern-page-title">
            <i class="fas fa-plus-circle"></i>
            Добави нов тип задача
        </h1>
        <p class="modern-page-subtitle">
            Създайте нов тип задача за системата
        </p>
    </div>

    <div class="modern-card">
        <div class="modern-card-header">
            <h3>
                <i class="fas fa-tag"></i>
                Информация за типа
            </h3>
        </div>
        <div class="modern-card-body">
            <form method="post" class="modern-type-form">
                <div class="form-group">
                    <label asp-for="Name" class="modern-form-label">
                        <i class="fas fa-tags"></i>
                        Име на типа <span class="required">*</span>
                    </label>
                    <input asp-for="Name" class="modern-form-control" placeholder="Въведете име на типа задача...">
                    <span asp-validation-for="Name" class="modern-validation-error"></span>
                    <div class="form-help">
                        <i class="fas fa-info-circle"></i>
                        Примери: Геодезическо заснемане, Кадастрални услуги, Топографски план
                    </div>
                </div>

                <div class="form-actions">
                    <button type="submit" name="Add" class="modern-btn modern-btn-primary">
                        <i class="fas fa-save"></i>
                        Добави тип
                    </button>
                    <a asp-controller="Type" asp-action="All" class="modern-btn modern-btn-secondary">
                        <i class="fas fa-arrow-left"></i>
                        Назад към списъка
                    </a>
                </div>
            </form>
        </div>
    </div>

    <!-- Preview Section -->
    <div class="modern-card">
        <div class="modern-card-header">
            <h3>
                <i class="fas fa-eye"></i>
                Преглед
            </h3>
        </div>
        <div class="modern-card-body">
            <div class="type-preview">
                <div class="preview-item">
                    <span class="type-badge preview" id="typePreview">
                        <i class="fas fa-tag"></i>
                        Нов тип
                    </span>
                </div>
                <p class="preview-description">Така ще изглежда новият тип в системата</p>
            </div>
        </div>
    </div>
</div>

@section Scripts
{
    <partial name="_ValidationScriptsPartial">

    <script>
        $(document).ready(function() {
            // Live preview of type name
            $('input[name="Name"]').on('input', function() {
                const typeName = $(this).val() || 'Нов тип';
                $('#typePreview').html('<i class="fas fa-tag"></i> ' + typeName);
            });
        });
    </script>
}
