namespace TaskManager.Services.Data.Interfaces
{
    using TaskManager.Data.Models;
    using TaskManager.Web.ViewModels.Kanban;

    /// <summary>
    /// Service interface for Kanban board operations
    /// </summary>
    public interface IKanbanService
    {
        /// <summary>
        /// Gets the main team kanban board
        /// </summary>
        /// <returns>Kanban board view model</returns>
        Task<KanbanBoardViewModel> GetTeamBoardAsync();

        /// <summary>
        /// Gets a specific member's kanban board
        /// </summary>
        /// <param name="memberId">Member ID</param>
        /// <returns>Kanban board view model</returns>
        Task<KanbanBoardViewModel> GetMemberBoardAsync(string memberId);

        /// <summary>
        /// Gets a specific member's Kanban board by User ID
        /// </summary>
        /// <param name="userId">User ID</param>
        /// <returns>Member's Kanban board</returns>
        Task<KanbanBoardViewModel> GetMemberBoardByUserIdAsync(string userId);

        /// <summary>
        /// Gets all team members with their boards
        /// </summary>
        /// <returns>List of team members with board info</returns>
        Task<IEnumerable<TeamMemberBoardViewModel>> GetAllMemberBoardsAsync();

        /// <summary>
        /// Creates a new kanban card
        /// </summary>
        /// <param name="model">Card creation model</param>
        /// <returns>Created card ID</returns>
        Task<string> CreateCardAsync(CreateKanbanCardViewModel model);

        /// <summary>
        /// Updates an existing kanban card
        /// </summary>
        /// <param name="model">Card update model</param>
        Task UpdateCardAsync(UpdateKanbanCardViewModel model);

        /// <summary>
        /// Moves a card to a different column
        /// </summary>
        /// <param name="cardId">Card ID</param>
        /// <param name="newColumnId">Target column ID</param>
        /// <param name="newPosition">New position in column</param>
        Task MoveCardAsync(string cardId, string newColumnId, int newPosition);

        /// <summary>
        /// Deletes a kanban card
        /// </summary>
        /// <param name="cardId">Card ID</param>
        Task DeleteCardAsync(string cardId);

        /// <summary>
        /// Creates a kanban card automatically when a GeoTask is created
        /// </summary>
        /// <param name="geoTaskId">GeoTask ID</param>
        /// <param name="title">Card title</param>
        /// <param name="assignedToId">Assigned user ID</param>
        Task CreateCardForGeoTaskAsync(string geoTaskId, string title, string? assignedToId = null);

        /// <summary>
        /// Updates the kanban card when a GeoTask is updated
        /// </summary>
        /// <param name="geoTaskId">GeoTask ID</param>
        /// <param name="title">Updated title</param>
        /// <param name="assignedToId">Updated assigned user ID</param>
        Task UpdateCardForGeoTaskAsync(string geoTaskId, string title, string? assignedToId = null);

        /// <summary>
        /// Gets a specific kanban card by ID
        /// </summary>
        /// <param name="cardId">Card ID</param>
        /// <returns>Card view model</returns>
        Task<KanbanCardViewModel?> GetCardByIdAsync(string cardId);

        /// <summary>
        /// Initializes the default team board with default columns
        /// </summary>
        Task InitializeDefaultBoardAsync();

        /// <summary>
        /// Checks if the default board exists
        /// </summary>
        /// <returns>True if board exists</returns>
        Task<bool> DefaultBoardExistsAsync();

        /// <summary>
        /// Gets all GeoTasks that don't have associated Kanban cards
        /// </summary>
        /// <returns>List of GeoTasks without cards</returns>
        Task<List<GeoTask>> GetGeoTasksWithoutCardsAsync();

        /// <summary>
        /// Gets all columns for a worker's personal board
        /// </summary>
        /// <param name="userId">User ID of the worker</param>
        /// <returns>List of columns for the worker's board</returns>
        Task<List<KanbanColumnViewModel>> GetWorkerBoardColumnsAsync(string userId);

        /// <summary>
        /// Gets detailed information about a specific card for editing
        /// </summary>
        /// <param name="cardId">Card ID</param>
        /// <returns>Card details object</returns>
        Task<object> GetCardDetailsAsync(string cardId);

        /// <summary>
        /// Gets comments for a specific card
        /// </summary>
        /// <param name="cardId">Card ID</param>
        /// <returns>List of comments</returns>
        Task<List<object>> GetCardCommentsAsync(string cardId);

        /// <summary>
        /// Adds a comment to a card
        /// </summary>
        /// <param name="cardId">Card ID</param>
        /// <param name="comment">Comment text</param>
        /// <param name="userEmail">User email</param>
        Task AddCardCommentAsync(string cardId, string comment, string userEmail);
    }
}
