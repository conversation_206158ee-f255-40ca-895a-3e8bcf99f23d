namespace GeoSpatialDataKRBR.Controllers.Api
{
    using Microsoft.AspNetCore.Authorization;
    using Microsoft.AspNetCore.Mvc;
    using GeoSpatialDataKRBR.Services.Data.Interfaces;
    using GeoSpatialDataKRBR.Web.ViewModels.GeoLayer;
    using GeoSpatialDataKRBR.Data.Models;
    using System.Security.Claims;

    [ApiController]
    [Route("api/[controller]")]
    [Authorize]
    [IgnoreAntiforgeryToken]
    public class GeoLayerApiController : ControllerBase
    {
        private readonly IGeoLayerService geoLayerService;
        private readonly IUserLayerPreferenceService userLayerPreferenceService;

        public GeoLayerApiController(
            IGeoLayerService geoLayerService,
            IUserLayerPreferenceService userLayerPreferenceService)
        {
            this.geoLayerService = geoLayerService;
            this.userLayerPreferenceService = userLayerPreferenceService;
        }

        [HttpGet]
        public async Task<IActionResult> GetAllLayers()
        {
            try
            {
                var layers = await this.geoLayerService.GetAllLayersAsync();
                var layerViewModels = layers.Select(l => new GeoLayerListViewModel
                {
                    Id = l.Id,
                    Name = l.Name,
                    Description = l.Description,
                    LayerName = l.LayerName,
                    Workspace = l.Workspace,
                    WmsUrl = l.WmsUrl,
                    WfsUrl = l.WfsUrl,
                    LayerType = l.LayerType,
                    IsVisible = l.IsVisible,
                    IsBaseLayer = l.IsBaseLayer,
                    DisplayOrder = l.DisplayOrder,
                    Opacity = l.Opacity,
                    GeoServerConfigurationName = l.GeoServerConfiguration?.Name,
                    CreatedOn = l.CreatedOn
                });

                return Ok(layerViewModels);
            }
            catch (Exception ex)
            {
                return StatusCode(500, new { message = "Грешка при зареждане на слоевете", error = ex.Message });
            }
        }

        [HttpGet("visible")]
        public async Task<IActionResult> GetVisibleLayers()
        {
            try
            {
                var userId = GetCurrentUserId();
                var layers = await this.geoLayerService.GetLayersByUserPreferencesAsync(userId);
                
                var layerViewModels = layers.Select(l => new GeoLayerListViewModel
                {
                    Id = l.Id,
                    Name = l.Name,
                    Description = l.Description,
                    LayerName = l.LayerName,
                    Workspace = l.Workspace,
                    WmsUrl = l.WmsUrl,
                    WfsUrl = l.WfsUrl,
                    LayerType = l.LayerType,
                    IsVisible = l.IsVisible,
                    IsBaseLayer = l.IsBaseLayer,
                    DisplayOrder = l.DisplayOrder,
                    Opacity = l.Opacity,
                    GeoServerConfigurationName = l.GeoServerConfiguration?.Name,
                    CreatedOn = l.CreatedOn
                });

                return Ok(layerViewModels);
            }
            catch (Exception ex)
            {
                return StatusCode(500, new { message = "Грешка при зареждане на видимите слоеве", error = ex.Message });
            }
        }

        [HttpGet("{id:guid}")]
        public async Task<IActionResult> GetLayer(Guid id)
        {
            try
            {
                var layer = await this.geoLayerService.GetLayerByIdAsync(id);
                if (layer == null)
                {
                    return NotFound(new { message = "Слоят не беше намерен" });
                }

                var layerViewModel = new GeoLayerViewModel
                {
                    Id = layer.Id,
                    Name = layer.Name,
                    Description = layer.Description,
                    LayerName = layer.LayerName,
                    Workspace = layer.Workspace,
                    WmsUrl = layer.WmsUrl,
                    WfsUrl = layer.WfsUrl,
                    StyleName = layer.StyleName,
                    LayerType = layer.LayerType,
                    IsVisible = layer.IsVisible,
                    IsBaseLayer = layer.IsBaseLayer,
                    DisplayOrder = layer.DisplayOrder,
                    Opacity = layer.Opacity,
                    GeoServerConfigurationId = layer.GeoServerConfigurationId ?? Guid.Empty,
                    GeoServerConfigurationName = layer.GeoServerConfiguration?.Name,
                    CreatedOn = layer.CreatedOn,
                    ModifiedOn = layer.ModifiedOn
                };

                return Ok(layerViewModel);
            }
            catch (Exception ex)
            {
                return StatusCode(500, new { message = "Грешка при зареждане на слоя", error = ex.Message });
            }
        }

        [HttpPost("toggle-visibility/{id:guid}")]
        public async Task<IActionResult> ToggleLayerVisibility(Guid id)
        {
            try
            {
                // Check if user is authenticated
                if (!IsUserAuthenticated())
                {
                    return Ok(new { message = "Слоят беше променен локално (потребителят не е логнат)" });
                }

                var userId = GetCurrentUserId();
                var success = await this.userLayerPreferenceService.ToggleLayerVisibilityAsync(userId, id);

                if (success)
                {
                    return Ok(new { message = "Видимостта на слоя беше променена успешно" });
                }

                return BadRequest(new { message = "Грешка при промяна на видимостта" });
            }
            catch (Exception ex)
            {
                return StatusCode(500, new { message = "Грешка при промяна на видимостта", error = ex.Message });
            }
        }

        [HttpPost("update-opacity/{id:guid}")]
        public async Task<IActionResult> UpdateLayerOpacity(Guid id, [FromBody] double opacity)
        {
            try
            {
                if (opacity < 0 || opacity > 1)
                {
                    return BadRequest(new { message = "Прозрачността трябва да бъде между 0 и 1" });
                }

                // Check if user is authenticated
                if (!IsUserAuthenticated())
                {
                    return Ok(new { message = "Прозрачността беше променена локално (потребителят не е логнат)" });
                }

                var userId = GetCurrentUserId();
                var success = await this.userLayerPreferenceService.UpdateLayerOpacityAsync(userId, id, opacity);

                if (success)
                {
                    return Ok(new { message = "Прозрачността беше променена успешно" });
                }

                return BadRequest(new { message = "Грешка при промяна на прозрачността" });
            }
            catch (Exception ex)
            {
                return StatusCode(500, new { message = "Грешка при промяна на прозрачността", error = ex.Message });
            }
        }

        [HttpPost("update-order/{id:guid}")]
        public async Task<IActionResult> UpdateLayerOrder(Guid id, [FromBody] int displayOrder)
        {
            try
            {
                var userId = GetCurrentUserId();
                var success = await this.userLayerPreferenceService.UpdateLayerOrderAsync(userId, id, displayOrder);
                
                if (success)
                {
                    return Ok(new { message = "Редът на слоя беше променен успешно" });
                }
                
                return BadRequest(new { message = "Грешка при промяна на реда" });
            }
            catch (Exception ex)
            {
                return StatusCode(500, new { message = "Грешка при промяна на реда", error = ex.Message });
            }
        }

        [HttpGet("user-preferences")]
        public async Task<IActionResult> GetUserPreferences()
        {
            try
            {
                var userId = GetCurrentUserId();
                var preferences = await this.userLayerPreferenceService.GetUserPreferencesAsync(userId);
                
                return Ok(preferences);
            }
            catch (Exception ex)
            {
                return StatusCode(500, new { message = "Грешка при зареждане на потребителските настройки", error = ex.Message });
            }
        }

        [HttpDelete("user-preferences/reset")]
        public async Task<IActionResult> ResetUserPreferences()
        {
            try
            {
                var userId = GetCurrentUserId();
                var success = await this.userLayerPreferenceService.ResetUserPreferencesAsync(userId);
                
                if (success)
                {
                    return Ok(new { message = "Потребителските настройки бяха нулирани успешно" });
                }
                
                return BadRequest(new { message = "Грешка при нулиране на настройките" });
            }
            catch (Exception ex)
            {
                return StatusCode(500, new { message = "Грешка при нулиране на настройките", error = ex.Message });
            }
        }

        private Guid GetCurrentUserId()
        {
            var userIdClaim = User.FindFirst(ClaimTypes.NameIdentifier)?.Value;
            if (Guid.TryParse(userIdClaim, out var userId))
            {
                return userId;
            }
            throw new UnauthorizedAccessException("Невалиден потребител");
        }

        private bool IsUserAuthenticated()
        {
            return User.Identity?.IsAuthenticated == true;
        }
    }
}
