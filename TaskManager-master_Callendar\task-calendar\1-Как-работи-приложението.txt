КАЛЕНДАРНО ПРИЛОЖЕНИЕ ЗА УПРАВЛЕНИЕ НА ЗАДАЧИ
=====================================================

ОБЩО ОПИСАНИЕ:
Това е уеб-базирано календарно приложение, създадено с React, което позволява управление на задачи и екипи в седмичен изглед. Приложението е напълно локализирано на български език и оптимизирано за мобилни устройства.

ОСНОВНИ ФУНКЦИОНАЛНОСТИ:
========================

1. КАЛЕНДАРЕН ИЗГЛЕД:
   - Седмичен календар с 7 дни (Понеделник - Неделя)
   - Времеви слотове от 08:00 до 18:00 на всеки 30 минути
   - Компактен дизайн с полета с височина намалена 2.2 пъти
   - Минимална ширина 1800px за гарантирана видимост на всички 7 колони
   - Пълна поддръжка на български текст без съкращения

2. УПРАВЛЕНИЕ НА ЗАДАЧИ:
   - Създаване на нови задачи чрез кликване върху времеви слот
   - Редактиране на съществуващи задачи
   - Изтриване на задачи
   - Преместване на задачи между различни дни и часове (drag & drop)
   - Преоразмеряване на задачи за промяна на продължителността
   - Задачите могат да обхващат няколко часа без повторение

3. УПРАВЛЕНИЕ НА ЕКИПИ:
   - Странична лента с членове на екипа
   - Възможност за показване/скриване на задачи по екипи
   - Множество припокриващи се екипи
   - Инициали на работниците в избрани цветове
   - Чекбоксове за избор на екипни членове (позиционирани отдясно)
   - Работниците са некъкнати по подразбиране
   - Имената на работниците са кликаеми бутони за подчертаване на задачи
   - Икони "око" за показване/скриване на функционалност

4. ФОРМИ ЗА ЗАДАЧИ:
   - Падащи менюта за избор на членове на задачата
   - Полета за заглавие, описание, време и продължителност
   - Валидация на данните
   - Автоматично запазване на промените

5. НАВИГАЦИЯ:
   - Кликване върху ден в месечен изглед води към седмичен изглед
   - Кликване върху месец в годишен изглед води към месечен изглед
   - Бутони за навигация между седмици
   - Бърза навигация до днешна дата

ТЕХНИЧЕСКИ ДЕТАЙЛИ:
==================

АРХИТЕКТУРА:
- Frontend: React с функционални компоненти и hooks
- Стилизиране: CSS с Flexbox за responsive дизайн
- Състояние: React useState и useEffect hooks
- Локализация: Пълна българска локализация

ФАЙЛОВА СТРУКТУРА:
- src/App.js - Основен компонент на приложението
- src/App.css - Стилове с responsive дизайн
- src/components/ - Компоненти за календар, задачи, екипи
- public/ - Статични файлове

RESPONSIVE ДИЗАЙН:
- Desktop (>1024px): Пълен календар с всички функции
- Tablet (768px-1024px): Адаптиран интерфейс
- Mobile (480px-768px): Компактен изглед
- Small Mobile (<480px): Минимален интерфейс

ЦВЕТОВА СХЕМА:
- Прости цветови палитри за UI дизайн
- Червено подчертаване за уикенди и български неработни дни
- Цветово кодиране на екипните членове
- Подчертаване на задачи при избор на работник

ИНТЕРАКЦИИ:
- Drag & drop за преместване на задачи
- Resize за промяна на продължителността
- Click за създаване/редактиране на задачи
- Touch-friendly за Android мобилни устройства
- Потвърждаващи съобщения за конфликти в разписанието

ЛОКАЛИЗАЦИЯ:
- Всички прозорци са напълно локализирани на български
- Български имена на дни и месеци
- Формат на дати съобразен с българските стандарти
- Понеделник като първи ден от седмицата

ПРОИЗВОДИТЕЛНОСТ:
- Оптимизирано за бързо зареждане
- Ефективно управление на състоянието
- Минимални re-renders
- Responsive изображения и стилове

СЪВМЕСТИМОСТ:
- Всички модерни браузъри
- Android мобилни браузъри
- Поддръжка за touch устройства
- Scalable за различни размери на екрана

ХОСТИНГ И РАЗГРЪЩАНЕ:
- Готово за production build
- Може да се хоства на всеки статичен хостинг
- Документация за временни 1-дневни хостинг решения
- Оптимизирано за CDN разпространение
