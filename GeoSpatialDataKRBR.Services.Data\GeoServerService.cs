namespace GeoSpatialDataKRBR.Services.Data
{
    using System.Text;
    using System.Text.Json;
    using GeoSpatialDataKRBR.Data.Models;
    using GeoSpatialDataKRBR.Services.Data.Interfaces;

    public class GeoServerService : IGeoServerService
    {
        private readonly IHttpClientFactory httpClientFactory;

        public GeoServerService(IHttpClientFactory httpClientFactory)
        {
            this.httpClientFactory = httpClientFactory;
        }

        public async Task<bool> TestConnectionAsync(GeoServerConfiguration configuration)
        {
            try
            {
                var client = this.httpClientFactory.CreateClient("GeoServer");
                var credentials = Convert.ToBase64String(Encoding.ASCII.GetBytes($"{configuration.Username}:{configuration.Password}"));
                client.DefaultRequestHeaders.Authorization = new System.Net.Http.Headers.AuthenticationHeaderValue("Basic", credentials);

                var url = $"{configuration.BaseUrl.TrimEnd('/')}/rest/workspaces.json";
                var response = await client.GetAsync(url);

                return response.IsSuccessStatusCode;
            }
            catch
            {
                return false;
            }
        }

        public async Task<IEnumerable<string>> GetWorkspacesAsync(GeoServerConfiguration configuration)
        {
            try
            {
                var client = this.httpClientFactory.CreateClient("GeoServer");
                var credentials = Convert.ToBase64String(Encoding.ASCII.GetBytes($"{configuration.Username}:{configuration.Password}"));
                client.DefaultRequestHeaders.Authorization = new System.Net.Http.Headers.AuthenticationHeaderValue("Basic", credentials);

                var url = $"{configuration.BaseUrl.TrimEnd('/')}/rest/workspaces.json";
                var response = await client.GetAsync(url);

                if (response.IsSuccessStatusCode)
                {
                    var content = await response.Content.ReadAsStringAsync();
                    var jsonDoc = JsonDocument.Parse(content);
                    
                    var workspaces = new List<string>();
                    if (jsonDoc.RootElement.TryGetProperty("workspaces", out var workspacesElement) &&
                        workspacesElement.TryGetProperty("workspace", out var workspaceArray))
                    {
                        foreach (var workspace in workspaceArray.EnumerateArray())
                        {
                            if (workspace.TryGetProperty("name", out var nameElement))
                            {
                                workspaces.Add(nameElement.GetString() ?? "");
                            }
                        }
                    }
                    
                    return workspaces;
                }
            }
            catch
            {
                // Log error
            }

            return new List<string>();
        }

        public async Task<IEnumerable<string>> GetLayersAsync(GeoServerConfiguration configuration, string workspace)
        {
            try
            {
                var client = this.httpClientFactory.CreateClient("GeoServer");
                var credentials = Convert.ToBase64String(Encoding.ASCII.GetBytes($"{configuration.Username}:{configuration.Password}"));
                client.DefaultRequestHeaders.Authorization = new System.Net.Http.Headers.AuthenticationHeaderValue("Basic", credentials);

                var url = $"{configuration.BaseUrl.TrimEnd('/')}/rest/workspaces/{workspace}/layers.json";
                var response = await client.GetAsync(url);

                if (response.IsSuccessStatusCode)
                {
                    var content = await response.Content.ReadAsStringAsync();
                    var jsonDoc = JsonDocument.Parse(content);
                    
                    var layers = new List<string>();
                    if (jsonDoc.RootElement.TryGetProperty("layers", out var layersElement) &&
                        layersElement.TryGetProperty("layer", out var layerArray))
                    {
                        foreach (var layer in layerArray.EnumerateArray())
                        {
                            if (layer.TryGetProperty("name", out var nameElement))
                            {
                                layers.Add(nameElement.GetString() ?? "");
                            }
                        }
                    }
                    
                    return layers;
                }
            }
            catch
            {
                // Log error
            }

            return new List<string>();
        }

        public async Task<string> GetLayerCapabilitiesAsync(GeoServerConfiguration configuration, string layerName)
        {
            try
            {
                var client = this.httpClientFactory.CreateClient("GeoServer");
                var url = $"{configuration.BaseUrl.TrimEnd('/')}/wms?service=WMS&version=1.3.0&request=GetCapabilities";
                var response = await client.GetAsync(url);

                if (response.IsSuccessStatusCode)
                {
                    return await response.Content.ReadAsStringAsync();
                }
            }
            catch
            {
                // Log error
            }

            return string.Empty;
        }

        public async Task<string> GetFeatureInfoAsync(GeoServerConfiguration configuration, string layerName, 
            double x, double y, string srs, int width, int height, string bbox)
        {
            try
            {
                var client = this.httpClientFactory.CreateClient("GeoServer");
                var url = $"{configuration.BaseUrl.TrimEnd('/')}/wms?" +
                         $"service=WMS&version=1.3.0&request=GetFeatureInfo&" +
                         $"layers={layerName}&query_layers={layerName}&" +
                         $"info_format=application/json&" +
                         $"i={x}&j={y}&" +
                         $"crs={srs}&" +
                         $"width={width}&height={height}&" +
                         $"bbox={bbox}";

                var response = await client.GetAsync(url);

                if (response.IsSuccessStatusCode)
                {
                    return await response.Content.ReadAsStringAsync();
                }
            }
            catch
            {
                // Log error
            }

            return string.Empty;
        }

        public async Task<byte[]> GetMapImageAsync(GeoServerConfiguration configuration, string layerName, 
            string bbox, int width, int height, string srs, string format = "image/png")
        {
            try
            {
                var client = this.httpClientFactory.CreateClient("GeoServer");
                var url = $"{configuration.BaseUrl.TrimEnd('/')}/wms?" +
                         $"service=WMS&version=1.3.0&request=GetMap&" +
                         $"layers={layerName}&" +
                         $"format={format}&" +
                         $"crs={srs}&" +
                         $"width={width}&height={height}&" +
                         $"bbox={bbox}";

                var response = await client.GetAsync(url);

                if (response.IsSuccessStatusCode)
                {
                    return await response.Content.ReadAsByteArrayAsync();
                }
            }
            catch
            {
                // Log error
            }

            return new byte[0];
        }

        public async Task<string> GetWfsFeatureAsync(GeoServerConfiguration configuration, string layerName, 
            string filter = "", int maxFeatures = 100)
        {
            try
            {
                var client = this.httpClientFactory.CreateClient("GeoServer");
                var url = $"{configuration.BaseUrl.TrimEnd('/')}/wfs?" +
                         $"service=WFS&version=2.0.0&request=GetFeature&" +
                         $"typeNames={layerName}&" +
                         $"outputFormat=application/json&" +
                         $"maxFeatures={maxFeatures}";

                if (!string.IsNullOrEmpty(filter))
                {
                    url += $"&filter={Uri.EscapeDataString(filter)}";
                }

                var response = await client.GetAsync(url);

                if (response.IsSuccessStatusCode)
                {
                    return await response.Content.ReadAsStringAsync();
                }
            }
            catch
            {
                // Log error
            }

            return string.Empty;
        }
    }
}
