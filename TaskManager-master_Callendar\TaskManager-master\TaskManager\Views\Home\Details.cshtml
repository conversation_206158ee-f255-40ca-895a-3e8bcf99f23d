﻿@using TaskManager.Services.Data.Interfaces;
@using TaskManager.Web.Infrastructure.Extentions;
@model FrontDescriptionTypeViewModel
@inject IUserService UserService;

@{
    bool isUserAdmin = this.User.isAdmin();
}
<h2 class="text-center">@Model.Title</h2>
<hr />

<div class="container" style="display:inline">
    <div class="row">
        <div class="col-4">
            <img class="card-img-top" style="width: 25rem;" src="@Model.ImageUrl" alt="@Model.Title">
        </div>
        <div class="card col-8 border-0">
            <p> <b>Описание:</b> @Model.Description</p>
            <p> <b>Цена за обиквовенна услуга: </b>@($"{Model.Price:F2} лв.")</p>
            <p> <b>Срок за изпълнение</b> @Model.TermDays работни дни.</p>
            @if (isUserAdmin)
            {
                 <div class="text">
                    <a class="btn btn-primary" asp-controller="FrontDescription" asp-action="Edit" asp-route-id="@Model.Id">Промени</a>
                 </div>               
            }
        </div>
    </div>
</div>