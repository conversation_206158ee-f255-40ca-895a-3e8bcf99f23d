﻿using System;
using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace TaskManager.Data.Migrations
{
    public partial class AddKanbanEntitiesFixed : Migration
    {
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.CreateTable(
                name: "KanbanBoards",
                columns: table => new
                {
                    Id = table.Column<Guid>(type: "uniqueidentifier", nullable: false),
                    Name = table.Column<string>(type: "nvarchar(100)", maxLength: 100, nullable: false),
                    Description = table.Column<string>(type: "nvarchar(500)", maxLength: 500, nullable: true),
                    CreatedOn = table.Column<DateTime>(type: "datetime2", nullable: false),
                    UpdatedOn = table.Column<DateTime>(type: "datetime2", nullable: true),
                    IsActive = table.Column<bool>(type: "bit", nullable: false, defaultValue: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_KanbanBoards", x => x.Id);
                });

            migrationBuilder.CreateTable(
                name: "KanbanColumns",
                columns: table => new
                {
                    Id = table.Column<Guid>(type: "uniqueidentifier", nullable: false),
                    Name = table.Column<string>(type: "nvarchar(50)", maxLength: 50, nullable: false),
                    Description = table.Column<string>(type: "nvarchar(200)", maxLength: 200, nullable: true),
                    Position = table.Column<int>(type: "int", nullable: false),
                    Color = table.Column<string>(type: "nvarchar(7)", maxLength: 7, nullable: true),
                    CreatedOn = table.Column<DateTime>(type: "datetime2", nullable: false),
                    UpdatedOn = table.Column<DateTime>(type: "datetime2", nullable: true),
                    IsActive = table.Column<bool>(type: "bit", nullable: false, defaultValue: true),
                    BoardId = table.Column<Guid>(type: "uniqueidentifier", nullable: false)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_KanbanColumns", x => x.Id);
                    table.ForeignKey(
                        name: "FK_KanbanColumns_KanbanBoards_BoardId",
                        column: x => x.BoardId,
                        principalTable: "KanbanBoards",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Cascade);
                });

            migrationBuilder.CreateTable(
                name: "KanbanCards",
                columns: table => new
                {
                    Id = table.Column<Guid>(type: "uniqueidentifier", nullable: false),
                    Title = table.Column<string>(type: "nvarchar(200)", maxLength: 200, nullable: false),
                    Description = table.Column<string>(type: "nvarchar(1000)", maxLength: 1000, nullable: true),
                    Position = table.Column<int>(type: "int", nullable: false),
                    Color = table.Column<string>(type: "nvarchar(7)", maxLength: 7, nullable: true),
                    Labels = table.Column<string>(type: "nvarchar(500)", maxLength: 500, nullable: true),
                    DueDate = table.Column<DateTime>(type: "datetime2", nullable: true),
                    CreatedOn = table.Column<DateTime>(type: "datetime2", nullable: false),
                    UpdatedOn = table.Column<DateTime>(type: "datetime2", nullable: true),
                    IsActive = table.Column<bool>(type: "bit", nullable: false, defaultValue: true),
                    ColumnId = table.Column<Guid>(type: "uniqueidentifier", nullable: false),
                    AssignedToId = table.Column<Guid>(type: "uniqueidentifier", nullable: true),
                    CreatedById = table.Column<Guid>(type: "uniqueidentifier", nullable: true),
                    GeoTaskId = table.Column<Guid>(type: "uniqueidentifier", nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_KanbanCards", x => x.Id);
                    table.ForeignKey(
                        name: "FK_KanbanCards_AspNetUsers_AssignedToId",
                        column: x => x.AssignedToId,
                        principalTable: "AspNetUsers",
                        principalColumn: "Id");
                    table.ForeignKey(
                        name: "FK_KanbanCards_AspNetUsers_CreatedById",
                        column: x => x.CreatedById,
                        principalTable: "AspNetUsers",
                        principalColumn: "Id");
                    table.ForeignKey(
                        name: "FK_KanbanCards_GeoTasks_GeoTaskId",
                        column: x => x.GeoTaskId,
                        principalTable: "GeoTasks",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.SetNull);
                    table.ForeignKey(
                        name: "FK_KanbanCards_KanbanColumns_ColumnId",
                        column: x => x.ColumnId,
                        principalTable: "KanbanColumns",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Cascade);
                });

            migrationBuilder.UpdateData(
                table: "AspNetUsers",
                keyColumn: "Id",
                keyValue: new Guid("296193d2-f02c-4410-b8fe-9eab4ffb4801"),
                columns: new[] { "ConcurrencyStamp", "PasswordHash" },
                values: new object[] { "d3c59d6b-3e13-429a-920b-f6d8612caff9", "AQAAAAEAACcQAAAAEHbmR+oq25IoxJSNhrXLvE8DrPUx6ZZo6mTP/Ynm7NtXI1czw9mWef35+lOT6v/Vmg==" });

            migrationBuilder.UpdateData(
                table: "AspNetUsers",
                keyColumn: "Id",
                keyValue: new Guid("35679ef5-bb0b-4d6a-bb37-6b0bfb32e801"),
                columns: new[] { "ConcurrencyStamp", "PasswordHash" },
                values: new object[] { "26f96a78-e522-431a-b563-f2b52791c5d6", "AQAAAAEAACcQAAAAEE/lt6CSjvJRJnN3i+iUrz+t9nbAtmCRsJzB0J5vk9wkWIRZkihZtFDpWs8wYnQ9WA==" });

            migrationBuilder.UpdateData(
                table: "AspNetUsers",
                keyColumn: "Id",
                keyValue: new Guid("6cef6e21-5151-4e88-be5c-f4f78953bf84"),
                columns: new[] { "ConcurrencyStamp", "PasswordHash" },
                values: new object[] { "09448cbc-70fc-4307-a61f-e54a6aebbfb7", "AQAAAAEAACcQAAAAEFbQ+yopAa3eRWjHwGqoTvzOwGdW9MRoPYZjI3Yx+Ku6My/DgqJfEMeO8c5DcUcfGA==" });

            migrationBuilder.UpdateData(
                table: "AspNetUsers",
                keyColumn: "Id",
                keyValue: new Guid("6e70d07b-eaa7-4384-b233-c5ef9c0bf8de"),
                columns: new[] { "ConcurrencyStamp", "PasswordHash" },
                values: new object[] { "cdbeca77-15d2-4c61-9daf-c532e3579aa2", "AQAAAAEAACcQAAAAEKzfwDiEuydXLlL7kXUDN05kgRBJR/qnR53VvFcVqqfeWAFpp5cu/jTGCRbRrTIpVg==" });

            migrationBuilder.UpdateData(
                table: "AspNetUsers",
                keyColumn: "Id",
                keyValue: new Guid("bcb4f072-ecca-43c9-ab26-c060c6f364e4"),
                columns: new[] { "ConcurrencyStamp", "PasswordHash" },
                values: new object[] { "2dc149c8-4f28-45fa-b2fa-a5bb26d6c7e3", "AQAAAAEAACcQAAAAEMVjwssIuGkvS3AUJaZdVS2I2YsCcbaJ7PPQ0/3Br7hA3oyqdSakNcslxMkuOf1gvQ==" });

            migrationBuilder.UpdateData(
                table: "Comentars",
                keyColumn: "Id",
                keyValue: 1,
                column: "CreateDate",
                value: new DateTime(2025, 6, 29, 12, 15, 57, 107, DateTimeKind.Local).AddTicks(8359));

            migrationBuilder.UpdateData(
                table: "Comentars",
                keyColumn: "Id",
                keyValue: 2,
                column: "CreateDate",
                value: new DateTime(2025, 6, 29, 12, 15, 57, 107, DateTimeKind.Local).AddTicks(8395));

            migrationBuilder.UpdateData(
                table: "Comentars",
                keyColumn: "Id",
                keyValue: 3,
                column: "CreateDate",
                value: new DateTime(2025, 6, 29, 12, 15, 57, 107, DateTimeKind.Local).AddTicks(8398));

            migrationBuilder.UpdateData(
                table: "Comentars",
                keyColumn: "Id",
                keyValue: 4,
                column: "CreateDate",
                value: new DateTime(2025, 6, 29, 12, 15, 57, 107, DateTimeKind.Local).AddTicks(8400));

            migrationBuilder.UpdateData(
                table: "GeoTasks",
                keyColumn: "Id",
                keyValue: new Guid("6e6de662-426f-4ff2-9886-984fa1c46a81"),
                columns: new[] { "CreateDate", "EndDate" },
                values: new object[] { new DateTime(2025, 6, 29, 12, 15, 57, 108, DateTimeKind.Local).AddTicks(1585), new DateTime(2025, 6, 29, 12, 15, 57, 108, DateTimeKind.Local).AddTicks(1586) });

            migrationBuilder.UpdateData(
                table: "GeoTasks",
                keyColumn: "Id",
                keyValue: new Guid("*************-4962-9c31-d32e86f6ec0c"),
                columns: new[] { "CreateDate", "EndDate" },
                values: new object[] { new DateTime(2025, 6, 29, 12, 15, 57, 108, DateTimeKind.Local).AddTicks(1563), new DateTime(2025, 6, 29, 12, 15, 57, 108, DateTimeKind.Local).AddTicks(1574) });

            migrationBuilder.UpdateData(
                table: "Salaries",
                keyColumn: "Id",
                keyValue: 1,
                column: "ChangeDate",
                value: new DateTime(2025, 6, 29, 12, 15, 57, 108, DateTimeKind.Local).AddTicks(5280));

            migrationBuilder.UpdateData(
                table: "Salaries",
                keyColumn: "Id",
                keyValue: 2,
                column: "ChangeDate",
                value: new DateTime(2025, 6, 29, 12, 15, 57, 108, DateTimeKind.Local).AddTicks(5284));

            migrationBuilder.UpdateData(
                table: "Salaries",
                keyColumn: "Id",
                keyValue: 3,
                column: "ChangeDate",
                value: new DateTime(2025, 6, 29, 12, 15, 57, 108, DateTimeKind.Local).AddTicks(5287));

            migrationBuilder.CreateIndex(
                name: "IX_KanbanCards_AssignedToId",
                table: "KanbanCards",
                column: "AssignedToId");

            migrationBuilder.CreateIndex(
                name: "IX_KanbanCards_ColumnId",
                table: "KanbanCards",
                column: "ColumnId");

            migrationBuilder.CreateIndex(
                name: "IX_KanbanCards_CreatedById",
                table: "KanbanCards",
                column: "CreatedById");

            migrationBuilder.CreateIndex(
                name: "IX_KanbanCards_GeoTaskId",
                table: "KanbanCards",
                column: "GeoTaskId",
                unique: true,
                filter: "[GeoTaskId] IS NOT NULL");

            migrationBuilder.CreateIndex(
                name: "IX_KanbanColumns_BoardId",
                table: "KanbanColumns",
                column: "BoardId");
        }

        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropTable(
                name: "KanbanCards");

            migrationBuilder.DropTable(
                name: "KanbanColumns");

            migrationBuilder.DropTable(
                name: "KanbanBoards");

            migrationBuilder.UpdateData(
                table: "AspNetUsers",
                keyColumn: "Id",
                keyValue: new Guid("296193d2-f02c-4410-b8fe-9eab4ffb4801"),
                columns: new[] { "ConcurrencyStamp", "PasswordHash" },
                values: new object[] { "fd026052-**************-08bc34f1e083", "AQAAAAEAACcQAAAAEF09tJIx9ByCAVc+9WKPJ5np1+TF3gSlLsfZeg+tlCGmWqGDNPHMlUzJuz/Hqb07/Q==" });

            migrationBuilder.UpdateData(
                table: "AspNetUsers",
                keyColumn: "Id",
                keyValue: new Guid("35679ef5-bb0b-4d6a-bb37-6b0bfb32e801"),
                columns: new[] { "ConcurrencyStamp", "PasswordHash" },
                values: new object[] { "fc93b80f-002b-4058-a5b3-c7e6ac805157", "AQAAAAEAACcQAAAAEBW8+Y76NUvPpTDHtl85+bowwgdpdj4hjpCdMquzGuQ/eJHgUIy1LTmBNGMK9Ewx0Q==" });

            migrationBuilder.UpdateData(
                table: "AspNetUsers",
                keyColumn: "Id",
                keyValue: new Guid("6cef6e21-5151-4e88-be5c-f4f78953bf84"),
                columns: new[] { "ConcurrencyStamp", "PasswordHash" },
                values: new object[] { "3a6b9e08-e4a8-4473-be84-61d8c2a01339", "AQAAAAEAACcQAAAAEM/kSoaNrfLiPEgzYtWT8Cv3KriNWBqhlcukpKdb58KcFbWo2BZ4XEfWV7JagEpZXA==" });

            migrationBuilder.UpdateData(
                table: "AspNetUsers",
                keyColumn: "Id",
                keyValue: new Guid("6e70d07b-eaa7-4384-b233-c5ef9c0bf8de"),
                columns: new[] { "ConcurrencyStamp", "PasswordHash" },
                values: new object[] { "2f52e977-2eb3-4b97-9151-1aace1fcaf06", "AQAAAAEAACcQAAAAEDOkaDfdOLbpXamlBEgej4eZXOqrYDL2Ut1R/WKm5Xe4XdauSvULxS0SIJ6ZsD01lA==" });

            migrationBuilder.UpdateData(
                table: "AspNetUsers",
                keyColumn: "Id",
                keyValue: new Guid("bcb4f072-ecca-43c9-ab26-c060c6f364e4"),
                columns: new[] { "ConcurrencyStamp", "PasswordHash" },
                values: new object[] { "3d54dd52-7415-409a-8837-ca37f1bcc9b4", "AQAAAAEAACcQAAAAECa2Waybi/ch1fNsHgW3aW26xopsI3YHTQ/SxTXPSI14rYTNDKsc45tDyqmAupj/Yg==" });

            migrationBuilder.UpdateData(
                table: "Comentars",
                keyColumn: "Id",
                keyValue: 1,
                column: "CreateDate",
                value: new DateTime(2025, 6, 28, 20, 56, 47, 473, DateTimeKind.Local).AddTicks(449));

            migrationBuilder.UpdateData(
                table: "Comentars",
                keyColumn: "Id",
                keyValue: 2,
                column: "CreateDate",
                value: new DateTime(2025, 6, 28, 20, 56, 47, 473, DateTimeKind.Local).AddTicks(487));

            migrationBuilder.UpdateData(
                table: "Comentars",
                keyColumn: "Id",
                keyValue: 3,
                column: "CreateDate",
                value: new DateTime(2025, 6, 28, 20, 56, 47, 473, DateTimeKind.Local).AddTicks(490));

            migrationBuilder.UpdateData(
                table: "Comentars",
                keyColumn: "Id",
                keyValue: 4,
                column: "CreateDate",
                value: new DateTime(2025, 6, 28, 20, 56, 47, 473, DateTimeKind.Local).AddTicks(493));

            migrationBuilder.UpdateData(
                table: "GeoTasks",
                keyColumn: "Id",
                keyValue: new Guid("6e6de662-426f-4ff2-9886-984fa1c46a81"),
                columns: new[] { "CreateDate", "EndDate" },
                values: new object[] { new DateTime(2025, 6, 28, 20, 56, 47, 473, DateTimeKind.Local).AddTicks(4585), new DateTime(2025, 6, 28, 20, 56, 47, 473, DateTimeKind.Local).AddTicks(4586) });

            migrationBuilder.UpdateData(
                table: "GeoTasks",
                keyColumn: "Id",
                keyValue: new Guid("*************-4962-9c31-d32e86f6ec0c"),
                columns: new[] { "CreateDate", "EndDate" },
                values: new object[] { new DateTime(2025, 6, 28, 20, 56, 47, 473, DateTimeKind.Local).AddTicks(4560), new DateTime(2025, 6, 28, 20, 56, 47, 473, DateTimeKind.Local).AddTicks(4574) });

            migrationBuilder.UpdateData(
                table: "Salaries",
                keyColumn: "Id",
                keyValue: 1,
                column: "ChangeDate",
                value: new DateTime(2025, 6, 28, 20, 56, 47, 473, DateTimeKind.Local).AddTicks(4806));

            migrationBuilder.UpdateData(
                table: "Salaries",
                keyColumn: "Id",
                keyValue: 2,
                column: "ChangeDate",
                value: new DateTime(2025, 6, 28, 20, 56, 47, 473, DateTimeKind.Local).AddTicks(4811));

            migrationBuilder.UpdateData(
                table: "Salaries",
                keyColumn: "Id",
                keyValue: 3,
                column: "ChangeDate",
                value: new DateTime(2025, 6, 28, 20, 56, 47, 473, DateTimeKind.Local).AddTicks(4814));
        }
    }
}
