using Microsoft.EntityFrameworkCore;
using Moq;
using TaskManager.Data;
using TaskManager.Data.Models;
using TaskManager.Services.Data;
using TaskManager.Services.Data.Interfaces;
using TaskManager.Web.ViewModels.Calendar;

namespace TaskManager.Services.Tests
{
    [TestFixture]
    public class CalendarConflictTests
    {
        private DbContextOptions<TaskManagerDbContext> dbContextOptions;
        private TaskManagerDbContext dbContext;
        private ICalendarService calendarService;
        private Guid worker1Id;
        private Guid worker2Id;

        [OneTimeSetUp]
        public void OneTimeSetUp()
        {
            this.dbContextOptions = new DbContextOptionsBuilder<TaskManagerDbContext>()
                .UseInMemoryDatabase("CalendarConflictTestDb" + Guid.NewGuid().ToString())
                .Options;

            this.dbContext = new TaskManagerDbContext(this.dbContextOptions);
            this.dbContext.Database.EnsureCreated();
            this.calendarService = new CalendarService(this.dbContext);

            SeedTestData();
        }

        private void SeedTestData()
        {
            // Add test users
            var user1 = new ApplicationUser
            {
                Id = Guid.NewGuid(),
                FirstName = "Петър",
                LastName = "Петров",
                Email = "<EMAIL>",
                UserName = "<EMAIL>"
            };

            var user2 = new ApplicationUser
            {
                Id = Guid.NewGuid(),
                FirstName = "Георги",
                LastName = "Георгиев",
                Email = "<EMAIL>",
                UserName = "<EMAIL>"
            };

            this.dbContext.Users.AddRange(user1, user2);

            // Add test workers
            var worker1 = new Worker
            {
                Id = Guid.NewGuid(),
                UserId = user1.Id,
                User = user1,
                PhoneNumber = "************",
                Position = "Test Position 1"
            };

            var worker2 = new Worker
            {
                Id = Guid.NewGuid(),
                UserId = user2.Id,
                User = user2,
                PhoneNumber = "************",
                Position = "Test Position 2"
            };

            this.worker1Id = worker1.Id;
            this.worker2Id = worker2.Id;

            this.dbContext.Workers.AddRange(worker1, worker2);
            this.dbContext.SaveChanges();
        }

        [Test]
        public async Task UpdateTask_WithEmptyDate_ShouldNotTriggerConflictAlert()
        {
            // Arrange
            var testDate = DateTime.Today;

            // Create a calendar task with a date
            var calendarTask = new CalendarTask
            {
                Id = Guid.NewGuid(),
                Title = "Test Task",
                Description = "Test Description",
                Date = testDate,
                StartTime = TimeSpan.FromHours(9), // 09:00
                EndTime = TimeSpan.FromHours(10)  // 10:00
            };

            // Add worker assignment
            var assignment = new CalendarTaskWorker
            {
                CalendarTaskId = calendarTask.Id,
                WorkerId = this.worker1Id
            };

            this.dbContext.CalendarTasks.Add(calendarTask);
            this.dbContext.CalendarTaskWorkers.Add(assignment);
            await this.dbContext.SaveChangesAsync();

            // Create update model - using DateTime.MinValue to represent empty date
            var updateModel = new UpdateCalendarTaskViewModel
            {
                Title = "Updated Task",
                Description = "Updated Description",
                Date = DateTime.MinValue, // Use MinValue to represent empty date
                StartTime = "09:00",
                EndTime = "10:00",
                AssignedMemberIds = new List<string> { this.worker1Id.ToString() }
            };

            // Act & Assert - Should not throw any conflict exception
            Assert.DoesNotThrowAsync(async () =>
                await this.calendarService.UpdateTaskAsync(calendarTask.Id.ToString(), updateModel));

            // Verify task was updated
            var updatedTask = await this.dbContext.CalendarTasks.FindAsync(calendarTask.Id);
            Assert.IsNotNull(updatedTask);
            Assert.That(updatedTask.Title, Is.EqualTo("Updated Task"));
        }

        [Test]
        public async Task UpdateTask_WithMinValueDate_ShouldNotTriggerConflictAlert()
        {
            // Arrange
            var testDate = DateTime.Today;

            var calendarTask = new CalendarTask
            {
                Id = Guid.NewGuid(),
                Title = "Test Task 2",
                Description = "Test Description 2",
                Date = testDate,
                StartTime = TimeSpan.FromHours(14), // 14:00
                EndTime = TimeSpan.FromHours(15)   // 15:00
            };

            var assignment = new CalendarTaskWorker
            {
                CalendarTaskId = calendarTask.Id,
                WorkerId = this.worker1Id
            };

            this.dbContext.CalendarTasks.Add(calendarTask);
            this.dbContext.CalendarTaskWorkers.Add(assignment);
            await this.dbContext.SaveChangesAsync();

            // Create update model with MinValue date (represents empty)
            var updateModel = new UpdateCalendarTaskViewModel
            {
                Title = "Updated Task 2",
                Description = "Updated Description 2",
                Date = DateTime.MinValue, // MinValue represents empty date
                StartTime = "14:00",
                EndTime = "15:00",
                AssignedMemberIds = new List<string> { this.worker1Id.ToString() }
            };

            // Act & Assert
            Assert.DoesNotThrowAsync(async () =>
                await this.calendarService.UpdateTaskAsync(calendarTask.Id.ToString(), updateModel));
        }

        [Test]
        public async Task UpdateTask_WithValidTimeConflict_ShouldUpdateSuccessfully()
        {
            // Arrange
            var testDate = DateTime.Today.AddDays(1);

            // Create first task
            var task1 = new CalendarTask
            {
                Id = Guid.NewGuid(),
                Title = "Existing Task",
                Description = "Existing Description",
                Date = testDate,
                StartTime = TimeSpan.FromHours(10), // 10:00
                EndTime = TimeSpan.FromHours(12)   // 12:00
            };

            var assignment1 = new CalendarTaskWorker
            {
                CalendarTaskId = task1.Id,
                WorkerId = this.worker1Id
            };

            // Create second task to update
            var task2 = new CalendarTask
            {
                Id = Guid.NewGuid(),
                Title = "Task to Update",
                Description = "Description to Update",
                Date = testDate.AddDays(1), // Different date initially
                StartTime = TimeSpan.FromHours(8),  // 08:00
                EndTime = TimeSpan.FromHours(9)    // 09:00
            };

            var assignment2 = new CalendarTaskWorker
            {
                CalendarTaskId = task2.Id,
                WorkerId = this.worker1Id
            };

            this.dbContext.CalendarTasks.AddRange(task1, task2);
            this.dbContext.CalendarTaskWorkers.AddRange(assignment1, assignment2);
            await this.dbContext.SaveChangesAsync();

            // Update second task to conflict with first task
            var updateModel = new UpdateCalendarTaskViewModel
            {
                Title = "Conflicting Task",
                Description = "This will conflict",
                Date = testDate, // Same date as task1
                StartTime = "11:00", // Overlaps with task1 (10:00-12:00)
                EndTime = "13:00",
                AssignedMemberIds = new List<string> { this.worker1Id.ToString() }
            };

            // Act & Assert - Backend service should update successfully
            // (conflict detection is handled in frontend JavaScript)
            Assert.DoesNotThrowAsync(async () =>
                await this.calendarService.UpdateTaskAsync(task2.Id.ToString(), updateModel));

            // Verify task was updated
            var updatedTask = await this.dbContext.CalendarTasks.FindAsync(task2.Id);
            Assert.IsNotNull(updatedTask);
            Assert.That(updatedTask.Title, Is.EqualTo("Conflicting Task"));
            Assert.That(updatedTask.Date, Is.EqualTo(testDate));
        }

        [Test]
        public async Task CreateTask_WithEmptyDate_ShouldNotTriggerConflictAlert()
        {
            // Arrange
            var createModel = new CreateCalendarTaskViewModel
            {
                Title = "New Task Without Date",
                Description = "No date set",
                Date = DateTime.MinValue, // MinValue represents empty date
                StartTime = "16:00",
                EndTime = "17:00",
                AssignedMemberIds = new List<string> { this.worker1Id.ToString() }
            };

            // Act & Assert
            Assert.DoesNotThrowAsync(async () =>
                await this.calendarService.CreateTaskAsync(createModel));
        }

        [Test]
        public async Task UpdateTask_RemovingDateFromExistingTask_ShouldNotTriggerConflictAlert()
        {
            // Arrange
            var testDate = DateTime.Today.AddDays(2);

            var calendarTask = new CalendarTask
            {
                Id = Guid.NewGuid(),
                Title = "Task With Date",
                Description = "Has a date initially",
                Date = testDate,
                StartTime = TimeSpan.FromHours(13), // 13:00
                EndTime = TimeSpan.FromHours(14)   // 14:00
            };

            var assignment = new CalendarTaskWorker
            {
                CalendarTaskId = calendarTask.Id,
                WorkerId = this.worker1Id
            };

            this.dbContext.CalendarTasks.Add(calendarTask);
            this.dbContext.CalendarTaskWorkers.Add(assignment);
            await this.dbContext.SaveChangesAsync();

            // Update to remove the date
            var updateModel = new UpdateCalendarTaskViewModel
            {
                Title = "Task Without Date",
                Description = "Date removed",
                Date = DateTime.MinValue, // MinValue represents removing the date
                StartTime = "13:00",
                EndTime = "14:00",
                AssignedMemberIds = new List<string> { this.worker1Id.ToString() }
            };

            // Act & Assert
            Assert.DoesNotThrowAsync(async () =>
                await this.calendarService.UpdateTaskAsync(calendarTask.Id.ToString(), updateModel));

            // Verify task was updated
            var updatedTask = await this.dbContext.CalendarTasks.FindAsync(calendarTask.Id);
            Assert.IsNotNull(updatedTask);
            Assert.That(updatedTask.Title, Is.EqualTo("Task Without Date"));
        }

        [TearDown]
        public void TearDown()
        {
            // Clean up test data after each test
            this.dbContext.CalendarTaskWorkers.RemoveRange(this.dbContext.CalendarTaskWorkers);
            this.dbContext.CalendarTasks.RemoveRange(this.dbContext.CalendarTasks);
            this.dbContext.SaveChanges();
        }

        [OneTimeTearDown]
        public void OneTimeTearDown()
        {
            this.dbContext.Database.EnsureDeleted();
            this.dbContext.Dispose();
        }


    }
}
